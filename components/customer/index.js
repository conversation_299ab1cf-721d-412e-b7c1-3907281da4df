const Customer = {

	init:() => {
		if(localStorage.getItem('customer')){
			window.customer = _n.merge(window.customer || {}, -1, JSON.parse(localStorage.getItem('customer')))
		}
		Customer.sync();
	},

	sync: () => {
		['email','first_name','last_name','phone','id'].forEach(field => {
			if (!customer.identity[field] && !!customer.account[field]) 
				Customer.identify(field, customer.account[field]);
		});
	},

	get:() => {
		return new Promise((res, rej) => {
			fetch('/account?view=data').then(r=>{
				if (r.redirected) { res(false); return; }
				r.json().then(d=>{
					Customer.set(d.customer).then(c=>{
						res(d.customer)	
					})
				})	
			})
			.catch(err => {
				console.error('Error retrieving customer data:', err);
				rej(err);
			});
		})
	},

	set:customer => {
    
	    return new Promise( (res, rej) => {

	    	if(customer && window.customer) {
	    		customer = {...window.customer, ...customer}
	    		const stored_customer = {... customer}
	    		delete stored_customer.account
	    		localStorage.setItem('customer',JSON.stringify(stored_customer))
	    	} else {
	    		localStorage.removeItem('customer')
	    	}
				
			window.customer = customer
	      
	      	_n.trigger(window, 'Customer:set',true,true, customer)
	      	res(customer);

	    })

	},

	logout:() => {
		return new Promise((res, rej) => {

      if(!_n.trigger(window, 'Customer:logout',true,true)) { res({}); return; }

			fetch('/account/logout').then(r=>r.text()).then(d=>{
				window.customer.account = false
				Customer.set({}).then(c=>{
					res(d.customer)	
				})
			})
		})
	},

	login:(login, password) => {

		let email = login

		if(typeof login == 'object' && login.email) {
			password = login.password
			email = login.email
		}

		return new Promise((res, rej) => {

			if(!_n.trigger(window, 'Customer:login',true,true, {email:email,password:password})) { res(false); return; }

			fetch('/account/login', {
				method:'POST',
				headers:{
					'Content-Type':'application/x-www-form-urlencoded'
				},
				body: _n.urlparams.build({
					form_type:'customer_login',
					utf8:'✓',
					'customer[email]': email,
				    'customer[password]': password
				})
			})
			.then(r=>r.text()).then(d=>{
				Customer.get().then(c=>{
					_n.trigger(window, 'Customer:authenticated',true,true, c)
					res(c)
				})
			})
			.catch(err => {
				console.error('Error logging in:', err);
				rej(err);
			});
		})
	},

	register: (login, password, firstName, lastName, phone) => {
		return new Promise((res, rej) => {

			let email = login

			if(typeof login == 'object' && login.email) {
				password = login.password
				email = login.email
			}

			if(!_n.trigger(window, 'Customer:register',true,true, {email:email,password:password,firstName:firstName,lastName:lastName,phone:phone})) { res(false); return; }

			fetch('/account', {
				method:'POST',
				headers:{
					'Content-Type':'application/x-www-form-urlencoded'
				},
				body: _n.urlparams.build({
					form_type:'create_customer',
					utf8:'✓',
					'customer[email]': email,
				    'customer[password]': password,
				    'customer[first_name]': firstName||'',
				    'customer[last_name]': lastName||'',
				    'customer[note][phone_number]': phone||'',
				})
			})
			.then(r=>r.text()).then(d=>{
				Customer.get().then(c=>{
					_n.trigger(window, 'Customer:registered',true,true, c)
					_n.trigger(window, 'Customer:authenticated',true,true, c)
					res(c)
				})
			})
			.catch(err => {
				console.error('Error registering customer:', err);
				rej(err);
			});
		})
	},

	form: form => {
		return new Promise((res, rej) => {
			
			if(!_n.trigger(window, 'Customer:formSubmit',true,true, {form:form})) { res(false); return; }

			fetch(form.dataset.action,{
				method:'POST',
				headers:{'Content-Type':'application/x-www-form-urlencoded'},
				body:_n.urlparams.build(Object.fromEntries(Array.from(form.querySelectorAll('input, select')).map(el=>{return[el.name,el.value]})))
			}).then(r=>r.text()).then(d=>{
				Customer.get().then(c=>{
					if(c) Modal.close()
					res(c)
				})
			})
		})
	},

	request: config => {
		return new Promise((res, rej) => {

			if(!_n.trigger(window, 'Customer:request',true,true, config)) { res(false); return; }

			if(!!customer.account.email) {
				return res(customer);
			} else {
				
				Modal.open('login')

				window.addEventListener('Customer:authenticated', e => {
					res(customer)
				})
			}
		})
	},

	update: (updates={}) => {
    	_n.trigger(window, 'Customer:update',true,true,{customer:customer,updates:updates})
    	return Customer.set(updates)
	},

	identify: (key, value) => {

		_n.trigger(window, 'Customer:identify',true,true,Object.fromEntries([[key,value]]))

		if(!!customer.identity[key] && (customer.identity[key] == value || JSON.stringify(customer.identity[key]) == JSON.stringify(value)) )return;

		customer.identity[key] = value

		Customer.update({'identity':customer.identity}).then(c=>{
			_n.trigger(window, 'Customer:identified',true,true,Object.fromEntries([[key,value]]))	
		});
		
	},

	subscriptions: {
		subscribe: (keys, source) => {
			const _keys = _n.array(keys).filter(key=>!customer.subscriptions[key.trim()]);
			_keys.forEach(key=>{
				customer.subscriptions[key.trim()] = true
			})
			_n.trigger(window, 'Customer:subscribe',true,true, {keys:_keys,source:source} )
			Customer.update({subscriptions:customer.subscriptions})
		},
		unsubscribe: keys => {
			_n.trigger(window, 'Customer:unsubscribe',true,true, keys)
			_n.array(keys).forEach(key=>{
				customer.subscriptions[key.trim()] = false
			})
			Customer.update({subscriptions:customer.subscriptions})
		},
		toggle: (key, source) => {
			if (customer.subscriptions[key.trim()]) return Customer.subscriptions.unsubscribe(key, source);
			return Customer.subscriptions.subscribe(key, source);
		}

	},

	peripheral:(key, data) => {

		customer.peripherals = customer.peripherals || {}
		customer.peripherals[key] = {
			updated:Date.now(),
			data:data
		}
		_n.trigger(window, 'Customer:peripheral',true,true, Object.fromEntries([[key,data]]));
		Customer.update({'peripherals':customer.peripherals});

	}
}

Customer.init()
window.Customer = Customer;
export default Customer;
