Collection.Endless = {

    scrollListener: false,
    scrollDirection: false,
    oldScroll: 0,
    selector: '[product-grid]',

    scroll: () => {
        if (!Collection.Endless.scrollListener) return;
        if (Collection.loading) return;
        Collection.Endless.scrollDirection = Collection.Endless.oldScroll > window.scrollY ? 'up' : 'down';
        if (Collection.Endless.scrollDirection == 'down') {
            if (document.querySelector(Collection.Endless.selector).getBoundingClientRect().bottom < 2000){

                let next = Math.max(...collection.pagination.pages.filter(p=>p.loaded).map(p=>p.number))+1;
                
                if(collection.pagination.pages.map(p=>p.number).includes(next)){
                
                    Collection.Endless.scrollListener = false
                    Collection.page(next)
                }
            }

        } else if (Collection.Endless.scrollDirection == 'up') {

            if (document.querySelector(Collection.Endless.selector).getBoundingClientRect().top > 0){
        
                let previous = Math.min(...collection.pagination.pages.filter(p=>p.loaded).map(p=>p.number))-1;
                
                if(collection.pagination.pages.map(p=>p.number).includes(previous)){
                
                    Collection.Endless.scrollListener = false

                    let beforeHeight = document.querySelector(Collection.Endless.selector).clientHeight+0;

                    Collection.page(previous).then(()=>{
                        let diffHeight = document.querySelector(Collection.Endless.selector).clientHeight - beforeHeight;
                        window.scrollTo(0, window.scrollY + diffHeight );
                    })
                }
            }
        }
        Collection.Endless.oldScroll = window.scrollY;
    },

    init: () => {
        window.addEventListener('scroll', Collection.Endless.scroll );
        Collection.Endless.scrollListener = true;
        console.log('Pagination Init');

        window.addEventListener('Collection:loaded', () => Collection.Endless.scrollListener = true)
    }

};

window.Endless =  Collection.Endless

if (collection.settings.paging == 'scroll') {
    window.addEventListener('Collection:data', Collection.Endless.init, { once: true });
}

export default Endless