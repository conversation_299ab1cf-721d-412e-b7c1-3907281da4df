_n.hooks = {

  registry: {},

  register: (event, func) => {
    // func is a function must return a promise
    if(!_n.hooks.registry[event]) _n.hooks.registry[event] = [];
    _n.hooks.registry[event].push(func)
    return true
  },

  process: (event, data) => {
    let result = Promise.resolve();
    (_n.hooks.registry[event]||[]).forEach((hooked, index) => {
      result = result.then(() =>
        new Promise(resolve => {
          resolve(hooked(data));
        })
      );
    });
    return result.then(() => data);
  }

}

_n.getCookie = (key) => {
  const obj = Object.fromEntries(document.cookie.split(';').map(v=>v.split('=').map(kv=>kv.trim())))
  if(!!key) return obj[key];
  return obj
}

_n.generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      var r = Math.random() * 16 | 0,
          v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
  });
}

_n.decode = (string) => {
  return string.replace(/&#(\d+);/g, (match, dec) => {
    return String.fromCharCode(dec)
  })
}