window.Geo = {

	config: window.geo_config,

  location:{
  	"country": "US"
  },
  
  selectedRegion: false,
  
  overrides: {
    region: false,
    country: false,
    currency: false
  },
  
  init:()=>{

    Geo.recall()
  	Geo.locate()

  },
  
  locate: () => {

    if(!!sessionStorage.getItem('region')){
      Geo.selectedRegion = sessionStorage.getItem('region');
    }

    if(!!sessionStorage.getItem('ipinfo')){
      Geo.location = JSON.parse(sessionStorage.getItem('ipinfo'))
      Geo.handle.region()
      Geo.handle.country()
    } else {
      fetch("https://ipinfo.io/json").then(r=>r.json()).then(d=>{
        if(d.country) {
          sessionStorage.setItem('ipinfo',JSON.stringify(d))
          Geo.location = d;
        }
        Geo.handle.region()
        Geo.handle.country()
      });
    }

  },
  
  override: (what, value) => {
    
    sessionStorage.setItem(`geo:override:${what}`, value)
    Geo.overrides[what] = value
    
  },

  recall: () => {

    for(what in sessionStorage) {
      if(what.includes('geo:override:'))
        Geo.overrides[what.replace('geo:override:','')] = sessionStorage[what]
    }
    for(what in _n.urlparams.get()) {
      if(what.includes('geo:override:'))
        Geo.overrides[what.replace('geo:override:','')] = _n.urlparams.get(what)
    }

  },
  
  handle: {

    region: () => {

      // what is current
      Geo.current_site = Geo.config.sites.find(s=>s.this_website)

      // what is the target
      Geo.target_site = Geo.config.sites.find(s=>s.countries.split(',').includes(Geo.location.country))
      
      // fallback to catch-all
      if(!Geo.target_site) Geo.target_site = Geo.config.sites.find( s => s.default)

      // allow region override
      if(!!Geo.overrides.region)
        Geo.target_site = Geo.config.sites.find(s=>s.region_code == Geo.overrides.region)

      if(!Geo.target_site || !Geo.current_site) return;

      if(Geo.target_site.region_code != Geo.current_site.region_code && Geo.target_site.redirect) {
        window.location = Geo.target_site.url;
        return;
      }

      if(Geo.target_site.region_code != Geo.current_site.region_code && Geo.target_site.modal) {
        Neptune.liquid.load('Geo');
        modal.open('international');
        return;
      }

    },

    country: () => {

      if(!Geo.current_site) return;

      if(!Geo.current_site.convert_currency_auto && !Geo.current_site.convert_currency_manual) return;

      Geo.target_country = Geo.location.country
      if(Geo.overrides.country) Geo.target_country = Geo.overrides.country 
      
      if(Shopify.country != Geo.target_country && Geo.current_site.convert_currency_auto) {
        Geo.localize(Geo.target_country)
      }

    }

  },

  localize: country => {

    Geo.override('country', country)
    _n.qs('#localization_form [name="country_code"]').value = country
    _n.qs('#localization_form').submit()

  }

}
window.addEventListener('DOMContentLoaded',Geo.init)