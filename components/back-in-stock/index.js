const BackInStock = {

  init: () => {
    const productOptionSelected = document.querySelector('.product-essentials--form .option-selection input[type="radio"]:checked');
    if (productOptionSelected) {
      BackInStock.show(productOptionSelected);
    }
  },

  send: (email, product, variant) => {

    const publicKey = atob(window.HSTV45);

    const options = {
      method: 'POST',
      headers: {
        accept: 'application/vnd.api+json',
        revision: '2024-10-15',
        'content-type': 'application/vnd.api+json'
      },
      body: JSON.stringify({
        data: {
          type: "back-in-stock-subscription",
          attributes: {
            profile: {
              data: {
                type: "profile",
                attributes: {
                  email: email
                }
              }
            },
            channels: ["EMAIL"]
          },
          relationships: {
            variant: {
              data: {
                type: "catalog-variant",
                id: `$shopify:::$default:::${variant}`
              }
            }
          }
        }
      })
    };

    fetch(`https://a.klaviyo.com/client/back-in-stock-subscriptions?company_id=${publicKey}`, options)
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.text().then(text => text ? JSON.parse(text) : {});
    })
    .then(response => {
      const trigger = _n.qs(`.back-in-stock button[data-product-id="${product}"]`);
      const parent = trigger.closest('.back-in-stock');

      if (response.errors) {
        parent.querySelector('.back-in-stock__message--prompt').textContent = response.errors[0].detail;
        parent.querySelector('.back-in-stock__message--prompt').classList.remove('hidden');
      } else {
        parent.querySelector('.back-in-stock__message--success').classList.remove('hidden');
        parent.querySelector('.back-in-stock__message--prompt').classList.add('hidden');
        parent.querySelector('.back-in-stock__field').classList.add('hidden');
        parent.querySelector('.back-in-stock__opt-in').classList.add('hidden');
      }
    })
    .catch(err => {
      console.error('Error in the request:', err);
    });
  },
  
  show: (inputSelected) => {
    if(document.querySelector('.product-item__variants') || document.querySelector('.product-essentials--form')){
      const inputParent = inputSelected.closest('.product-item__contents') || inputSelected.closest('.product-essentials--form')

      const bis = inputParent.querySelector('.back-in-stock');
      const addToCartButton = inputParent.querySelector('#AddToCart');

     
      if((inputSelected.getAttribute('data-available') == '0' && inputSelected.closest('.product-item__variants') != null) || (inputSelected.getAttribute('data-inventory') == '0' && inputSelected.closest('.product-essentials--form') != null)) {
        bis.classList.remove('hidden');
        if (addToCartButton) {
          addToCartButton.classList.add('hidden');
          addToCartButton.disabled = true;
        }
      } else {
        bis.classList.add('hidden');
        if (addToCartButton) {
          addToCartButton.classList.remove('hidden');
          addToCartButton.disabled = false;
        }
      }

      inputParent.querySelector('.back-in-stock__field button').dataset.variantId = inputSelected.dataset.variant;

      if (_n.parents(inputSelected, '.swiper-container')) {
        const swiper = _n.parents(inputSelected, '.swiper').swiper
        swiper.updateAutoHeight()
      }
    }
  },

  optin: (inputCheckbox) => {
    if (inputCheckbox.checked) {
      const backinstockParent = inputCheckbox.closest('.back-in-stock');
      const publicKey = atob(window.HSTV45);
      const listId = window.listId;
      const email = backinstockParent.querySelector('.back-in-stock__field input[type="text"]').value;
  
      const options = {
        method: 'POST',
        headers: {
          revision: '2024-10-15',
          'content-type': 'application/vnd.api+json'
        },
        body: JSON.stringify({
          data: {
            type: 'subscription',
            attributes: {
              profile: {
                data: {
                  type: 'profile',
                  attributes: {
                    email: email
                  }
                }
              }
            },
            relationships: {
              list: {
                data: {
                  type: 'list',
                  id: listId
                }
              }
            }
          }
        })
      };
  
      fetch(`https://a.klaviyo.com/client/subscriptions?company_id=${publicKey}`, options)
        .then(response => response.text())
        .then(response => console.log(response))
        .catch(err => console.error('error', err));
    }
  }

}

window.BackInStock = BackInStock;
export default BackInStock;

document.addEventListener('product:init', () => setTimeout(BackInStock.init, 100));