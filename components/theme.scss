/* Header
========================================================================== */

#shopify-section-header {
  z-index: 40;
  position: sticky;
  width: 100%;
  [scroll-direction="down"] & {
    position: sticky;
    transition: top 300ms ease-in-out;
    top: -32px;
  }

  [scroll-direction="up"] & {
    position: sticky;
    transition: top 300ms ease-in-out;
    top: 0px;
  }

  body:not(.scrolled) & {
    top: 0px;
  }
}

#shopify-section-search {
  z-index: 40;
  position: sticky;
  width: 100%;
  display: none;
  &.active {
    display: block;
  }
  [scroll-direction="down"] & {
    top: var(--dynamic-header-height);
    transition: top 300ms ease-in-out;
  }

  [scroll-direction="up"] & {
    top: 110px;
    transition: top 300ms ease-in-out;
  }
}

.header__logo svg {
  max-width: 100%;
}



[scroll-direction="down"] .top-live-header {
  top: 53px;
  transition: top 300ms ease-in-out;
}
[scroll-direction="up"] .top-live-header {
  top: 85px;
  transition: top 300ms ease-in-out;
}


.mega-menu {
  @apply hidden;
  .use-mega-menu & {
    @apply lg:block;
  }
}

.mega-overlay {
  @apply hidden;
  .use-mega-menu & {
    @apply lg:block;
  }
}

#shopify-section-panel-menu {
  .use-mega-menu & {
    @apply lg:hidden;
  }
}


@media(max-width: 1023px) {
  [scroll-direction="down"] .top-live-header {
    top: 41px;
  }
  [scroll-direction="up"] .top-live-header {
    top: 73px;
  }

}

.modal-overlay-insert {
  display: none;
}


.account-menu-wrapper {
  border: 1px solid #A0A0A0;
  display: none;
}

.account-dropdown-wrapper:hover .account-menu-wrapper {
  display: block;
}

[data-active-modal-mobile] .modal-overlay-insert {
  display: none;
}

.nav__item .mega-menu .mega__link_list ul:not(.nested_links) li a{
  text-transform: inherit;
}

.nav__item .mega-menu .mega__link_list ul:not(.nested_links) li{
  padding-bottom: 0;
}

@media(max-width: 1023px) {

  .header__nav,
  .mega-menu {
    background-color: #f7f7f7 !important;
  }
  .nav_item__link.text-white {
    color: #222;
  }

  .header__nav.active {
    visibility: visible;
    transform: translateX(0);
  }
  .header__nav {
    transform: translateX(-100%);
    transition: none;
  }
  .nav__item .mega-menu {
    transform: translateX(100%);
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    max-height: 100vh;
    overflow: hidden;
  }

  .nav__item.active .mega-menu {
    transform: translateX(0);
    max-height: 100%;
    overflow: visible;
  }
  .nav__item:last-child .nav_item__wrap {
    border: none;
  }
  #country-picker-mobile {
    position: relative;
  }
  #country-picker-mobile:before {
    content: '';
    border-top: 1px solid #dddddd;
    width: 100vw;
    position: absolute;
    top: 1px;
    left: 50%;
    transform: translateX(-50%);
  }
  #country-picker-mobile.flow-country-picker-dropdown-trigger {
    padding: 1rem;
  }
  .nav__sublist {
    transform: translateX(100%);
    transition: none;
    min-height: 100vh;
    height: auto;
    padding-bottom: 120px;
  }
  .nav__sublist.active {
    visibility: visible;
    transform: translateX(0);
  }
}


/* Panel Menus
========================================================================== */

.modal-overlay {
  [data-active-modal="panel-nav"] & {
    @apply top-main h-main;
  }
}

.modal-right {
  visibility: hidden;
  transform:translateX(100%);
  &.active {
    visibility:visible;
    transform:translateX(0%);
  }
}

.modal-border {
  opacity:0;
  transform-origin: center center;
  transform:scale(0.9) translate(-50%, -50%);
  pointer-events: none;
  &.active {
    transform-origin: center center;
    pointer-events: all;
    visibility:visible;
    transform:scale(1.0) translate(-50%, -50%);
    opacity:1;
  }
}

.panel__item--image-text {
  a {
    height: calc(100vh - 136px);
    @media (min-width: 1024px) {
      height: calc(100vh - 190px);
    }
  }
}

@media (min-width: 1024px) {
  body.scrolled {
    [data-modal="panel-nav"] {
      [scroll-direction="down"] & {
        &.lg\:h-main {
          height: calc(100vh - var(--scrolled-header-height));
        }
        &.lg\:top-main {
          top: var(--scrolled-header-height);
        }
      }
      [scroll-direction="up"] & {
        &.lg\:h-main {
          height: calc(100vh - var(--unscrolled-header-height));
        }
        &.lg\:top-main {
          top: var(--unscrolled-header-height);
        }
      }
    }
  }

  [data-active-modal=panel-nav] body.scrolled .modal-overlay {
    height: calc(100vh - var(--scrolled-header-height));
    top: var(--scrolled-header-height);
  }


}

.subpane__close {
  display: none;
}
@media (min-width: 1024px) {
  .subpanes .pane.active ~ .subpane__close {
    display: block;
  }
  .panel-close {
    display: block; 
  }
  .pane--has-nav-items.active ~ .panel-close {
    display: none; 
  }
}

/* Mega Menu
========================================================================== */

.nav {
  &--item {
    .nav--item-link:hover,
    &.active .nav--item-link {
      span {
        @apply relative;
        &:after {
          content: "";
          width: 100%;
          border-bottom: 1px solid #000;
          height: 1px;
          position: absolute;
          left: 0;
          bottom: -4px;
        }
      }
    }
  }
}

.header {
  &__mega-menu {
    .header--nav {
      @media (max-width: 1024px){
        position: fixed;
        top: var(--dynamic-header-height);
      }
      &.active {
        @media (max-width: 1024px){
          display: block;
        }
      }
      #mobileSearchForm {
        @media (max-width: 1024px){
          padding-bottom: 0;
        }
      }
    }
  }
}

.mega {
  &-overlay {
    top: var(--unscrolled-header-height);
    background-color: rgba(180, 180, 180, 0.753)
  }
}

/* Style for mega-menu AB test */
[data-active-modal=panel-nav] .use-mega-menu .modal-overlay {
  display: none;
}

/* Style for PDP Financing messaging AB test */

.segment-test__financing-messaging .product-essentials--buy-box .paymentsBanner_container{
  display: none;
}
/* Brought over from Globals
========================================================================== */

.MonumentGrotesk-Regular {
  font-family: var(--font-body-family);
  font-weight:  normal;
  font-style:   normal;
  font-stretch: normal;
}

.MonumentGrotesk-Medium {
  font-family: var( --font-body-alt-family);
  font-weight:  normal;
  font-style:   normal;
  font-stretch: normal;
}

.MonumentGrotesk-Medium-Italic {
  font-family: var(--font-body-alt-italic-family);
  font-weight:  normal;
  font-style:   italic;
  font-stretch: normal;
}

.MonumentGrotesk-Bold {
  font-family: var(--font-heading-family);
  font-weight:  normal;
  font-style:   normal;
  font-stretch: normal;
}

.ABC-Monument {
  font-family: var(--font-pop-family);
  font-weight:  normal;
  font-style:   normal;
  font-stretch: normal;
}

.Futura-Condensed {
  font-family: var(--font-subheading-family);
  font-weight:  normal;
  font-style:   normal;
  font-stretch: normal;
}

.Libre-Caslon {
  font-family: 'Libre Caslon Text', serif;
}

.Monument-Regular {
  font-family: var(--font-heading-alt-family);
  font-weight:  normal;
  font-style:   normal;
  font-stretch: normal;
}

a.skip-to-content-link {
    left:-999px;
    height:1px;
    overflow:hidden;
    position:absolute;
    top:auto;
    width:1px;
    z-index:-999;
}

.keyboard-user a.skip-to-content-link:focus, .keyboard-user a.sskip-to-content-link:active {
    left: auto;
    top: auto;
    width: auto;
    height: auto;
    overflow:auto;
    padding:5px;
    text-align:center;
    font-size:1;
    z-index:999;
}

body .acsb-skip-link {
    display: none !important;
}
.keyboard-user {
    .acsb-skip-link {
        display: inline-flex !important;
    }
}

.ada-outline-fix:focus {
    outline: 0;
    border: none;
    .keyboard-user & {
        border: 2px solid rgb(59, 153, 252);
    }
}

button:focus {
    outline: -webkit-focus-ring-color auto 5px;
}

@media (min-width: 1024px) {
  .show-on-focus  {
    display: block;
    height: 0;
    overflow: hidden;
    position: absolute;
    width: 0;
    top: 18px;
  }

  button.show-on-focus {
    background: transparent;
    border: 0;
    line-height: 0;
    padding: 0;
  }

  .show-on-focus.active,
  .show-on-focus:focus {
    overflow: visible;
    position: relative;
  }
}


.announcement-bar .swiper-slide:not(.swiper-slide-active):not(:only-child) {
  visibility: hidden;
}

.announcement-header .swiper-slide div p{
  @apply text-2xs uppercase font-heading transform -translate-y-px;
  margin: 0;
}

.video-stop-play {
  bottom: 15px;
  left: 8px;
  height: 20px;
  z-index: 30;
}

.video-stop-play button {
  background-color: transparent;
  border: 0;
  left: 0;
  position: absolute;
  top: 0;
  z-index:40;
}

.video-stop-play button {
  color: white;
  font-size: 24px;
  -webkit-text-stroke: 1px black;
}

*:focus{outline:none!important;}

.keyboard-user {
  input {
    &:focus-visible {
      outline: 2px solid black !important;
    }
  }

  :is(a, button, .button, .btn, .quick-add__form--option) {
    &:focus-visible {
      outline: 2px solid black !important;
      outline-offset: 2px !important; 
    }
  }

  label:has(input) {
    &:focus-within {
      outline: 2px solid black !important;
      outline-offset: 2px !important; 
    }
  }

  summary,
  button:has(.icon),
  a:not(.button,.btn,.nav--item-link,:has(img)) {
    &:focus-visible {
      outline: 2px solid black !important;
      outline-offset: -2px !important;
    }
  }

  .swiper,
  .swiper-container,
  .ticker {
    a:has(img){
      outline-offset: -2px !important;
    }
  }
}

.dn {display: none;}
.db {display: block;}

@media (min-width: 1024px) {
  .dn-l {display: none;}
  .db-l {display: block;}
}

/* PLP Filters & Sort
========================================================================== */

.collection-tools {
  .checkbox-controlled-height {
    li {
      .active-underline {
        font-family: var(--font-body-family);;
        text-decoration: none;
      }
      &.active {
        .active-underline {
          text-decoration: underline;
          font-family: var(--font-heading-family);
        }
      }
    }
  }
}

/* PLP Quick Shop
========================================================================== */

/*! purgecss start ignore */

[data-available=""]:not(.swatch--nested) ~ *,
[data-available=""]:not(.swatch--nested) ~ * *,
[data-available="0"]:not(.swatch--nested) ~ *,
[data-available="0"]:not(.swatch--nested) ~ * *,
[data-available].unavailable, 
[data-available].unavailable ~ *,
button[disabled] {
  @apply text-gray-dark;
  text-decoration: line-through;
  .quick-shop & {
    pointer-events: none;
  }
}

/*! purgecss end ignore */

/* Seach Spring Styles
========================================================================== */

/*! purgecss start ignore */

.ss-no-results {
  @apply p-8; 
}

#shopify-section-collection.search--results {
  min-height: 1px;
}

/*! purgecss end ignore */

/* Collection Grid
  ========================================================================== */

.collection {
  &__filters {
    @apply lg:pt-10;
    padding-bottom: 180px;

    .filters-open & {
      @apply overflow-y-scroll visible block;
    }

    &.filter-options--list{
      details {
        @apply bg-light;
      }

    }

    &.filter-buttons, &.filter-images{
      @apply bg-white;
      details{
        @apply bg-white;
      }
    }

    &.filter-images{
      .accordion-title{
        @apply px-5;
      }
    }

    &.filter-list
    &.ss-open, &.open {
      @apply overflow-y-scroll visible block;
    }
    
    &--trigger {
      @apply flex border-r-2 border-white px-3 py-5;
    }
    &--facets {
      @apply flex overflow-x-auto p-4 flex-grow overscroll-contain;
      &::-webkit-scrollbar { 
        display: none;
      }
    }
    &--buttons {
      @apply lg:px-10 px-5 py-5 grid gap-1.5 grid-cols-2;
      .button {
        @apply w-full;
      }
    }
    &--close {
      @apply py-2.5 lg:px-10 px-5 flex justify-end w-full;
    }
    &--toggle {
      .filters-open &::after {
        display: block;
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0,0,0,0.3);
        z-index: 20;
      }
      &.filter-trigger--text-icon {
        @apply flex items-center;
        [data-active-modal="filters"] & {
          svg {
            @apply transform rotate-180;
          }
        }
        & > svg {
          @apply hidden;
        }
        & > div {
          @apply font-highlight text-2xs uppercase tracking-wider flex items-center;
        }
      }
    }

    .accordion {
      &-title {
      @apply m-0 py-2 cursor-pointer;
      label {
        @apply flex w-full cursor-pointer;
        span.plus {
          @apply ml-auto;
          .accordion-title__minus {
            @apply hidden;
          }
          .accordion-title__plus {
            @apply block;
          }
        }
        span.minus {
          @apply ml-auto;
          .accordion-title__minus {
            @apply block;
          }
          .accordion-title__plus {
            @apply hidden;
          }
        }
        span svg {
          width: 16px;
          height: 16px;
        }
      }
    }

      &-panel {
        @apply m-0 py-0 lg:px-5 px-5 list-none;
      li {
        &.active {
          font-family: var(--font-heading-family);
          font-weight: var(--font-heading-weight);
          font-style: var(--font-heading-style);
        }
      }
      li:last-child {
        @apply mb-2;
      }
    }

      &-group {
        &--size,
        &--size-new,
        &--waist-size {
          label {
            @apply w-auto min-w-8 justify-center uppercase;
          }
        }
      }

      &-label {
        @apply w-full my-0 py-2 cursor-pointer leading-6 text-base;
      }
    }

    .filter-options {
      &--list {
        .accordion {
          &-image {
            @apply hidden;
          }
        }
      }
      &--buttons {
        @apply flex flex-col bg-white;

        .accordion {
          &-title {
            @apply border-black border-t pt-5 pb-2;;
            .icon {
              display: none;
            }
          }
          &[open] {
            .accordion-title {
              @apply pt-[28px] pb-[14px];
            }
          }
          &:not([open]) {
            .accordion-title {
              @apply py-[28px];
            }
          }
          &-panel {
            .field {
              @apply pb-[30px];
            }
          }
          &-image {
            @apply hidden;
          }
          &-group {
            @apply gap-x-1 gap-y-2;
    
            display:grid;
            grid-template-columns:repeat(auto-fill,minmax(103px, 1fr));
    
            label {
              @apply rounded-xl border px-1 py-1.5 items-center justify-center;
              border-color: var(--color-gray-2);
    
              &:has(input[type=checkbox]:checked ~ span) {
                @apply border-black;
                font-family: var(--font-heading-alt-family);
              }
    
              span {
                @apply uppercase text-[11px] tracking-wider whitespace-nowrap;
              }
            }
    
            &--size,
            &--size-new,
            &--waist-size {
              @apply flex flex-wrap;
              label {
                width: calc(25% - 4px);
              }
            }
    
            &--category {
              grid-template-columns:repeat(auto-fill,minmax(150px, 1fr));
            }
    
            &--rise,
            &--stretch-rating {
              grid-template-columns:repeat(auto-fill,minmax(115px, 1fr));
            }
    
            &--color,
            &--designers {
              grid-template-columns:repeat(auto-fill,minmax(140px, 1fr));
            }
    
            &--color {
              label {
                @apply justify-start px-3;
                input + span {
                  @apply rounded-none max-w-[33px] min-w-[33px] w-full h-3;
                }
                span:last-of-type {
                  @apply w-full;
                }
              }
            }
          }
        }
      }
      &--images {
        .accordion {
          &-title {
            @apply pointer-events-none pt-5 lg:px-5 px-5;
            
            .group-icons {
              @apply hidden;
            }
          }
          &-image {
            @apply block w-full;
          }
          &-panel {
            @apply px-0;
            
            &--size,
            &--size-new,
            &--waist-size {
              @apply px-5;
            }
            &--color {
              @apply px-5;
            }
          }
          &-group {
            &:not(.accordion-group--size, .accordion-group--color) {
              @apply flex flex-row overflow-x-scroll m-0 flex-nowrap gap-x-2.5 lg:px-5;

              label{
                @apply flex-col justify-end gap-y-1.5 w-32 flex-shrink-0;

                &:last-child{
                  @apply lg:pr-0 pr-5;
                }
    
                input[type=checkbox]:checked + img{
                  border: 0.6px solid #000000;
                }
    
                img {
                  @apply rounded-[15px];
                  border: 0.6px solid #E5E6E9;
                }

                span{
                  @apply text-[11px] uppercase;
                }
              }
            }
          }
        }
        
        .group-filters {
          @apply overflow-x-auto lg:px-0 px-5;
          &::-webkit-scrollbar {
            @apply hidden;
          }
        }
      }
    }
  }
  &__tools {
    @apply flex justify-between items-center border-b-2 border-white px-3.5 py-2 relative;
    &--sort {
      @apply font-highlight text-2xs uppercase tracking-wider;
      &.active {
        span:last-child {
          @apply transform rotate-180;
        }
      }
      select {
        @apply cursor-pointer p-0 border-none bg-transparent w-[69px] text-[.688rem];
      }
    }
    &--wrapper {
      @apply ml-auto;
    }
    &--grid {
      span {
        @apply ml-4;
      }
    }
    &--filters {
      @apply flex justify-start content-center border-b-2 border-white sticky bg-light z-30;
      top: var(--unscrolled-header-height);
      transition: top 300ms ease-in-out;
      [scroll-direction="down"] & {
        top: var(--scrolled-header-height);
      }
      [scroll-direction="up"] & {
        top: var(--unscrolled-header-height);
      }
    }
    &--count {
      @apply font-highlight text-2xs uppercase tracking-wider;
    }
  }
  &__summary {
    &--list {
      @apply m-0 px-2.5 py-1.5 list-none flex border-b-2 border-white;
    }
    &--filter {
      @apply font-highlight text-xs px-1.5 py-1 border border-white bg-white text-black rounded-md w-max flex items-center whitespace-nowrap cursor-pointer;
      
      span {
        @apply ml-1;
      }
    }
  }
  &__sort {
    @apply absolute list-none p-5 right-2 top-full text-left z-10 bg-white leading-8 hidden;
    &.ss-open, &.open {
      @apply block;
    }
    ul {
      min-width: var(--sort-width);
    }
    .font-bold {
      font-weight: normal;
      font-family: 'MonumentGrotesk-Bold';
    }
  }
  &-grid {
    margin-right: -2px;
    overflow: hidden;
    width: 100vw;
    li {
      list-style: none;
    }
  }
   &--data-shopify {
    .collection-tools, .collection-sort {
      @apply hidden;
    }
  }
}

.ss-top-facets {
  &:after {
    background: -moz-linear-gradient(left,  rgba(247,247,247,0) 0%, rgba(247,247,247,1) 64%, rgba(247,247,247,1) 100%); /* FF3.6-15 */
    background: -webkit-linear-gradient(left,  rgba(247,247,247,0) 0%,rgba(247,247,247,1) 64%,rgba(247,247,247,1) 100%); /* Chrome10-25,Safari5.1-6 */
    background: linear-gradient(to right,  rgba(247,247,247,0) 0%,rgba(247,247,247,1) 64%,rgba(247,247,247,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00f7f7f7', endColorstr='#f7f7f7',GradientType=1 ); /* IE6-9 */
    content: "";
    @apply absolute -right-px top-0 bottom-0.5 w-12 block;
  }
}

.collection__tools--search-results {
  .template-collection & {
    display: none;
  }
}

.top-facets {
  &.swiper-container {
    &::after {
      @apply content-none
    }
  }

  .btn-control {
    @apply w-[18px] h-full absolute top-0 flex bg-white bg-opacity-80;

    svg {
      @apply w-[18px] h-[18px];
    }

    &.swiper-next {
      @apply right-0;
    }

    &.swiper-prev {
      @apply left-0;
    }
  }

}

/* Search Modal
  ========================================================================== */

.search-form {
  @apply hidden md:flex justify-start content-center border-b-2 border-white sticky bg-light z-30;
  top: var(--unscrolled-header-height);
  transition: top 300ms ease-in-out;
  [scroll-direction="down"] &, [scroll-direction="up"] & {
    top: var(--scrolled-header-height);
  }
  [scroll-direction="up"] & {
    top: var(--unscrolled-header-height);
  }
}

.search-mobile .predictive-word{
  font-weight: 700;
}

.auto__results {
  height: calc(100vh - 133px);
  margin: 0;
  overflow-y: scroll;
  position: absolute;
  right: 0;
  text-align: left;
  top: 100%;
  -webkit-transition: opacity .1s ease-in-out;
  transition: opacity .1s ease-in-out;
  width: 100%;
  z-index: 50;
  pointer-events: none;
  [search-suggestions], [search-results] {
    pointer-events: all;
  }

  .suggested-search-words{
    span{
      font-family: 'MonumentGrotesk-Regular';
    }
    .predictive-word{
      font-family: 'MonumentGrotesk-Bold';
    }
  }
}

.search-results {

  &__container{
    .search-results__item{
       
    &:not(:has(.product-item__wrapper)) {
      @apply hidden;
    }

    &:last-child {
        & article:before{
          right: 0;
        }
      }
    }
  }
  .product-item {
    &__wrapper {
      border: none;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: -2px;
        bottom: 0;
        border: 2px solid #ffffff;
        z-index: 10;
      }

      @media (max-width: 1023px) {
        &::before {
          content: '';
          right: 0px;
          border: 1px solid #ffffff;
        }
      }
    }
    &__title {
      @apply text-left;
      &--meta {
        @apply w-full flex justify-start items-start lg:gap-[1px] flex-col;
  
        h4 {
          @apply lg:text-[12px] text-[10px] leading-[1.2]; 
        }
        h3{
          @apply lg:text-[16px] text-[12px] leading-[1.4] block;
        }
      }
    }
    &__price {
      @apply w-full text-left text-body leading-normal mt-[0.15rem];
    }
    &__price:first-child {
      @apply hidden;
    }

    &__link{
      @apply z-20;
      margin-top: 2px;
    }

    &__contents{
      padding-bottom: 0;
    }
  }
}

/* Product Item
  ========================================================================== */

.product-item {
  @apply w-full h-full relative;

  --swiper-pagination-bullet-size: 5px;
  --swiper-pagination-bullet-horizontal-gap: 1.5px;

  .collection--capsule-grid & {
    @apply lg:w-1/4 w-1/2;
  }

  &__wrapper {
    @apply w-full h-full list-none p-0 m-0 border-b-2 border-r-2 border-white;

    .collection-grid &, [data-collection-view="4"] & {
      @apply lg:w-1/4 w-1/2;
    }
    [data-collection-view="2"] & {
      @apply lg:w-1/3 w-full;
    }
    .section-slideshow-collection & {
      @apply border-t-2;
    }
    .product-essentials__extras & {
      @apply border-0;
    }
  }


  &__inner-wrapper, &__contents {
    @apply w-full h-full pb-4 flex flex-col;
  }

  &__contents {
    @apply lg:flex lg:flex-col;
  }

  &__link {
    @apply lg:flex lg:flex-col;
  }

  &__image-wrapper {
    @apply w-full overflow-x-scroll lg:overflow-x-hidden scroll-smooth will-change-scroll flex lg:flex-col flex-row;
    scroll-snap-type: x mandatory;
    &::-webkit-scrollbar { 
      display: none;
    }

    .product-image__hover {
      @media (hover: none) {
        @apply hidden;
      }
    }
  }

  &__image {
    @apply aspect-w-9 aspect-h-11 w-full m-0 shrink-0 overflow-hidden snap-center snap-always;
    &--main {
      @apply  object-contain;
    }
    &--hover {
      @apply lg:hidden lg:absolute aspect-w-9 aspect-h-11 relative m-0 w-full shrink-0 overflow-hidden snap-start snap-always;
      .product-item__link:hover &, .product-item__link:focus & {
        @apply block;
      }
    }
    &--alt, &--video {
      @apply absolute object-contain inset-0 w-full h-full;
    }
  }
  &__badges {
    @apply flex lg:flex-row flex-col gap-2;
    .section-slideshow & {
      @apply hidden;
    }
  }

  &__badge {
    @apply px-2 lg:py-1.5 lg:px-5 py-1 rounded-md lg:text-xs text-3xs lg:block hidden;
    min-width: 70px;
    border-color: #1F9F5B;
    color: #1F9F5B;
    &:first-child {
      @apply mt-0 block;
    }
    &[data-badge="Organic!"] {
      border-color: #B1A956;
      color: #B1A956;
    }
    &[data-badge="new"] {
      border-color: #29BAF1;
      color: #29BAF1;
    }
  }
	&-image-badge__container {
		@apply absolute inset-0 w-full h-full;
	}
	&-image-badge {
		@apply z-10 w-1/6 lg:w-[12%] h-auto;

		&[data-image-badge-position="top-left"] {
			@apply top-2.5 left-2.5;
		}

		&[data-image-badge-position="top-right"] {
			@apply top-2.5 right-2.5;
		}

		&[data-image-badge-position="bottom-left"] {
			@apply bottom-2.5 left-2.5;
		}

		&[data-image-badge-position="bottom-right"] {
			@apply bottom-2.5 right-2.5;
		}
	}
	
  &__promo {
    @apply text-left block text-sm;
  }

  &__info {
    @apply pb-0 lg:h-full;
  }

  &__outer {
    @apply overflow-visible relative;
  }

  &__meta {
    @apply flex flex-col justify-between items-start px-3 py-2;
    .section-slideshow-collection & {
      @apply items-center text-center;
    }
  }

  &__title {
    @apply font-body font-light lg:text-base text-xs m-0 line-clamp-1;
    .product-essentials__extras & {
      @apply mt-2.5;
    }
  }

  &__subtitle {
    @apply font-highlight lg:text-xs text-[10px] m-0 font-light line-clamp-1;
  }

  &__price {
    @apply font-body font-light lg:text-base text-xs pt-0 my-1 gap-[3px];
    s {
      color: #999999;
    }
  }

  &__footer {
    @apply relative px-3;

    .product-item__add-to-cart:not(.quick-add__button) {
      letter-spacing: .05em;
      line-height: 1.5;
      padding: .5rem 2rem;
      font-size: .688rem;
      font-family: var(--font-body-family);

      .quick-add__button--plus, .quick-add__button--minus{
        display: none;
      }
    }

    .search-results & {
      @apply hidden;
    }
  }

  &__tools {
    @apply flex items-start justify-between px-3 w-full shrink-0;
    &--wrapper {
      @apply flex flex-wrap items-start justify-between;
    }
  }

  &__color {
    @apply px-4;
  }

  &__button {
    &--wishlist {
      @apply bg-white px-8 py-2 flex items-center space-x-1 text-sm ;

      span {
        @apply inline;
      }
    }
  }

  &__quickadd {
    @apply mb-8 lg:mb-0 lg:absolute w-full rounded-sm z-30;

    @media (min-width: 768px) {
      .product-item__outer:hover &, .product-item__outer.quick-active & {
        @apply block;
      
        &:after {
          content: '';
          @apply absolute -inset-8 top-0 -z-10 bg-white drop-shadow-down rounded-sm;
        }
      }
    }
  }

  &__quick-add-toggle {
    display:none;
    .product-item__footer--quick-add-toggle & {
      display:flex;
    }
  } 

  &__container {
    display:none;

    .product-item__quick-add-toggle.active + & {
      display:block;
    }
    .product-item__footer--variant-selection & {
      display:block;
    }
    .back-in-stock{
      &__message{
        @apply font-light lg:text-xs text-[10px];
      }
      &-optin__label{
        @apply lg:text-xs text-[10px] lg:mt-1 mt-0;
      }
    }

    label {
      input:checked {
        & + span {
          // @apply underline;
        }
      }
    }
  }
  &__add-to-cart {
    
    display:none;
    
    .product-item__footer--variant-selection & {
      @apply flex justify-center w-full bg-black text-white order-4;
    }

    .product-essentials__extras & {
      svg {
        display: none;
      }
    }

    &[disabled] {
      text-decoration: none;
      opacity: .3;
    }

  }

  &__siblings {
    @apply flex flex-wrap justify-center my-2.5;
    padding-left: 5px;
  }

  &__details-link {
    @apply text-xs underline text-center block mt-4;
  }

  .section-slideshow-collection & {
    &__outer {
      &:hover {
        &:before {
          display: none;
        }
      }
    }
    /*
    &__quickadd, &__footer {
      display: none;
    }
    */
  }

  .pagination {
    @media (max-width: 1023px) {
      position: absolute;
      display: flex;
      z-index: 20;
      list-style: none;
      margin: 0;
      padding: 0;
      top: 10px;
      left: 10px;
      height: auto;
      width: auto;
      right: inherit;
      bottom: inherit;
      align-items: flex-start;
      justify-content: flex-start;
      column-gap: 0;
    }
    li {
      width: 5px;
      height: 5px;
      border-radius: 100%;
      margin-right: 3px;
      background-color: #8E8F97;
      &:first-child { opacity: 0.2}
      &:last-child { opacity:1.0 }
    }
    &.start {
      li {
        &:first-child { opacity: 1.0}
        &:last-child { opacity:0.2 }   
      }
    }

  }
}

.product-essentials__gallery:has(.product-item-image-badge[data-image-badge-position="top-left"]),
.product-item__image-bg:has(.product-item-image-badge[data-image-badge-position="top-left"]) {
	.product__pagination .swiper-pagination-bullets.swiper-pagination-horizontal {
		text-align: right;
	}
}



.quick-add {
  @apply max-h-0 w-full basis-full pointer-events-none transition-all ease-cubic overflow-hidden;
  &.active {
    @apply max-h-52 pointer-events-auto;
  }
  &__button {
    @apply inline-flex justify-center items-center lg:py-1.5 lg:px-5 px-3.5 py-1 rounded-md lg:text-xs text-3xs;
    &.hidden {
      display: none;
    }
    &.product-item__quick-add-toggle.hidden {
      display: flex;
    }
    &--plus, &--minus {
      @apply w-3 h-3 flex justify-center items-center ml-1; 
    }
    &--minus {
      @apply hidden;
    }
    &.active {
      .quick-add__button--plus {
        display: none;
      }
      .quick-add__button--minus {
        display: flex;
      }
    }
  }
  .ss-add-to-cart {
    @apply px-3 py-2 pb-0 font-highlight text-xs;
  }
  &__form {
    &--wrapper {
      @apply flex flex-wrap items-center justify-center py-2;
      margin: 0 -0.5rem;
      @media (max-width: 1023px) {
        margin: 0 -0.75rem;
      }
    }
    &--option {
      @apply px-3 py-2 pb-0 font-highlight font-light text-xs;
    }
  }
}

/* Product
  ========================================================================== */

.product-gallery{
  &--wrapper {
    @apply lg:grid-rows-1 lg:grid-cols-2;
  }
  &--slide {
    @apply max-w-full aspect-w-3 aspect-h-4;
    &-image-wrapper {
      model-viewer, iframe {
        @apply w-full h-full m-0;
      }
    }
    &:only-child {
      @apply col-span-2;
    }
    &:nth-child(5n) {
      border-bottom: none;
    }
  }
  &--slide-image-wrapper::after {
    content: "";
    @apply bg-white absolute inset-0 transform scale-150 origin-center;
    --tw-scale-x: 5;
    --tw-scale-y: 5;
    z-index: -10;
  }
}

.product-form {
  &__quantity {
    @apply hidden;
  }
}

.motivator {
  margin: 2rem -1rem 2rem;
}

.icon-afterpay {
  margin-top: 2px; 
}

.zoomable {
  opacity: 0;
  display: block;
  background-position: 50% 50%;
  overflow: hidden;
  transition: opacity 300ms ease;
}

.zoomable.zoomable-active {
  opacity: 1;
}

.section-slideshow {
  @media (min-width: 1024px) {
    .btn-control {
      opacity: 0;
    }

    &:hover,
    &:focus {
      .btn-control {
        opacity: 1;
      }
    }
  }
}

.product-essentials {
  &--buy-box {

    .scarcity-message{
      letter-spacing: -0.05;
      &::before{
        @apply block w-[8px] h-[8px] absolute left-0 rounded-full top-1/2;
        transform: translateY(-50%);
        content: '';
        background: #DB3D1A;
      }
    }

    .back-in-stock {
      &__message {
        @apply font-light text-xs;
      }

      &-optin__label {
        @apply text-xs lg:mt-1 mt-0;
      }
    }

    [data-international-hide] {
      @apply hidden;
    }
  }

  .spr-badge {
    @apply flex items-center justify-center;

    &[data-rating="0.0"] {
      @apply hidden;
    }
  }

  &__extras {
    .section-slideshow {
      @media (min-width: 1024px) {
        .btn-control {
          opacity: 1;
        }
      }
    }
  }
}

.spr-container {
  @apply container mx-auto lg:px-3 px-0;
}

#playPauseButton,
#muteUnmuteButton {
  span {
    &:first-child {
      display: none;
    }
    &:last-child {
      display: block;
    }
  }
  &.active {
    span {
      &:first-child {
        display: block;
      }
      &:last-child {
        display: none;
      }
    }
  }
}

.product__pagination {
  .swiper-horizontal > .swiper-pagination-bullets, .swiper-pagination-bullets.swiper-pagination-horizontal, .swiper-pagination-custom, .swiper-pagination-fraction {
    top: 0;
    bottom: inherit;
    text-align: left;
    padding: 4px 6px;
  }
}

.breadcrumbs {
  .breadcrumb__delimiter:last-of-type {
    display: none;
  }
}

.option {
  &__item {
    &--label {
      @apply cursor-pointer flex flex-col items-center justify-center rounded-full bg-transparent mr-2.5;
      min-width: 27px;
      height: 27px;
    }
  }
}

input[type=radio]:checked ~ .option__item--label {
  @apply bg-black text-white;
}

input[data-availability="false"] + label {
  text-decoration: line-through;
  opacity: .5;
}


/* PDP Option Selection
========================================================================== */

.option-selection {
  border: 2px solid transparent;
}

.section-title, h2{
    font-size: 32px;
}

.section-description{
    font-size: 18px;
}

.page-width{
    max-width: 1200px;
    margin: 0 auto;
}

@media screen and (min-width: 990px) {
  .page-width--narrow {
    max-width: 72.6rem;
  }
}

input[type=text],input[type=email],input[type=number], textarea, input[type=date], select{
    outline: none; 
}

.hero-banner{
    background-repeat: no-repeat;
    background-size: cover;
}

.account-menu-wrapper{
    border: 1px solid #A0A0A0;
    display: none;
}

.account-dropdown-wrapper:hover .account-menu-wrapper{
    display: block;
}

.icon svg.hidden {
    display: block;
}

@media screen and (min-width: 1024px){
    .group:hover .lg\:group-hover\:visible{
        visibility: visible;
    }    
    .group:hover .lg\:group-hover\:pointer-events-auto, .gropu:hover .lg\:group-focus\:pointer-events-auto{
        pointer-events: auto;
    }
}

.group:hover .lg\:group-hover\:opacity-100,
.group:focus-within .lg\:group-hover\:opacity-100,
.group:focus .lg\:group-hover\:opacity-100,
.group:focus-visible .lg\:group-hover\:opacity-100 {
  opacity: 1;
}

/* PDP Visitor Counter
--------------------------------------------- */

.visitor-counter-content-box-carecartbysalespop-2020 p, 
.visitor-counter-content-box-carecartbysalespop-2020 span {
  @apply text-xs;
}

.visitor-counter-content-box-carecartbysalespop-2020 {
  height: auto !important;
}
.visitor-counter-content-box-carecartbysalespop-2020 .counter-text-carecartbysalespop-2020 {
  min-height: auto !important;
}


/* Background Colors
--------------------------------------------- */

.bg-black-10 {
    background-color: rgba(0,0,0,.1);
}

.bg-black-20 {
    background-color: rgba(0,0,0,.2);
}

.bg-black-30 {
    background-color: rgba(0,0,0,.3);
}

.bg-black-40 {
    background-color: rgba(0,0,0,.4);
}

.bg-black-50 {
    background-color: rgba(0,0,0,.5);
}

.bg-black-60 {
    background-color: rgba(0,0,0,.6);
}

.bg-black-70 {
    background-color: rgba(0,0,0,.7);
}

.bg-black-80 {
    background-color: rgba(0,0,0,.8);
}

.bg-black-90 {
    background-color: rgba(0,0,0,.9);
}

.bg-black-100 {
    background-color: rgba(0,0,0,1);
}


#MainContent {
  z-index:0;
  background:#f7f7f7;
}

#shopify-section-footer {
  z-index:-1;
  // position:sticky;
  // bottom:0;
}
.user-select-none {
  user-select:none;
}
.has-angle-shade:after {
    position: absolute;
    display: block;
    background: #374151;
    opacity: .05;
    pointer-events: none;
    content: '';
    height: 200px;
    width: 200%;
    z-index: 100;
    bottom: 0px;
    transform: rotate(-5deg);
}

/* Challenge
--------------------------------------------- */

.shopify-challenge__container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.shopify-challenge__button {
  background: var(--os-primary);
}


/* Helpers
--------------------------------------------- */

.circle-divider::after {
  content: '\2022';
  margin: 0 1.3rem 0 1.5rem;
}

.circle-divider:last-of-type::after {
  display: none;
}

@media (min-width: 1024px) {
  .dropdown--wrap {
    @apply lg:absolute lg:transform lg:-translate-x-1/2 lg:left-1/2 lg:top-full lg:w-52 w-full lg:pt-2.5 box-border;
  }
  .dropdown--content {
    @apply bg-white lg:rounded lg:shadow-2xl py-6 px-8 w-full;
  }
  .dropdown--nav-item:before {
    content: "";
    display: block;
    position: absolute;
    width: 0px;
    height: 0px;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid rgb(255, 255, 255);
    bottom: -18px;
    left: 50%;
    transform: rotate(90deg) translateX(-50%);
    opacity: 0;
  }
  details[open] .dropdown--nav-item:before { 
    opacity: 1; 
  }
}


.multiply {
    mix-blend-mode:multiply;
}

@media screen and (min-width: 768px){
    .md\:grid-cols-5 {
        grid-template-columns: repeat(5,minmax(0,1fr));
    }
    .md\:grid-cols-6{
        grid-template-columns: repeat(6,minmax(0,1fr));
    }
}

.fade-in {
  opacity: 0;
	&.active {
		opacity: 1;
	}
}

.fade-in-rise {
  opacity: 0;
  transform: translateY(40px);
  &.active {
    opacity: 1;
    transform: translateY(0);
  }
}

.text-fade-rise {

  .block__text_content *,
  .content-item__buttons {
    opacity: 0;
    transform: translateY(100%);
  }
  
  @for $i from 1 through 5 {
    .block__text_content > *:nth-child(#{$i}) {
      transition: all 0.8s cubic-bezier(0.5, 0, 0, 1) #{$i * 0.15}s;
    }
  }
  @for $i from 2 through 5 {
		&:has( .block__text_content > *:nth-child(#{$i})) {
			.content-item__buttons  {
				transition: all 0.8s cubic-bezier(0.5, 0, 0, 1) #{($i * 0.15)+0.15}s;
			}
		}
  }
	&:has(.block__title.active) {
		.block__text_content *,
		.content-item__buttons {
			opacity: 1;
			transform: translateY(0%);
		}
	}
}

.zoom-in {
	img {
		scale: 1.2;
    transition: scale 1.2s cubic-bezier(0.5, 0, 0, 1) 0s;
	}
	.active & {
		img {
			scale: 1;
		}
	}
}

.invert-color {
	.content-item__text-container {
    position: relative;
    z-index: 999;
    max-width: 100%;
    height: 100%;

    .content-item__text {
      display: grid;
      grid-template-rows: 1fr;
      grid-template-columns: 1fr;
    }

    .content-item__text-inner {
      width: 100%;
      grid-column: 1 / 1;
      grid-row: 1 / 1;
    }

		.content-item__text-inner:not(.content-item__text-inner--duplicate) {
			*:not(a, span, svg, use) {
        color: var(--color-secondary) !important;
      }

			.content-item__buttons {
        a:not(.button--text) {
          background-color: var(--color-secondary) !important;
          color: var(--color-primary) !important;
          border-color: var(--color-secondary) !important;

          &:hover {
            background-color: var(--color-primary) !important;
            color: var(--color-secondary) !important;
            border-color: var(--color-primary) !important;
          }
        }
        a.button--text {
          color: var(--color-secondary) !important;

          &:hover {
            color: var(--color-primary) !important;
          }
        }
			}
		}
	}
}

.sticky-text {
	.content-item__text-container {
    height: 100%;
	}

	.content-item__text {
    @media (min-width: 1024px) {
      top: 65vh;
    }
		position: sticky;
  }
}


.in-view {
  
  &.in-view\:fade-in{
    opacity:1;
  }

  &.in-view\:fade-in-rise {
    opacity: 1;
    transform: translateY(0);
  }

	&.in-view\:zoom-in {
    transform: scale(1);
	}
}

.animated-spread {
  width: 0;

  .active & {
    width: 100%;
  }
}

.animate {
  transition: transform,opacity,visibility,margin,padding,color,background-color 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);

  &.delay-1 {
    transition-delay: 1s;
  }

  &.animate-slowest {
    transition-duration: 2.5s
  }

  &.animate-slow {
    transition-duration: 1.2s
  }
  
  &.animate-fast {
    transition-duration: 0.3s
  }

    &.push-hover {
    transform: scale(1.05);
    :hover > & {
      transform: scale(1.0);
    }
  }
  &.pull-hover {
    transform: scale(1.0);
    :hover > & {
      transform: scale(1.05);
    }
  }
}

.swiper-button-disabled {
    opacity:0;
    pointer-events:none;
}

.swiper-container {
  &:not(.swiper-initialized) {
    ~ .btn-control {
      display: none !important;
    }
  }
}

.accordion--section .accordion:after {
  display: none;
}

#collectionFilters {
    @media screen and (min-width: 768px){
        max-height: calc(100vh - 82px);
    }
}

shopify-payment-terms {
  margin: 0;
  padding: 0;
  line-height: 1.38;
  @apply font-highlight font-light;
  button {
    font-size: 11px;
  }
}

.br5 {
  border-radius: 42px;
}

.br6 {
  border-radius: 248px;
}

@media screen and (min-width: 1024px){
  .br5-l {
    border-radius: 42px;
  }

  .br6-l {
    border-radius: 248px;
  }
}

.br--top-right {
  border-top-right-radius: 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.br--top-left {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.br--bottom-right {
  border-bottom-right-radius: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.br--bottom-left {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

@media screen and (min-width: 1024px){
  .br--top-right-l {
    border-top-right-radius: 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }

  .br--top-left-l {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }

  .br--bottom-right-l {
    border-bottom-right-radius: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }

  .br--bottom-left-l {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
}

.top-nav-l {
  @media screen and (min-width: 1024px){
    top: 100px;
  }
}

.product--img-xl {
  width: 130%;
  transform: translateX(-12%);
  max-width: 130%;
  height: auto;
}

.active-show {
  display:none;
}
.active span.active-show {
  display:inline-block;
}
.active div.active-show,
.active ul.active-show {
  display:block;
}
.active .flex.active-show {
  display:flex;
}
.active .active-hide {
  display:none;
  .collection__tools & {
    display:inline-block;
    opacity: .5;
  }
}

.checkbox-controlled-display {
  display:none;
  letter-spacing: 0;
}
input[type=checkbox]:checked ~ .checkbox-controlled-display,
input[type=radio]:checked ~ .checkbox-controlled-display {
  display:block;
}
input[type=checkbox]:checked ~ .checkbox-controlled-display.flex,
input[type=radio]:checked ~ .checkbox-controlled-display.flex {
  display:flex;
}
input[type=checkbox]:checked ~ .checkbox-controlled-display.dib,
input[type=radio]:checked ~ .checkbox-controlled-display.dib {
  display:inline-block;
}

input[type=checkbox] ~ .checkbox-controlled-bold,
input[type=radio] ~ .checkbox-controlled-bold {
  min-width: 20px;
  height: 20px;
  @media only screen and (max-width: 1039px) {
    min-width: 25px;
    height: 25px;
    line-height: 25px;
  }
}

input[type=checkbox]:checked ~ .checkbox-controlled-bold,
input[type=radio]:checked ~ .checkbox-controlled-bold,
input[type=checkbox]:checked ~ label > .checkbox-controlled-bold,
input[type=radio]:checked ~ label > .checkbox-controlled-bold {
  font-weight: 300;
  border-bottom: 1px solid currentColor;
  position: relative;
  z-index: 2;
}

.checkbox-controlled-height {
  max-height:0;
  overflow:hidden;
  transition:max-height 0.2s cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

input[type=checkbox]:checked ~ .checkbox-controlled-height,
input[type=radio]:checked ~ .checkbox-controlled-height {
  max-height: 150vh;
}

input[type=checkbox]:checked + h5,
input[type=radio]:checked + h5 {
  background: red;
}

.checkbox-controlled .active-show {
  display: none;
}

input[type=checkbox]:checked ~ .checkbox-controlled .active-show,
input[type=radio]:checked ~ .checkbox-controlled .active-show,
input[type=radio]:checked ~ .active-show,
input[type=radio]:checked ~ * .active-show{
  display:block;
}
input[type=checkbox]:checked ~ .checkbox-controlled .active-show.flex,
input[type=radio]:checked ~ .checkbox-controlled .active-show.flex,
input[type=radio]:checked ~ .active-show.flex,
input[type=radio]:checked ~ * .active-show.flex {
  display:flex;
}
input[type=checkbox]:checked ~ .checkbox-controlled .active-show.dib,
input[type=radio]:checked ~ .checkbox-controlled .active-show.dib,
input[type=radio]:checked ~ .active-show.dib,
input[type=radio]:checked ~ * .active-show.dib {
  display:inline-block;
}

input[type=checkbox]:checked ~ .checkbox-controlled .active-hide,
input[type=radio]:checked ~ .checkbox-controlled .active-hide,
input[type=radio]:checked ~ .active-hide,
input[type=radio]:checked ~ * .active-hide {
  display:none;
}

input[type=radio]:checked ~ .nav__sub1-btn {
  .plus {
    display: none;
  }
}

input[type=radio]:checked ~ .nav__sub1-btn {
  .minus {
    display: inline-block;
  }
}

@media(max-width:1039px){

  .checkbox-controlled-display-s {
    display:none;
  }
  input[type=checkbox]:checked ~ .checkbox-controlled-display-s,
  input[type=radio]:checked ~ .checkbox-controlled-display-s {
    display:block;
  }
  input[type=checkbox]:checked ~ .checkbox-controlled-display-s.flex,
  input[type=radio]:checked ~ .checkbox-controlled-display-s.flex {
    display:flex;
  }
  input[type=checkbox]:checked ~ .checkbox-controlled-display-s.dib,
  input[type=radio]:checked ~ .checkbox-controlled-display-s.dib {
    display:inline-block;
  }
  .checkbox-controlled-height-s {
    max-height:0;
    overflow:hidden;
    transition:
    max-height 0.2s ease;
  }
  input[type=checkbox]:checked ~ .checkbox-controlled-height-s,
  input[type=radio]:checked ~ .checkbox-controlled-height-s {
    max-height: 150vh;
  }

}
@media(min-width:1040px){

  .checkbox-controlled-display-l {
    display:none;
  }
  input[type=checkbox]:checked ~ .checkbox-controlled-display-l,
  input[type=radio]:checked ~ .checkbox-controlled-display-l {
    display:block;
  }
  input[type=checkbox]:checked ~ .checkbox-controlled-display-l.flex,
  input[type=radio]:checked ~ .checkbox-controlled-display-l.flex {
    display:flex;
  }
  input[type=checkbox]:checked ~ .checkbox-controlled-display-l.dib,
  input[type=radio]:checked ~ .checkbox-controlled-display-l.dib {
    display:inline-block;
  }

}

.blog-articles {
  @media (min-width: 1024px) {
    padding-top: 100px;
    padding-bottom: 100px;

    @apply grid grid-cols-1 lg:grid-cols-3 gap-x-8 gap-y-16;
  }
  .blog-articles__article {
    width: 100%;
    text-align: left;
  }
  .article-card {
    margin-bottom: 40px;
    width: 100%;
  }
}

.article-card {
  &__title {
    @apply text-black MonumentGrotesk-Bold tracking-widest;
  }

  &__category,
  &__date,
  &__divider {
    @apply text-[11px] text-black MonumentGrotesk-Regular tracking-widest uppercase mb-0;
  }

  &__excerpt {
    @apply text-black Libre-Caslon;
  }
}

.blog__breadcrumbs {
  position: absolute;
  top: 102px;
  width: 100%;
  left: 0;
  z-index: 5;
  .breadcrumbs {
    margin: 0 0.5rem;
  }
}

.multipleSlidesWrapper .swiper-slide .block__text_content {
  display:none;
}

.multipleSlidesWrapper .slideshow-controls--content {
  opacity:1;
  transform: translateY(0px);
  min-height: 150px;
  @media (min-width: 1024px) {
    width: 33.3333333%;
  }
}
.multipleSlidesWrapper .slideshow-controls--content.loading {
  opacity:0;
  transform: translateY(20px);
}

.multipleSlidesWrapper .slideshow-controls .slideshow-controls--arrows {
  @media (min-width: 1024px) {
    width: 624px;
    transform: translateX(-50%);
    left: 50%;
  }
}

.multipleSlidesWrapper .block__text_content a:hover, .multipleSlidesWrapper .block__text_content a:focus {
  text-decoration: none;
}

[neptune-liquid]:not([neptune-templated]):not(.has-skeleton){
  opacity: 0;
  height: 0;
  pointer-events: none;
  display: none;
}

[type="search"] {
  -webkit-appearance: none; 
}

#shopify-section-mega-menu {
  display: none;
}

#footer {
  font-size: 68.8%;
  .text-lg {
    font-size: 12px;
  }
  .text-base {
    font-size: 11px;
  }
  li h4 {
    font-family: var(--font-body-family);
  }
  @media (min-width: 1024px) {
    .lg\:text-base {
      font-size: 11px;
    }
  }
}

.border-inset {
  border-style: inset;
}

details,
details summary {
  background-image:none;
  -webkit-appearance:none;
}

details summary::-webkit-details-marker {
  display:none;
}

.recently-viewed {
  display: none;
  .template-collection & {
    display: block;
  }
  .product-item__wrapper {
    @apply border-b-0;
  }
  .product-item {
    &__title, &__price, &__siblings, &__meta, &__footer {
      display:none;
    }
  }
  &__products {
    @apply overflow-x-scroll;
  }
  &__product-item {
    width: 28%;
    flex-shrink: 0;
    @media (min-width: 1024px) {
      width: 8.333333%;
    }
  }
  &__header {
    a {
      text-decoration: underline;
    }
  }
  &__persistent-footer {
    @apply border-t-2 border-white;
    button {
      @apply w-full;
    }
  }
}

@media (min-width:1024px) {
  .lg\:text-justify-last {
    -moz-text-align-last: justify;
    text-align-last: justify;
  }
}

@media (max-width:1024px) {
  .text-justify-last {
    -moz-text-align-last: justify;
    text-align-last: justify;
  }
}

@media only screen and (max-width: 1023px) {
  .text-justify-last {
    .in-grid__title_html,
    .in-grid__subtitle_html,
    .block__featured_content_html {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      width: 100%;
      & > div {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        width: 100%;
      }
      .break {
        flex-basis: 100%;
      }
      span {
        padding: 0 4px;
      }
    }
  }
}
@media only screen and (min-width: 1024px) {
  .lg\:text-justify-last {
    .in-grid__title_html,
    .in-grid__subtitle_html,
    .block__featured_content_html {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      width: 100%;
      & > div {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        width: 100%;
      }
      .break {
        flex-basis: 100%;
      }
      span {
        padding: 0 4px;
      }
    }
  }
}


/* Page Header Section
========================================================================== */

.page-header {
  @apply w-full relative;

  .page-header__title-wrap {
    background-color: var(--color-light);
  }
  
  .page-header__title {
    .animated-spread {
      @apply flex justify-end;
    }

    &-1 {
      background-color: var(--color-light) !important;
      position: relative !important;
      z-index: 1;
    }
  }
}


/* Slideshow Items Section
========================================================================== */

.slideshow-items {
  .pagination {
    @media (max-width: 1023px) {
      position: absolute;
      display: flex;
      z-index: 20;
      list-style: none;
      margin: 0;
      padding: 0;
      left: 50%;
      height: auto;
      width: auto;
      bottom: 0;
      transform: translateX(-50%);
    }

    .swiper-pagination-bullet {
      width: 5px;
      height: 5px;
      border-radius: 100%;
      margin-right: 3px;
      border: none;
      background-color: #8E8F97;
      opacity: 0.2;
      &.active, &.swiper-pagination-bullet-active {
        opacity:1.0
      }
    }
  }
}

/* Slideshow Sequence Section
========================================================================== */

.section-slideshow-sequence {
  .pagination {
    position: absolute;
    bottom: 0px;
    z-index: 10;
  }
}

/* Popups
========================================================================== */

#shopify-section-popups {
  ul.popup__international {
    list-style: none;
    li {
      position: relative;
      &:before {
        content: "✓";
        position: absolute;
        left: -1rem;
      }
    }
  }
}


.popup {
  @apply translate-x-0 translate-y-0;

  // Center positioning adjustments
  &.top-1\/2 {
    @media (max-width: 767px) {
      --tw-translate-y: -50%;
    }
  }

  &.lg\:top-1\/2 {
    @media (min-width: 768px) {
      --tw-translate-y: -50%;
    }
  }

  &--animation {
    &-fade {
      // Start hidden, animate to visible when active
      @apply opacity-0;

      &.popup--active {
        @apply opacity-100;
      }
    }

    &-slide {

      // Slide from right - start off-screen
      &.from-right {
        --tw-translate-x: calc(100% + 50vw);

        &.popup--active {
          --tw-translate-x: 0px;
        }
      }

      // Slide from left - start off-screen
      &.from-left {
        --tw-translate-x: calc(-100% - 50vw);

        &.popup--active {
          --tw-translate-x: 0px;
        }
      }

      // Slide from bottom - start off-screen
      &.from-bottom {
        --tw-translate-y: calc(100% + 50vh);

        &.popup--active {
          --tw-translate-y: 0px;

          // Respect center positioning for vertical slides
          &.top-1\/2 {
            @media (max-width: 767px) {
              --tw-translate-y: -50%;
            }
          }

          &.lg\:top-1\/2 {
            @media (min-width: 768px) {
              --tw-translate-y: -50%;
            }
          }
        }
      }

      // Slide from top - start off-screen
      &.from-top {
        --tw-translate-y: calc(-100% - 50vh);

        &.popup--active {
          --tw-translate-y: 0px;

          // Respect center positioning for vertical slides
          &.top-1\/2 {
            @media (max-width: 767px) {
              --tw-translate-y: -50%;
            }
          }

          &.lg\:top-1\/2 {
            @media (min-width: 768px) {
              --tw-translate-y: -50%;
            }
          }
        }
      }
    }
  }

  /* Respect reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    transition-duration: 0.1s !important;
  }
}

/* Size Chart 
========================================================================== */
[data-modal="size-guide"] {
  @apply lg:w-[720px] w-[360px] max-w-[92%];
  max-height: 100dvh;

  .field {
    select {
      @apply text-[.6875rem] rounded-[10px] py-[0.4025rem];
    }
  }

  .table {
    &__heading {
      @apply text-[.6875rem] p-3.5 whitespace-nowrap flex items-center justify-center uppercase bg-white;
    }

    &__item {
      @apply text-[.6875rem] p-3.5 whitespace-nowrap odd:bg-secondary even:bg-white;
    }
  
    &__grid {
      @apply inline-grid auto-rows-[43px] gap-px bg-black border-black border border-l-0 rounded-r-[10px] lg:mr-8 mr-[22px] overflow-hidden flex-shrink-0;
      grid-template-columns: repeat(var(--grid-size), minmax(70px, 1fr));
    }
  }
}

.size-guide-page{
  
  &__title{
    @apply lg:capitalize text-[36px];
    font-family: var(--font-body-family);
  }

  .size-guide{
    &__country-selector{
      @apply container lg:pr-16;

      label{
        @apply mb-3 block;
      }
    }
    &__table{
      @apply container lg:px-[9.4rem] lg:mt-3;

      .size-guide--wrapper{
        @apply lg:pb-[4px] pb-[12px];
      }

      .size-guide__note{
        @apply lg:mt-0 mt-3.5;
      }
    }
    &__unit-switcher{
      @apply container lg:px-[9.4rem] lg:justify-center gap-8;

      fieldset{
        @apply lg:absolute lg:right-[9.4rem] lg:bottom-0;
      }

      h4{
        @apply lg:mb-8;
      }
    }
    &__images{
      @apply lg:mt-[3rem];
      @media (max-width: 1023px) {
        margin-top: 1.75rem;
      }

      &-content{
        @apply lg:max-w-[620px] m-auto;

        div{
          @apply lg:max-w-[180px] max-w-[90px];

          &.text-chest{
            @apply lg:left-0;
            @media (max-width: 1023px) {
              top: 10px;
            }
          }
          &.text-hip{
            @apply lg:left-0;
            @media (max-width: 1023px) {
              bottom: -30px;
            }
          }
          &.text-waist{
            @apply lg:right-[-15px]
          }
        }
      }
      h4{
        @apply lg:text-center mb-[1.2rem];
      }
    }
  }

  .field{
    @apply lg:max-w-[340px] m-auto;
  }

  .table {
    &__heading {
      @apply text-[.6875rem] lg:p-3.5 p-[0.87rem] whitespace-nowrap flex items-center justify-center uppercase bg-white;
    }

    &__item {
      @apply text-[.6875rem] p-3.5 whitespace-nowrap odd:bg-secondary even:bg-white;
    }
  
    &__grid {
      @apply inline-grid lg:auto-rows-auto auto-rows-[43px] gap-px bg-black border-black border border-l-0 rounded-r-[10px] lg:mr-8 mr-[22px] overflow-hidden flex-shrink-0;
      grid-template-columns: repeat(var(--grid-size), minmax(70px, 1fr)); 


      @media (min-width: 1024px) {
        width: calc(100% - 120px);
        margin-right: 0;
        grid-template-columns: repeat(var(--grid-size), minmax(auto, 1fr));
      }

    }
  }
}

.size-guide{
  &__country-selector{
    @apply lg:pl-16 lg:pr-8 px-[22px] mt-4;

    select{
      border-radius: 10px;
      @media (max-width: 1023px) {
       font-size: 11px; 
       padding-right: 4rem;
       padding-left: 20px;
       padding-top: 0.4rem;
       padding-bottom: 0.4rem;
      }
    }
  }
  &__header{
    width: 120px;
  }
  &__block-content{
    .size-guide--wrapper{
      @apply lg:pb-1 pb-[4px];
    }
  }
  &__table{
    @apply lg:pl-16 pl-[22px] mt-4;
  }
  &__unit-switcher{
    @apply  lg:pl-16 lg:pr-8 px-[22px] mt-9 gap-x-4 justify-between;
  }
}

.size-chart {
  h4 {
    @apply mb-4 font-pop uppercase text-sm text-center
  }

  &__content {

    @apply w-full;

    table {
      @apply text-center w-full;

      td {
        @apply p-2 whitespace-nowrap;
      }
    }

    &--desktop {
      table {
        tr {
          @apply border-t
        }

        tr:first-child {
          @apply font-bold font-pop border-t-0
        }
      }
    }

    &--mobile {
      table {
        td {
          @apply border-l p-1;

          width: calc(100% / 8) !important
        }

        td:first-child {
          @apply border-l-0 font-bold font-pop
        }
      }
    }
  }

  p {
    @apply text-sm;
    
    strong {
      @apply font-pop uppercase
    }
  }
}

.size-guide {
  &--wrapper {
    padding-bottom: 4px;
  }
}

.scrollbar-show {
  &::-webkit-scrollbar {
    -webkit-appearance: none;
  }

  &::-webkit-scrollbar:horizontal {
    height: 12px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 8px;
    border: 2px solid white; /* change 'white' to whatever color you prefer */
    background-color: rgba(0, 0, 0, .5); /* change 'rgba(0, 0, 0, .5)' to whatever color you prefer */
  }
}

/* Collection filters
========================================================================== */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none; 
}

.top-facets:after {
  background: -moz-linear-gradient(left, rgba(247, 247, 247, 0) 0%, #f7f7f7 64%, #f7f7f7 100%);
  /* FF3.6-15 */
  background: -webkit-linear-gradient(left, rgba(247, 247, 247, 0) 0%, #f7f7f7 64%, #f7f7f7 100%);
  /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to right, rgba(247, 247, 247, 0) 0%, #f7f7f7 64%, #f7f7f7 100%);
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#00f7f7f7", endColorstr="#f7f7f7",GradientType=1 );
  /* IE6-9 */
  @apply content-[''] absolute -right-px top-0 bottom-0.5 w-12 block;
}

/* Collection In Grid Banners
========================================================================== */

.in-grid .spread article > div,
.in-grid .spread article:before {
    height:100%;
}

.in-grid .spread article:before {
    content:'';
    display:block;
}

.in-grid .spread article > div{
    display:flex;
}

.in-grid .spread article button {
  align-self: end;
  justify-self: end;

}
.in-grid button.link {

}


/* Cart Modal
========================================================================== */
.cart {
  &__modal {
    height: 100dvh;
    max-height: 100dvh;

    p:empty {
      @apply hidden;
    }
  }
  &__footer {
    .bag__checkout {
      @apply lg:h-[39px];
    }
    .button--shoppay {
      display: none;
    }
  }
}

/* Attentive
========================================================================== */

#attentive_overlay {
  z-index: 40 !important;
}

/* Cart Offers
========================================================================== */

@import './cart-offers/theme.scss';


/* Product Recommendations
========================================================================== */

.product-recommendations {
  @apply relative;
  
  &--grid {
    .product-item {
      &__contents {
        @apply pb-0;
      }

      &__meta {
        @apply items-start text-left;
      }
    }
  }
}

/* AB Test: Size Scale Higher on PDP
========================================================================== */

.product-essentials {
  &__liquid_block {
    &:has(.stretch-meter__container) {
      @apply mt-5 lg:mt-6;
    }

    .stretch-meter__container {
      @apply mt-5;
    }
  }
}


.mobile--nav {
  @apply relative overflow-hidden;
  
  &-item{
    &.swiper-slide{
      @apply w-auto;

      button, a{
        @apply w-full items-center justify-center;
      }
    }
  }

  .swiper-mobile-nav{
    svg{
      width: 20px;
      height: 20px;
    }
    &.swiper-next{
      @apply top-0 right-0 absolute flex bg-white;
      width: 20px;
      height: 40px;
    }
    &.swiper-prev{
      @apply top-0 left-0 absolute flex bg-white;
      width: 20px;
    }
  }
}

/* AB Test: Search Bar Changes
========================================================================== */
.nav__tools--search .search__text span {
  @apply text-xs;
  letter-spacing: .025em;
}

/* Shop The Look - Ab test
========================================================================== */

.segment-test__shop-the-look {
  .product-essentials__extras{
    .complete-the-look{

      @media (max-width: 1023px) {
        .swiper{      
          &-slide{
            height: 100%;
            box-shadow: inset 0 0 0 2px #fff;
            padding: 5px;
            margin-right: -2px;

            &:has(.unique-product){
              max-width: 65%;
              margin: 0;
            }
          }

          &-wrapper{
            &:has(.swiper-slide-active:first-child){
              transform: none !important; 
            }
          }
        }

        .product{
          &-item{
            &__wrapper{
              border-right: 0;
            }

            &__meta{
              text-align: left;
              padding: 10px 15px;
              border-top: 2px solid #fff;
              margin-left: -4px;
              margin-right: -4px;
            }
  
            &__title{
              font-size: 16px;
              margin: 0 0 5px;
              line-height: normal;
  
  
            &--meta{
              width: 100%;
            }
            }
  
            &__subtitle{
              font-size: 12px;
              line-height: normal;
              margin: 0 0 5px;
            }
  
            &__price{
              width: 100%;
              font-size: 16px;
              gap: 5px;
            }

            &__details-link{
              margin-top: 12px;
              text-align: left;
            }

            &__add-to-cart{
              max-width: 60%;
              padding: 0.5rem 1.5rem;
              font-size: 11px;

              .quick-add__button--minus, .quick-add__button--plus{
                display: none;
              }
            }

            &__container{
              @apply order-1;

              .quick-add__form{
                &--option{
                  padding: 0.5rem;

                  &-container{
                    input[type=radio]:checked~label{
                      position: relative;

                      & >.checkbox-controlled-bold{
                        color: #fff;
                        position: relative;
                        border-bottom: 0;
                      }

                      &::after{
                        content: "";
                        background: #000;
                        display: block;
                        position: absolute;
                        width: 22px;
                        height: 22px;
                        border-radius: 50px;
                        left: 50%;
                        top: 49%;
                        transform: translate(-50%, -50%);
                      }
                    }
                  }                 
                }

                &--wrapper{
                  padding-top: 0;
                  justify-content: flex-start;
                  padding-left: 5px;
                  padding-bottom: 0.75rem;
                }
              }
            }

            &__footer{
              max-width: 100%;
              align-items: flex-start;
              flex-flow: column;
              justify-content: flex-end;
              height: 100%;

              .add-to-cart__variant-selection, .add-to-cart__no-variants.quick-add__button{
                max-width: 60%;
                padding: 0.5rem 1.5rem;
                font-size: 11px;

                .quick-add__button--plus, .quick-add__button--minus{
                  display: none;
                }
              }
            }
          }
          &-recommendations{
            overflow: hidden;
          }
        }
  
        .section-title{
          padding: 0;
        }
  
        .swiper-button{
          &-next-unique{
            right: -5px;
            display: flex;
            height: 100%;
            background: #ffffffa5;
            padding: 1px;
          }
  
          &-prev-unique{
            left: -5px;
            display: flex;
            height: 100%;
            background: #ffffffa5;
            padding: 1px;
          }
        }
      }
    }
  }

}

/* Account Pages
========================================================================== */
.template-customers {

  &-login,
  &-register,
  &-reset_password,
  &-addresses,
  &-account,
  &-order {
    .footer-section {
      @apply lg:border-t-[2px] lg:border-white;

      #footer {
        @media screen and (max-width: 1023px) {
          padding-top: 0;
        }
      }
    }
  }

  &-account,
  &-order {
    h1 {
      font-family: var(--font-body-family);
      margin-bottom: 0.5rem;
      margin-top: 0.5rem;
    }

    .mother-pagination ol li:not(.current)::before {
      display: none;
    }

    .order-history {
      &__title {
        font-family: var(--font-body-family);
      }
    }
  }
}

.account-nav {
  .acc-information-btn {
    &.active {
      background: #E1E2E5;
    }
  }

  .order-history-btn {
    &.active {
      background: #E1E2E5;
    }
  }
}

.customer {

  h1,
  .section-header h2 {
    font-family: var(--font-body-family);
  }

  form {
    .field {
      position: relative;
      margin-bottom: 20px;

      .input--label {
        font-size: 11px;
        position: absolute;
        margin: 0;
        top: 50%;
        left: 19px;
        transform: translateY(-50%);
        line-height: normal;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
      }

      input,
      textarea,
      select {
        font-size: 12px;
        text-transform: none;

        &:focus,
        &:focus-visible {
          border: 1px solid var(--color-dark);
          outline: none !important;
        }
      }

      input {
        padding: 0.65rem 2rem 0.65rem 1rem;
        border-radius: 10px;
        line-height: normal;
        height: 39px;

        &::placeholder {
          font-size: 0px;
        }

        &[type="password"] {
          font-size: 20px;
          letter-spacing: 2px;
          caret-width: 2px;
          line-height: 1;
          text-decoration: none;
        }

        &[type="text"] {
          &[placeholder="Password"] {
            text-transform: none;
          }
        }
      }

      .error-message {
        font-size: 12px;
        color: #e92a14;
        position: absolute;
        top: -16px;
      }

      .toggle-password {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        right: 1rem;
      }

      label {
        input[type="checkbox"] {
          &+span {
            width: 11px;
            height: 11px;
            border: 1px solid #000;
            ;
          }

          &:checked {
            &~span {
              font-weight: 400;
            }

            &+span {
              background: transparent;

              &::before {
                content: "";
                position: absolute;
                width: 1px;
                height: 14px;
                transform: rotate(45deg);
                top: -2px;
                left: 4px;
                background: #000;
              }
            }
          }
        }

        &:has(input):focus-within {
          outline: none !important;
        }
      }

      &:has(input:focus, input:not(:placeholder-shown)) {
        .input--label {
          top: 3px;
          left: 17px;
          color: #8E8F97;
          transform: translate(0);
          transition: all 0.3s ease;
        }

        input {
          padding: 1rem 1.8rem 0.3rem 1rem;
        }
      }
    }

    button {
      &.btn {
        line-height: normal;
        font-size: 11px;
        border-radius: 5px;
        text-transform: uppercase;
        margin-bottom: 0.4rem;
        padding: 6.5px 32px;

        &.register-submit {
          margin-top: 0;
        }
      }
    }
  }

  .modal {
    border-radius: 10px;

    &-title {
      font-family: var(--font-body-family);
      font-size: 16px;
      margin-bottom: 0.5rem;
      margin-top: 0;
      padding: 0 16px 10px 16px;
      border-bottom: 2px solid #f7f7f7;
    }

    .address-form {
      .field {
        &:last-child {
          margin-bottom: 10px;
        }
      }
    }

    .field-group {
      margin-bottom: 0;

      &:has(.address-country),
      &:has(.address-province) {
        label {
          top: 3px;
          left: 17px;
          color: #8e8f97;
          transform: translate(0);
          transition: all .3s ease;
        }

        select {
          padding: 1rem 1.8rem .3rem 1rem;
        }
      }

      select {
        cursor: pointer;
        padding: .65rem 2rem .65rem 1rem;
        border-radius: 10px;
        line-height: normal;
        height: 39px;
      }
    }

    .field-checkbox {
      margin: 0 0 15px;

      label {
        margin-bottom: 0;
      }
    }
  }

  .account__login {
    .account__register-button {
      #customer_register_link {
        line-height: normal;
        color: #fff;
        background: #000;
        border-radius: 5px;
        padding: 7.5px 32px;
        width: 100%;
        text-transform: uppercase;
        display: block;
        text-align: center;
        font-size: 11px;
      }
    }
  }

  &.reset-password {
    form {
      max-width: 350px;
      margin: auto;
    }
  }

  .mother-pagination {
    ol {
      margin-bottom: 0;

      li {
        a {
          padding: 1rem 0 0;
        }
      }
    }
  }

  #addressInformation {
    .mother-pagination {
      margin-top: -16px;

      ol {
        margin-top: 0;

        li {
          &:not(.current)::before {
            display: none;
          }

          a {
            padding-top: 0;
          }
        }
      }
    }
  }
}

.customer.reset-password {
  .error-message {
    top: -22px !important;
  }
  .field:has(.error-message) {
    label {
      color: red;
    }
    input {
      @apply border-red-500  mt-9;
    }
  }
  .field:has(input:focus) label {
    color: black !important;
  }
}

.account-container {
  &:has(.acc-information-btn.active) {
    #accountInformation {
      display: block;
    }

    #orderHistory {
      display: none;
    }
  }

  &:has(.order-history-btn.active) {
    #accountInformation {
      display: none;
    }

    #orderHistory {
      display: block;
      padding: 0 10px;
    }
  }

  .order-history {
    &:has(:not(.mother-pagination)) {
      .order-history__content {
        margin-bottom: 30px;
      }
    }

    .mother-pagination {
      margin-top: -16px;

      ol {
        margin-top: 0;

        a {
          padding-top: 0;
        }
      }
    }
  }
}

/* Universal Modals
========================================================================== */

.universal-modal {
  &__content {
    .modal:not(.modal-full) & {
      max-height: calc(100dvh - var(--header-height));
      overflow-x: auto;
    }
  }

  [data-modal-close] {
    // color: rgba(0, 0, 0, 0.9);
    // filter: invert(1) grayscale(1) brightness(1.3) contrast(9000);
    mix-blend-mode: difference;
  }
}

.modal:has(.universal-modal) {

  /* Respect reduced motion */
  @media (prefers-reduced-motion: reduce) {
    transition-duration: 0.1s !important;
  }
}


/* Flex Pop-up
========================================================================== */

.flex-popup {
  &-trigger {

    :is(a, button) {
      @apply flex flex-col gap-[inherit];
      text-align: inherit;
    }
  }
}