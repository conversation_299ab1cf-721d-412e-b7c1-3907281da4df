/* Color Guidelines
  ==========================================================================
  primary: Highest level user attention.
  secondary: Second highest level user attention.
  tertiary: Third highest level user attention.
  light: Most prominent light background color. Must be able to overlay on top of dark.
  dark: Most prominent dark background color. Must be able to overlay on top of light.
  pop: Usage examples are badges.
  highlight: Think about this as using a highlighter pen.
  body: Most common text color.
  header: Most common text color for headers.
*/


:root {
  
  --color-primary: #000000;
  --color-secondary: #f7f7f7;
  --color-tertiary: #dddddd;
  --color-light: #f7f7f7;
  --color-dark: #000000;
  --color-pop: #86B597;
  --color-highlight: #2B433E;
  --color-body: #000000;
  --color-heading: #000000;

  --color-custom-1: #C6B197;
  --color-custom-2: #D8D3C1;
  --color-custom-3: #B1A956;
  --color-custom-4: #C9996C;
  --color-custom-5: #86B597;
  --color-custom-6: #2B433E;

  --color-gray-1: #f7f7f7;
  --color-gray-2: #E5E6E9;
  --color-gray-3: #8E8F97;

  --color-near-black: #222;
  --color-near-white: #f7f7f7;
  --color-gray-dark: #dddddd;
  --color-gray-3: #dddddd;
  --color-strike: #999999;

  --header-height:109px;
  --preheader-height: 34px;
  --header-height-half:54px;
  --unscrolled-header-height: 110px;
  --scrolled-header-height: 77px;
  --dynamic-header-height: 109px;
  --panel-height: 102px;
  --inner-panel-height: 189px;

  @media screen and (max-width: 1024px) {
    --header-height:68px;
    --preheader-height: 32px;
    --header-height-half: 34px;
    --unscrolled-header-height: 84px;
    --scrolled-header-height: 51px;
    --dynamic-header-height: 83px;
     --panel-height: 102px;
  }

  --bottom-buffer: 250px;

  --font-body-family: 'ABC Monument Grotesk Light', sans-serif;
  --font-body-alt-family: 'ABC Monument Grotesk Medium', sans-serif;
  --font-body-alt-italic-family: 'ABC Monument Grotesk Medium-italic', sans-serif;
  --font-heading-family: 'MonumentGrotesk-Bold', sans-serif;
  --font-heading-alt-family: 'MonumentGrotesk-Regular', sans-serif;
  --font-subheading-family: 'Futura Condensed BQ', sans-serif;
  --font-alt-family: 'Libre Caslon Text', serif;
  --font-pop-family: 'ABC Monument Grotesk Black', sans-serif;
  --font-highlight-family: 'ABC Monument Grotesk Light', sans-serif;

  --font-body-weight: 300;
  --font-body-style: normal;
  --font-heading-weight: 400;
  --font-heading-style: normal;
  --font-subheading-weight: 400;
  --font-subheading-style: normal;
  --font-alt-weight: 400;
  --font-alt-style: normal;
  --font-pop-weight: 400;
  --font-pop-style: normal;
  --font-highlight-weight: 300;
  --font-highlight-style: normal;

  --payment-terms-background-color: #f7f7f7;
  --swiper-pagination-color: #adaeb3;
  --swiper-pagination-bullet-inactive-color: #e5e6e9;
  --swiper-pagination-bullet-inactive-opacity: 1;

  --payment-terms-background-color: #f7f7f7;

}


//@layer base {

  /* Type Scale (Build your scale https://type-scale.com/) - Custom
  ========================================================================== */

  html {font-size: 100%;} /*11px*/

  body, .body {
    background: var(--color-light) !important;
    font-family: var(--font-body-family);
    font-weight: var(--font-body-weight);
    line-height: 1.38;
    color: var(--color-body);
    overflow-x: hidden;
  }

  body{@apply m-0}

  p, .p { 
    @apply font-body text-base font-normal leading-relaxed; 
    margin: 0 0 1.875em;

    .reset-p & {
      margin-bottom: 0;
    }

    &:last-child {
      #MainContent & {
         margin-bottom: 0;
      }
    }
  }
  .p-reset p {
    margin:auto;
  }

  b, strong {
    font-family: var(--font-heading-family);
    font-weight: var(--font-heading-weight);
  }

  h1, .h1 { @apply font-heading text-5xl font-normal; }

  h1, .h1, h2, .h2 { @apply mx-0 my-2 leading-snug normal-case; }

  h2, .h2 { @apply font-heading text-3xl font-normal normal-case; }

  h3, .h3 { @apply font-heading normal-case; }

  h3, .h3, h4, .h4 { @apply mx-0 my-2; }

  h4, .h4 { @apply text-gray text-2xs font-heading; }

  h5, .h5 { @apply font-highlight text-2xl; }

  h5, .h5, h6, .h6 { @apply mx-0 my-2; }

  h6, .h6 { @apply font-body text-2xs font-normal; }

  body,
  button,
  input,
  select,
  textarea {
      -webkit-font-smoothing: antialiased;
      text-size-adjust: 100%
  }

  ul {
    @apply list-disc pl-8 my-4;
    #shopify-section-popups & {
      @apply pl-4;
    }
  }

  /* Type Styles
  ========================================================================== 
   Guidelines
  ==========================================================================
    **Type Styles**

    primary: Primary Headline or Title type.
    secondary: Commonly used as Subtitle type, compliments Primary.
    tertiary: Third highest level user attention, smaller but stylistically similar to primary.

    **Type Layouts / Spacing**

    page: Most common text color.
    section: Most common text style for headers.
    article: Most common text style for headers.

  */

  .font-body {
    font-family: var(--font-body-family);
    font-weight: var(--font-body-weight);
    font-weight: var(--font-body-style);
  }

  .font-heading {
    font-family: var(--font-heading-family);
    font-weight: var(--font-heading-weight);
    font-style: var(--font-heading-style);
  }

  .font-subheading {
    font-family: var(--font-subheading-family);
    font-weight: var(--font-subheading-weight);
    font-style: var(--font-subheading-style);
  }

  .font-alt {
    font-family: var(--font-alt-family);
    font-weight: var(--font-alt-weight);
    font-style: var(--font-alt-style);
  }

  .font-pop {
    font-family: var(--font-pop-family);
    font-weight: var(--font-pop-weight);
    font-style: var(--font-pop-style);
  }

  .font-highlight {
    font-family: var(--font-highlight-family);
    font-weight: var(--font-highlight-weight);
    font-style: var(--font-highlight-style);
    p {
      font-family: var(--font-highlight-family);
      font-weight: var(--font-highlight-weight);
      font-style: var(--font-highlight-style);
    }
  }

  .font-legal {
    @apply font-highlight text-3xs normal-case;
  }

  .font-caption-caps {
    @apply font-highlight text-2xs uppercase tracking-wider;
  }

  .font-desktop-nav {
    @apply font-body text-sm uppercase tracking-wider;
  }

  .font-caption {
    @apply font-highlight text-xs;
  }

  .font-link {
    @apply font-body text-xs underline font-light;
  }

  .type, .title {

    &--primary {
      @apply h1;
    }
    &--secondary {
      @apply h1;
    }
    &--page {
      @apply h2;
    }
    &--section {
      @apply h1;
    }
    &--article {
      @apply h2;
    }
    &--eyebrow {
      @apply font-subheading uppercase text-base;
    }
    &--headline {
      @apply h1;
    }
    &--subline {
      @apply h2;
    }
    &--product {
      @apply font-highlight lg:text-2xl text-base font-light;
      line-height: 1.38;
    }
  }

  /* Buttons and Controls
  ========================================================================== */

  .button, .btn {
    @apply text-2xs font-body uppercase tracking-wider leading-normal py-2 px-8 border border-primary bg-primary text-white rounded-md text-center;
    &--primary { 
      @apply border-primary bg-primary text-white; 
      text-decoration: none;
    }
    &--secondary { @apply border-dark bg-secondary text-dark; }
    &--tertiary { @apply border-tertiary bg-tertiary text-dark;  }
    &--light { @apply border-dark bg-white text-dark; }
    &--dark { @apply border-light bg-dark text-light; }
    &--pop { @apply bg-pop border-pop text-white; }
    &--highlight { @apply bg-highlight border-highlight text-white; }
    &--link { @apply bg-transparent text-dark p-0 border-0 rounded-none border-b pb-0 text-base font-pop tracking-normal; }
    &--text { @apply p-0 bg-transparent no-underline border-none text-xs; }
    &--w-icon { 
      @apply flex items-center border-dark bg-white text-dark;
      span {
        &:first-child {
          @apply mr-3 flex items-center;
        }
      }
    }
    &[disabled],&.disabled,&.button--disabled {
      opacity:0.5;
      cursor:not-allowed;
      text-decoration: none;
    }
    &--shoppay { 
      @apply flex justify-center items-center border-dark bg-white text-dark lg:h-[39px] mt-2;
    }
  }

  .button--inverse{
    @apply block cursor-pointer uppercase text-black text-center text-[12px] px-[10px] py-[5px] leading-none border border-black rounded-[5px] bg-white;

    &.active{
      @apply bg-[#e1e2e5];
    }
  }

  .btn-control {
    @apply bg-transparent p-3 text-dark transform z-10 opacity-100 transition-all select-none items-center flex-col justify-center;

    .btn-next {

    }
    .btn-prev {
      
    }
  }

  .pagination {
    @apply text-center flex items-center justify-center px-3 pb-3 pt-6;

    &.slideshow-pagination {
      @apply gap-x-1.5;
    }

    .pagination-bullet, .swiper-pagination-bullet {
      @apply  rounded-full mx-0 cursor-pointer relative opacity-100 bg-transparent border border-solid border-black w-3 h-3 mr-3;
      &.active, &.swiper-pagination-bullet-active {
        @apply opacity-100 bg-black;
      }
    }
    &.pagination-dash {
      .pagination-bullet, .swiper-pagination-bullet {
        @apply bg-dark w-20 h-1 rounded-none mx-3;
        &.active, &.swiper-pagination-bullet-active {
          @apply opacity-100 bg-light;
        }
      }
    }
    &.swiper-pagination-progressbar.swiper-pagination-horizontal {
      height: 2px;
      @apply relative p-0 bg-gray-100;
      .swiper-pagination-progressbar-fill {
        @apply bg-dark;
      }
    }
    &.swiper-pagination-bullets.swiper-pagination-horizontal {}
  }

  /* Form
  ========================================================================== */

  .field {
    @apply relative mb-2;

    &:last-child {
      @apply mb-0;
    }
    
    label {
      
      @apply flex items-center text-sm font-body leading-normal mb-2 text-left;

      &.back-in-stock-optin__label{
        @apply mb-0;
      }

      .collection__filters & {
        @apply justify-start;
      }

      & ~ label { @apply mt-2; }
    
      input[type="checkbox"], input[type="radio"] {
        @apply sr-only;

        & ~ span {
          @apply select-none;
        }

        & + span {
          @apply w-6 h-6 mr-2 border border-gray-dark bg-white flex items-center justify-center cursor-pointer;
          svg {
            display:none;
          }
        }

        &[checked], &:checked {
          & + span {
            @apply bg-dark;
          }

          & ~ span {
            @apply font-bold
          }
        }
      }
      input[type="radio"] + span { @apply rounded-full; }

    }
    input, textarea, select {
      @apply block lg:text-2xs text-base font-body uppercase tracking-wider leading-snug py-2 px-5 border border-dark bg-white w-full rounded-md;
      &#back-in-stock-optin{
        @apply w-3.5;
      }
      &.back-in-stock__input{
        @apply py-2 lg:text-xs text-[10px] lg:px-4 px-2;
        &::placeholder {
          @apply lg:text-xs text-[10px];
        }
      }
      &:focus-visible {
        @apply border border-dark;
      }
      &::placeholder {
        @apply text-2xs;
      }
    }

    select {
      @apply pr-16;
    }

    &--select {
      &:after {
        @apply pointer-events-none absolute block inset-y-1 right-1 flex items-center px-2 bg-white text-dark bg-no-repeat bg-center w-12 border border-transparent rounded-md;
        border-style: inset;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='1' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
        content: '';
      }
      select {
        @apply appearance-none cursor-pointer;
      }
    }

    &.field-plus-minus {
      button {
        @apply text-base border border-dark bg-white text-black cursor-pointer h-8 w-8 text-center flex items-center justify-center;

        svg {
          @apply stroke-2;
        }
      }

      button:first-child {
        @apply border-r-0;
      }

      button:last-child {
        @apply border-l-0;
      }

      input {
        @apply border border-black border-l-0 border-r-0 bg-white text-black text-base w-12 h-8 text-center rounded-none;
      }
    }
    
  }

  /* Sections
  ========================================================================== */

  .list {
    .accordion {
      &:last-child {
        @apply border-b-2 border-white;
      } 
    }
  }

  .accordion {
    &:last-child {
      @apply border-b-2 border-white;
    }
    .accordion-control {
      &--minus {
        display: none;
      }
      &--plus {
        display: flex;
      }
    } 
    &[open] {
      .accordion-control {
        &--minus {
          display: flex;
        }
        &--plus {
          display: none;
        }
      } 
    }
    .accordion-title {
      @apply bg-transparent text-dark font-caption-caps uppercase flex flex-row w-full border-2 border-white border-l-0 border-b-0 border-r-0 text-left items-center justify-between leading-normal px-5 lg:px-5 py-5 text-xs cursor-pointer;
      span:first-child {
        @apply flex items-center;
      }
      .accordion-control {
        @apply ml-auto p-0 bg-transparent text-dark border-0;
        .icon {}
      }
    }
    .accordion-panel {
      > div {
        @apply pb-4 pt-0 px-5 lg:px-10 border-0 font-highlight;
        .collection__filters & {
          @apply px-0;
        }
      }
      p:last-child {
        @apply mb-0;
      }
    }
  }

  .tabs {
    ul:not(.start) {
      @apply pl-0 flex justify-end w-full;
      li {
        @apply ml-5;
      }
    }

    ul.start {
      @apply space-x-4;
    }

    .tab-title {
      @apply btn btn--tertiary;
      &.active {
        @apply btn--light; 
      }
    }
    .tab-panel {
      > div {
        @apply py-6;
      }
    }
  }

  .product-motivator {
    @apply px-5 lg:px-10 my-6;

    &__link {
      text-decoration: underline;
      text-decoration-thickness: from-font;
      text-underline-offset: 2px;
    }
  }

  /* Global Helpers
  ========================================================================== */

  a:focus,a:hover {
      color: inherit
  }   

  main { @apply block; }

  .top-live-header {
      top:var(--scrolled-header-height);
  }

  .container { 
    @apply 2xl:max-w-screen-2xl xl:max-w-screen-xl lg:max-w-screen-lg max-w-full mx-auto; 
    &.container--small {
      @apply lg:max-w-screen-md max-w-full mx-auto;
    }
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  input[type="search"]::-webkit-search-decoration,
  input[type="search"]::-webkit-search-cancel-button,
  input[type="search"]::-webkit-search-results-button,
  input[type="search"]::-webkit-search-results-decoration {
    -webkit-appearance:none;
  }
  
//}

.no-js {
  .header {
    pointer-events:none; 
  }
}

.search-results .product-item__wrapper::before {
  pointer-events: none;
}

.template-search :is([data-section-type="custom-content"],.section-slideshow-collection) {
  display: none;
}
