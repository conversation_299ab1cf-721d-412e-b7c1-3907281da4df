window._Animations = false;

document.addEventListener("DOMContentLoaded", function () {

  setInterval(() => {
    if (!!window._Animations) {
      return;
    }
    if (window.Animations && window.Animations.gsap) {

      window._Animations = true;

      const tickerSections = document.querySelectorAll(".section__ticker");
      let pauseTimeout;

      tickerSections.forEach((section, index) => {
        const speed = parseInt(section.dataset.speed, 10) * 50;
        const direction = section.dataset.direction;
        const ul = section.querySelector("ul");
        const lis = ul.querySelectorAll("li");
        const tickerItemsSpan = document.createElement("span");
        tickerItemsSpan.className = "ticker-items";

        lis.forEach((li) => tickerItemsSpan.appendChild(li));
        ul.appendChild(tickerItemsSpan);

        const tickerItemsWidth = tickerItemsSpan.offsetWidth;

        const clone1 = tickerItemsSpan.cloneNode(true);
        const clone2 = tickerItemsSpan.cloneNode(true);
        ul.appendChild(clone1);
        ul.appendChild(clone2);

        const tickerWrapperSpan = document.createElement("span");
        tickerWrapperSpan.className = "ticker-wrapper";
        while (ul.firstChild) {
          tickerWrapperSpan.appendChild(ul.firstChild);
        }
        ul.appendChild(tickerWrapperSpan);

        Animations.gsap.set(tickerWrapperSpan, { x: -tickerItemsWidth });
        const loopDuration = tickerItemsWidth / speed;

        const tl = Animations.gsap.timeline({
          repeat: -1,
          defaults: { ease: "none" },
        });

        switch (direction) {
          case "reverse":
            tl.to(tickerWrapperSpan, {
              duration: loopDuration,
              x: 0,
            }).to(tickerWrapperSpan, {
              duration: 0,
              x: -tickerItemsWidth,
            });
            break;
          default:
            tl.to(tickerWrapperSpan, {
              duration: loopDuration,
              x: -tickerItemsWidth * 2,
            }).to(tickerWrapperSpan, {
              duration: 0,
              x: -tickerItemsWidth,
            });
        }

        Animations.Draggable.create(tickerWrapperSpan, {
          type: "x",
          trigger: ul,
          throwProps: true,
          onPressInit: function () {
            tl.pause();
            clearTimeout(pauseTimeout);
          },
          onDrag: function () {
            let progressX = this.x / tickerItemsWidth;
            progressX = direction === "reverse" ? progressX % 1 : 1 - (progressX % 1);
            if (progressX < 0) progressX += 1;
            tl.progress(progressX);
          },
          onThrowUpdate: function () {
            let progressX = this.x / tickerItemsWidth;
            progressX = direction === "reverse" ? progressX % 1 : 1 - (progressX % 1);
            if (progressX < 0) progressX += 1;
            tl.progress(progressX);
          },
          onThrowComplete: function () {
            tl.resume();
            Animations.gsap.fromTo(
              tl,
              { timeScale: 0 },
              { timeScale: 1, ease: "none" }
            );
          },
        });

        document.addEventListener(
          "scroll",
          function () {
            tl.resume();
          },
          { passive: true }
        );
      });

    }

  }, 500);
});
