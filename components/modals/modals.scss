[data-active-modal], [data-active-modal-mobile], .modal-open, .no-scroll {
  height:100vh;
  overflow:hidden;
}

[data-active-modal="search"], [data-active-modal="panel-nav"] {
  height: auto;
  overflow: inherit;
}

[data-active-modal-mobile] {
  @media (max-width: 1023px) {
    position: fixed;
    max-width: 100%;
  }
}

.modal-overlay {
  pointer-events:none;
  visibility:hidden;
  opacity:0;
  [data-active-modal] & {
    pointer-events:all;
    visibility:visible;
    opacity:1;
  }
  [data-active-modal-mobile] & {
    z-index: 30;
  }
  [data-active-modal="search"] &, [data-active-modal-mobile] & {
    pointer-events:none;
    visibility:hidden;
    opacity:0;
  }
  [data-active-modal-mobile] & {
    pointer-events:all;
    visibility:visible;
    opacity:1;
  }
  body[data-active-modal=cart] & {
    opacity: 0;
  }
}

.modal-overlay-insert {
  pointer-events:none;
  visibility:hidden;
  opacity:0;
  [data-active-modal-mobile] & {
    pointer-events:all;
    visibility:visible;
    opacity:1;
  }
}

.modal {

  &-search-form{
    @media (max-width: 1023px) {
      .search__input{
        font-size: 16px;
      }
    }
  }

  .modals--initialized & {
    display:block;
    visibility:hidden;
  }

  visibility:hidden;
  pointer-events:none;
  transition-delay: 0ms;

  &.active {
    pointer-events:all;
    visibility:visible;
    [data-active-modal="panel-nav"] & {
      pointer-events:none;
      display:flex;
    }
  }

  &-left {
    transform:translateX(-100%);
    &.active {
      
      transform:translateX(0%);
    }
  }

  &-right {
    transform:translateX(100%);
    &.active {
      
      transform:translateX(0%);
    }
  }

  &-top {
    transform:translateY(-100%);
    &.active {
      
      transform:translateY(0%);
    }
  }

  &-main {
    transform:translateY(-100%);
    &.active {
      
      transform:translateY(var(--header-height));
    }
  }

  &-bottom {
    transform:translateY(100%);
    &.active {
      transform:translateY(0%);
    }
  }

  &-center {
    opacity:0;
    transform:scale(0.9) translate(-50%, -50%);
    &.active {
      opacity:1;
      transform:scale(1.0) translate(-50%, -50%);
    }
  }

  &-full {
    opacity:0;
    transform:scale(0.9);
    &.active {
      opacity:1;
      transform:scale(1.0);
    }
  }

}

