const Modal = {

  init: p => {

    p = p || document
    
    p.addEventListener('engage:action', function(e){
      
      const a = e.detail.info.attributes.find(A=>A.att=='data-active-modal')

      if(!!a) {

        Modal.hide()

        Modal.show(a.set)
        
      }
    });

    Modal.drags()

    _n.qs('html').classList.add('modals--initialized')


  },

  open: (m, config={}) => {

    Modal.show(m)

    if(!!config.returnFocus) config.returnFocus.setAttribute('data-return-focus','here') 
    if(!!config.aria) config.returnFocus.setAttribute('aria-expanded',true) 

    // if(!!multiple) return Modal.show(m, multiple);
    return Neptune.engage.action(
      _n.qs('html'),
      {
        attributes: {
          att:'data-active-modal',
          set:m
        }  
      }
    );

  },

  close: () => {
    // if(!_n.qs('html').hasAttribute('data-active-modal')) return false
    Neptune.engage.action(_n.qs('html'),{attributes:{att:'data-active-modal',set:'_remove'}})
    if(_n.exists('[data-return-focus]')){
      _n.qs('[data-return-focus]').focus()
      if(_n.qs('[data-return-focus]').hasAttribute('aria-expanded')) { _n.qs('[data-return-focus]').setAttribute('aria-expanded','false') }
      _n.qs('[data-return-focus]').removeAttribute('data-return-focus')
    }
    if (_n.qs('#shopify-section-search').classList.contains("active")) {
      _n.qs('#shopify-section-search').classList.remove("active");
    }
  },

  show: (m, multiple) => {

    const modal = _n.qs(`[data-modal="${m}"]`)

    if(!modal) return false;
   
    modal.classList.add('active')
    
    if(_n.exists(`[data-modal="${m}"] button`) && !modal.classList.contains('no-focus-penetration')) {
      setTimeout(()=>{_n.qs(`[data-modal="${m}"] button`).focus() },100)
    } else {
      setTimeout(()=>{modal.focus() },100)
    }

    if(!!multiple){
      _n.qs(`[data-modal="${m}"] [back]`)?.classList.remove('hidden')
      _n.qs(`[data-modal="${m}"] [close]`)?.classList.add('hidden')
    } else {
      _n.qs(`[data-modal="${m}"] [back]`)?.classList.add('hidden')
      _n.qs(`[data-modal="${m}"] [close]`)?.classList.remove('hidden')
    }

    _n.trigger(document,'modal:show', false, false, m)

    modal.addEventListener('keydown', e => {
      if (e.which==27) Modal.close();
    })
    

  },

  hide: m => {

    let modal = null

    if(!!m) {
      modal = _n.qs(`[data-modal="${m}"]`)

      if (!modal) return;

      modal.classList.remove('active')

      if(_n.qs('html').dataset.activeModal == m)
        Modal.close()
    } else {
      modal = _n.qs('[data-modal].active')

      if (modal) modal.classList.remove('active')
    }

    // Reset iframes
    if (modal) {
      const iframe = modal.querySelector('iframe');
      if (iframe && (iframe.src.includes('youtube') || iframe.src.includes('vimeo'))) {
        iframe.src = iframe.src;
      }
    }

    _n.trigger(document,'modal:hide', false, false, m)

  },

  drags: () => {
    _n.qsa('[modal-drag-handle]').forEach( handle => {
      handle.settings = USON.parse(handle.getAttribute('modal-drag-handle'))[0]
      const panel = _n.parents(handle, '[data-modal]');
      ['mousedown','touchstart'].forEach( evt => {
        handle.addEventListener(evt, e => {
          panel.dragging = true
          panel.style.transition = 'none'
          panel.style.transform = 'translateY(var(--translate))'
        })
      });
      ['mouseup','touchend'].forEach( evt => {
        document.body.addEventListener(evt, e => {
          panel.dragging = false
          panel.style.transform = null
          panel.style.transition = null
          let position = (panel.lastY - panel.startY) / panel.clientHeight * 100 
          if( position > handle.settings.threshold ){window.modal.close()}
          panel.style.setProperty('--translate', null)
          delete panel.startY
        })
      });
      ['mousemove','touchmove'].forEach( evt => {
        document.body.addEventListener(evt, e => {
          if (!!e.touches) e.clientY = e.touches[0].clientY
          if(panel.dragging){
            if(!panel.startY) panel.startY = e.clientY
            let y = e.clientY - panel.startY
            panel.lastY = e.clientY
            if(y>0)panel.style.setProperty('--translate',`${y}px`)
          }
        })
      })
    })
  }

}
window.addEventListener('hashchange', e=>{
  if (document.location.hash.includes('#modal:')) {
    Modal.open(document.location.hash.replace('#modal:',''))
  }
})
window.modal = Modal;
export default Modal;