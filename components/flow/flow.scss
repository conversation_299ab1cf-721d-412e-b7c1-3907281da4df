.bag__flow_info {
  display: none;
}

html.flow-localized {

  body.proxy {
    #shopify-section-header, #shopify-section-footer {
        display:none;
    }
    .application__footer{
        display:none;
    }
    .application__header {
      display: flex;
      background: #f7f7f7;
      align-items: center;
      top: 0;
      margin: 0;
      justify-content: center;
      transform: translateY(-6px);
    }
    .flow-generic-mobile-improvements .header .image__object {
      height: 26px;
    }
    main.flow-checkout {
        min-height:100vh;
    }
    .checkout-layout__breadcrumb-container {
        display:none;

        @media only screen and (min-width: 768px) {
            position: absolute;
            z-index: 2;
            width: 40%;
            left: 10%;
            top: 84px;
            &:before{
                
            }
        }
    }
    .badge {
      background-color: transparent;
      position: relative;
      display: inline-block;
      padding: 12px;
      border-radius: 0;
    }
    .application__content-area {
        padding-top:0px;
        padding-bottom:0px;
    }
    .checkout-layout,
    .checkout-layout__checkout-content {
        max-width:none;
    }
    .checkout-layout__checkout-content-container {
        @media only screen and (min-width: 768px) {
          background:#FFF;
          border-right:1px solid #DDD;
          padding:72px;
        }
    }

    .button {
        border-radius:0;
        &.button--default {
            background:#000;
        }
        &.button--secondary {
            background:#000;
        }
        &.button--positive {
            background:#000;
        }

    }
    .text-field__input {
        border-radius:0;
    }
    .select-field__input {
        border-radius:0;
    }
    .checkbox {
      border: none;
    }
    .checkbox__input~label:before,.checkbox__input:checked~label:before, .checkbox__input:indeterminate~label:before {
        border-radius:0;
        background-image:none;
    }
    .checkbox__input:checked~label:after {
      background-color:#000;
      top: 2px;
      left: 3px;
      width: 18px;
      height: 18px;
    }
    .proxy-logo.hidden {
        display:block;
        position:relative;
        width:200px;
        z-index: 2;
        left:calc( 30% - 100px );
        top: 0;
        margin: 30px 0;
        @media only screen and (max-width: 767px) {
            left:calc( 50% - 100px );
            top:10px;
        }
    }
    .flow-consent-container {
      position: fixed;
      z-index:90;
      bottom: 0;
      left: 0;
      right: 0;
      top:0;
      pointer-events:none;
        
        .flow-consent-message-container-notice {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          top: 0;
          background: rgba(0,0,0,.5);
          pointer-events:all;

        .flow-consent-message {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: #FFF;
          padding: 4rem;    
          padding-bottom: 5rem;
          p {
              font-size: 1.25rem;
          }
        }
      }
      .flow-consent-dismiss-btn {
        color: #fff;
        background: #000;
        padding: 1rem 3rem;
        font-size: 1rem;
        position: absolute;
        bottom: 3rem;
        left: 4rem;
        pointer-events: all;
        cursor:pointer;
      }
    }
  }
  .page-wrap {
    @media (min-width: 1040px) {
      padding-top: 62px;
    }
  }
  .hdr--promo-on {
    .page-wrap {
      @media (min-width: 1040px) {
        padding-top: 92px;
      }
    }
  }
  .bag__total {
    &.mb2 {
      margin-bottom: 0.5rem;
    }
  }
  .bag__flow_info {
    display: block;
  }
  .bag--mini {
    top: 62px;
  }
  .pv-help {
    display: none;
  }
  shopify-payment-terms {
    display: none !important;
  }
}

[flow-selector="prices.compare_at.label"] {
    text-decoration: line-through;
    color: #999999;
    margin-right: 3px;
}

/* Country Picker dropdown container (Name depends on element being used as a container) */
.dropdown-container {
  display: inline-block;
  color: white;
  cursor: pointer;
  position: relative;
}

/* Dropdown Trigger */
.flow-country-picker-modal-trigger,
.flow-country-picker-dropdown-trigger,
.flow-currency-picker-dropdown-trigger {
  border: none;
  border-radius: 0;
  cursor: pointer;
  display: inline-block;
  margin-bottom: 0;
  padding: .75rem 0 .75rem .75rem;
}

// .flow-country-picker-dropdown-trigger::after,
// .flow-currency-picker-dropdown-trigger::after {
//   content: '';
//   border-style: solid;
//   border-color: transparent;
//   border-top-color: #bbb;
//   border-width: 8px 6px 0 6px;
//   display: inline-block;
//   width: 0;
//   height: 0;
//   margin-left: 6px;
// }

.flow-country-picker-modal-trigger > img,
.flow-country-picker-dropdown-trigger > img {
  display: none !important;
}

.flow-country-picker-modal-trigger-text,
.flow-country-picker-dropdown-trigger-text,
.flow-currency-picker-dropdown-trigger-text {
  color: #000000;
  text-transform: uppercase;
}

/* Backdrop that takes over the screen */
.flow-country-picker-dropdown-backdrop {
  @apply bg-black bg-opacity-30;
  height: 100%;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 40;
}

.flow-country-picker-instruction-txt {
  color: black;
  display: block;
  padding: 12px 20px;
  font-size: 20px;
  line-height: 34px;
  border-bottom: 1px solid #fff;
  margin-bottom: 20px;
  text-align: center;
}

/* Dropdown button on hover & focus */
.flow-country-picker-modal-trigger:hover,
.flow-country-picker-modal-trigger:focus,
.flow-country-picker-modal-trigger.flow-country-picker-open,
.flow-country-picker-dropdown-trigger:hover,
.flow-country-picker-dropdown-trigger:focus,
.flow-country-picker-dropdown-trigger.flow-country-picker-open,
.flow-currency-picker-dropdown-trigger:hover,
.flow-currency-picker-dropdown-trigger:focus,
.flow-currency-picker-dropdown-trigger.flow-country-picker-open {
  background-color: #f7f7f7;
}

.flow-country-picker-country-logo {
  display: none;
}

.flow-country-picker-country-logo > img {
  display: none;
}

/* Dropdown Content (Hidden by Default) */
.flow-country-picker-dropdown-menu {
  visibility: hidden;
  opacity: 0;
  border-radius: 0;
  -webkit-transition: visiblity 0.10s, opacity 0.10s linear;
  transition: visiblity 0.10s, opacity 0.10s linear;
  position: fixed;
  background-color: #f7f7f7;
  width: 450px;
  max-width: 100%;
  height: 100vh;
  overflow-y: scroll;
  z-index: 50;
  margin-top: 0px;
  left: 0;
  top: 0;
  .svg-check {
    display: none;
  }
  #country-picker-mobile & {
    position: relative;
    border-left: none;
    border-right: none;
    max-height: 0;
    overflow: hidden;
    width: 100vw;
    &.flow-country-picker-show {
      max-height: 2000px;
    }
  }
}
.flow-country-picker-dropdown-menu a:last-child {
  margin-bottom: 4px;
}

/* Links inside the dropdown */
.flow-country-picker-dropdown-menu a {
  text-decoration: none;
  padding: 0 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-line-pack: center;
      align-content: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}

.flow-country-picker-dropdown-menu a:hover {
  opacity: 1;
  background-color: #f7f7f7;
}

/* The actual text inside the dropdown option */
.flow-country-picker-dropdown-option-text {
  -ms-flex-item-align: center;
  align-self: center;
  color: #000000;
  text-transform: uppercase;
  position: relative;
  display: inline-block;
  width: calc(100% - 33px);
  top: -1px;
  cursor: pointer;
  font-size: 20px;
  line-height: 34px;
}

.flow-country-picker-show {
  visibility: visible;
  opacity: 1;
}

.flow-country-picker-open {
  color: #000000;
}

/* Modal Country Picker */
.flow-country-picker-modal {
  background-color: #f7f7f7;
  border-radius: 0px;
  height: 100vh;
  left: 0;
  padding: 0px;
  position: fixed;
  top: 0;
  width: 450px;
  max-width: 100%;
  z-index: 50;
}

/* An item inside the modal */
.flow-country-picker-modal-item-container {
  cursor: pointer;
  display: inline-block;
  font-size: 20px;
  line-height: 34px;
  padding: 0;
  width: 100%;
}
.flow-country-picker-modal-item-container:hover {}

.flow-country-picker-modal-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-line-pack: center;
      align-content: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}

/* The selected item in the modal */
.flow-country-picker-selected-modal-item {
  background-color: #C0DFFF;
  cursor: auto;
  cursor: initial;
}

/* Backdrop that takes over the screen */
.flow-country-picker-modal-backdrop {
  background-color: rgba(0, 0, 0, .30);
  height: 100%;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 40;
}

/* The text within the modal item */
.flow-country-picker-modal-text {
  -ms-flex-item-align: center;
      align-self: center;
  color: #000000;
}

/* Modal item logo */
.flow-country-picker-modal-logo {
  -ms-flex-item-align: center;
      align-self: center;
  border-radius: 2px;
  -webkit-box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .10);
          box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .10);
  flex-shrink: 0;
  height: 15px;
  margin-left: 10px;
  margin-right: 8px;
  width: 21px;
}

/* Modal Header */
.flow-country-picker-modal-header {
  padding: 10px 20px;
  border-bottom: 1px #ffffff solid;
  margin-bottom: 0;
}

.flow-country-picker-modal-title {
  color: #000000;
  display: block;
  text-align: center;
  font-size: 20px;
  line-height: 34px;
}

/* Modal close icon */
.flow-country-picker-modal-close {
  position: absolute;
  cursor: pointer;
  width: 10px;
  height: 10px;
  top: 22px;
  right: 22px;
  scale: 1.5;
  stroke: #000000;
  g {
    stroke-width: 1px;
  }
}
.flow-country-picker-modal-close:hover {
    stroke: #808080;
  }

/* Modal Body */
.flow-country-picker-modal-body {
  max-height: calc(100% - 55px);
  padding: 20px;
  overflow-y: scroll;
}

/* Modal Body Content */
.flow-country-picker-modal-body-content {
  display: flex;
  flex-direction: column;
}

/* Currently selected experience text */
.flow-country-picker-current-experience-txt {
  display: inline-block;
  padding-left: 7px;
  bottom: 6px;
  position: relative;
}

.flow-country-picker-selected-logo {
  display: inline-block;
}

.flow-country-picker-button-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}

.flow-country-picker-button {
  font-size: 1rem;
  display: inline-block;
  min-width: 80px;
  color: #ffffff;
  border: solid 1px #191919;
  border-radius: 4px;
  background-color: #000000;
  cursor: pointer;
  flex-basis: 30%;
  margin-left: 20px;
  padding: 8px;
}

.flow-country-picker-button.flow-country-picker-button-secondary {
  border: solid 1px #191919;
  color: #000000;
  background-color: #ffffff;
}

.flow-country-picker-advanced .flow-country-picker-modal-body-content {
  display: flex;
  justify-content: center;
  padding: 30px 0;
}

.flow-country-picker-advanced .flow-country-picker-wrapper,
.flow-country-picker-advanced .flow-currency-picker-wrapper {
  padding: 20px;
}

/* Some example responsive rules */
@media (max-width: 768px) {
  .flow-country-picker-modal {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100%;
    width: 100%;
    max-height: unset;
  }
  .flow-country-picker-modal-body-content {
    column-count: unset;
  }
}

.flow-information-tooltip {
  max-width: 100%; 
  width: 220px; 
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  padding: 20px;
}

.flow-information-close {
  svg {
    width: 100%;
    height: auto;
  }
}

.flow-information {
  display: none;
  .flow-localized & {
    display: flex;
  }
}

.flow-grid {
  .radio input, .checkbox input {
    height: 0;
    left: 0;
    opacity: 0;
    position: absolute;
    top: 0;
    width: 0;
  }
}

.flow-grid .badge {
  background-color: transparent;
  position: relative;
  display: inline-block;
  padding: 12px;
  border-radius: 0;
}
