@tailwind base;
@import './globals.scss';
@tailwind components;
@tailwind utilities;
@import './@helper-styles/helpers.scss';

@layer components {
	@import '../neptune/styles/swiper/swiper.min.scss';
	@import '../components/modals/modals';
	@import '../components/global-e/global-e';
	@import '../components/searchspring/isp';
	@import '../components/one-trust/onetrust';
	@import '../components/sizzle-fit/sizzle-fit';
	@import '../components/Siblings/siblings';
	@import '../components/slider-cart/slider-cart';
}

@layer utilities {

  .scroll-snap-none {
    scroll-snap-type: none;
  }
  .scroll-snap-x {
    scroll-snap-type: x mandatory;
  }
  .scroll-snap-y {
    scroll-snap-type: y mandatory;
  }

	.snap-align-center > * {
	  scroll-snap-align: center;
	}
	.snap-align-start > * {
	  scroll-snap-align: start;
	}
	.snap-align-end > * {
	  scroll-snap-align: end;
	}
	.flex-slider.snap-align-none > * {
	  scroll-snap-align: none;
	}

  .no-scrollbar {
	  overflow: -moz-scrollbars-none;
	  -ms-overflow-style: none;
	}
	.no-scrollbar::-webkit-scrollbar { 
	  width: 0 !important; 
	  background-color: transparent;
	  height: 0 !important
	}
	.no-scrollbar::-webkit-scrollbar-track {
	  background-color: transparent;
	}
	.no-scrollbar::-webkit-scrollbar-thumb {
	  background-color: transparent;
	}

}

.icon svg {
	width: 100%;
}

[neptune-liquid]:not([neptune-templated]){
	opacity: 0;
	height: 0;
	pointer-events: none;
	display: none;
}
