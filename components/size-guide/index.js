const SizeGuide = {
  changeCountry: (value) => {
    size_guide.active_country = value
    Neptune.liquid.load('SizeGuide')
  },

  changeUnit: (value, blockIndex = null) => {
    if (blockIndex !== null && size_guide.charts && size_guide.charts.length > 0) {
      size_guide.charts[blockIndex].unit = value;
    } else {
      size_guide.unit = value;
    }
    Neptune.liquid.load('SizeGuide');
    /*
    let measurements = {}

    switch (value) {
      case 'cm':
        for (const key in size_guide.measurements) {
          measurements[key] = size_guide.measurements[key].map(value => value * 2.54);
        }
        break;
      default:
        measurements = size_guide.measurements
        break;
    }

    console.log(measurements)
    */
  },

  init: () => {
    if (size_guide.countries[Shopify.countryName] != undefined || size_guide.countries.includes(Shopify.countryName) || size_guide.charts[0].countries_sizes[Shopify.countryName] != undefined ) {
      size_guide.active_country = Shopify.countryName
    } else {
      size_guide.active_country = Object.keys(size_guide.countries)[0]
    }
    Neptune.liquid.load('SizeGuide')
  }
}

window.addEventListener('DOMContentLoaded', () => {
  window.setTimeout(SizeGuide.init(), 500)
})

window.SizeGuide = SizeGuide;
export default SizeGuide;