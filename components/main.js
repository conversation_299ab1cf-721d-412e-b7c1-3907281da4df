import './main.css';
import './theme.scss';

import 'regenerator-runtime/runtime';

import "../neptune/scripts/_n";
import "../neptune/scripts/neptune";
import "../neptune/scripts/debug";
import "../neptune/scripts/manifest";
import "../neptune/scripts/engage";
import "../neptune/scripts/brig";
import "../neptune/scripts/surface";
import "../neptune/scripts/slider";
import "../neptune/scripts/details";
import "../neptune/scripts/liquid";

import "../neptune/scripts/history";
//import "../neptune/scripts/passport";
import "../neptune/scripts/transport";
import "../neptune/scripts/periscope";
import "../neptune/scripts/product";
import "../neptune/scripts/cart";
import "../neptune/scripts/swiper";
import "../neptune/scripts/sort";
import "../neptune/scripts/complete";
import "../neptune/scripts/cursor";
import "../neptune/scripts/uncomment";
import "../neptune/scripts/height";
import "../neptune/scripts/later";

import '../components/utilities';
import '../components/enhanced-product';
import '../components/sibling-data-sync';
import Modal from "../components/modals";
import CartOffers from "../components/cart-offers";
import _n from '../neptune/scripts/_n';
import Gender from '../components/gender-images/gender-images'
import SlideShowMultiple from '../components/slideshow-multiple/slideshow-multiple';
import Popups from '../components/popups/popups';
import QuickAdd from '../components/quick-add';
import BackInStock from '../components/back-in-stock';
import Siblings from '../components/Siblings';
import '../components/global-e/global-e';
import Routes from '../components/routes';
import Search from '../components/search';
import '../components/ticker';
import Dragger from './dragger';

Neptune.debug.init();

document.addEventListener("cart:updated", function (e) {
  CartOffers.run().then(()=>{
    Neptune.liquid.load("cart", window.cart);
  })
});
document.addEventListener("cart:error", function (e) {
  Neptune.liquid.load("cart", window.cart);
});

document.addEventListener("keyup", function (e) {
  if(e.key === 'Tab')
    document.documentElement.classList.add('keyboard-user')
})

document.addEventListener("cart:itemAdded", function (e) {
  setTimeout(() => {
    modal.open('cart')
    _n.qsa(".button--loader.active").forEach((b) => {
      b.classList.remove("active");
    });
    _n.qs('body').classList.add('cart-item-added');
  },100)
  setTimeout(() => {
    _n.qs('body').classList.remove('cart-item-added');
  }, 1100);

});

// document.addEventListener("product:variantSelected", () => {
//   if (Neptune.products[0].product.variants[0].images.length == 0) return;
//   _n.qs(".product-gallery-slider").swiper.slideTo(
//     _n.index(
//       _n.qs(
//         `[data-slide][data-variants*="${
//           _n.qs("[neptune-product]").variant.id
//         }"]`
//       ).parentNode
//     )
//   );
//   _n.qsa(`.thumbnail-slider [data-variants]`).forEach(
//     (t) => (t.style.display = "none")
//   );
//   _n.qsa(
//     `.thumbnail-slider [data-variants*="${
//       _n.qs("[neptune-product]").variant.id
//     }"]`
//   ).forEach((t) => (t.style.display = null));
// });
document.addEventListener("product:init", () => {
  // setTimeout(() => {
  //   if (Neptune.products[0].product.variants[0].images.length == 0) return;
  //   _n.qsa(`.thumbnail-slider [data-variants]`).forEach(
  //     (t) => (t.style.display = "none")
  //   );
  //   _n.qsa(
  //     `.thumbnail-slider [data-variants*="${
  //       _n.qs("[neptune-product]").variant.id
  //     }"]`
  //   ).forEach((t) => (t.style.display = null));
  // }, 50);

  let param_variant_id = _n.urlparams.get('variant')

  if (!!param_variant_id) {
    let current_product = _n.qs('[neptune-product]')
    let new_variant = current_product.product.variants.find(v => JSON.stringify(v.id) === param_variant_id)

    if (!!current_product && !!new_variant) {
      current_product.variant = new_variant
      Neptune.liquid.load('ProductOptions')
    }
  }
});

let regionNames = new Intl.DisplayNames(['en'], {type: 'region'});
Shopify.countryName = regionNames.of(Shopify.country);


document.addEventListener("surface:changeDirection", () => {
  _n.qs('html').setAttribute('scroll-direction',Neptune.surface.direction)

  setTimeout(() => {
    const new_height = _n.qs("#shopify-section-header").getClientRects()[0].bottom + "px"

    document.documentElement.style.setProperty("--dynamic-header-height", new_height)
  }, 400)
})

document.addEventListener('engage:action', e => {
  e.detail.info.attributes.forEach(A=>{
    if(A.att=='data-active-modal' && A.set=='panel-nav'){
      _n.qs('[data-modal="panel-nav"] .panes').scrollTo(0,0)
    }
  })
})

document.addEventListener("DOMContentLoaded", function (e) {

  Neptune.product.init();

  Neptune.manifest.init();
  Neptune.engage.init();
  Neptune.brig.init();
  Neptune.surface.init();
  Neptune.details.init();
  Neptune.liquid.init();
  Neptune.periscope.init();
  //Neptune.passport.init();
  Neptune.slider.init();
  Neptune.swiper.init();
  Neptune.transport.init();
  Neptune.sort.init();
  Neptune.complete.init();
  Neptune.cursor.init();
  Neptune.uncomment.init();

  CartOffers.run().then(()=>{
    Neptune.liquid.load("cart", window.cart);
  })

  Neptune.height.init();
  Neptune.later.init();

  Modal.init();
  SlideShowMultiple.init();
  Routes.init();

});

document.addEventListener("template:rendered", function (e) {

  const el = e.detail.info.el;
  Neptune.transport.init(el);
  Neptune.product.init(el);
  Neptune.manifest.init(el);
  Neptune.engage.init(el);
  Neptune.brig.init(el);
  Neptune.surface.init(el);
  Neptune.details.init(el);
  Neptune.liquid.init(el);
  Neptune.periscope.init(el);
  Neptune.sort.init(el);
  Neptune.product.init(el);
  Neptune.slider.init(el);
  Neptune.swiper.init(el);
  Neptune.cursor.init()
  Neptune.uncomment.init(el);
  try {
    globalE.handleLocalizedContent()
  } catch(err){/*tbss*/}

  const template_search = document.querySelector('.template-search');
  const product_grid = template_search?.querySelector('.collection__product-grid');
 
  if (product_grid?.children.length === 0) {
    template_search.querySelectorAll('[data-section-type="custom-content"], .section-slideshow-collection').forEach(el => el.style.display = "block");
  }
});

document.addEventListener("periscope:viewed", function (e) {
  const el = e.detail.info.el;

  Neptune.transport.init();
  Neptune.product.init(el);
  Neptune.manifest.init(el);
  Neptune.engage.init(el);
  Neptune.brig.init(el);
  Neptune.surface.init(el);
  Neptune.details.init(el);
  Neptune.liquid.init(el);
  Neptune.periscope.init(el);
  Neptune.sort.init(el);
  Neptune.product.init(el);
  Neptune.swiper.init(el);
  Neptune.cursor.init();
  Neptune.uncomment.init();
  setTimeout(Neptune.slider.init, 500);
});

document.addEventListener("periscope:loaded", function (e) {
  const el = e.detail.info.el;

  Neptune.transport.init();
  Neptune.product.init(el);
  Neptune.manifest.init(el);
  Neptune.engage.init(el);
  Neptune.brig.init(el);
  Neptune.surface.init(el);
  Neptune.liquid.init(el);
  Neptune.periscope.init(el);
  Neptune.sort.init(el);
  Neptune.product.init(el);
  Neptune.swiper.init(el);
  Neptune.cursor.init();
  Neptune.uncomment.init();
  setTimeout(Neptune.slider.init, 500);
});

document.addEventListener("shopify:section:load", function (event) {
  Neptune.product.init();

  Neptune.manifest.init();
  Neptune.engage.init();
  Neptune.brig.init();
  Neptune.surface.init();
  Neptune.liquid.init();
  Neptune.periscope.init();
  //Neptune.passport.init();
  Neptune.slider.init();
  Neptune.swiper.init();
  Neptune.transport.init();
  Neptune.sort.init();
  Neptune.cursor.init()
  Neptune.uncomment.init();
  Neptune.height.init();
});


window.addEventListener("DOMContentLoaded", () => {
  document
    .querySelectorAll(".sticky-text.invert-color .content-item__text-inner:not(.content-item__text--duplicate)")
    .forEach((el) => {
      if (
        !!el.previousElementSibling &&
        el.previousElementSibling.matches(".content-item__text-inner--duplicate")
      )
        el.previousElementSibling.remove();

      let clone = el.cloneNode(true);
      clone.classList.add("content-item__text-inner--duplicate");
      clone.innerHTML = el.innerHTML;
      el.insertAdjacentElement("beforebegin", clone);
	});
	document.dispatchEvent(new CustomEvent("duplicate:text"));
});


