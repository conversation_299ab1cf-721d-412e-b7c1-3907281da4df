// render against this data
window.search = {
  ...window.search || {}, 
  ...{
    loading:false,
    results:false,
    fromInput: true,
  }
};

// base controller
window.Search = {

  query: term => {

    if(search.loading) return;

    search.loading = true;
    search.query = term;

    return new Promise( (res, rej) => {

      let fetch_config = {}
      Object.keys(search.settings.remote).forEach(key=>{
        if (key.includes('config_') && !!search.settings.remote[key])
          fetch_config[key.replace('config_','')] = _n.literal(search.settings.remote[key], {term:term})
      })
      if(!!fetch_config.headers && typeof fetch_config.headers == 'string')
      fetch_config.headers = JSON.parse(fetch_config.headers)

      fetch(Search.url({term:term}),fetch_config)
        .then(response=>response.json())
        .then(data=>Search.map(data))
        .then(data=>{
          _n.hooks.process('Products:enhance', data.products)
            .then(processedData => {
              data.products = processedData
              search.results = data
            })
            .then(()=>{
              search.term = term
              search.loading = false;
              _n.trigger(window,'Search:loaded')

              document.querySelector('[search-results]').style.display = ""
              document.querySelector('[search-suggestions]').style.display = ""

              setTimeout(()=>{search.suggestions = false;},100)
              res(true)
            })
        })

    })

  },
  
  init:() => {

    window.addEventListener('input',_n.debounce(e=>{
      if(e.target.name=='q' && !e.target.value) {
        search.results = false
        search.term = ''

        document.querySelectorAll('[search-suggestions]').forEach(el => el.innerHTML = '');
        document.querySelectorAll('[search-results]').forEach(el => el.innerHTML = '');
      }
  
      search.suggestions = true;
  
      if(e.target.name === 'q' && !!e.target.value) {
        window.search.fromInput = true;
        Search.query(e.target.value);
      }
    }, search.settings.debounce || 500));
  },

  url: (config={}) => {
  
    return _n.literal(search.settings.remote_url, config)
  
  },

  map: (data, map) => {

    return new Promise(res=>{

      map = map || search.settings.map || false;

      if(!map) res(data);

      res(_n.map(data,map))

    })
    
  }

}

document.addEventListener('DOMContentLoaded', Search.init)