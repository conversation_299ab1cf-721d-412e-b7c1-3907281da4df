const Popup = {
  instance: {
    session:{},
    local:{}
  },
  active: false,
  init: () => {
    Popup.increment()
    for (let id in Popups) {
      const trigger = Popups[id].on;
      if (trigger === 'scroll') {
        let timeout;
        window.addEventListener('scroll', () => {
          clearTimeout(timeout);
          timeout = setTimeout(() => Popup.check(id), 200);
        });
      } else {
        window.addEventListener(trigger, () => Popup.check(id));
      }
    }
  },
  checker: {},
  check: id => {
    const _inst = Popup.instance[Popups[id].track][id]
    if(Popup.active) return false;
    if(!Popup.qualify(id)) return false;
    if(Popups[id].minimum_page_views > (_inst.page_views || 0)) return false;
    if(window.scrollY < Popups[id].scrolled_past) return false;
    if(_inst.opens >= Popups[id].maximum_opens) return false;
    if(_inst.closes >= Popups[id].maximum_closes) return false;

    if(!!Popups[id].js_display_logic && !eval(Popups[id].js_display_logic)) return false;

    Popup.open(id)
  },
  open: id => {
    // Set active popup ID
    Popup.active = id;

    _n.trigger(document,'popup:load', false, false, { id:id });
  },
  close: () => {
    const id = Popup.active;
    if (!id) return;

    _n.trigger(document,'popup:close', false, false, { id:id });
  },
  save: (id, key, val) => {
    if (!id || !Popups[id]) return false;
    const track = Popups[id].track;
    if (key !== null && key !== null){
      Popup.instance[track][id][key]=val
    }
    window[`${track}Storage`].setItem(`Popups`, JSON.stringify(Popup.instance[track]))
  },
  recall: () => {
    for (let type in Popup.instance) {
      Popup.instance[type] = JSON.parse(window[`${type}Storage`].getItem(`Popups`)  ) || {}
    }
    for (let id in Popups) {
      if(!Popup.instance[Popups[id].track][id])Popup.instance[Popups[id].track][id]={};
    }
  },

  qualify: id => {
    let qualified_by_inclusions = Popups[id].including.split(',').map(path=>path.trim()).filter(path=>!!path).filter(path=>{
      if (path.includes('*') && document.location.pathname.includes(path.replace('*',''))) return true;
      if (document.location.pathname == `/${path}`) return true;
      return false
    }).length > 0 || Popups[id].including==='';
    
    let qualified_by_exclusions = Popups[id].excluding.split(',').map(path=>path.trim()).filter(path=>!!path).filter(path=>{
      if (path.includes('*') && document.location.pathname.includes(path.replace('*',''))) return true;
      if (document.location.pathname == `/${path}`) return true;
      return false
    }).length == 0 || Popups[id].including===''

    return qualified_by_inclusions && qualified_by_exclusions
  },

  increment: () => {

    Popup.recall()
    
    for (let id in Popups) {
      
      if(Popup.qualify(id)){
        Popup.tick(id,'page_views')
      }

    }
  },
  tick: (id, key) => {
    Popup.save(id,key,(Popup.instance[Popups[id].track][id][key] || 0) + 1)
  }
}

// To reset popup, add this to the console: Popup.reset('YOUR_POPUP_ID_HERE');

Popup.reset = (id) => {
  const track = Popups[id]?.track || 'session';
  if (Popup.instance[track][id]) {
    Popup.instance[track][id].closes = 0;
    Popup.instance[track][id].opens = 0;
    window[`${track}Storage`].setItem('Popups', JSON.stringify(Popup.instance[track]));
  }
};

document.addEventListener('popup:load', (e) => {
  const { id } = e.detail.info;
  Neptune.liquid.load('Popup');
  Popup.tick(id, 'opens');

  // Apply delay if specified
  const delay = Popups[id].delay || 0;

  setTimeout(() => {

    // Create and dispatch a "popup:animate" event after DOM update
    requestAnimationFrame(() => {
      _n.trigger(document,'popup:animate', false, false, { id: id });
    });

  }, delay);
});

document.addEventListener('popup:animate', (e) => {
  const { id } = e.detail.info;
  const popupEl = document.getElementById(id);
  if (popupEl) {
    popupEl.classList.add('popup--active');
  }
});

document.addEventListener('popup:close', (e) => {
  const { id } = e.detail.info;
  const popupEl = document.getElementById(id);

  let duration = 0;

  if (!!popupEl && popupEl.dataset.animationType != 'none') {
    // Get animation duration from inline style or use default
    duration = parseInt(popupEl.style.transitionDuration.replace('ms','')) || 500;
  }

  // Remove active class to trigger exit animation
  popupEl.classList.remove('popup--active');

  // Create and dispatch a "popup:cleanup" event after animation
  setTimeout(() => {

    _n.trigger(document,'popup:cleanup', false, false, { id: id });

  }, duration);
});

document.addEventListener('popup:cleanup', (e) => {
  const { id } = e.detail.info;
  Popup.tick(id, 'closes');
  Popup.active = false;
  Neptune.liquid.load('Popup');
});

window.Popup = Popup;
// window.addEventListener('DOMContentLoaded', Popup.init)
Popup.init();