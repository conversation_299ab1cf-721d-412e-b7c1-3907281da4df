const Filters = {
	init: () => {
		const sortMap = collection.settings.filter_sort_map.split("\n").map(group=>{
			return{
				set:group.split(':')[0],
				group:group.includes('(') ? group.split('(')[1].split(':')[0] : '',
				values: group.split(':').at(-1).replace(')','').split(',').map(v=>v.trim())
			}
		})

		collection.filters.all.map(set=>{

			set.option_groups = [];
		
			const groups = sortMap.filter(ss=>ss.set.toLowerCase()==set.label.toLowerCase())
		
			if (groups.length === 0) {
				set.option_groups.push({options:set.options})
			}
			
			groups.forEach(group=>{
				const options = []

				group.values.forEach(val=>{
					const option = set.options.find(o=>o.label.trim().toLowerCase()==val.trim().toLowerCase())
					if (!!option) {
						options.push(option)
					} 
					if(val=='*'){
						const assigned = groups.map(g=>g.values).flat().map(v=>v.trim())
		
						set.options.filter(o=>!assigned.includes(o.label.trim())).forEach(o=>{
							// console.log('push',o)
							options.push(o)
						})
					}
				})
				set.option_groups.push({
					name:group.group,
					options:options
				})
		
			})
			return set;
		})

	}
}

window.Filters = Filters

window.addEventListener('Collection:data', Filters.init)

export default Filters