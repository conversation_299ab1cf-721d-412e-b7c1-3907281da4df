const Routes = {

  init: p => {

    

    if (window.Shopify.routes.root == '/') return;

    if(!window.localCart){
      window.localCart = 'pending';
      //fetch(Shopify.routes.root+'cart?view=local').then(r=>r.json()).then(d=>window.localCart=d);
      
      
      // // block cart modal from opening
      // document.addEventListener("cart:itemAdded", function (e) {
      //   modal.close()
      //   window.location = Shopify.routes.root+'/cart'
      // })


      // document.addEventListener("cart:updated", function (e) {
      //   // Handle inadequacy of Shopify Cart API to add items in the correct currency to boot them to the cart page.
      //   if(Shopify.currency.active != cart.currency){
      //     modal.close()
      //     window.location = Shopify.routes.root+'/cart'
      //   }
      // })
      // document.addEventListener("cart:itemAdded", function (e) {
      //   console.log('cart:itemAdded',Shopify.currency.active,cart.currency);
      //   if(Shopify.currency.active != cart.currency){
      //     modal.close()
      //     window.location = Shopify.routes.root+'/cart'
      //   }
      // })

    }

    p = p || document

    _n.qsa(`a[href]:not([href*="${Shopify.routes.root.replace(/\/+$/, '')}"])`).filter(el=>el.getAttribute('href').indexOf('/')===0).forEach(el=>{
      el.setAttribute('href', el.getAttribute('href').replace(/\//, Shopify.routes.root))
    })
    _n.qsa(`form[action]:not([action*="${Shopify.routes.root.replace(/\/+$/, '')}"])`).filter(el=>el.getAttribute('action').indexOf('/')===0).forEach(el=>{
      el.setAttribute('action', el.getAttribute('action').replace(/\//, Shopify.routes.root))
    })

  },

  url: url => {
    if (!url) return false
    if (url.includes(Shopify.routes.root)) return url;
    
    return url.replace(/([^\/])\/([^\/])/, `$1${Shopify.routes.root}$2`)
  }

}
window.Routes = Routes;
export default Routes;

window.addEventListener('DOMContentLoaded',()=>{
  Neptune.liquid.engine.registerFilter("url", (v) => Routes.url(v));
})

window.addEventListener('Collection:productData', () => {
  if (collection.redirect) {
    collection.redirect = Routes.url(collection.redirect)
  }
  return;
})
