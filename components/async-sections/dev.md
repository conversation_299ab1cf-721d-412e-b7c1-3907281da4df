To add this functionality to a Section, add this to it's schema section.settings
```
{
  "type": "checkbox",
  "id": "async",
  "label": "Asynchronously Load Section on Scroll",
  "default": false
}
```

Then include the logic snippet, optionally passing a Skeleton to show while loading
```
{% include 'async-section' skeleton:'<div class="p-16 w-full h-96 bg-gray-100 "><div class="bg-white h-full w-full"><span class="sr-only">Skeleton</span></div></div>' %}

```
