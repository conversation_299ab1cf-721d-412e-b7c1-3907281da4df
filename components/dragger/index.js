Neptune.dragger = {
    init:(p=document)=>{
      const slider = p.querySelectorAll('.dragger-scroll-x');
      let isDown = false;
      let startX;
      let scrollLeft;

      if (slider) {
        slider.forEach(element => {
          element.addEventListener('wheel', (e) => {
            element.classList.add('snap-x');
          })
          element.addEventListener('mousedown', (e) => {
            isDown = true;
            element.classList.remove('snap-x');
            startX = e.pageX - element.offsetLeft;
            scrollLeft = element.scrollLeft;
          });
          element.addEventListener('mouseleave', () => {
            isDown = false;
          });
          element.addEventListener('mouseup', () => {
            isDown = false;
      
          });
          element.addEventListener('mousemove', (e) => {
            if(!isDown) return;
            e.preventDefault();
            const x = e.pageX - element.offsetLeft;
            const walk = (x - startX) * 3; //scroll-fast
            element.scrollLeft = scrollLeft - walk;
          });
        });
      }
  
    }
  }
  document.addEventListener('DOMContentLoaded', e=>{
    Neptune.dragger.init()
  })
  document.addEventListener('template:rendered', e=>{
    Neptune.dragger.init()
  })