// Wait for _n to be available before registering the hooks
const registerSiblingSync = () => {
  if (typeof _n === 'undefined' || !_n.hooks) {
    setTimeout(registerSiblingSync, 100);
    return;
  }

  // Register Products:enhance hook to filter sibling data
  _n.hooks.register('Products:enhance', filterSiblingData);
};

// Function to filter sibling data directly in JavaScript
function filterSiblingData(data) {
  return new Promise(resolve => {
    if (!data || !Array.isArray(data)) {
      return resolve(data);
    }
    
    // Process each product without deep cloning the entire structure
    data.forEach(product => {
      // Skip if no siblings
      if (!product || !product.siblings || !Array.isArray(product.siblings)) {
        return;
      }
      
      // Filter out siblings with 'sale' or 'hide-swatches' tags
      product.siblings = product.siblings.filter(sibling => {
        if (!sibling || !sibling.tags || !Array.isArray(sibling.tags)) {
          return true; // Keep siblings without tags
        }
        
        // Check for 'sale' or 'hide-swatches' tags
        return !sibling.tags.some(tag => 
          typeof tag === 'string' && (
            tag.toLowerCase() === 'sale' || 
            tag.toLowerCase() === 'hide-swatches'
          )
        );
      });
    });
    
    // Return the modified original data
    resolve(data);
  });
}

// Start the registration process
registerSiblingSync();
