const MegaMenu = {
  remove: e => {
    const current = document.elementFromPoint(e.clientX, e.clientY)

    if ( !_n.parents(current, '.header--nav') || (_n.parents(current, '.nav--item-link') && _n.parents(current, '.nav--item-link').hasAttribute('href')) ) {
      _n.qs('.nav--item.active')?.classList.remove('active')
      document.documentElement.classList.remove('active-mega')
      document.removeEventListener('mousemove', MegaMenu.remove)
    }
  },

  init: () => {
    const triggers = _n.qsa('.header .mega-trigger')

    triggers.forEach(trigger => {
      trigger.addEventListener('mouseover', e => {
        document.addEventListener('mousemove', MegaMenu.remove)
      }, { passive: true })
    })
  }
}

window.MegaMenu = MegaMenu

window.addEventListener('DOMContentLoaded', MegaMenu.init)