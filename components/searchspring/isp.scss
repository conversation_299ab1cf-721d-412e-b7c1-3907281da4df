.isp_polite_powered_by_id {
  display:none !important;
}

.shopify-section div#isp_search_result_page_container {

  display:flex;


  #isp_left_container {
    width: 25%;
    margin:0;
  }

  #isp_center_container {
    width:75%;
    margin-left:3rem;

    ul#isp_search_results_container {
      float:none;
      display:flex;
      flex-wrap:wrap;

      li.isp_grid_product {
        width:50%;
        margin:0;
        height:448px;

        div.isp_product_image_wrapper {
          height:350px;
        }

        .isp_product_color_swatch,
        a.isp_product_quick_view_button {
          display:none !important;
        }
        &:hover {
          a.isp_product_quick_view_button {
            display:none !important;
          }
        }
      }
    }
  }

}

.parent-group.active .group-active:underline {
  text-decoration: underline;
}

.parent-group.active .group-active:font-semibold {
  font-weight: 600;
}

.ss-auto__border-top {
  left: 0;
  margin: -0.875em 0.3125em 0 -0.375em;
  position: absolute;
  right: 0;
  top: 0; }

.ss-auto__results {
  margin: 0;
  padding: 1.25em 1.25em 0.625em;
  position: absolute;
  right: 0;
  top: 100%;
  text-align: left;
  -webkit-transition: opacity 100ms ease-in-out;
  transition: opacity 100ms ease-in-out;
  width: 100%;
  background-color: #f7f7f7;
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  height: calc(100vh - 133px);
  overflow-y: scroll; 
  z-index: 50;
  @media (max-width: 60em) {
    margin-top: 10px;
    position: static;
    overflow-y: visible;
    padding: 0;
    height: auto;
    border-color: #fff;
  }
}

.ss-auto__title {
  font-size: 0.875em;
  margin: 0 0 0.57143em;
  text-transform: uppercase;
  font-family: "MonumentGrotesk-Bold"; 
  @media (max-width: 60em) {
    margin: .5rem 0;
  }
}

.ss-auto__hr {
  border: 0;
  height: 0.0625rem;
  margin: 0.625em 0;
  background-color: #ddd;
  @media (max-width: 60em) {
    background-color: #fff;
  }
}

.ss-auto__suggestions {
  margin: 0 0 1.5625em;
  padding: 0.25em 0;
  display: flex; 
  list-style: none;
}

.ss-auto__suggestion {
  cursor: pointer;
  font-size: 0.875em;
  padding: 0.42857em 0 !important;
  font-weight: 300;
  margin-right: 2rem; }

.ss-auto__suggestion em {
  font-style: normal;
  font-weight: 700; }

.ss-auto__suggest span {
  display: inline-block; }

.ss-auto__suggest em {
  font-style: normal; }

.ss-auto__products {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  flex-direction: row;
  flex-wrap: wrap;
  padding: 0.3125em 0; }

.ss-auto__product {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-direction: column;
  flex-direction: column;
  font-size: 0.875em;
  margin: 0.42857em 0;
  width: 25%; }
  @media (max-width: 60em) {
    .ss-auto__product {
      width: 50%; } }

.ss-auto-p em {
  font-style: normal;
  font-weight: 700; }

.ss-auto-p__name {
  font-weight: 300;
  line-height: 1.1; }

.ss-auto-p__price {
  display: block;
  font-size: 11px;
  padding-top: 0.71429em;
  font-weight: 300; }

.ss-auto-p__image {
  min-width: 4.6875rem; }

.ss-auto-p__figure {
  margin: 0;
  padding-bottom: 133.33333%; }

.ss-auto-p__figure img {
  display: block; }

.ss-auto-p__info {
  padding: 0;
  text-align: center;
  margin-top: 1rem;
  font-size: 11px; }

.btn__ss-auto-more {
  border: 0;
  display: block;
  font-size: 0.875em;
  padding: 0.42857em 0 0.57143em;
  position: relative;
  text-align: left;
  text-transform: lowercase;
  width: 100%;
  z-index: 2;
  font-family: "MonumentGrotesk-Bold";
  @media (max-width: 60em) {
    padding-top: 0;
  }
}

.btn__ss-auto-more span {
  position: relative; }

.btn__ss-auto-more span:before {
  bottom: -0.35714em;
  content: "";
  display: inline-block;
  display: block;
  height: 0.71429em;
  left: 0;
  margin: 0 -0.35714em;
  position: absolute;
  right: 0;
  z-index: -1; 
}

.new-hidden {
  display: none !important;
}

.product-grid-item {
  a {
    &:hover, &:focus {
      .quick-shop form {
        transform: translateY(3px);
        visibility: visible;
      }
    }
  }
}

.ss-pagination,
.mother-pagination {
  text-align:center;
  display:block;
  width:100%;
  & > * {
    display:inline-block;
  }
  ol {
    list-style:none;
    padding:0;
    
    li {
      display:inline-block;
      text-align:center;
      position:relative;
      width:2rem;
      
      a {
        padding:1rem 0;
        display:block;
      }
      &:before {
        opacity: 0;
      }
      &.current {
        span {
          position:relative;
          display:block;
          color:#fff; 
        }
        &:before {
          opacity: 1;
          display:block;
          content: '';
          position: absolute;
          width: 1.5rem;
          height: 1.5rem;
          background: #000;
          border-radius: 100%;
          padding: 0;
          display: block;
          transform: translate(-50%, -50%);
          top: 50%;
          left: 50%;
        }
      }
            
    }
  }
  .pages-prev,.pages-next {
    display:inline-block;
    a {
      display:block;
      padding:1rem 0;
      width:2rem;
      text-align:center;
      &:before {
        content:'&larr;';
        display:block;
        position:relative;
      }
    }
  }
  .pages-prev a:before {
    content:'\2190';
  }
  .pages-next a:before {
    content:'\2192';
  }
}

.template-collection .collection-filters.top-0, .template-search .collection-filters.top-0 {
  @media (max-width: 1023px) {
    top: 44px !important;
  }
}

.ss-top-facets {
  @media (max-width: 1023px) {
    padding: 1.5rem 1.5rem 1.5rem .75rem;
  }
  .ss-top-facets-top, .ss-top-facets-bottom {
    @media (max-width: 60em) {
      max-width: 100%;
      overflow-x: scroll;
      flex-wrap: nowrap;
      white-space: nowrap;
      align-items: flex-start;
      display: block;
    }
    > div {
      @media (max-width: 1023px) {
        display: inline-block;
      }
    }
    &::-webkit-scrollbar {
      display: none;
    }
  }
}

.pi__img .quick-shop form button.ss-display {
    display: block;
}

.ss-facet-container h2 label {
  font-size: 12px;
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.ss-results .product-grid-item .product-item>.absolute {
  position: relative;
}

.ss-facet,
.ss-sort {
  border: none;
  background: transparent;
  color: #777;
}

.flex-end {
  justify-content: flex-end;
}

.ss-toggle {
  display: none;
  border: none;
  background: transparent;
  color: #777;
}

.show-4 .ss-toggle.view-4,
.show-2 .ss-toggle.view-2 {
  display: inline;
}

.ss-summary .applied-filters {
  padding-top: 4px;
}

.ss-targeted .ss-tool-wrap {
  padding-top: 4px;
}

.ss-tool {
  height: 24px;
}

#main>.collection {
  padding: 0;
}

#ss-sort .active {
  font-weight: bold;
}

.ss-facet-container input.ss-expanded~.checkbox-controlled-height,
.ss-facet-container input.ss-expanded.active~.checkbox-controlled-height {
  max-height: 150vh !important;
}

.ss-facet-container input.ss-collapsed~.checkbox-controlled-height,
.ss-facet-container input.collapsed~.checkbox-controlled-height {
  max-height: 0 !important;
}

.ss-facets .ss-facet-container .ss-title button label {
  cursor: pointer;
}

.tools>ul.list,
.collection-tools>ul.list {
  display: flex;
  justify-content: flex-end;
}

@media (min-width: 1040px) {
  .collection-tools,
  .collection-tools>ul {
      margin-left: auto;
  }
  .ph4-l {
      padding-left: 2rem;
      padding-right: 2rem;
  }
}

@media screen and (min-width: 1040px) {
  .collection-tools.ss-targeted,
  .tools.ss-targeted {
    width: 50%;
  }
}

@media screen and (max-width: 1040px) {
  .ss-apply {
      border-right: 0;
  }
}

.ss-top-facets {
  .ss-facets-row {
    @apply lg:mx-2.5 mx-1.5;
    &:first-child {
      margin-left: 0;
    }
  }
  .ss-top-value {
    @apply button button--light flex whitespace-nowrap;
    padding-left: 14px;
    padding-right: 14px;
    &.active {
      @apply bg-gray-cool;
    }
  }
}

#filtersToggle[aria-expanded="true"]:after {
  display: block;
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.3);
  z-index: 20;
}