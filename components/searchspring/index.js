const Collection = {
  
  config: {
    container: '.collection-grid',
    item: '.product-item__wrapper',
    quickshop: '[data-variants]'
  },
  
  init:() => {
    console.log('Collection.init')
    Collection.injections.run()
    Collection.quickshop.init()
    Collection.quickshop.run()
    // Collection.urgency.run()
    Collection.skeleton.remove()
    Neptune.uncomment.init(_n.qs(Collection.config.container))
  },
  
  injections: {
  
    run: () => {
      
      const page = parseInt(_n.urlparams.get('page') || 1)
      
      _n.qsa(`[collection-injection][injected]`).forEach(el=>{
        el.remove()
      })

      _n.qsa(`[collection-injection]:not([injected])`).forEach(el=>{
        el.injection = USON.parse(el.getAttribute('collection-injection'))[0]

        if(el.injection.page != page) return;
        if(document.location.hash.includes('filter:') && !el.injection.persist.filter) return;
        if(document.location.hash.includes('filter:') && !el.injection.persist.filter) return;
        if(document.location.hash.includes('sort:') && !el.injection.persist.sort) return;

        if(_n.exists(`${Collection.config.item}:nth-child(${el.injection.position})`, _n.qs(Collection.config.container))){
          let clone = el.cloneNode(true)
          _n.qs(`${Collection.config.item}:nth-child(${el.injection.position})`, _n.qs(Collection.config.container)).before(clone)
          clone.setAttribute('injected','true')
        }
      })
    } 
  
  },

  quickshop: {
    init: () => {
      const quickshop_forms = _n.qsa(".quick-shop form")
        
      quickshop_forms.map((form) => {
        const swatches = _n.qsa("input", form)

        swatches.forEach((swatch, ind, arr) => {
          if (!!swatch.dataset.available) {
            swatch.checked = true
            arr.length = ind + 1;
          }
        });
      });
    },

    run: () => {
      _n.qsa(Collection.config.quickshop).forEach(el=>{
        el.addEventListener('change', e => {
          //console.log(e)
        })
      })
    }
  },

  urgency: {

    run: () => {
      _n.qsa(Collection.config.item).forEach(el => {
        const handle = _n.qs('a', el).href.split('/').reverse()[0]
        if(!!inventory.products[handle]){

          let size = false
          let available = false
          if(!!localStorage.getItem('alphaSize') && !!inventory.products[handle][localStorage.getItem('alphaSize')]){
            size = localStorage.getItem('alphaSize')
            available = inventory.products[handle][size];
          }
          if(!!localStorage.getItem('numericSize') && !!inventory.products[handle][localStorage.getItem('numericSize')]){
            size = localStorage.getItem('numericSize')
            available = inventory.products[handle][size];
          }
          if(!available) return;
          if(available <= inventory.threshold){
            const badges = _n.qs('.product-item--badges', el)
            var badge = document.createElement('div')
            badge.classList.add('product-item--badge')
            badge.classList.add('badge--inventory')
            badge.innerHTML = inventory.message.replace('**n**',available).replace('**size**',size)
            badges.appendChild(badge)
          }

        }
      })
    }

  },

  skeleton: {
    remove: () => {
      if(_n.exists('.collection-skeleton')) _n.qs('.collection-skeleton').remove();
    }
  }

}
window.addEventListener('searchspring.motherdenim.domReady', Collection.init);
window.Collection = Collection;

export default Collection;
