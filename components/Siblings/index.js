// const Siblings = {
// 	listen: e => {
// 		console.log(_n.array(e.path).slice(0, -4))
// 		let swatch = _n.array(e.path).slice(0, -4).find(p=>!!p && p.matches('[sibling-swatch]'))
// 		if(!swatch) return;
// 		Siblings.swap()
// 	},
// 	swap: (topic, index, handle) => {
// 		console.log(topic, index, handle)
// 	}
// }
// window.Siblings = Siblings
// document.addEventListener('click', Siblings.listen)