// render against this data
window.collection = {
  ...window.collection, 
  ...{
		handle: window.location.pathname.split('/').pop(),
    products:[],
    filters: {
      all:[],
      applied:{},
      active_set: ''
    },
    pagination: {
      pages:[]
    }
  },
  params:{},
  elements: {
    exposedFilters: {
      initialIndex: 0
    }
  }
};

// base controller
window.Collection = {

  loading:false,
  
  page: (page=1, url=false) => {
    collection.page = page;
    if(collection.settings.paging == 'scroll'){
      if (collection.pagination.pages.filter(p=>p.loaded).map(p=>p.number).includes(page)) return false;
    } else {
      window.scrollTo(0, 0)
    }
    if(url){
      window.history.replaceState(history.state,null,`${ document.location.pathname }?${ _n.urlparams.set('page',page) }`)
    }
    return Collection.load({page:page,sort:collection.sort||''})
  },
  
  filter: (key, vals, swiper) => {
    return new Promise( (res, rej) => {
      // if (clear == true) collection.filters.applied[key] = []
      Array.isArray(vals)?vals:[vals].forEach(val => {
        if (!collection.filters.applied[key])collection.filters.applied[key]=[];
        if (collection.filters.applied[key].includes(val)) {
          collection.filters.applied[key].splice(collection.filters.applied[key].indexOf(val),1)
        } else {
          collection.filters.applied[key].push(val)
        }
        if (collection.filters.applied[key].length == 0)
          delete collection.filters.applied[key];
      })

      collection.filters.active_set = key;

      window.history.replaceState(history.state,null,`${document.location.pathname}?${_n.urlparams.build(
        {
          ...Object.fromEntries(Object.entries(collection.filters.applied).map(e=>{e[0] = `filter[${e[0]}]`; return e})),
          ...collection.params
        }
      )}`)

      if (!!swiper) {
        collection.elements.exposedFilters.initialSlide = swiper.activeIndex
      }
      
      _n.trigger(window,'Collection:filter', true, true, collection.filters.applied)

      Collection.reset().then( () => {
        Collection.page()
      }).then( () => {
        res(true)
      })
    })
  },

  clear: () => {
    return new Promise( (res, rej) => {
      collection.sort = ''
      collection.filters.applied = {}
      window.history.replaceState(
        history.state,
        null,
        `${document.location.pathname}?${_n.urlparams.build({
          ...collection.params,
        })}`
      );
      Collection.reset().then( () => {
        Collection.page()
      }).then( () => {
        res(true)
      })
    })
  },

  sort: (by) => {
    return new Promise( (res, rej) => {
      collection.sort = by
      Collection.reset().then( () => {
        Collection.page()
      }).then( () => {
        res(true)
      })
    })
  },

  reset: () => {
    return new Promise( (res, rej) => {
      collection.products = [];
      collection.pagination = {
        pages:[]
      }
      window.scrollTo(0, 0)
      res(true)
    })
  },
  
  load:(config) => {

    collection.loading = true;

    return new Promise( (res, rej) => {

      let fetch_config = {}
      Object.keys(collection.settings.remote).forEach(key=>{
        if (key.includes('config_') && !!collection.settings.remote[key])
          fetch_config[key.replace('config_','')] = _n.literal(collection.settings.remote[key], config)
      })
      if(!!fetch_config.headers && typeof fetch_config.headers == 'string')
      fetch_config.headers = JSON.parse(fetch_config.headers)

      fetch(Collection.url(config),fetch_config)
        .then(response=>response.json())
        .then(data=> { 
          _n.trigger(window,'Collection:fetch', true, true, data)
          return Collection.map(data)
        })
        .then(data => {
          // wrap this in a hook for endless
          let loaded = collection.pagination.pages.filter(p=>p.loaded).map(p=>p.number)

          data.products = data.products.filter(p=>!!p).map((p,i)=>{
            p.page = config.page
            p.index = i
            p.position = ((p.page - 1) * collection.settings.limit) + p.index + 1
            return p
          })

          _n.hooks.process('Products:enhance', data.products)
            .then(processedData => {
              // Processed data after all hooks have been executed

              data.products = processedData;

              if(collection.settings.paging == 'scroll'){
                collection.products = (loaded.length && config.page < Math.min(...loaded))?[...data.products,...collection.products]:[...collection.products,...data.products];
              } else {
                collection.products = data.products
              }

              collection.total_products = data.total_products;

              if (window.Routes && window.Routes.url) {
                collection.redirect = window.Routes.url(data.redirect);
              } else {
                collection.redirect = data.redirect;
              }

              _n.trigger(window,'Collection:productData')

              // handle api-defined redirects
              if(!!collection.redirect) window.location = collection.redirect;

              collection.total_products = data.total_products;
              collection.filters = {
                all:(data.filters || collection.settings.filters ||[]).map(set=>{
                  if (!set.options) { return }
                  set.options = set.options.map(option=>{
                    option.active = collection.filters.applied[set.key] && collection.filters.applied[set.key].includes(option.value)
                    return option
                  })
                  return set
                }),
                applied:collection.filters.applied,
                active_set:collection.filters.active_set
              };
              
              _n.trigger(window,'Collection:data')

            })
            .then(()=>{
              Collection.paginate(config.page)
            })
            .then(()=>{
              collection.loading = false;
              _n.trigger(window,'Collection:loaded')
              res(true)
            })
        })
    })

  },
  
  init:() => {

    const params = Object.fromEntries(  
      new URLSearchParams(window.location.search)
    )

    for (key in params) {
      if (key.includes('filter[')) {
        collection.filters.applied[key.split('[')[1].split(']')[0]] = params[key].split(',')
      } else if (key == 'sort') {
        collection.sort = params['sort']
      } else {
        // preserve
        collection.params[key] = params[key]

      }

    }
    
    if(!!window.history.state && !!window.history.state.page) {
      history.scrollRestoration = 'manual';
      Collection.page(window.history.state.page).then(()=>{
        window.scrollTo(0,document.querySelectorAll('[product-grid] article')[window.history.state.index].offsetTop - 100)
        window.history.replaceState({},window.document.title,document.location.pathname)
      })
    } else {
      history.scrollRestoration = 'auto';
      Collection.page(Number(collection.params.page) || 1, !!collection.params.page)
    }

  },

  url: (config={}) => {
    config = {...{sort:collection.sort||'', filters:collection.filters.applied}, ...config}

    return _n.decode(_n.literal(collection.settings.remote_url, config))
  },

  map: (data, map) => {

    return new Promise(res=>{

      map = map || collection.settings.map || false;

      transform = collection.settings.transform || false;
      if (transform) {
        data = transform(data);
      }

      if(!map) res(data);

      res(_n.map(data,map))

    })
    
  },

  paginate: page => {

    return new Promise( (res, rej) => {

      collection.pagination.pages = collection.pagination.pages || []

      if(collection.pagination.pages.length==0){
        
        collection.pagination.total_products = collection.total_products;
        collection.pagination.total_pages = Math.ceil(collection.total_products/collection.settings.limit);
        collection.pagination.pages = [...Array(collection.pagination.total_pages).keys()].map(n=>{
          return {
            index: n,
            number: n+1,
            loaded: false
          }
        })
        
      }
      collection.pagination.current_page = page
      
      collection.pagination.pages.find(p=>p.number==page).loaded = true
      
      res(true)  
    })
    
  }

}

document.addEventListener('DOMContentLoaded', Collection.init)
