_n.hooks.register('Products:enhance', data => {
  return new Promise((resolve, reject) => {
    // Perform hook-specific operations

    if (Shopify.country === 'US') resolve(false);

    if (!window.SSAT46) resolve(false);

    const productIds = data?.map(product => `gid://shopify/Product/${product.id}`);
    const siblingsIds = data?.reduce((acc, product) => {
      if (product?.siblings) {
        acc = acc.concat(
          product.siblings?.map(sibling => (sibling?.id && (sibling?.id !== product.id)) ? `gid://shopify/Product/${sibling.id}` : undefined).filter(Boolean)
        );
      }
      return acc;
    }, []);
    
    const allIds = [...new Set(productIds.concat(siblingsIds))];
    
    if (!allIds || allIds?.length === 0) resolve(false);

    const accessToken = atob(window.SSAT46);
    const url = `https://${Shopify.shop}/api/2024-01/graphql.json`;

    const query = `
      query EnhancedPrice @inContext(country: ${Shopify.country}) {
        nodes(ids: ${JSON.stringify(allIds)}) {
          ... on Product {
            id
            title
            priceRange {
              minVariantPrice {
                amount
                currencyCode
              }
              maxVariantPrice {
                amount
                currencyCode
              }
            }
            compareAtPriceRange {
              minVariantPrice {
                amount
                currencyCode
              }
              maxVariantPrice {
                amount
                currencyCode
              }
            }
          }
        }
      }
    `;
    const body = JSON.stringify({ query });

    fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Storefront-Access-Token': accessToken,
      },
      body,
    })
      .then(response => response.json())
      .then(results => {
        const modifiedData = data.map(product => {
          const match = results.data.nodes.find(node => node?.id?.includes(product.id));

          if (match) {
            product.price = match.priceRange.minVariantPrice.amount * 100;
            product.compare_at_price = match.compareAtPriceRange.minVariantPrice.amount * 100;
          }
          product.siblings?.map(sibling => {
            const match = results.data.nodes.find(node => node?.id?.includes(sibling.id));

            if (match) {
              sibling.price = match.priceRange.minVariantPrice.amount * 100;
              sibling.compare_at_price = match.compareAtPriceRange.minVariantPrice.amount * 100;
            }
            return sibling;
          })
          return product;
        });

        resolve(modifiedData);
      })
      .catch(error => {
        reject(error);
      });
  });

});

_n.hooks.register('Products:enhance', data => {
  return new Promise((resolve, reject) => {
    // Perform hook-specific operations

    if (!window.SSAT46) resolve(false);

    const swatchIds = data?.map(product => product.swatch_image).filter(image => image != undefined)
    const productIds = data?.map(product => `gid://shopify/Product/${product.id}`);

    const accessToken = atob(window.SSAT46);
    const url = `https://${Shopify.shop}/api/2024-01/graphql.json`;

    const query = `
      query GetMediaUrls {
        nodes(ids: ${JSON.stringify(swatchIds)}) {
          ... on MediaImage {
            id
            image {
              url
              altText
            }
          }
        }
      }
    `;
    const body = JSON.stringify({ query });

    fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Storefront-Access-Token': accessToken,
      },
      body,
    })
      .then(response => response.json())
      .then(results => {
        const modifiedData = data.map(product => {
          const match = results.data.nodes.find(node => node?.id?.includes(product.swatch_image));

          if (match) {
            product.swatch_image = match.image.url;
            if (product.siblings?.length > 0) {
              product.siblings[0].swatch_image = match.image.url;
            }
          }
          return product;
        });

        resolve(modifiedData);
      })
      .catch(error => {
        reject(error);
      });
  });

});