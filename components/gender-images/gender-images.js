const Gender = {
	current: 'women',
	init: () => {
		
		if(window.location.pathname.split('/').find(p=>p.includes('mens')))
			Gender.update('men');

    	window.addEventListener('searchspring.motherdenim.domReady', Gender.setCollectionImages)

	},

	setDetailImages: () => {
		const hide = () => { 
			console.log('Setting the detail images')
		
			if (Gender.current == 'men') {
				product.images = all_images.filter(i => !i.alt.toLowerCase().match(/women|woman|https/g))
			}

			if (Gender.current == 'women') {
				product.images = all_images.filter(i => i.alt.toLowerCase().match(/women|woman|https/g))
			}

			Neptune.liquid.load('ProductImages', product.images)
		}
		let all_images = []
		let product = _n.qs('[neptune-product]').product

		document.addEventListener("product:init", () => {
			const current_product = _n.qs('[neptune-product]').product

			if (!!current_product) {
				product = current_product
				all_images = product.images
				hide()
			}
		})

		_n.qs('body').classList.add('data-unisex-active')
	},

	setCollectionImages: () => {

		console.log('setCollectionImages')

		if (Gender.current != 'men') return;

		let collectionData = _n.qs('[data-unisex-products]');
		if (!collectionData) return;

		const data = JSON.parse(collectionData.innerHTML);

		let products = data.products

		products.forEach(product => {

			let wrapper = _n.qs(`[content="${product.title}"]`) ? _n.array(_n.parents(_n.qs(`[content="${product.title}"]`), '.product-item'))[0] : false
			
			if (!wrapper || !product[Gender.current]) return false

			let mainImg = _n.qs('.product-item__image img', wrapper)
			let hoverImg = _n.qsa('.product-item__image--hover img', wrapper).reverse()[0]
			let hoverVid = _n.qs('.product-item__image--hover .product-item__image--video', wrapper)

			if (wrapper) {
				mainImg.src = product[Gender.current][0]
				mainImg.srcset = product[Gender.current][0]
				mainImg.setAttribute('data-src', product[Gender.current][0])
				mainImg.setAttribute('data-srcset', product[Gender.current][0])
			}

			if (hoverVid && Gender.current === 'men') {
				_n.qs('.product-item__image--hover', wrapper).innerHTML = mainImg.outerHTML

				hoverImg = _n.qs('.product-item__image--hover img', wrapper)
			}

			if (hoverImg) {
				if (product[Gender.current][1]) {
					hoverImg.src = product[Gender.current][1] 
					hoverImg.srcset = product[Gender.current][1] 
					hoverImg.setAttribute('data-src', product[Gender.current][1])
					hoverImg.setAttribute('data-srcset', product[Gender.current][1])
					hoverImg.style.visibility = 'visible'

				} else {
					hoverImg.src = product[Gender.current][0] 
					hoverImg.srcset = product[Gender.current][0] 
					hoverImg.setAttribute('data-src', product[Gender.current][0])
					hoverImg.setAttribute('data-srcset', product[Gender.current][0])
				}
			}
		})
		

	},

	setItemImages: (p) => {

		if(p.alt_images == undefined || p.alt_images == '') return;

		if (Gender.current != 'men') return;
	
		var alt_indexes = false;
	
		// additional logic to handle unstringified data (shopify product-item-data.liquid)
		if(typeof p.alt_images == 'object' && p.alt_images.length > 0){
		  alt_indexes = p.alt_images.map(i=>Number(i)-1)
		}
		if(typeof p.alt_images == 'string'){
		  alt_indexes = JSON.parse(p.alt_images).map(i=>Number(i)-2)
		}
	
		if(!alt_indexes) return;
		const [firstImage, secondImage] = alt_indexes.map(i => p.images[i]);
		p.featured_image = firstImage?.url ?? firstImage;
		p.hover_image = secondImage?.url ?? secondImage;
	
		return p
	},

	update: (gender) => {
		Gender.current = gender
		console.log('Updating browsing experience: ' + Gender.current)
	}
}

Gender.init()

window.Gender = Gender

window.addEventListener('Collection:productData', e=>{
	collection.products.map(p=>{
		
		return Gender.setItemImages(p)
		
	})
})

export default Gender