// Constants
const REQUIRED_FIELD_ERROR = window.requiredError || "* Indicates a required field";
const ERROR_CLASS = "error-message";
const REQUIRED_BORDER_COLOR = "#E92A14";

class FormValidator {
  constructor(form) {
    this.form = form;
    this.setupEventListeners();
  }

  setupEventListeners() {
    const submitButton = this.form.querySelector(".form-btn-submit");
    if (submitButton) {
      submitButton.addEventListener("click", (event) => this.validateForm(event));
    }
  }

  validateForm(event) {
    const fields = this.form.querySelectorAll(".field");
    let formValid = true;

    fields.forEach((field) => {
      if (!this.validateField(field)) {
        formValid = false;
      }
    });

    if (!formValid) {
      event.preventDefault();
    }
  }

  validateField(field) {
    const input = field.querySelector("input");
    const label = field.querySelector("label");
    const requiredSpan = label?.querySelector("span.required");

    if (!requiredSpan || !input) return true;

    if (input.value.trim() === "") {
      this.showError(field);
      return false;
    }

    this.clearError(field);
    return true;
  }

  showError(field) {
    let errorMessage = field.querySelector(`.${ERROR_CLASS}`);
    if (!errorMessage) {
      errorMessage = document.createElement("div");
      errorMessage.classList.add(ERROR_CLASS);
      errorMessage.textContent = REQUIRED_FIELD_ERROR;
      field.insertBefore(errorMessage, field.firstChild);
    }
    field.querySelector("input").style.borderColor = REQUIRED_BORDER_COLOR;
  }

  clearError(field) {
    const errorMessage = field.querySelector(`.${ERROR_CLASS}`);
    if (errorMessage) {
      errorMessage.remove();
    }
    field.querySelector("input").style.borderColor = "";
  }
}

class AddressManager {
  constructor(form) {
    this.form = form;
    this.select = form.querySelector(".address-country");
    this.provinceSelectContainer = form.querySelector(".address-province-container");
    this.provinceSelect = form.querySelector(".address-province");
    this.provinceInputContainer = form.querySelector(".address-province-input-container");
    this.provinceInput = form.querySelector(".address-province-input");

    this.initializeCountry();
    this.setupEventListeners();
  }

  initializeCountry() {
    const defaultCountry = this.select.getAttribute("data-default");
    if (defaultCountry) {
      this.select.value = defaultCountry;
    }
    this.updateProvinceDisplay();
    this.select.querySelector('option[value="---"]').disabled = true;
  }

  setupEventListeners() {
    this.select.addEventListener("change", () => this.updateProvinceDisplay());
  }

  updateProvinceDisplay() {
    const selectedOption = this.select.options[this.select.selectedIndex];
    const provincesData = selectedOption.getAttribute("data-provinces");

    this.provinceSelect.innerHTML = "";

    if (provincesData && provincesData !== "[]") {
      this.handleProvinceOptions(JSON.parse(provincesData));
      this.showProvinceSelect();
    } else {
      this.showProvinceInput();
    }
  }

  handleProvinceOptions(provinces) {
    provinces.forEach(([name, value]) => {
      const option = document.createElement("option");
      option.value = value;
      option.textContent = name;
      this.provinceSelect.appendChild(option);
    });

    const defaultProvince = this.provinceSelect.getAttribute("data-default");
    if (defaultProvince) {
      this.provinceSelect.value = defaultProvince;
    }
  }

  showProvinceSelect() {
    this.provinceSelectContainer.style.display = "block";
    this.provinceSelect.disabled = false;

    this.provinceInputContainer.style.display = "none";
    this.provinceInput.disabled = true;
  }

  showProvinceInput() {
    this.provinceSelectContainer.style.display = "none";
    this.provinceSelect.disabled = true;

    this.provinceInputContainer.style.display = "block";
    this.provinceInput.disabled = false;
    this.provinceInput.value = "";
  }
}

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
  // Initialize form validation for all forms
  document.querySelectorAll("form").forEach(form => {
    new FormValidator(form);
  });

  // Initialize address management for forms with country selectors
  document.querySelectorAll("form").forEach(form => {
    if (form.querySelector(".address-country")) {
      new AddressManager(form);
    }
  });
});