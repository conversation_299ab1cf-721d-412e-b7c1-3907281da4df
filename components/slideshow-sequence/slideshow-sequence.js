
const slideShowMultiple = {
  showInfo: wrapper => {
    _n.qs('.slideshow-controls--content', wrapper).classList.add('loading');
      setTimeout(()=>{
        _n.qs('.slideshow-controls--content', wrapper).innerHTML = _n.qs('.swiper-slide-active .block__text').innerHTML
        _n.qs('.slideshow-controls--content', wrapper).classList.remove('loading');
      },500)
  },

  init: () => {
    
    _n.qsa('.multipleSlidesWrapper').forEach( wrapper => {

      const swiper = _n.qs('.swiper-container', wrapper).swiper

      swiper.on('slideChangeTransitionEnd', e => {

        slideShowMultiple.showInfo(wrapper)
      
      })
      swiper.on('afterInit', e => {

        slideShowMultiple.showInfo(wrapper)
      
      })
      
      setTimeout(()=>{slideShowMultiple.showInfo(wrapper)},200)

    })
    

  }

}

window.slideShowMultiple = slideShowMultiple;

export default slideShowMultiple;
