document.addEventListener('cart:updated', function(e){
	Neptune.liquid.load('cart',window.cart)
})
document.addEventListener('cart:error', function(e){
	Neptune.liquid.load('cart',window.cart)
})
document.addEventListener('cart:itemAdded', function(e){
	Neptune.engage.action(_n.qs('html'),{
    'attributes':[{
      'att':'data-active-modal',
      'set':'drawer--cart'
    }]
  })
	_n.qsa('.button--loader.active').forEach(b=>{b.classList.remove('active')})
})