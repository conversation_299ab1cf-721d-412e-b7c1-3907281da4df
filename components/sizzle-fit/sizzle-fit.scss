#SizzleFitModal {
  bottom: auto !important;
  height: 100vh;
  .SizzleFit_box {
    max-width: 90%;
    border-radius: 0;
    top: 50%;
    bottom: inherit;
    transform: translate(-50%, -50%);
    @media(min-width:1040px){
      max-width: 460px;
    }
  }
  .SizzleFit_guts {
    padding: 0 20px 20px;
    text-align: left;
    text-transform: uppercase;
    font-size: 11px;
    @media(min-width:1040px){
      padding: 0 40px 30px;
    }
  }
  .SizzleFit_question {
    border: none;
    padding-top: 0;
    padding-bottom: 0;
    margin-top: 0;
    margin-bottom: 0;
  }
  .SizzleFit_question_prompt {
    font-size: 11px;
    font-family: 'MonumentGrotesk-Bold';
  }
  .SizzleFit_question_fieldset {
    position: relative;
    width: 100%;
    label {
      position: absolute;
      right: 0;
      top: 12px;
      font-size: 8px;
      font-style: italic;
      .SizzleFit_question_text {
        display: none;
      }
      .SizzleFit_requirement_text--required {
        color: #0000FF;
      }
      .SizzleFit_requirement_text--optional {
        color: #666;
      }
    }
  }
  .SizzleFit_cta {
    @apply flex flex-col gap-y-4;

    button {
      width: 100%;
      @apply button button--primary;
    }
  }
  .SizzleFit_header h3 {
    font-family: 'MonumentGrotesk-Bold';
    text-transform: uppercase;
    font-size: 11px;
  }
  .SizzleFit_question select {
    @apply block lg:text-2xs text-base font-body uppercase tracking-wider leading-snug py-2 px-5 border border-dark bg-white w-full rounded-xl cursor-pointer;
    &:focus, &:focus-visible {
      @apply border border-dark;
    }
    &::placeholder {
      @apply text-2xs;
    }
  }
  .SizzleFit_header {
    padding: 1rem 20px;
    position: relative;
    border: none;
    @media(min-width:1040px){
      padding: 1rem 40px;
    }
  }
  .SizzleFit_close {
    @apply absolute;
    top: 3px;
    right: 6px;
    width: 40px;
    height: 40px;
    padding: .5rem;
  }
  .SizzleFit_header .SizzleFit_units {
    @media(min-width:1040px){
      margin-right: 0;
    }
  }
  #SizzleFit_recommendation_statement .SizzleFit_size_badge {
    font-family: 'MonumentGrotesk-Bold';
    text-transform: uppercase;
    font-size: 11px;
  }
  #SizzleFit_demographic_percent {
    display: inline-block;
    margin-bottom: .5rem;
  }
  .SizzleFit_demographics_bar {
    background: #0000FF;
  }

  .SizzleFit_size_scale_indicator {
    width: 20px;
    height: 20px;
    border-radius: 100%;
    top: -10px;
    bottom: inherit;
    background: #0000FF;
    transform: none;
  }

  .SizzleFit_size_scale_bar {
    background: #000;
    height: 1px;
    margin-bottom: 2rem;
  }
  .SizzleFit_size_scale_legend {
    color: #000;
    font-size: 11px;
    margin-bottom: 1.5rem;
  }
  .SizzleFit_header .SizzleFit_units_option {
    text-transform: uppercase;
    color: #000;
    font-size: 11px;
    padding: 10px 20px;
    line-height: 1;
    &.active {
      background-color: #EFEEF0;
    }
  }
  .SizzleFit_quote {
    border: none;
    background: #EFEEF0;
    font-style: normal;
    display: none;
  }
  #SizzleFit_demographic_statement {
    display: none;
  }
  #SizzleFit_recommendation_statement {
    margin-bottom: 3rem;
  }
  #SizzleFit_demographics {
    margin-bottom: 3rem;
  }
  .SizzleFit_question select {
    @media (max-width:1039px){
      font-size: 16px;
    }
  }
  #SizzleFit_results {
    display: none;
  }
}