const globalE = {

  redirect(){
    var country = localStorage.getItem('UserCountry')
    if(country!=Shopify.country && !localStorage.getItem('UserSelectedCountry')){
      console.log('switch to '+country)
      if(!!document.querySelector(`#localization_form a[data-value="${country}"]`)){
        document.querySelector('#localization_form input[name="country_code"]').value = country
        document.querySelector('#localization_form').submit()
      }
    }
  },

  init() {
    if(!localStorage.getItem('UserCountry')){
      fetch('https://get.geojs.io/v1/ip/country.json').then(r=>r.json()).then(d=>{
        localStorage.setItem('UserCountry',d.country)
        globalE.redirect()
      })
    } else {
      globalE.redirect()
    }

    customElements.define('localization-form', LocalizationForm);
    globalE.handleLocalizedContent()
    globalE.addLocalizedRoutes()

    document.addEventListener("template:rendered", function (e) {
      globalE.addLocalizedRoutes()
    })
    
    // window.addEventListener('Collection:loaded', function() {
    //   if (window.Shopify.country == 'US') return false
    //   globalE.addLocalizedRoutes()
    //   if (window.localizedPrices) {
    //     setTimeout(() => {
    //       _n.qsa('.product-item__link').map(function(el) {
    //         return globalE.updateProductItem(el, window.localizedPrices)
    //       })
    //     }, 500)
    //   }
    //   globalE.fetchLocalizedPrices(_n.qsa('.product-item__link:not(.localized-price)'), globalE.updateProductItem)
    //   globalE.fetchLocalizedPrices(_n.qsa('.modal-search-form .ss-auto__products .ss-auto__product:not(.localized-price)'), globalE.updateInstantSearchItem)

    // })
    
  },

  fetchLocalizedPrices(elements, callback) {
    var missedProductItems = elements
    var fetchUrl = window.location.origin + Shopify.routes.root + 'collections/all' + '?view=prices-by-handle&handle=' + missedProductItems.map(
      function(el) {
        let url = el.href ? el.href : el.querySelector('a').href
        let handle = url.split('/').reverse()[0]
        
        if (handle.length) return handle
      }).join()

    if (!missedProductItems.length) return false

    fetch(fetchUrl)
      .then(function(res) { return res.json()})
      .then(function(data) {
        missedProductItems.map(function(el) {
          callback(el, data.products)
        })
      })
  },

  updateProductItem(el, data) {
    var handle = el.href.split('/').reverse()[0]
    var priceData = data[handle]
    var priceTemplate = el.querySelector('.product-item__price [global-selector="prices.item.label"]')
    var compareAtPriceTemplate = el.querySelector('.product-item__price [global-selector="prices.compare_at.label"]')

    if (!priceData) return false
    if (!!priceTemplate) priceTemplate.innerText = priceData.price
    if (!!compareAtPriceTemplate) compareAtPriceTemplate.innerText = priceData.compare_at

    el.classList.add('localized-price')
    
    return true
  },

  updateInstantSearchItem(el, data) {
    var handle = _n.qs('a', el).href.split('/').reverse()[0]
    var priceData = data[handle]
    var priceTemplate = el.querySelector('.ss-auto-p__price')
    var compareAtPriceTemplate = el.querySelector('.ss-auto-p__price del')

    if (!priceData) return false
    if (!!compareAtPriceTemplate) {
      compareAtPriceTemplate.innerText = priceData.compare_at
      return priceTemplate.innerHTML = compareAtPriceTemplate.outerHTML + ' ' + priceData.price
    }
    if (!!priceTemplate) priceTemplate.innerText = priceData.price

    el.classList.add('localized-price')
    priceTemplate.classList.add('global-localized')
    
    return true
  },

  handleLocalizedContent() {
    const country = Shopify.country
    const currency = Shopify.currency.active

    _n.qsa('[data-international]', document, el => { 
      if (el.dataset.international == '') return el.classList.remove('hidden')
      if (el.dataset.international.includes(country) || el.dataset.international.includes(currency)) return el.classList.remove('hidden')
      if (el.hasAttribute('data-international-remove')) {
        return el.remove();
      }
      el.classList.add('hidden')
    })
    
    if (country == 'US') {
      _n.qsa('[data-international]', document, el => el.setAttribute('data-international-hide', ''))
    }else{
      _n.qsa('[data-domestic]:not([data-international])', document, el => el.classList.add('hidden'))

      _n.qsa('[data-country-inclusion][data-international]').forEach((data)=>{
        const countryInclusionAttribute = data.getAttribute('data-country-inclusion');
        const countryCodes = countryInclusionAttribute.split(',').map(code => code.trim());

        if (!countryCodes.includes(country)) {
          data.classList.add('hidden');
        }
      })
    }

    _n.qs('body').classList.remove('hide-localized-content')
  },

  addLocalizedRoutes() {
   _n.qsa('a[href^="/"],a[href*="'+ top.location.host +'"]', document, (el) => {
      if (el.href.includes(Shopify.routes.root)) return false
      el.href = el.href.replace(top.location.host + '/', top.location.host + Shopify.routes.root)
    })
  }
}

// Localization form from shopify
class LocalizationForm extends HTMLElement {
  constructor() {
    super();
    this.elements = {
      input: this.querySelector('input[name="language_code"], input[name="country_code"]'),
      button: this.querySelector('button'),
      panel: this.querySelector('section'),
    };
    this.elements.button.addEventListener('click', this.openSelector.bind(this));
    this.elements.button.addEventListener('focusout', this.closeSelector.bind(this));
    this.addEventListener('keyup', this.onContainerKeyUp.bind(this));

    this.querySelectorAll('a').forEach(item => item.addEventListener('click', this.onItemClick.bind(this)));
  }

  hidePanel() {
    this.elements.button.setAttribute('aria-expanded', 'false');
    this.elements.panel.setAttribute('hidden', true);
  }

  onContainerKeyUp(event) {
    if (event.code.toUpperCase() !== 'ESCAPE') return;

    this.hidePanel();
    this.elements.button.focus();
  }

  onItemClick(event) {
    event.preventDefault();
    const form = this.querySelector('form');
    this.elements.input.value = event.currentTarget.dataset.value;
    window.localStorage.setItem('UserSelectedCountry', this.elements.input.value)
    if (form) form.submit();
  }

  openSelector() {
    this.elements.button.focus();
    this.elements.panel.toggleAttribute('hidden');
    this.elements.button.setAttribute('aria-expanded', (this.elements.button.getAttribute('aria-expanded') === 'false').toString());
  }

  closeSelector(event) {
    const shouldClose = event.relatedTarget && event.relatedTarget.nodeName === 'BUTTON';
    if (event.relatedTarget === null || shouldClose) {
      this.hidePanel();
    }
  }
}

globalE.init()

window.globalE = globalE

export default globalE
