[global-selector="prices.compare_at.label"] {
    text-decoration: line-through;
    color: #999999;
    margin-right: 3px;
}

.hide-localized-content [data-international],
.hide-localized-content [data-domestic] {
  display: none;
}

/* Dropdown Trigger */
.country-picker-modal-trigger,
.country-picker-dropdown-trigger,
.currency-picker-dropdown-trigger {
  border: none;
  border-radius: 0;
  cursor: pointer;
  display: inline-block;
  margin-bottom: 0;
  padding: .75rem 0 .75rem .75rem;
}

// .country-picker-dropdown-trigger::after,
// .currency-picker-dropdown-trigger::after {
//   content: '';
//   border-style: solid;
//   border-color: transparent;
//   border-top-color: #bbb;
//   border-width: 8px 6px 0 6px;
//   display: inline-block;
//   width: 0;
//   height: 0;
//   margin-left: 6px;
// }

.country-picker-modal-trigger > img,
.country-picker-dropdown-trigger > img {
  display: none !important;
}

.country-picker-modal-trigger-text,
.country-picker-dropdown-trigger-text,
.currency-picker-dropdown-trigger-text {
  color: #000000;
  text-transform: uppercase;
}

/* Backdrop that takes over the screen */
.country-picker-dropdown-backdrop {
  @apply bg-black bg-opacity-30;
  height: 100%;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 40;
}

.country-picker-instruction-txt {
  color: black;
  display: block;
  padding: 12px 20px;
  font-size: 20px;
  line-height: 34px;
  border-bottom: 1px solid #fff;
  margin-bottom: 20px;
  text-align: center;
}

/* Dropdown button on hover & focus */
.country-picker-modal-trigger:hover,
.country-picker-modal-trigger:focus,
.country-picker-modal-trigger.country-picker-open,
.country-picker-dropdown-trigger:hover,
.country-picker-dropdown-trigger:focus,
.country-picker-dropdown-trigger.country-picker-open,
.currency-picker-dropdown-trigger:hover,
.currency-picker-dropdown-trigger:focus,
.currency-picker-dropdown-trigger.country-picker-open {
  background-color: #f7f7f7;
}

.country-picker-country-logo {
  display: none;
}

.country-picker-country-logo > img {
  display: none;
}

/* Dropdown Content (Hidden by Default) */
.country-picker-dropdown-menu {
  visibility: hidden;
  opacity: 0;
  border-radius: 0;
  -webkit-transition: visiblity 0.10s, opacity 0.10s linear;
  transition: visiblity 0.10s, opacity 0.10s linear;
  position: fixed;
  background-color: #f7f7f7;
  width: 450px;
  max-width: 100%;
  height: 100vh;
  overy: scroll;
  z-index: 50;
  margin-top: 0px;
  left: 0;
  top: 0;
  .svg-check {
    display: none;
  }
  #country-picker-mobile & {
    position: relative;
    border-left: none;
    border-right: none;
    max-height: 0;
    overflow: hidden;
    width: 100vw;
    &.country-picker-show {
      max-height: 2000px;
    }
  }
}
.country-picker-dropdown-menu a:last-child {
  margin-bottom: 4px;
}

/* Links inside the dropdown */
.country-picker-dropdown-menu a {
  text-decoration: none;
  padding: 0 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-line-pack: center;
      align-content: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}

.country-picker-dropdown-menu a:hover {
  opacity: 1;
  background-color: #f7f7f7;
}

/* The actual text inside the dropdown option */
.country-picker-dropdown-option-text {
  -ms-flex-item-align: center;
  align-self: center;
  color: #000000;
  text-transform: uppercase;
  position: relative;
  display: inline-block;
  width: calc(100% - 33px);
  top: -1px;
  cursor: pointer;
  font-size: 20px;
  line-height: 34px;
}

.country-picker-show {
  visibility: visible;
  opacity: 1;
}

.country-picker-open {
  color: #000000;
}

/* Modal Country Picker */
.country-picker-modal {
  background-color: #f7f7f7;
  border-radius: 0px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  left: 0;
  padding: 0px;
  position: fixed;
  top: 0;
  width: 450px;
  max-width: 100%;
  z-index: 50;
}

/* An item inside the modal */
.country-picker-modal-item-container {
  cursor: pointer;
  display: inline-block;
  font-size: 20px;
  line-height: 34px;
  padding: 0;
  width: 100%;
}
.country-picker-modal-item-container:hover {}

.country-picker-modal-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-line-pack: center;
      align-content: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}

/* The selected item in the modal */
.country-picker-selected-modal-item {
  background-color: #C0DFFF;
  cursor: auto;
  cursor: initial;
}

/* Backdrop that takes over the screen */
.country-picker-modal-backdrop {
  background-color: rgba(0, 0, 0, .30);
  height: 100%;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 40;
}

/* The text within the modal item */
.country-picker-modal-text {
  -ms-flex-item-align: center;
      align-self: center;
  color: #000000;
}

/* Modal item logo */
.country-picker-modal-logo {
  -ms-flex-item-align: center;
      align-self: center;
  border-radius: 2px;
  -webkit-box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .10);
          box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .10);
  flex-shrink: 0;
  height: 15px;
  margin-left: 10px;
  margin-right: 8px;
  width: 21px;
}

/* Modal Header */
.country-picker-modal-header {
  padding: 10px 20px;
  border-bottom: 1px #ffffff solid;
  margin-bottom: 0;
}

.country-picker-modal-title {
  color: #000000;
  display: block;
  text-align: center;
  font-size: 20px;
  line-height: 34px;
}

/* Modal close icon */
.country-picker-modal-close {
  position: absolute;
  cursor: pointer;
  width: 10px;
  height: 10px;
  top: 22px;
  right: 22px;
  scale: 1.5;
  stroke: #000000;
  g {
    stroke-width: 1px;
  }
}
.country-picker-modal-close:hover {
    stroke: #808080;
  }

/* Modal Body */
.country-picker-modal-body {
  max-height: calc(100% - 55px);
  padding: 20px;
  overy: scroll;
}

/* Modal Body Content */
.country-picker-modal-body-content {
  display: flex;
  flex-direction: column;
}

/* Currently selected experience text */
.country-picker-current-experience-txt {
  display: inline-block;
  padding-left: 7px;
  bottom: 6px;
  position: relative;
}

.country-picker-selected-logo {
  display: inline-block;
}

.country-picker-button-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}

.country-picker-button {
  font-size: 1rem;
  display: inline-block;
  min-width: 80px;
  color: #ffffff;
  border: solid 1px #191919;
  border-radius: 4px;
  background-color: #000000;
  cursor: pointer;
  flex-basis: 30%;
  margin-left: 20px;
  padding: 8px;
}

.country-picker-button.country-picker-button-secondary {
  border: solid 1px #191919;
  color: #000000;
  background-color: #ffffff;
}

.country-picker-advanced .country-picker-modal-body-content {
  display: flex;
  justify-content: center;
  padding: 30px 0;
}

.country-picker-advanced .country-picker-wrapper,
.country-picker-advanced .currency-picker-wrapper {
  padding: 20px;
}

/* Some example responsive rules */
@media (max-width: 768px) {
  .country-picker-modal {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100%;
    width: 100%;
    max-height: unset;
  }
  .country-picker-modal-body-content {
    column-count: unset;
  }
}

/* Geolocation Popup
========================================================================== */

.recommendation-modal__container {
  .recommendation-modal__flag {
    display: none;
  }

  .recommendation-modal__message {
    color: rgb(0,0,0) !important;
    font-family: var(--font-highlight-family) !important;
    font-style: var(--font-highlight-style) !important;
    font-weight: var(--font-highlight-weight) !important;
    font-size: 1.25rem !important;
    line-height: 1.38;
    text-align: left !important;
    width: 100%;
    margin-bottom: 15px !important;
    .recommendation-modal__message--bold {
      font-weight: normal !important;
    }
  }
  .recommendation-modal__close-button {
    margin-right: 20px !important;
    margin-top: 20px !important;
  }

  .recommendation-modal__content {
    .recommendation-modal__form {
      button {
        text-transform: uppercase;
        font-size: .688rem !important;
        letter-spacing: .05em;
        padding: .75rem 2rem !important;
        line-height: 1.5 !important;
        border-radius: .375rem !important;
        height: auto !important;
      }
    }
  }
  .recommendation-modal__content {
    padding: 0 2.5rem 2.5rem;
  }
  .recommendation-modal__benefits {
    ul {
      margin: 0 !important;
      padding-left: 0 !important;
      li {
        font-size: 14px !important;
        line-height: 1.4 !important;
        list-style-type: disc;
        margin-bottom: 4px;
        text-align: left;
        list-style-position: inside;
        color: #000 !important;
      }
      .recommendation-modal__button--minimal {
        font-size: 12px !important;
      }
    }
  } 
}

