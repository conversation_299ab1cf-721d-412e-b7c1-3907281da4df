{
  "type": "header",
  "content": "Content Item Settings"
},
{
  "type": "select",
  "id": "content_direction",
  "label": "Direction (Desktop)",
  "options": [
    {
      "value": "lg:flex-row",
      "label": "Row"
    },
    {
      "value": "lg:flex-row-reverse",
      "label": "Row Reverse"
    },
    {
      "value": "lg:flex-col",
      "label": "Column"
    },
    {
      "value": "lg:flex-col-reverse",
      "label": "Column Reverse"
    }
  ],
  "default": "lg:flex-col"
},
{
  "type": "select",
  "id": "content_direction_mobile",
  "label": "Direction (Mobile)",
  "options": [
    {
      "value": "flex-row",
      "label": "Row"
    },
    {
      "value": "flex-row-reverse",
      "label": "Row Reverse"
    },
    {
      "value": "flex-col",
      "label": "Column"
    },
    {
      "value": "flex-col-reverse",
      "label": "Column Reverse"
    }
  ],
  "default": "flex-col"
},
{
  "type": "select",
  "id": "content_gap",
  "label": "Gap (Desktop)",
  "options": [
    {
      "value": "lg:gap-0",
      "label": "None"
    },
    {
      "value": "lg:gap-xs",
      "label": "XS"
    },
    {
      "value": "lg:gap-sm",
      "label": "SM"
    },
    {
      "value": "lg:gap-base",
      "label": "MD / Base"
    },
    {
      "value": "lg:gap-lg",
      "label": "LG"
    },
    {
      "value": "lg:gap-xl",
      "label": "XL"
    },
    {
      "value": "lg:gap-2xl",
      "label": "2XL"
    },
    {
      "value": "lg:gap-3xl",
      "label": "3XL"
    },
    {
      "value": "lg:gap-4xl",
      "label": "4XL"
    },
    {
      "value": "lg:gap-5xl",
      "label": "5XL"
    },
    {
      "value": "lg:gap-6xl",
      "label": "6XL"
    },
    {
      "value": "lg:gap-7xl",
      "label": "7XL"
    }
  ],
  "default": "lg:gap-base"
},
{
  "type": "select",
  "id": "content_gap_mobile",
  "label": "Gap (Mobile)",
  "options": [
    {
      "value": "gap-0",
      "label": "None"
    },
    {
      "value": "gap-xs",
      "label": "XS"
    },
    {
      "value": "gap-sm",
      "label": "SM"
    },
    {
      "value": "gap-base",
      "label": "MD / Base"
    },
    {
      "value": "gap-lg",
      "label": "LG"
    },
    {
      "value": "gap-xl",
      "label": "XL"
    },
    {
      "value": "gap-2xl",
      "label": "2XL"
    }
  ],
  "default": "gap-base"
},
{
  "type": "header",
  "content": "Text Settings"
},
{
  "type": "color",
  "id": "text_color",
  "label": "Text color"
},
{
  "type": "select",
  "id": "text_align",
  "label": "Text Alignment (Desktop)",
  "options": [
    {
      "value": "lg:text-left",
      "label": "Left"
    },
    {
      "value": "lg:text-center",
      "label": "Center"
    },
    {
      "value": "lg:text-right",
      "label": "Right"
    },
    {
      "value": "lg:text-justify lg:text-justify-last",
      "label": "Justify"
    }
  ],
  "default": "lg:text-left"
},
{
  "type": "select",
  "id": "text_align_mobile",
  "label": "Text Alignment (Mobile)",
  "options": [
    {
      "value": "text-left",
      "label": "Left"
    },
    {
      "value": "text-center",
      "label": "Center"
    },
    {
      "value": "text-right",
      "label": "Right"
    },
    {
      "value": "text-justify text-justify-last",
      "label": "Justify"
    }
  ],
  "default": "text-left"
},
{
  "type": "header",
  "content": "Title Image Options"
},
{
  "type": "image_picker",
  "label": "Title Image",
  "id": "title_image"
},
{
  "type": "text",
  "label": "Title SVG",
  "id": "title_svg",
  "info": "Choose an SVG from your [files](/admin/settings/files) and paste the file name in the field above."
},
{
  "type": "text",
  "label": "Title Image/SVG Max width",
  "id": "title_image_width",
  "info": "Use a pixel or percentage value here.",
  "default": "100%"
},
{
  "type": "header",
  "content": "Pretitle Typeface Options"
},
{
  "type": "text",
  "id": "pretitle",
  "label": "Pretitle"
},
{
  "type": "select",
  "id": "pretitle_type",
  "label": "Typeface",
  "options": [
    {
      "value": "MonumentGrotesk-Regular",
      "label": "Monument Grotesk Regular"
    },
    {
      "value": "MonumentGrotesk-Medium ",
      "label": "Monument Grotesk Medium "
    },
    {
      "value": "MonumentGrotesk-Medium-Italic",
      "label": "Monument Grotesk Medium Italic"
    },
    {
      "value": "MonumentGrotesk-Bold",
      "label": "Monument Grotesk Bold"
    },
    {
      "value": "ABC-Monument",
      "label": "ABC Monument"
    },
    {
      "value": "font-pop",
      "label": "ABC Monument Grotesk Black"
    },
    {
      "value": "font-highlight",
      "label": "ABC Monument Grotesk Light"
    },
    {
      "value": "Libre-Caslon",
      "label": "Libre Caslon"
    }
  ],
  "default": "MonumentGrotesk-Regular"
},
{
  "type": "number",
  "id": "pretitle_size",
  "label": "Pretitle Font Size (Desktop)",
  "default": 11
},
{
  "type": "number",
  "id": "pretitle_size_mobile",
  "label": "Pretitle Font Size (Mobile)",
  "default": 11
},
{
  "type": "color",
  "id": "pretitle_color",
  "label": "Pretitle color",
  "default": "#504F4F"
},
{
  "type": "text",
  "id": "pretitle_leading",
  "label": "Preitle Line Height",
  "default": "1.38"
},
{
  "type": "text",
  "id": "pretitle_tracking",
  "label": "Pretitle Letter Spacing"
},
{
  "type": "header",
  "content": "Title Typeface Options"
},
{
  "type": "text",
  "id": "title",
  "label": "Title"
},
{
  "type": "select",
  "id": "title_type",
  "label": "Typeface",
  "options": [
    {
      "value": "MonumentGrotesk-Regular",
      "label": "Monument Grotesk Regular"
    },
    {
      "value": "MonumentGrotesk-Medium ",
      "label": "Monument Grotesk Medium "
    },
    {
      "value": "MonumentGrotesk-Medium-Italic",
      "label": "Monument Grotesk Medium Italic"
    },
    {
      "value": "MonumentGrotesk-Bold",
      "label": "Monument Grotesk Bold"
    },
    {
      "value": "ABC-Monument",
      "label": "ABC Monument"
    },
    {
      "value": "font-pop",
      "label": "ABC Monument Grotesk Black"
    },
    {
      "value": "font-highlight",
      "label": "ABC Monument Grotesk Light"
    },
    {
      "value": "Libre-Caslon",
      "label": "Libre Caslon"
    }
  ],
  "default": "MonumentGrotesk-Bold"
},
{
  "type": "number",
  "id": "title_size",
  "label": "Title Font Size (Desktop)",
  "default": 60
},
{
  "type": "number",
  "id": "title_size_mobile",
  "label": "Title Font Size (Mobile)",
  "default": 50
},
{
  "type": "color",
  "id": "title_color",
  "label": "Title color",
  "default": "#000000"
},
{
  "type": "text",
  "id": "title_leading",
  "label": "Title Line Height",
  "default": "1.38"
},
{
  "type": "text",
  "id": "title_tracking",
  "label": "Title Letter Spacing"
},
{
  "type": "header",
  "content": "Subtitle Typeface Options"
},
{
  "type": "text",
  "id": "subtitle",
  "label": "Subtitle"
},
{
  "type": "select",
  "id": "subtitle_type",
  "label": "Typeface",
  "options": [
    {
      "value": "MonumentGrotesk-Regular",
      "label": "Monument Grotesk Regular"
    },
    {
      "value": "MonumentGrotesk-Medium ",
      "label": "Monument Grotesk Medium "
    },
    {
      "value": "MonumentGrotesk-Medium-Italic",
      "label": "Monument Grotesk Medium Italic"
    },
    {
      "value": "MonumentGrotesk-Bold",
      "label": "Monument Grotesk Bold"
    },
    {
      "value": "ABC-Monument",
      "label": "ABC Monument"
    },
    {
      "value": "font-pop",
      "label": "ABC Monument Grotesk Black"
    },
    {
      "value": "font-highlight",
      "label": "ABC Monument Grotesk Light"
    },
    {
      "value": "Libre-Caslon",
      "label": "Libre Caslon"
    }
  ],
  "default": "MonumentGrotesk-Regular"
},
{
  "type": "number",
  "id": "subtitle_size",
  "label": "Subtitle Font Size (Desktop)",
  "default": 20
},
{
  "type": "number",
  "id": "subtitle_size_mobile",
  "label": "Subtitle Font Size (Mobile)",
  "default": 20
},
{
  "type": "color",
  "id": "subtitle_color",
  "label": "Subtitle color",
  "default": "#000000"
},
{
  "type": "text",
  "id": "subtitle_leading",
  "label": "Subtitle Line Height",
  "default": "1.38"
},
{
  "type": "text",
  "id": "subtitle_tracking",
  "label": "Subtitle Letter Spacing"
},
{
  "type": "header",
  "content": "Featured Content Typeface Options"
},
{
  "type": "richtext",
  "id": "featured_content",
  "label": "Featured Content"
},
{
  "type": "select",
  "id": "featured_content_type",
  "label": "Typeface",
  "options": [
    {
      "value": "MonumentGrotesk-Regular",
      "label": "Monument Grotesk Regular"
    },
    {
      "value": "MonumentGrotesk-Medium ",
      "label": "Monument Grotesk Medium "
    },
    {
      "value": "MonumentGrotesk-Medium-Italic",
      "label": "Monument Grotesk Medium Italic"
    },
    {
      "value": "MonumentGrotesk-Bold",
      "label": "Monument Grotesk Bold"
    },
    {
      "value": "ABC-Monument",
      "label": "ABC Monument"
    },
    {
      "value": "font-pop",
      "label": "ABC Monument Grotesk Black"
    },
    {
      "value": "font-highlight",
      "label": "ABC Monument Grotesk Light"
    },
    {
      "value": "Libre-Caslon",
      "label": "Libre Caslon"
    }
  ],
  "default": "MonumentGrotesk-Regular"
},
{
  "type": "number",
  "id": "featured_content_size",
  "label": "Featured Content Font Size (Desktop)",
  "default": 16
},
{
  "type": "number",
  "id": "featured_content_size_mobile",
  "label": "Featured Content Font Size (Mobile)",
  "default": 16
},
{
  "type": "color",
  "id": "featured_content_color",
  "label": "Featured Content color",
  "default": "#000000"
},
{
  "type": "text",
  "id": "featured_leading",
  "label": "Featured Line Height",
  "default": "1.38"
},
{
  "type": "text",
  "id": "featured_tracking",
  "label": "Featured Letter Spacing"
},
{
  "type": "header",
  "content": "Content Typeface Options"
},
{
  "type": "richtext",
  "id": "text",
  "label": "Content"
},
{
  "type": "header",
  "content": "HTML Options"
},
{
  "type": "html",
  "id": "html",
  "label": "HTML"
},
{
  "type": "header",
  "content": "Link Settings"
},
{
  "type": "select",
  "id": "link_element",
  "label": "Link Element",
  "options": [
    {
      "value": "",
      "label": "Button Only"
    },
    {
      "value": "block",
      "label": "Entire Block"
    }
  ],
  "default": ""
},
{
  "type": "header",
  "content": "Button Alignment Settings"
},
{
  "type": "select",
  "id": "button_horizontal_align",
  "label": "Justify (Desktop)",
  "options": [
    {
      "value": "lg:justify-start",
      "label": "Start"
    },
    {
      "value": "lg:justify-center",
      "label": "Center"
    },
    {
      "value": "lg:justify-end",
      "label": "End"
    },
    {
      "value": "lg:justify-between",
      "label": "Between"
    }
  ],
  "default": "lg:justify-start"
},
{
  "type": "select",
  "id": "button_horizontal_align_mobile",
  "label": "Justify (Mobile)",
  "options": [
    {
      "value": "justify-start",
      "label": "Start"
    },
    {
      "value": "justify-center",
      "label": "Center"
    },
    {
      "value": "justify-end",
      "label": "End"
    },
    {
      "value": "justify-between",
      "label": "Between"
    }
  ],
  "default": "justify-start"
},
{
  "type": "select",
  "id": "button_vertical_align",
  "label": "Self Align (Desktop)",
  "options": [
    {
      "value": "lg:self-start",
      "label": "Start"
    },
    {
      "value": "lg:self-center",
      "label": "Center"
    },
    {
      "value": "lg:self-end",
      "label": "End"
    }
  ],
  "default": "lg:self-center"
},
{
  "type": "select",
  "id": "button_vertical_align_mobile",
  "label": "Self Align (Mobile)",
  "options": [
    {
      "value": "self-start",
      "label": "Start"
    },
    {
      "value": "self-center",
      "label": "Center"
    },
    {
      "value": "self-end",
      "label": "End"
    }
  ],
  "default": "self-center"
},
{
  "type": "header",
  "content": "Button 1 Layout Settings"
},
{
  "type": "text",
  "id": "link_title",
  "label": "Link title"
},
{
  "type": "url",
  "id": "url",
  "label": "Link"
},
{
  "type": "select",
  "id": "link_target",
  "label": "Link Target",
  "options": [
    {
      "value": "_blank",
      "label": "New Window"
    },
    {
      "value": "_self",
      "label": "Same Window"
    }
  ],
  "default": "_self"
},
{
  "type": "select",
  "id": "button_style",
  "label": "Button Style",
  "options": [
    {
      "value": "button button--primary",
      "label": "Button Primary"
    },
    {
      "value": "button button--secondary",
      "label": "Button Secondary"
    },
    {
      "value": "button button--tertiary",
      "label": "Button Tertiary"
    },
    {
      "value": "button button--light",
      "label": "Button Light"
    },
    {
      "value": "button button--higlight",
      "label": "Button Highlight"
    },
    {
      "value": "button button--link",
      "label": "Button Text"
    },
    {
      "value": "button button--text",
      "label": "Text link"
    }
  ],
  "default": "button button--primary"
},
{
  "type": "color",
  "id": "btn_color",
  "label": "Button bg color",
  "default": "#f7f7f7"
},
{
  "type": "color",
  "id": "btn_text_color",
  "label": "Button text color",
  "default": "#222222"
},
{
  "type": "color",
  "id": "btn_border_color",
  "label": "Button border color",
  "default": "#222222"
},
{
  "type": "number",
  "id": "button_size",
  "label": "Button Font Size",
  "info": "Optional"
},
{
  "type": "number",
  "id": "button_size_mobile",
  "label": "Button Font Size (Mobile)",
  "info": "Optional"
},
{
  "type": "header",
  "content": "Button 2 Layout Settings"
},
{
  "type": "text",
  "id": "link_title_two",
  "label": "Link title"
},
{
  "type": "url",
  "id": "url_two",
  "label": "Link"
},
{
  "type": "select",
  "id": "link_target_two",
  "label": "Link Target",
  "options": [
    {
      "value": "_blank",
      "label": "New Window"
    },
    {
      "value": "_self",
      "label": "Same Window"
    }
  ],
  "default": "_self"
},
{
  "type": "select",
  "id": "button_style_two",
  "label": "Button Style",
  "options": [
    {
      "value": "button button--primary",
      "label": "Button Primary"
    },
    {
      "value": "button button--secondary",
      "label": "Button Secondary"
    },
    {
      "value": "button button--tertiary",
      "label": "Button Tertiary"
    },
    {
      "value": "button button--light",
      "label": "Button Light"
    },
    {
      "value": "button button--higlight",
      "label": "Button Highlight"
    },
    {
      "value": "button button--link",
      "label": "Button Text"
    },
    {
      "value": "button button--text",
      "label": "Text link"
    }
  ],
  "default": "button button--primary"
},
{
  "type": "color",
  "id": "btn_color_two",
  "label": "Button bg color",
  "default": "#f7f7f7"
},
{
  "type": "color",
  "id": "btn_text_color_two",
  "label": "Button text color",
  "default": "#000000"
},
{
  "type": "color",
  "id": "btn_border_color_two",
  "label": "Button border color",
  "default": "#000000"
},
{
  "type": "number",
  "id": "button_size_two",
  "label": "Button Font Size",
  "info": "Optional"
},
{
  "type": "number",
  "id": "button_size_mobile_two",
  "label": "Button Font Size (Mobile)",
  "info": "Optional"
},
{
  "type": "header",
  "content": "Content Layout Settings"
},
{
  "type": "header",
  "content": "Desktop Content Layout Settings"
},
{
  "type": "select",
  "id": "content_width",
  "label": "Content Width",
  "options": [
    {
      "value": "lg:w-full",
      "label": "100%",
      "group": "Percentage"
    },
    {
      "value": "lg:w-3/4",
      "label": "75%",
      "group": "Percentage"
    },
    {
      "value": "lg:w-3/5",
      "label": "60%",
      "group": "Percentage"
    },
    {
      "value": "lg:w-1/2",
      "label": "50%",
      "group": "Percentage"
    },
    {
      "value": "lg:w-1/4",
      "label": "25%",
      "group": "Percentage"
    },
    {
      "value": "lg:max-w-xl",
      "label": "XS",
      "group": "Fixed"
    },
    {
      "value": "lg:max-w-2xl",
      "label": "SM",
      "group": "Fixed"
    },
    {
      "value": "lg:max-w-3xl",
      "label": "MD",
      "group": "Fixed"
    },
    {
      "value": "lg:max-w-4xl",
      "label": "LG",
      "group": "Fixed"
    },
    {
      "value": "lg:max-w-5xl",
      "label": "XL",
      "group": "Fixed"
    },
    {
      "value": "lg:max-w-6xl",
      "label": "2XL",
      "group": "Fixed"
    }
  ],
  "default": "lg:w-full"
},
{
  "type": "select",
  "id": "content_position",
  "label": "Content Position",
  "options": [
    {
      "value": "lg:items-start lg:justify-start",
      "label": "Top Left"
    },
    {
      "value": "lg:items-center lg:justify-start",
      "label": "Top Center"
    },
    {
      "value": "lg:items-end lg:justify-start",
      "label": "Top Right"
    },
    {
      "value": "lg:items-start lg:justify-center",
      "label": "Middle Left"
    },
    {
      "value": "lg:items-center lg:justify-center",
      "label": "Middle Center"
    },
    {
      "value": "lg:items-end lg:justify-center",
      "label": "Middle Right"
    },
    {
      "value": "lg:items-start lg:justify-end",
      "label": "Bottom Left"
    },
    {
      "value": "lg:items-center lg:justify-end",
      "label": "Bottom Center"
    },
    {
      "value": "lg:items-end lg:justify-end",
      "label": "Bottom Right"
    }
  ],
  "default": "lg:items-center lg:justify-center"
},
{
  "type": "select",
  "id": "content_vertical_padding",
  "label": "Content Vertical Padding",
  "options": [
    {
      "value": "lg:py-0",
      "label": "None"
    },
    {
      "value": "lg:py-1",
      "label": ".25rem"
    },
    {
      "value": "lg:py-2",
      "label": ".5rem"
    },
    {
      "value": "lg:py-4",
      "label": "1rem"
    },
    {
      "value": "lg:py-8",
      "label": "2rem"
    },
    {
      "value": "lg:py-16",
      "label": "4rem"
    },
    {
      "value": "lg:py-32",
      "label": "8rem"
    },
    {
      "value": "lg:py-64",
      "label": "16rem"
    }
  ],
  "default": "lg:py-0"
},
{
  "type": "select",
  "id": "content_horizontal_padding",
  "label": "Content Horizontal Padding",
  "options": [
    {
      "value": "lg:px-0",
      "label": "None"
    },
    {
      "value": "lg:px-1",
      "label": ".25rem"
    },
    {
      "value": "lg:px-2",
      "label": ".5rem"
    },
    {
      "value": "lg:px-4",
      "label": "1rem"
    },
    {
      "value": "lg:px-8",
      "label": "2rem"
    },
    {
      "value": "lg:px-16",
      "label": "4rem"
    },
    {
      "value": "lg:px-32",
      "label": "8rem"
    },
    {
      "value": "lg:px-64",
      "label": "16rem"
    }
  ],
  "default": "lg:px-0"
},
{
  "type": "header",
  "content": "Mobile Content Layout Settings"
},
{
  "type": "select",
  "id": "content_position_mobile",
  "label": "Content Position",
  "options": [
    {
      "value": "h-full",
      "label": "None"
    },
    {
      "value": "items-center justify-start",
      "label": "Top"
    },
    {
      "value": "items-center justify-center",
      "label": "Middle"
    },
    {
      "value": "items-center justify-end",
      "label": "Bottom"
    }
  ],
  "default": "items-center justify-start"
},
{
  "type": "select",
  "id": "content_vertical_padding_mobile",
  "label": "Content Vertical Padding",
  "options": [
    {
      "value": "py-0",
      "label": "None"
    },
    {
      "value": "py-1",
      "label": ".25rem"
    },
    {
      "value": "py-2",
      "label": ".5rem"
    },
    {
      "value": "py-4",
      "label": "1rem"
    },
    {
      "value": "py-8",
      "label": "2rem"
    },
    {
      "value": "py-16",
      "label": "4rem"
    },
    {
      "value": "py-32",
      "label": "8rem"
    },
    {
      "value": "py-64",
      "label": "16rem"
    }
  ],
  "default": "py-0"
},
{
  "type": "select",
  "id": "content_horizontal_padding_mobile",
  "label": "Content Horizontal Padding",
  "options": [
    {
      "value": "px-0",
      "label": "None"
    },
    {
      "value": "px-1",
      "label": ".25rem"
    },
    {
      "value": "px-2",
      "label": ".5rem"
    },
    {
      "value": "px-4",
      "label": "1rem"
    },
    {
      "value": "px-8",
      "label": "2rem"
    },
    {
      "value": "px-16",
      "label": "4rem"
    },
    {
      "value": "px-32",
      "label": "8rem"
    },
    {
      "value": "px-64",
      "label": "16rem"
    }
  ],
  "default": "px-0"
},
{
  "type": "header",
  "content": "Desktop Text Format Settings"
},
{
  "type": "select",
  "id": "title_element",
  "label": "Heading Type",
  "options": [
    {
      "value": "h1",
      "label": "H1"
    },
    {
      "value": "h2",
      "label": "H2"
    },
    {
      "value": "h3",
      "label": "H3"
    },
    {
      "value": "h4",
      "label": "H4"
    },
    {
      "value": "h5",
      "label": "H5"
    }
  ],
  "default": "h2"
}