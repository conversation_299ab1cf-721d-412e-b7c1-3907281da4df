.cart-offers {

  &__carousel {
    @apply bg-white border-white py-0;

    .accordion-panel {
      @apply relative;
    }

    .accordion-panel > div.swiper-container {
      @apply px-2 md:px-0 mx-0 md:mx-5;
    }

    .swiper-prev,
    .swiper-next {
      top: calc(50% - 0.5rem - 20px);
      @apply absolute w-4 h-4 p-0 z-10 hidden lg:block;
    }

    .swiper-prev {
      @apply left-1;
    }

    .swiper-next {
      @apply right-1;
    }

    .cart-offers__prev-next-container, 
    .accordion-panel > .cart-offers__prev-next-container {
      @apply p-0;
    }

  }

  &__prompt,
  &__prompt.accordion-title {
    @apply p-2 px-2 md:px-5 mb-0 flex w-full justify-between;
    & > span {
      @apply text-sm lg:text-base;
      text-transform: initial;
    }
  }
}

.cart-offers .offer-item {
  @apply flex gap-2 relative;

  &__product-link {
    @apply absolute w-full h-full block z-10;
  }

  &__details {
    width: 60%;
    @apply flex flex-col justify-between md:justify-start pr-4;
  }

  &__image-outer {
    width: 40%;
  }

  &__image {
    @apply p-0;
  }

  &__action {
    @apply flex gap-0 relative z-20;
  }

  &__meta {}

  &__meta,
  &__action {
    @apply px-0;
  }

  &__action .quick-add__button {
    @apply w-full md:w-auto;
  }

  &__check {
    @apply hidden;
  }

  &__title {
    @apply mb-0 text-base;
    -webkit-line-clamp: 2;
  }

  &__type {
    @apply mb-0 text-[12px];
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  &__price {
    @apply text-base;
  }

  .button {
    line-height: 1;
    padding-top: 5px;
    padding-bottom: 5px;
  }

}
