const CartOffers = {

  bypass: [],

  init: () => {

    if (!Cart.offers) return;
    const offer_tags = Cart.offers.map(o => o.tag_requirement).filter(t => Neptune.products[0] && Neptune.products[0].product.tags.includes(t))
    if (offer_tags.length) {
      Neptune.products[0].el.properties._offer_tags = offer_tags;
    }

  },

  fetchProducts: async (source) => {
    try {
      const response = await fetch(source.url);
      let data = await response.json();
  
      if (source.transform) {
        data = source.transform(data)
      }

      data = _n.map(data, source.map);

      if (source.enhance) {
        data =  await _n.hooks.process('Products:enhance', data)
      }

      return data
    } catch (error) {
      console.error('Error fetching products:', error);
      throw error;
    }
  },

  run: () => {

    CartOffers.init()

    try {
      CartOffers.bypass = sessionStorage.getItem('BypassCartOffers').split(',')
    } catch (err) { console.warn(err) }

    return new Promise(async (resolve, reject) => {

      window.cart.offers = {}

      window.cart.offers.eligible = window.cart.offers.eligible || [];

      window.cart.offers.all = Cart.blocks

      Cart.offers = await Promise.all(Cart.blocks

        .filter(b => b.type === 'offer')

        .filter(b => !CartOffers.bypass.includes(b.settings.title.handle()))

        .map(async g => {
          if (!!g.source?.url) {
            g.products = await CartOffers.fetchProducts(g.source);
          }

          g.settings.products = g.products;

          return g.settings;
        })
      )

      Cart.offers.map(async g => {
        g.threshold_cents = parseInt(g.threshold_cents) || 0
        g.threshold_quantity = parseInt(g.threshold_quantity) || 0

        g.products = g.products || []
        if (!!g.metafield && !!cart.items[0]) {
          if (!g.source_product || (g.source_product && !cart.items.find(i => i.handle == g.source_product))) await fetch(`/products/${cart.items[0].handle}?view=metafields&field=${g.metafield}`)
            .then(r => r.json())
            .then((d) => {
              if (!d.metafield) return false
              g.metafield_products = d.metafield
              g.source_product = cart.items[0].handle
            })

          if (g.metafield_products && g.metafield_products.length) g.products = g.metafield_products.filter(p => !cart.items.find(i => i.product_id == p.id))
        }

        g.products.map(p => {
          p.cart_item = cart.items.find(i => i.product_id == p.id && !!i.properties._offer)
          p.in_cart = !!p.cart_item
        })
        g.all_in_cart = g.products.every(p => p.in_cart)
        g.some_in_cart = g.products.some(p => p.in_cart)

        g.eligible = (
          _n.sum(cart.items.filter(i => (!i.properties || !i.properties._offer) && (g.tag_requirement == '' || i.properties._offer_tags?.includes(g.tag_requirement))).map(i => i.final_price * i.quantity)) >= g.threshold_cents &&
          _n.sum(cart.items.filter(i => (!i.properties || !i.properties._offer) && (g.tag_requirement == '' || i.properties._offer_tags?.includes(g.tag_requirement))).map(i => i.quantity)) >= g.threshold_quantity
        );

        return g;

      })

      Cart.offers.sort((a, b) => (a.threshold_cents > b.threshold_cents) ? 1 : ((b.threshold_cents > a.threshold_cents) ? -1 : 0))

      CartOffers.init()

      // add single-variant auto-gift offers to the cart
      Neptune.cart.add(
        _n.flatten(
          Cart.offers
            .filter(g => {
              return g.automatic && g.eligible && Cart.offers.filter(o => {
                return o.eligible && o.threshold_cents > g.threshold_cents && !o.combineable
              }).length == 0;
            })
            .map(offer => {
              return offer.products
                .filter(p => !p.in_cart && p.variants.length === 1)
                .map(p => {
                  return { id: p.variants[0].id, quantity: 1, properties: { _offer: offer.title } }
                })
            })
        )
      )

      // window.cart.offers.all = Cart.offers

      window.cart.offers.eligible =

        Cart.offers

          .filter(g => {

            return g.eligible && Cart.offers.filter(o => {
              return o.eligible && o.threshold_cents > g.threshold_cents && !o.combineable
            }).length == 0;

          })

      resolve(window.cart.offers.eligible)

    });

  },

  select: (offerIndex, productIndex, select) => {
    cart.offers.eligible[offerIndex].products[productIndex].selected = select
    if (select) {
      _n.qs('#addOffer').classList.remove('pointer-events-none', 'opacity-50')
    }
  },

  complete: offer => {
    if (!CartOffers.bypass.includes(offer.handle())) {
      CartOffers.bypass.push(offer.handle())
      sessionStorage.setItem('BypassCartOffers', CartOffers.bypass.join(','))
      CartOffers.run().then(() => {
        _n.trigger(document, 'cart:offerComplete', false, false, offer)
        Neptune.liquid.load('Cart', window.cart)
      })
    }
  }
}

window.CartOffers = CartOffers;

export default CartOffers;
