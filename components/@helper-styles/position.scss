/*
	Position Settings
  ==========================================================================
*/
.absolute,
.fixed,
.sticky {
  &.origin {
    --offset-unit: 1%;
    --offset-value-x: var(--offset-x);
    --offset-value-y: var(--offset-y);

    @media (min-width: 1024px) {
      --offset-value-x: var(--offset-x--desktop);
      --offset-value-y: var(--offset-y--desktop);
    }

    &-top {
      bottom: auto;
      top: calc(var(--offset-value-y) * var(--offset-unit));
      --translate-y: 0%;
    }
    &-bottom {
      top: auto;
      bottom: calc(var(--offset-value-y) * var(--offset-unit));
      --translate-y: 0%;
    }
    &-left {
      right: auto;
      --translate-x: 0%;
      left: calc(var(--offset-value-x) * var(--offset-unit));
    }
    &-right {
      left: auto;
      --translate-x: 0%;
      right: calc(var(--offset-value-x) * var(--offset-unit));
    }
    &-center {
      --translate-x: -50%;
      --translate-y: -50%;
      top: calc(calc(var(--offset-value-y) * var(--offset-unit)) + 50%);
      left: calc(calc(var(--offset-value-x) * var(--offset-unit)) + 50%);
    }
    translate: var(--translate-x) var(--translate-y);
  }
}