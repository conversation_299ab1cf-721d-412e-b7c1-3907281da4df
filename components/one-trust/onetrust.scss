#onetrust-consent-sdk #onetrust-banner-sdk {
  background-color: #06060682 !important;
  bottom: 19px !important;
  max-width: 560px !important;
  padding: 0 !important;
  right: inherit !important;
  @media (min-width: 768px) {
    border-radius: 10px !important;
    transform: translateX(-50%);
    left: 50% !important;
  }
  @media (max-width: 768px) {
    left: 0;
    bottom: 0 !important;
    max-width: 100% !important;
    width: 100%;
  }
}

#onetrust-banner-sdk:not(.ot-iab-2) #onetrust-group-container {
  width: 100% !important;
}

#onetrust-banner-sdk #onetrust-policy {
  margin: 0 !important;
  font-size: 12px !important;
}

#onetrust-consent-sdk #onetrust-policy-text {
  color: white !important;
  font-family: var(--font-highlight-family) !important;
  font-size: 12px !important;
  text-align: center !important;
  a {
    color: white !important;
  }
}

#onetrust-consent-sdk #onetrust-accept-btn-handler {
  background-color: rgba(255, 255, 255, 0.8) !important;
  border-radius: 5px !important;
  font-size: 11px !important;
  text-transform: uppercase !important;
  color: #000 !important;
  max-width: 160px !important;
  margin-right: auto !important;
  margin-left: auto !important;
  margin-bottom: 0;
  text-transform: uppercase !important;
}

#onetrust-consent-sdk #onetrust-pc-btn-handler{
  background-color: transparent !important;
  border: 0 !important;
  width: fit-content;
  color: #fff !important;
  margin: 0 auto;
  padding: 12px 10px;
  order: 2;
}

#onetrust-consent-sdk {
  #onetrust-close-btn-container {
    filter: invert(1) contrast(3);
  }
}

#onetrust-banner-sdk .onetrust-close-btn-ui.onetrust-lg {
  display: none !important;
}

#onetrust-banner-sdk #onetrust-reject-all-handler {
  background-color: transparent !important;
  border-radius: 0 !important;
  background-color: #f7f7f7 !important;
  margin-right: 0 !important;
  color: #000 !important;
  text-transform: uppercase;
  font-size: 10px !important;
}


#onetrust-banner-sdk:not(.ot-iab-2) #onetrust-button-group-parent {
  margin: auto !important;
  width: 100% !important;
  position: static !important;
  transform: translateY(0) !important;
  padding-bottom: 15px;
}

#onetrust-banner-sdk #onetrust-button-group {
  display: flex !important;
  flex-direction: column !important;
}


button#onetrust-accept-btn-handler {
  order: 1;
}

#onetrust-banner-sdk .ot-sdk-container, #onetrust-pc-sdk .ot-sdk-container, #ot-sdk-cookie-policy .ot-sdk-container {
  padding: 0 !important;
}

#onetrust-banner-sdk #onetrust-policy-title {
  display: none !important;
}

#onetrust-banner-sdk #onetrust-close-btn-container .banner-close-button {
  filter: brightness(400%) contrast(200%);
}