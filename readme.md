# Neptune Theme 2.0
# Overview

The **Neptune 2.0 Theme** is a combination of a base theme (forked from Shopify's Dawn theme) and development environment for Shopify.

The idea behind it is to create functionality for Shopify stores in a component structure so that it could easily be transferred across projects and built upon.

Eventually our goal is to have these components in a component library to pull from.

---

# <PERSON><PERSON> used

## Yarn

Used for our node package manager

[1 - Introduction](https://yarnpkg.com/getting-started)

## Shopify Cli

Used for authentication with Shopify stores, creating development themes, and a development server.

[Shopify CLI for themes](https://shopify.dev/themes/tools/cli)

## Webpack

Bundles our javascript and styles

[Concepts | webpack](https://webpack.js.org/concepts/)

## Neptune - (Git Submodule)

SLTWTR's in-house utility javascript framework

[Atlassian](https://bitbucket.org/sltwtr/neptune/src/master/)

## TailwindCSS

Utility first css framework

[Documentation - Tailwind CSS](https://tailwindcss.com/docs)

---

# Installation

1. **Clone the repo** `git clone REPOSITORY_URL`
2. **Install Shopify CLI** [https://shopify.dev/themes/tools/cli#installation](https://shopify.dev/themes/tools/cli#installation)
3. **Use Node V 12 or greater** `nvm use stable`
4. **Install Node modules** `yarn`
5. **Install Git Submodule** `git submodule update --init`
6. **Update Git Submodule** `git submodule update --recursive --remote`

---

# Development

Login to your Shopify store `shopify login --store=STORENAME.myshopify.com`

Watching for changes in the components Directory `yarn watch` in one terminal

Serving the theme files `yarn serve` in another terminal

Theme serving as your dist folder

---

# Components

```bash
Neptune-theme 2.0
└── components
    ├── main.css
    ├── main.js
    └── slider-cart
        ├── sections
        │   ├── cart-modal.js
        │   └── cart-modal.liquid
        ├── snippets
        │   └── cart-item.liquid
        └── index.js
```

## components/main.js

This is the global javascript file that gets loaded on every page. Any global functionality can be added here (i.e. All the initialization of Neptune utilities).

## components/main.css

This is where the initial tailwind and global styles are added.

## components/globals.scss

This is where any base styling can be added with tailwind classes.

## components/**/

The components are structured as a folder named whatever the component is `components/slider-cart/`. Our naming convention is the component name lowercase and separated by "-".

### liquid files

For liquid files you can place them in subfolders for snippets and sections like `components/slider-cart/sections/cart-modal.liquid` or `components/slider-cart/snippets/cart-item.liquid`. These liquid files will get moved into the correct folders within the theme directory during development or a build `theme/sections/cart-modal.liquid` or `theme/snippets/cart-item.liquid`.

If you're in development and try to edit one of these directly in the theme directory there will be a comment warning you that there's a corresponding component to edit that file. 

### Javascript Assets

There are a couple ways to go about adding JS into components. 

1. **Adding the script tag directly to a liquid file**
Any javascript within a component named `index.js` will be processed as a separate entry point in Webpack and compiled into the assets folder under the name of the component `slider-cart.js`. 

To add it to a section you would include it in your liquid file using the `asset_url` liquid filter like `<script src="{{ 'slider-cart.js' | asset_url }}" defer></script>`

2. **Adding JS to the global `/components/main.js` file**
If the Javascript for your component is better suited in the global javascript just name it something pertaining `/components/slider-cart/slider-cart.js` to the component and import it into the `main.js` file. Webpack will process it with the main entry point.

---

# Theme

The theme directory is the folder structure for the Shopify theme. Any updates that don't need to be components can be made here.

 

```bash
Neptune-theme 2.0
└── theme
    ├── assets
    ├── config
    ├── layout
    ├── locales
    ├── sections
    ├── snippets
    └── templates
```

---

# Branch Management

## production 

Current state of the live production environment. Requires pull request from staging.

## staging

The main branch that every feature is branched off of. The only branch that should be merged into production. Requires pull request from feature branches. All sprint work will be merged into this branch for final review by SLTWTR and client.

## feature-qa

Development branch used solely for deploying to a QA environment. Feauture QA should never be merged into staging or production. This branch is for testing purposes.

## feature/`[trello card ID]`

Feature branch should always be branched off of staging. Pull requests should always be for merging into the staging branch. Feature branches can also be merged into the feature-qa branch, whenever a feature is ready to be reviewed or tested.

