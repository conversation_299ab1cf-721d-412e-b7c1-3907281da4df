const defaultConfig = require('./neptune/shopify-themes/tailwind.config.js')

// This is your opportunity to override the default Neptune configuration

defaultConfig.theme.extend.fontSize = {
  '3xs': '0.5rem',
  '2xs': '0.688rem',
  'xs': '0.75rem',
  'sm': '0.875rem',
  'base': '1rem',
  'lg': '1.125rem',
  'xl': '1.25rem',
  '2xl': '1.312rem',
  '3xl': '1.5rem'
};

defaultConfig.theme.extend.dropShadow = {
  ...defaultConfig.theme.extend.dropShadow,
  "up": "0 -10px 10px rgb(0 0 0 / 0.04)",
  "down": "0 10px 10px rgb(0 0 0 / 0.04)"
};

defaultConfig.theme.extend.colors = {
  ...defaultConfig.theme.extend.colors,
  "custom-1": "var(--color-custom-1)",
  "custom-2": "var(--color-custom-2)",
  "custom-3": "var(--color-custom-3)",
  "custom-4": "var(--color-custom-4)",
  "custom-5": "var(--color-custom-5)",
  "custom-6": "var(--color-custom-6)",
  "gray-web": "var(--color-gray-1)",
  "gray-cool": "var(--color-gray-2)",
  "gray-dark": "var(--color-gray-3)",
  gray: {
    DEFAULT: "var(--color-gray)",
    light: "var(--color-gray-light)",
    dark: "var(--color-gray-dark)",
    strike: "var(--color-strike)",
  },
  red: {
    main: "#e20000"
  },
  "near-white": "var(--color-near-white)",
  "near-black": "var(--color-near-black)",
};

defaultConfig.theme.extend.fontFamily = {
  ...defaultConfig.theme.extend.fontFamily,
  alt: "var(--font-alt-family)",
  pop: "var(--font-pop-family)",
  highlight: "var(--font-highlight-family)",
};

defaultConfig.theme.extend.height = {
  ...defaultConfig.theme.extend.height,
  "main-80": "calc(80vh - var(--header-height))",
  "item-fix": "calc(100% - 1px)", 
  "panel": "calc(100vh - var(--panel-height))",
  "inner-panel": "calc(100vh - var(--inner-panel-height))",
};

defaultConfig.theme.extend.maxHeight = { 
  ...defaultConfig.theme.extend.maxHeight,
  "20v": "20vh",
  "30v": "30vh",
  "40v": "40vh",
  "50v": "50vh",
  "60v": "60vh",
  "70v": "70vh",
  "main": "calc(100vh - var(--header-height))",
  "main-sm": "calc(100vh - var(--header-height-mobile))",
};

defaultConfig.theme.extend.minHeight = { 
  ...defaultConfig.theme.extend.minHeight,
  "main": "calc(100vh - var(--header-height))",
  "content": "calc(100vh - var(--header-height) - var(--bottom-buffer))",
  "md": "36.875em",
};

defaultConfig.theme.extend.minWidth = { 
  ...defaultConfig.theme.extend.minWidth,
  "6": "1.5rem",
  "8": "2rem",
};

defaultConfig.theme.extend.maxWidth = {
  ...defaultConfig.theme.extend.maxWidth,
  "100vw": "100vw",
};

defaultConfig.theme.extend.spacing = {
  ...defaultConfig.theme.extend.spacing,
  "main": 'var(--header-height)',
  "panel": 'var(--panel-height)',
  "main-sm": 'var(--header-height-mobile)',
  "dynamic-main": 'var(--dynamic-header-height)',
  "15": ".15rem",
  "-translate-x-capsule": "translateX(-12%)",
};

defaultConfig.theme.extend.borderStyle = {
  ...defaultConfig.theme.extend.borderStyle,
  inset: 'inset',
};

defaultConfig.theme.extend.translate = {
  ...defaultConfig.theme.extend.translate,
  form: '-6px',
};

module.exports = defaultConfig
