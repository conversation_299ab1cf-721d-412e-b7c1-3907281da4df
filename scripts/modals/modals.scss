[data-active-modal], [data-active-modal-mobile], .modal-open, .no-scroll {
  height:100vh;
  overflow:hidden;
}

.modal-overlay {
  pointer-events:none;
  visibility:hidden;
  opacity:0;
  [data-active-modal] & {
    pointer-events:all;
    visibility:visible;
    opacity:1;
  }
}

.modal-overlay-insert {
  pointer-events:none;
  visibility:hidden;
  opacity:0;
  [data-active-modal-mobile] & {
    pointer-events:all;
    visibility:visible;
    opacity:1;
  }
}

.modal-left {
  transform:translateX(-100%);
  &.active {
    visibility:visible;
    transform:translateX(0%);
  }
  &.active-l {
  }
}

.modal-right {
  transform:translateX(100%);
  &.active {
    visibility:visible;
    transform:translateX(0%);
  }
}

.modal-top {
  transform:translateY(-100%);
  &.active {
    visibility:visible;
    transform:translateY(0%);
  }
}

.modal-center {
  opacity:0;
  transform-origin: center center;
  transform:scale(0.9) translate(-50%, -50%);
  pointer-events: none;
  &.active {
    transform-origin: center center;
    pointer-events: all;
    visibility:visible;
    transform:scale(1.0) translate(-50%, -50%);
    opacity:1;
  }
}

.modal-overlay-mega {
  pointer-events:none;
  visibility:hidden;
  opacity:0;
}
