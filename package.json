{"name": "Neptune", "author": "SLTWTR", "private": true, "version": "2.0.0", "description": " ", "scripts": {"serve": "cd theme && shopify theme serve", "serve2": "cd theme && shopify2 theme serve", "dev": "shopify theme dev --store=motherdenim --path=./theme", "watch": "webpack --watch --mode development", "develop": "(yarn watch) & yarn serve", "build": "webpack --mode production", "build-dev": "webpack --mode development", "start": "shopify logout && sh -c 'shopify login --store=${0}.myshopify.com'", "pull": "shopify theme pull --only=\"config/settings_data.json\" --only=\"templates/*.json\" --store=motherdenim --path=./theme", "templates": "cd theme && shopify theme pull -o \"templates/*.json\"", "templates2": "cd theme && shopify2 theme pull -o \"templates/*.json\"", "settings": "cd theme && shopify theme pull -o \"config/settings_data.json\"", "settings2": "cd theme && shopify2 theme pull -o \"config/settings_data.json\"", "theme-gen": "node neptune/shopify-themes/deployments/theme-generate"}, "license": "ISC", "devDependencies": {"@babel/core": "^7.15.0", "@babel/preset-env": "^7.15.0", "@fullhuman/postcss-purgecss": "^4.0.3", "@tailwindcss/line-clamp": "^0.4.0", "autoprefixer": "^10.3.1", "babel-loader": "^8.2.2", "concat-text-webpack-plugin": "^0.2.1", "copy-webpack-plugin": "^6.4.1", "css-loader": "^6.2.0", "dotenv": "^16.0.0", "glob": "^7.1.7", "inquirer": "^8.2.2", "postcss": "^8.3.6", "postcss-loader": "^6.1.1", "postcss-preset-env": "^6.7.0", "prettier": "^2.3.2", "sass-loader": "^12.1.0", "style-loader": "^3.2.1", "webpack": "^5.50.0", "webpack-cli": "^4.8.0"}, "dependencies": {"@tailwindcss/aspect-ratio": "^0.2.1", "cssnano": "^5.0.8", "form-serialize": "^0.7.2", "gsap": "./gsap-bonus.tgz", "liquidjs": "^9.25.1", "mini-css-extract-plugin": "^2.2.0", "regenerator-runtime": "^0.13.9", "sass": "^1.38.0", "swiper": "^7.0.7", "tailwindcss": "^3.0.23", "uson": "^0.3.5", "webpack-merge-and-include-globally": "^2.3.4"}}