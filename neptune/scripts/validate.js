Neptune.validate = {

	init: p => {
		
		p == p || document
		
		_n.qsa('[neptune-validate]', p).forEach( form => {

			setInterval(()=>{form.removeAttribute('onsubmit')},10)
			
			form.addEventListener('submit', event => {

				if (!Neptune.validate.validate(event.target)){
					event.preventDefault();
					return false;
				}
				return true;

			})

		})

	},

	form: form => {

		alert('invalid');
		return false;

	},

	field: field => {

		alert('invalid');
		return false;

	}
}

window.Neptune = Neptune;

export default Neptune.validate;