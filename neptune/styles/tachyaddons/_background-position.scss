
.bg-top-left {    
  background-repeat: no-repeat; 
  background-position: top left;    
}

.bg-top-right {    
  background-repeat: no-repeat; 
  background-position: top left;    
}

.bg-bottom-left { 
  background-repeat: no-repeat; 
  background-position: bottom left; 
}

.bg-bottom-right { 
  background-repeat: no-repeat; 
  background-position: bottom right; 
}

@media #{$breakpoint-not-small} {
  .bg-top-left-ns {    
    background-repeat: no-repeat; 
    background-position: top left;    
  }

  .bg-top-right-ns {    
    background-repeat: no-repeat; 
    background-position: top left;    
  }

  .bg-bottom-left-ns { 
    background-repeat: no-repeat; 
    background-position: bottom left; 
  }

  .bg-bottom-right-ns { 
    background-repeat: no-repeat; 
    background-position: bottom right; 
  }
}

@media #{$breakpoint-medium} {
  .bg-top-left-m {    
    background-repeat: no-repeat; 
    background-position: top left;    
  }

  .bg-top-right-m {    
    background-repeat: no-repeat; 
    background-position: top left;    
  }

  .bg-bottom-left-m { 
    background-repeat: no-repeat; 
    background-position: bottom left; 
  }

  .bg-bottom-right-m { 
    background-repeat: no-repeat; 
    background-position: bottom right; 
  }
}

@media #{$breakpoint-large} {
  .bg-top-left-l {    
    background-repeat: no-repeat; 
    background-position: top left;    
  }

  .bg-top-right-l {    
    background-repeat: no-repeat; 
    background-position: top left;    
  }

  .bg-bottom-left-l { 
    background-repeat: no-repeat; 
    background-position: bottom left; 
  }

  .bg-bottom-right-l { 
    background-repeat: no-repeat; 
    background-position: bottom right; 
  }
}
