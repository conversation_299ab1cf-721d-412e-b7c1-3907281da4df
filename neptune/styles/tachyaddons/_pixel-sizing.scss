@import '../tachyons/_variables';

@for $i from 0 through 110 {


  .f#{$i}px {
      font-size:#{$i}px;
  }

  // pixel padding

  .pa#{$i}px {
      padding:#{$i}px;
  }

  .pv#{$i}px {
      padding-top:#{$i}px;
      padding-bottom:#{$i}px;
  }

  .ph#{$i}px {
      padding-left:#{$i}px;
      padding-right:#{$i}px;
  }

  .pt#{$i}px {
      padding-top:#{$i}px;
  }

  .pb#{$i}px {
      padding-bottom:#{$i}px;
  }

  .pl#{$i}px {
      padding-left:#{$i}px;
  }

  .pr#{$i}px {
      padding-right:#{$i}px;
  }

  // pixel margin


  .ma#{$i}px {
      margin:#{$i}px;
  }

  .mv#{$i}px {
      margin-top:#{$i}px;
      margin-bottom:#{$i}px;
  }

  .mh#{$i}px {
      margin-left:#{$i}px;
      margin-right:#{$i}px;
  }

  .mt#{$i}px {
      margin-top:#{$i}px;
  }

  .mb#{$i}px {
      margin-bottom:#{$i}px;
  }

  .ml#{$i}px {
      margin-left:#{$i}px;
  }

  .mr#{$i}px {
      margin-right:#{$i}px;
  }

  .h#{$i}px {
      height:#{$i}px;
  }

} 

@for $i from 0 through 110 {


    // -ns
    @media #{$breakpoint-not-small} {
      .f#{$i}px-ns {
        font-size:#{$i}px;
      }

      .pa#{$i}px-ns {
        padding:#{$i}px;
      }

      .pv#{$i}px-ns {
          padding-top:#{$i}px;
          padding-bottom:#{$i}px;
      }

      .ph#{$i}px-ns {
          padding-left:#{$i}px;
          padding-right:#{$i}px;
      }

      .pt#{$i}px-ns {
          padding-top:#{$i}px;
      }

      .pb#{$i}px-ns {
          padding-bottom:#{$i}px;
      }

      .pl#{$i}px-ns {
          padding-left:#{$i}px;
      }

      .pr#{$i}px-ns {
          padding-right:#{$i}px;
      }

      .ma#{$i}px-ns {
        margin:#{$i}px;
      }

      .mv#{$i}px-ns {
          margin-top:#{$i}px;
          margin-bottom:#{$i}px;
      }

      .mh#{$i}px-ns {
          margin-left:#{$i}px;
          margin-right:#{$i}px;
      }

      .mt#{$i}px-ns {
          margin-top:#{$i}px;
      }

      .mb#{$i}px-ns {
          margin-bottom:#{$i}px;
      }

      .ml#{$i}px-ns {
          margin-left:#{$i}px;
      }

      .mr#{$i}px-ns {
          margin-right:#{$i}px;
      }

      .h#{$i}px-ns {
          height:#{$i}px;
      }
    }
    // -m

} 


@for $i from 0 through 110 {
    
  @media #{$breakpoint-medium} {
    .f#{$i}px-m {
        font-size:#{$i}px;
    }

    .pa#{$i}px-m {
      padding:#{$i}px;
    }

    .pv#{$i}px-m {
        padding-top:#{$i}px;
        padding-bottom:#{$i}px;
    }

    .ph#{$i}px-m {
        padding-left:#{$i}px;
        padding-right:#{$i}px;
    }

    .pt#{$i}px-m {
        padding-top:#{$i}px;
    }

    .pb#{$i}px-m {
        padding-bottom:#{$i}px;
    }

    .pl#{$i}px-m {
        padding-left:#{$i}px;
    }

    .pr#{$i}px-m {
        padding-right:#{$i}px;
    }
    
    .ma#{$i}px-m {
      margin:#{$i}px;
    }

    .mv#{$i}px-m {
        margin-top:#{$i}px;
        margin-bottom:#{$i}px;
    }

    .mh#{$i}px-m {
        margin-left:#{$i}px;
        margin-right:#{$i}px;
    }

    .mt#{$i}px-m {
        margin-top:#{$i}px;
    }

    .mb#{$i}px-m {
        margin-bottom:#{$i}px;
    }

    .ml#{$i}px-m {
        margin-left:#{$i}px;
    }

    .mr#{$i}px-m {
        margin-right:#{$i}px;
    }

    .h#{$i}px-m {
        height:#{$i}px;
    }
    
  }
  // -l

} 

@for $i from 0 through 110 {

  @media #{$breakpoint-large} {
    .f#{$i}px-l {
        font-size:#{$i}px;
    }

    .f200px {
      font-size:200px;
    }

    .pa#{$i}px-l {
      padding:#{$i}px;
    }

    .pv#{$i}px-l {
        padding-top:#{$i}px;
        padding-bottom:#{$i}px;
    }

    .ph#{$i}px-l {
        padding-left:#{$i}px;
        padding-right:#{$i}px;
    }

    .pt#{$i}px-l {
        padding-top:#{$i}px;
    }

    .pb#{$i}px-l {
        padding-bottom:#{$i}px;
    }

    .pl#{$i}px-l {
        padding-left:#{$i}px;
    }

    .pr#{$i}px-l {
        padding-right:#{$i}px;
    }

    .ma#{$i}px-l {
      margin:#{$i}px;
    }

    .mv#{$i}px-l {
        margin-top:#{$i}px;
        margin-bottom:#{$i}px;
    }

    .mh#{$i}px-l {
        margin-left:#{$i}px;
        margin-right:#{$i}px;
    }

    .mt#{$i}px-l {
        margin-top:#{$i}px;
    }

    .mb#{$i}px-l {
        margin-bottom:#{$i}px;
    }

    .ml#{$i}px-l {
        margin-left:#{$i}px;
    }

    .mr#{$i}px-l {
        margin-right:#{$i}px;
    }

    .h#{$i}px-l {
        height:#{$i}px;
    }
  }

} 


@for $i from 0 through 110 {
  // -xl
  @media #{$breakpoint-extra-large} {
    .f#{$i}px-xl {
        font-size:#{$i}px;
    }

    .pa#{$i}px-xl {
      padding:#{$i}px;
    }

    .pv#{$i}px-xl {
        padding-top:#{$i}px;
        padding-bottom:#{$i}px;
    }

    .ph#{$i}px-xl {
        padding-left:#{$i}px;
        padding-right:#{$i}px;
    }

    .pt#{$i}px-xl {
        padding-top:#{$i}px;
    }

    .pb#{$i}px-xl {
        padding-bottom:#{$i}px;
    }

    .pl#{$i}px-xl {
        padding-left:#{$i}px;
    }

    .pr#{$i}px-xl {
        padding-right:#{$i}px;
    }
    .ma#{$i}px-xl {
      margin:#{$i}px;
    }

    .mv#{$i}px-xl {
        margin-top:#{$i}px;
        margin-bottom:#{$i}px;
    }

    .mh#{$i}px-xl {
        margin-left:#{$i}px;
        margin-right:#{$i}px;
    }

    .mt#{$i}px-xl {
        margin-top:#{$i}px;
    }

    .mb#{$i}px-xl {
        margin-bottom:#{$i}px;
    }

    .ml#{$i}px-xl {
        margin-left:#{$i}px;
    }

    .mr#{$i}px-xl {
        margin-right:#{$i}px;
    }

    .h#{$i}px-xl {
        height:#{$i}px;
    }
  }

} 