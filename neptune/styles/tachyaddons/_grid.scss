@import '../tachyons/_variables';
.grid {display: grid;}

.grid-cols-1 {grid-template-columns: repeat(1, minmax(0, 1fr));}
.grid-cols-2 {grid-template-columns: repeat(2, minmax(0, 1fr));}
.grid-cols-3 {grid-template-columns: repeat(3, minmax(0, 1fr));}
.grid-cols-4 {grid-template-columns: repeat(4, minmax(0, 1fr));}
.grid-cols-5 {grid-template-columns: repeat(5, minmax(0, 1fr));}
.grid-cols-6 {grid-template-columns: repeat(6, minmax(0, 1fr));}
.grid-cols-7 {grid-template-columns: repeat(7, minmax(0, 1fr));}
.grid-cols-8 {grid-template-columns: repeat(8, minmax(0, 1fr));}
.grid-cols-9 {grid-template-columns: repeat(9, minmax(0, 1fr));}
.grid-cols-10 {grid-template-columns: repeat(10, minmax(0, 1fr));}
.grid-cols-11 {grid-template-columns: repeat(11, minmax(0, 1fr));}
.grid-cols-12 {grid-template-columns: repeat(12, minmax(0, 1fr));}

.grid-gap-0 {grid-gap: $spacing-none;}
.grid-gap-1 {grid-gap: $spacing-extra-small;}
.grid-gap-2 {grid-gap: $spacing-small;}
.grid-gap-3 {grid-gap: $spacing-medium;}
.grid-gap-4 {grid-gap: $spacing-large;}
.grid-gap-5 {grid-gap: $spacing-extra-large;}
.grid-gap-6 {grid-gap: $spacing-extra-extra-large;}
.grid-gap-7 {grid-gap: $spacing-extra-extra-extra-large;}

.col-span-1 { grid-column: span 1 / span 1;}
.col-span-2 { grid-column: span 2 / span 2;}
.col-span-3 { grid-column: span 3 / span 3;}
.col-span-4 { grid-column: span 4 / span 4;}
.col-span-5 { grid-column: span 5 / span 5;}
.col-span-6 { grid-column: span 6 / span 6;}
.col-span-7 { grid-column: span 7 / span 7;}
.col-span-8 { grid-column: span 8 / span 8;}
.col-span-9 { grid-column: span 9 / span 9;}
.col-span-10 { grid-column: span 10 / span 10;}
.col-span-11 { grid-column: span 11 / span 11;}
.col-span-12 { grid-column: span 12 / span 12;}

@media #{$breakpoint-not-small} {
	.grid-ns {display: grid;}
	
	.grid-cols-1-ns {grid-template-columns: repeat(1, minmax(0, 1fr));}
  .grid-cols-2-ns {grid-template-columns: repeat(2, minmax(0, 1fr));}
  .grid-cols-3-ns {grid-template-columns: repeat(3, minmax(0, 1fr));}
  .grid-cols-4-ns {grid-template-columns: repeat(4, minmax(0, 1fr));}
  .grid-cols-5-ns {grid-template-columns: repeat(5, minmax(0, 1fr));}
  .grid-cols-6-ns {grid-template-columns: repeat(6, minmax(0, 1fr));}
  .grid-cols-7-ns {grid-template-columns: repeat(7, minmax(0, 1fr));}
  .grid-cols-8-ns {grid-template-columns: repeat(8, minmax(0, 1fr));}
  .grid-cols-9-ns {grid-template-columns: repeat(9, minmax(0, 1fr));}
  .grid-cols-10-ns {grid-template-columns: repeat(10, minmax(0, 1fr));}
  .grid-cols-11-ns {grid-template-columns: repeat(11, minmax(0, 1fr));}
  .grid-cols-12-ns {grid-template-columns: repeat(12, minmax(0, 1fr));}


	.grid-gap-0-ns {grid-gap: $spacing-none;}
	.grid-gap-1-ns {grid-gap: $spacing-extra-small;}
	.grid-gap-2-ns {grid-gap: $spacing-small;}
	.grid-gap-3-ns {grid-gap: $spacing-medium;}
	.grid-gap-4-ns {grid-gap: $spacing-large;}
	.grid-gap-5-ns {grid-gap: $spacing-extra-large;}
	.grid-gap-6-ns {grid-gap: $spacing-extra-extra-large;}
	.grid-gap-7-ns {grid-gap: $spacing-extra-extra-extra-large;}

	.col-span-1-ns { grid-column: span 1 / span 1;}
	.col-span-2-ns { grid-column: span 2 / span 2;}
	.col-span-3-ns { grid-column: span 3 / span 3;}
	.col-span-4-ns { grid-column: span 4 / span 4;}
	.col-span-5-ns { grid-column: span 5 / span 5;}
  .col-span-6-ns { grid-column: span 6 / span 6;}
  .col-span-7-ns { grid-column: span 7 / span 7;}
  .col-span-8-ns { grid-column: span 8 / span 8;}
  .col-span-9-ns { grid-column: span 9 / span 9;}
  .col-span-10-ns { grid-column: span 10 / span 10;}
  .col-span-11-ns { grid-column: span 11 / span 11;}
  .col-span-12-ns { grid-column: span 12 / span 12;}
}

@media #{$breakpoint-medium} {
  .grid-m {display: grid;}
	
	.grid-cols-1-m {grid-template-columns: repeat(1, minmax(0, 1fr));}
  .grid-cols-2-m {grid-template-columns: repeat(2, minmax(0, 1fr));}
  .grid-cols-3-m {grid-template-columns: repeat(3, minmax(0, 1fr));}
  .grid-cols-4-m {grid-template-columns: repeat(4, minmax(0, 1fr));}
  .grid-cols-5-m {grid-template-columns: repeat(5, minmax(0, 1fr));}
  .grid-cols-6-m {grid-template-columns: repeat(6, minmax(0, 1fr));}
  .grid-cols-7-m {grid-template-columns: repeat(7, minmax(0, 1fr));}
  .grid-cols-8-m {grid-template-columns: repeat(8, minmax(0, 1fr));}
  .grid-cols-9-m {grid-template-columns: repeat(9, minmax(0, 1fr));}
  .grid-cols-10-m {grid-template-columns: repeat(10, minmax(0, 1fr));}
  .grid-cols-11-m {grid-template-columns: repeat(11, minmax(0, 1fr));}
  .grid-cols-12-m {grid-template-columns: repeat(12, minmax(0, 1fr));}
	
	.grid-gap-0-m {grid-gap: $spacing-none;}
	.grid-gap-1-m {grid-gap: $spacing-extra-small;}
	.grid-gap-2-m {grid-gap: $spacing-small;}
	.grid-gap-3-m {grid-gap: $spacing-medium;}
	.grid-gap-4-m {grid-gap: $spacing-large;}
	.grid-gap-5-m {grid-gap: $spacing-extra-large;}
	.grid-gap-6-m {grid-gap: $spacing-extra-extra-large;}
	.grid-gap-7-m {grid-gap: $spacing-extra-extra-extra-large;}

	.col-span-1-m { grid-column: span 1 / span 1;}
	.col-span-2-m { grid-column: span 2 / span 2;}
	.col-span-3-m { grid-column: span 3 / span 3;}
	.col-span-4-m { grid-column: span 4 / span 4;}
	.col-span-5-m { grid-column: span 5 / span 5;}
  .col-span-6-m { grid-column: span 6 / span 6;}
  .col-span-7-m { grid-column: span 7 / span 7;}
  .col-span-8-m { grid-column: span 8 / span 8;}
  .col-span-9-m { grid-column: span 9 / span 9;}
  .col-span-10-m { grid-column: span 10 / span 10;}
  .col-span-11-m { grid-column: span 11 / span 11;}
  .col-span-12-m { grid-column: span 12 / span 12;}
}

@media #{$breakpoint-large} {
  .grid-l {display: grid;}
	
	.grid-cols-1-l {grid-template-columns: repeat(1, minmax(0, 1fr));}
  .grid-cols-2-l {grid-template-columns: repeat(2, minmax(0, 1fr));}
  .grid-cols-3-l {grid-template-columns: repeat(3, minmax(0, 1fr));}
  .grid-cols-4-l {grid-template-columns: repeat(4, minmax(0, 1fr));}
  .grid-cols-5-l {grid-template-columns: repeat(5, minmax(0, 1fr));}
  .grid-cols-6-l {grid-template-columns: repeat(6, minmax(0, 1fr));}
  .grid-cols-7-l {grid-template-columns: repeat(7, minmax(0, 1fr));}
  .grid-cols-8-l {grid-template-columns: repeat(8, minmax(0, 1fr));}
  .grid-cols-9-l {grid-template-columns: repeat(9, minmax(0, 1fr));}
  .grid-cols-10-l {grid-template-columns: repeat(10, minmax(0, 1fr));}
  .grid-cols-11-l {grid-template-columns: repeat(11, minmax(0, 1fr));}
  .grid-cols-12-l {grid-template-columns: repeat(12, minmax(0, 1fr));}

	.grid-gap-0-l {grid-gap: $spacing-none;}
	.grid-gap-1-l {grid-gap: $spacing-extra-small;}
	.grid-gap-2-l {grid-gap: $spacing-small;}
	.grid-gap-3-l {grid-gap: $spacing-medium;}
	.grid-gap-4-l {grid-gap: $spacing-large;}
	.grid-gap-5-l {grid-gap: $spacing-extra-large;}
	.grid-gap-6-l {grid-gap: $spacing-extra-extra-large;}
	.grid-gap-7-l {grid-gap: $spacing-extra-extra-extra-large;}

	.col-span-1-l { grid-column: span 1 / span 1;}
	.col-span-2-l { grid-column: span 2 / span 2;}
	.col-span-3-l { grid-column: span 3 / span 3;}
	.col-span-4-l { grid-column: span 4 / span 4;}
	.col-span-5-l { grid-column: span 5 / span 5;}
  .col-span-6-l { grid-column: span 6 / span 6;}
  .col-span-7-l { grid-column: span 7 / span 7;}
  .col-span-8-l { grid-column: span 8 / span 8;}
  .col-span-9-l { grid-column: span 9 / span 9;}
  .col-span-10-l { grid-column: span 10 / span 10;}
  .col-span-11-l { grid-column: span 11 / span 11;}
  .col-span-12-l { grid-column: span 12 / span 12;}
}
