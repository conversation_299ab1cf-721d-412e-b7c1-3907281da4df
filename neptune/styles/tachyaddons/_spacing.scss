@import '../tachyons/_variables';

.mla {
	margin-left: auto;
}

.mra {  
	margin-right: auto;
}

.mha {
  margin-left: auto;
  margin-right: auto;
}

.mh-0 {
  margin-left: -$spacing-none;
  margin-right: -$spacing-none;
}

.mh-1 {
  margin-left: -$spacing-extra-small;
  margin-right: -$spacing-extra-small;
}

.mh-2 {
  margin-left: -$spacing-small;
  margin-right: -$spacing-small;
}

.mh-3 {
  margin-left: -$spacing-medium;
  margin-right: -$spacing-medium;
}

.mh-4 {
  margin-left: -$spacing-large;
  margin-right: -$spacing-large;
}

.mh-5 {
  margin-left: -$spacing-extra-large;
  margin-right: -$spacing-extra-large;
}

.mh-6 {
  margin-left: -$spacing-extra-extra-large;
  margin-right: -$spacing-extra-extra-large;
}

.mh-7 {
  margin-left: -$spacing-extra-extra-extra-large;
  margin-right: -$spacing-extra-extra-extra-large;
}

.mv-0 {
  margin-left: -$spacing-none;
  margin-right: -$spacing-none;
}

.mv-1 {
  margin-left: -$spacing-extra-small;
  margin-right: -$spacing-extra-small;
}

.mv-2 {
  margin-left: -$spacing-small;
  margin-right: -$spacing-small;
}

.mv-3 {
  margin-left: -$spacing-medium;
  margin-right: -$spacing-medium;
}

.mv-4 {
  margin-left: -$spacing-large;
  margin-right: -$spacing-large;
}

.mv-5 {
  margin-left: -$spacing-extra-large;
  margin-right: -$spacing-extra-large;
}

.mv-6 {
  margin-left: -$spacing-extra-extra-large;
  margin-right: -$spacing-extra-extra-large;
}

.mv-7 {
  margin-left: -$spacing-extra-extra-extra-large;
  margin-right: -$spacing-extra-extra-extra-large;
}

.ma-0 {
  margin-left: -$spacing-none;
  margin-right: -$spacing-none;
}

.ma-1 {
  margin-left: -$spacing-extra-small;
  margin-right: -$spacing-extra-small;
}

.ma-2 {
  margin-left: -$spacing-small;
  margin-right: -$spacing-small;
}

.ma-3 {
  margin-left: -$spacing-medium;
  margin-right: -$spacing-medium;
}

.ma-4 {
  margin-left: -$spacing-large;
  margin-right: -$spacing-large;
}

.ma-5 {
  margin-left: -$spacing-extra-large;
  margin-right: -$spacing-extra-large;
}

.ma-6 {
  margin-left: -$spacing-extra-extra-large;
  margin-right: -$spacing-extra-extra-large;
}

.ma-7 {
  margin-left: -$spacing-extra-extra-extra-large;
  margin-right: -$spacing-extra-extra-extra-large;
}

@media #{$breakpoint-not-small} {
  .mla-ns {
		margin-left: auto;
	}

	.mra-ns {  
		margin-right: auto;
	}

	.mha-ns {
	  margin-left: auto;
	  margin-right: auto;
	}
	.mh-0-ns {
	  margin-left: -$spacing-none;
	  margin-right: -$spacing-none;
	}

	.mh-1-ns {
	  margin-left: -$spacing-extra-small;
	  margin-right: -$spacing-extra-small;
	}

	.mh-2-ns {
	  margin-left: -$spacing-small;
	  margin-right: -$spacing-small;
	}

	.mh-3-ns {
	  margin-left: -$spacing-medium;
	  margin-right: -$spacing-medium;
	}

	.mh-4-ns {
	  margin-left: -$spacing-large;
	  margin-right: -$spacing-large;
	}

	.mh-5-ns {
	  margin-left: -$spacing-extra-large;
	  margin-right: -$spacing-extra-large;
	}

	.mh-6-ns {
	  margin-left: -$spacing-extra-extra-large;
	  margin-right: -$spacing-extra-extra-large;
	}

	.mh-7-ns {
	  margin-left: -$spacing-extra-extra-extra-large;
	  margin-right: -$spacing-extra-extra-extra-large;
	}

	.mv-0-ns {
	  margin-left: -$spacing-none;
	  margin-right: -$spacing-none;
	}

	.mv-1-ns {
	  margin-left: -$spacing-extra-small;
	  margin-right: -$spacing-extra-small;
	}

	.mv-2-ns {
	  margin-left: -$spacing-small;
	  margin-right: -$spacing-small;
	}

	.mv-3-ns {
	  margin-left: -$spacing-medium;
	  margin-right: -$spacing-medium;
	}

	.mv-4-ns {
	  margin-left: -$spacing-large;
	  margin-right: -$spacing-large;
	}

	.mv-5-ns {
	  margin-left: -$spacing-extra-large;
	  margin-right: -$spacing-extra-large;
	}

	.mv-6-ns {
	  margin-left: -$spacing-extra-extra-large;
	  margin-right: -$spacing-extra-extra-large;
	}

	.mv-7-ns {
	  margin-left: -$spacing-extra-extra-extra-large;
	  margin-right: -$spacing-extra-extra-extra-large;
	}

	.ma-0-ns {
	  margin-left: -$spacing-none;
	  margin-right: -$spacing-none;
	}

	.ma-1-ns {
	  margin-left: -$spacing-extra-small;
	  margin-right: -$spacing-extra-small;
	}

	.ma-2-ns {
	  margin-left: -$spacing-small;
	  margin-right: -$spacing-small;
	}

	.ma-3-ns {
	  margin-left: -$spacing-medium;
	  margin-right: -$spacing-medium;
	}

	.ma-4-ns {
	  margin-left: -$spacing-large;
	  margin-right: -$spacing-large;
	}

	.ma-5-ns {
	  margin-left: -$spacing-extra-large;
	  margin-right: -$spacing-extra-large;
	}

	.ma-6-ns {
	  margin-left: -$spacing-extra-extra-large;
	  margin-right: -$spacing-extra-extra-large;
	}

	.ma-7-ns {
	  margin-left: -$spacing-extra-extra-extra-large;
	  margin-right: -$spacing-extra-extra-extra-large;
	}
}

@media #{$breakpoint-medium} {
  .mla-m {
		margin-left: auto;
	}

	.mra-m {  
		margin-right: auto;
	}

	.mha-m {
	  margin-left: auto;
	  margin-right: auto;
	}
	.mh-0-m {
	  margin-left: -$spacing-none;
	  margin-right: -$spacing-none;
	}

	.mh-1-m {
	  margin-left: -$spacing-extra-small;
	  margin-right: -$spacing-extra-small;
	}

	.mh-2-m {
	  margin-left: -$spacing-small;
	  margin-right: -$spacing-small;
	}

	.mh-3-m {
	  margin-left: -$spacing-medium;
	  margin-right: -$spacing-medium;
	}

	.mh-4-m {
	  margin-left: -$spacing-large;
	  margin-right: -$spacing-large;
	}

	.mh-5-m {
	  margin-left: -$spacing-extra-large;
	  margin-right: -$spacing-extra-large;
	}

	.mh-6-m {
	  margin-left: -$spacing-extra-extra-large;
	  margin-right: -$spacing-extra-extra-large;
	}

	.mh-7-m {
	  margin-left: -$spacing-extra-extra-extra-large;
	  margin-right: -$spacing-extra-extra-extra-large;
	}

	.mv-0-m {
	  margin-left: -$spacing-none;
	  margin-right: -$spacing-none;
	}

	.mv-1-m {
	  margin-left: -$spacing-extra-small;
	  margin-right: -$spacing-extra-small;
	}

	.mv-2-m {
	  margin-left: -$spacing-small;
	  margin-right: -$spacing-small;
	}

	.mv-3-m {
	  margin-left: -$spacing-medium;
	  margin-right: -$spacing-medium;
	}

	.mv-4-m {
	  margin-left: -$spacing-large;
	  margin-right: -$spacing-large;
	}

	.mv-5-m {
	  margin-left: -$spacing-extra-large;
	  margin-right: -$spacing-extra-large;
	}

	.mv-6-m {
	  margin-left: -$spacing-extra-extra-large;
	  margin-right: -$spacing-extra-extra-large;
	}

	.mv-7-m {
	  margin-left: -$spacing-extra-extra-extra-large;
	  margin-right: -$spacing-extra-extra-extra-large;
	}

	.ma-0-m {
	  margin-left: -$spacing-none;
	  margin-right: -$spacing-none;
	}

	.ma-1-m {
	  margin-left: -$spacing-extra-small;
	  margin-right: -$spacing-extra-small;
	}

	.ma-2-m {
	  margin-left: -$spacing-small;
	  margin-right: -$spacing-small;
	}

	.ma-3-m {
	  margin-left: -$spacing-medium;
	  margin-right: -$spacing-medium;
	}

	.ma-4-m {
	  margin-left: -$spacing-large;
	  margin-right: -$spacing-large;
	}

	.ma-5-m {
	  margin-left: -$spacing-extra-large;
	  margin-right: -$spacing-extra-large;
	}

	.ma-6-m {
	  margin-left: -$spacing-extra-extra-large;
	  margin-right: -$spacing-extra-extra-large;
	}

	.ma-7-m {
	  margin-left: -$spacing-extra-extra-extra-large;
	  margin-right: -$spacing-extra-extra-extra-large;
	}
}

@media #{$breakpoint-large} {
  .mla-l {
		margin-left: auto;
	}

	.mra-l {  
		margin-right: auto;
	}

	.mr-0-l {  
		margin-right: 0;
	}

	.mha-l {
	  margin-left: auto;
	  margin-right: auto;
	}
	.mh-0-l {
	  margin-left: -$spacing-none;
	  margin-right: -$spacing-none;
	}

	.mh-1-l {
	  margin-left: -$spacing-extra-small;
	  margin-right: -$spacing-extra-small;
	}

	.mh-2-l {
	  margin-left: -$spacing-small;
	  margin-right: -$spacing-small;
	}

	.mh-3-l {
	  margin-left: -$spacing-medium;
	  margin-right: -$spacing-medium;
	}

	.mh-4-l {
	  margin-left: -$spacing-large;
	  margin-right: -$spacing-large;
	}

	.mh-5-l {
	  margin-left: -$spacing-extra-large;
	  margin-right: -$spacing-extra-large;
	}

	.mh-6-l {
	  margin-left: -$spacing-extra-extra-large;
	  margin-right: -$spacing-extra-extra-large;
	}

	.mh-7-l {
	  margin-left: -$spacing-extra-extra-extra-large;
	  margin-right: -$spacing-extra-extra-extra-large;
	}
	.mv-0-l {
	  margin-left: -$spacing-none;
	  margin-right: -$spacing-none;
	}

	.mv-1-l {
	  margin-left: -$spacing-extra-small;
	  margin-right: -$spacing-extra-small;
	}

	.mv-2-l {
	  margin-left: -$spacing-small;
	  margin-right: -$spacing-small;
	}

	.mv-3-l {
	  margin-left: -$spacing-medium;
	  margin-right: -$spacing-medium;
	}

	.mv-4-l {
	  margin-left: -$spacing-large;
	  margin-right: -$spacing-large;
	}

	.mv-5-l {
	  margin-left: -$spacing-extra-large;
	  margin-right: -$spacing-extra-large;
	}

	.mv-6-l {
	  margin-left: -$spacing-extra-extra-large;
	  margin-right: -$spacing-extra-extra-large;
	}

	.mv-7-l {
	  margin-left: -$spacing-extra-extra-extra-large;
	  margin-right: -$spacing-extra-extra-extra-large;
	}

	.ma-0-l {
	  margin-left: -$spacing-none;
	  margin-right: -$spacing-none;
	}

	.ma-1-l {
	  margin-left: -$spacing-extra-small;
	  margin-right: -$spacing-extra-small;
	}

	.ma-2-l {
	  margin-left: -$spacing-small;
	  margin-right: -$spacing-small;
	}

	.ma-3-l {
	  margin-left: -$spacing-medium;
	  margin-right: -$spacing-medium;
	}

	.ma-4-l {
	  margin-left: -$spacing-large;
	  margin-right: -$spacing-large;
	}

	.ma-5-l {
	  margin-left: -$spacing-extra-large;
	  margin-right: -$spacing-extra-large;
	}

	.ma-6-l {
	  margin-left: -$spacing-extra-extra-large;
	  margin-right: -$spacing-extra-extra-large;
	}

	.ma-7-l {
	  margin-left: -$spacing-extra-extra-extra-large;
	  margin-right: -$spacing-extra-extra-extra-large;
	}
}
