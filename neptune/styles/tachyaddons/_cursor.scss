@import '../tachyons/_variables';

.pointer-none {
	pointer-events: none;
}

.pointer-all {
	pointer-events: all;
}

.cursor-pointer {cursor:pointer}
.cursor-zoom-in {cursor:zoom-in}
.cursor-zoom-out {cursor:zoom-out}

@media #{$breakpoint-not-small} {
  .pointer-none-ns {
		pointer-events: none;
	}

	.pointer-all-ns {
		pointer-events: all;
	}
}

@media #{$breakpoint-medium} {
	.pointer-none-m {
		pointer-events: none;
	}

	.pointer-all-m {
		pointer-events: all;
	}
}

@media #{$breakpoint-large} {
	.pointer-none-l {
		pointer-events: none;
	}

	.pointer-all-l {
		pointer-events: all;
	}
}