@import '../tachyons/_variables';

@for $i from 1 through 10 {
	.clamp-#{$i} {
	    display: block;
	    display: -webkit-box;
	    overflow: hidden;
	    text-overflow: ellipsis;
	    -webkit-box-orient: vertical;
	    -webkit-line-clamp: #{$i};
	    /* autoprefixer: off */
	}
}

@media #{$breakpoint-not-small} {

	@for $i from 1 through 10 {
		.clamp-#{$i}-ns {
			overflow: hidden;
		    display: -webkit-box;
		    -webkit-line-clamp: #{$i};
		    -webkit-box-orient:vertical;
		    /* autoprefixer: off */
		}
	}

}

@media #{$breakpoint-medium} {

	@for $i from 1 through 10 {
		.clamp-#{$i}-m {
			overflow: hidden;
		    display: -webkit-box;
		    -webkit-line-clamp: #{$i};
		    -webkit-box-orient:vertical;
		    /* autoprefixer: off */
		}
	}

}

@media #{$breakpoint-large} {

	@for $i from 1 through 10 {
		.clamp-#{$i}-l {
			overflow: hidden;
		    display: -webkit-box;
		    -webkit-line-clamp: #{$i};
		    -webkit-box-orient:vertical;
		    /* autoprefixer: off */
		}
	}

}