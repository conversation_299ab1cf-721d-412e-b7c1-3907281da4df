@import '../tachyons/_variables';

.tracked       { letter-spacing:  $letter-spacing-1; }
.tracked-tight { letter-spacing: $letter-spacing-tight; }
.tracked-mega  { letter-spacing:  $letter-spacing-2; }
.tracked-slight { letter-spacing: 0.025em; }
.tracked-0 { letter-spacing: $spacing-none; }
.tracked-1 { letter-spacing: .1em; }
.tracked-2 { letter-spacing: .2em; }
.tracked-3 { letter-spacing: .3em; }
.tracked-4 { letter-spacing: .4em; }

@media #{$breakpoint-not-small} {
  .tracked-slight-ns { letter-spacing: 0.025em; }
	.tracked-0-ns { letter-spacing: $spacing-none; }
	.tracked-1-ns { letter-spacing: .1em; }
	.tracked-2-ns { letter-spacing: .2em; }
	.tracked-3-ns { letter-spacing: .3em; }
	.tracked-4-ns { letter-spacing: .4em; }
	.tracked-ns       { letter-spacing:  $letter-spacing-1; }
  .tracked-tight-ns { letter-spacing: $letter-spacing-tight; }
  .tracked-mega-ns  { letter-spacing:  $letter-spacing-2; }
}

@media #{$breakpoint-medium} {
  .tracked-slight-m { letter-spacing: 0.025em; }
	.tracked-0-m { letter-spacing: $spacing-none; }
	.tracked-1-m { letter-spacing: .1em; }
	.tracked-2-m { letter-spacing: .2em; }
	.tracked-3-m { letter-spacing: .3em; }
	.tracked-4-m { letter-spacing: .4em; }
	.tracked-m       { letter-spacing:  $letter-spacing-1; }
  .tracked-tight-m { letter-spacing: $letter-spacing-tight; }
  .tracked-mega-m  { letter-spacing:  $letter-spacing-2; }
}

@media #{$breakpoint-large} {
  .tracked-slight-l { letter-spacing: 0.025em; }
	.tracked-0-l { letter-spacing: $spacing-none; }
	.tracked-1-l { letter-spacing: .1em; }
	.tracked-2-l { letter-spacing: .2em; }
	.tracked-3-l { letter-spacing: .3em; }
	.tracked-4-l { letter-spacing: .4em; }
	.tracked-l       { letter-spacing:  $letter-spacing-1; }
  .tracked-tight-l { letter-spacing: $letter-spacing-tight; }
  .tracked-mega-l  { letter-spacing:  $letter-spacing-2; }
}