@import '../tachyons/_variables';

.out-right { transform:  translateX(100%); }
.out-left { transform:  translateX(-100%); }

@media #{$breakpoint-not-small} {
	.out-right-ns { transform:  translateX(100%); }
	.out-left-ns { transform:  translateX(-100%); }
}

@media #{$breakpoint-medium} {
	.out-right-m { transform:  translateX(100%); }
	.out-left-m { transform:  translateX(-100%); }
}

@media #{$breakpoint-large} {
	.out-right-l { transform:  translateX(100%); }
	.out-left-l { transform:  translateX(-100%); }
}