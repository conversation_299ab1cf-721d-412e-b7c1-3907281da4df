.animate {
	
	transition: all 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);

  &.animate-slow {
    transition-duration: 1.2s
  }
  &.animate-fast {
    transition-duration: 0.3s
  }

	&.push-hover {
    transform: scale(1.05);
    :hover > & {
      transform: scale(1.0);
    }
  }
  &.pull-hover {
    transform: scale(1.0);
    :hover > & {
      transform: scale(1.05);
    }
  }
}

.will-transform {
  will-change: transform;
}
[neptune-height] {
  will-change: height;
}

@media #{$breakpoint-not-small} {
  .animate-none-ns {
    transition: none;
  }
}

@media #{$breakpoint-medium} {
  .animate-none-m {
    transition: none;
  }
}

@media #{$breakpoint-large} {
  .animate-none-l {
    transition: none;
  }
}