
// Converted Variables


// Custom Media Query Variables


/*

   LINE HEIGHT / LEADING
   Docs: http://tachyons.io/docs/typography/line-height

   Media Query Extensions:
     -ns = not-small
     -m  = medium
     -l  = large

*/

  .lh-tight { line-height: .85; }
  .lh-copy-plus  { line-height: 1.75; }
  .lh-double  { line-height: 2; }

@media #{$breakpoint-not-small} {
  .lh-tight-ns { line-height: .85; }
  .lh-copy-plus-ns  { line-height: 1.75; }
  .lh-double-ns  { line-height: 2; }
}

@media #{$breakpoint-medium} {
  .lh-tight-m { line-height: .85; }
  .lh-copy-plus-ns  { line-height: 1.75; }
  .lh-double-m  { line-height: 2; }
}

@media #{$breakpoint-large} {
  .lh-tight-l { line-height: .85; }
  .lh-copy-plus-ns  { line-height: 1.75; }
  .lh-double-l  { line-height: 2; }
}

