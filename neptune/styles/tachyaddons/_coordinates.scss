
// Converted Variables


// Custom Media Query Variables


/*

   COORDINATES
   Docs: http://tachyons.io/docs/layout/position/

   Use in combination with the position module.

   Base:
     top
     bottom
     right
     left

   Modifiers:
     -0  = literal value 0
     -1  = literal value 1
     -2  = literal value 2
     --1 = literal value -1
     --2 = literal value -2

   Media Query Extensions:
     -ns = not-small
     -m  = medium
     -l  = large

*/

.top-top   { top:    0; transform: translateY(0); }
.top-center   { top:    50%; transform: translateY(-50%); }
.top-100 { top: 100%; }
.bottom-100 { bottom: 100%; }
.right-100  { right:  100%; }
.left-100   { left:   100%; }
.top-0 { top: 0; }
.bottom-0 { bottom: 0; }

.ap-tl {
  top: 0;
  left: 0
}

.ap-tc {
 top: 0;
  left: 50%;
  transform: translateX(-50%);
}

.ap-tr {
  top: 0;
  right: 0;
}

.ap-ml {
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}

.ap-mc {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.ap-mr {
  top: 50%;
  right: 0;
  transform: translateY(-50%);
}

.ap-bl {
  bottom: 0;
  left: 0;
}

.ap-bc {
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}

.ap-br {
  bottom: 0;
  right: 0;
}


@media #{$breakpoint-not-small} {
  .top-top-ns  { top:    0; transform: translateY(0); }
  .top-center-ns   { top:    50%; transform: translateY(-50%); }
  .top-100-ns { top: 100%; }
  .bottom-100-ns { bottom: 100%; }
  .right-100-ns  { right:  100%; }
  .left-100-ns   { left:   100%; }
  .top-0-ns { top: 0; }
  .bottom-0-ns { bottom: 0; }

  .ap-tl-ns {
    top: 0;
    left: 0
  }

  .ap-tc-ns {
   top: 0;
    left: 50%;
    transform: translateX(-50%);
  }

  .ap-tr-ns {
    top: 0;
    right: 0;
  }

  .ap-ml-ns {
    top: 50%;
    left: 0;
    transform: translateY(-50%);
  }

  .ap-mc-ns {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .ap-mr-ns {
    top: 50%;
    right: 0;
    transform: translateY(-50%);
  }

  .ap-bl-ns {
    bottom: 0;
    left: 0;
  }

  .ap-bc-ns {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
  }

  .ap-br-ns {
    bottom: 0;
    right: 0;
  }
}

@media #{$breakpoint-medium} {
  .top-top-m  { top:    0; transform: translateY(0); }
  .top-center-m   { top:    50%; transform: translateY(-50%); }
  .top-100-m { top: 100%; }
  .bottom-100-m { bottom: 100%; } 
  .right-100-m  { right:  100%; }
  .left-100-m   { left:   100%; }
  .top-0-m { top: 0; }
  .bottom-0-m { bottom: 0; }

  .ap-tl-m {
    top: 0;
    left: 0
  }

  .ap-tc-m {
   top: 0;
    left: 50%;
    transform: translateX(-50%);
  }

  .ap-tr-m {
    top: 0;
    right: 0;
  }

  .ap-ml-m {
    top: 50%;
    left: 0;
    transform: translateY(-50%);
  }

  .ap-mc-m {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .ap-mr-m {
    top: 50%;
    right: 0;
    transform: translateY(-50%);
  }

  .ap-bl-m {
    bottom: 0;
    left: 0;
  }

  .ap-bc-m {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
  }

  .ap-br-m {
    bottom: 0;
    right: 0;
  }
}

@media #{$breakpoint-large} {
  .top-top-l  { top:    0; transform: translateY(0); }
  .top-center-l   { top:    50%; transform: translateY(-50%); }
  .top-100-l { top: 100%; }
  .bottom-100-l { bottom: 100%; }
  .right-100-l  { right:  100%; }
  .left-100-l   { left:   100%; }
  .top-0-l { top: 0; }
  .bottom-0-l { bottom: 0; }

  .ap-tl-l {
    top: 0;
    left: 0
  }

  .ap-tc-l {
   top: 0;
    left: 50%;
    transform: translateX(-50%);
  }

  .ap-tr-l {
    top: 0;
    right: 0;
  }

  .ap-ml-l {
    top: 50%;
    left: 0;
    transform: translateY(-50%);
  }

  .ap-mc-l {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .ap-mr-l {
    top: 50%;
    right: 0;
    transform: translateY(-50%);
  }

  .ap-bl-l {
    bottom: 0;
    left: 0;
  }

  .ap-bc-l {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
  }

  .ap-br-l {
    bottom: 0;
    right: 0;
  }
}
