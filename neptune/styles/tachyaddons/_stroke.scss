.transparent {
	color:transparent;
}
.fill-transparent {
	-webkit-text-fill-color:transparent;
}
.stroke-text {
	-webkit-text-stroke-width: 1px;
 	-webkit-text-stroke-color: black;
}

.sw1 {-webkit-text-stroke-width: 2px;}
.sw2 {-webkit-text-stroke-width: 3px;}
.sw3 {-webkit-text-stroke-width: 5px;}
.sw4 {-webkit-text-stroke-width: 8px;}
.sw5 {-webkit-text-stroke-width: 13px;}

.s--black {        -webkit-text-stroke-color: $black; }
.s--near-black {   -webkit-text-stroke-color: $near-black; }
.s--dark-gray {    -webkit-text-stroke-color: $dark-gray; }
.s--mid-gray {     -webkit-text-stroke-color: $mid-gray; }
.s--gray {         -webkit-text-stroke-color: $gray; }
.s--silver {       -webkit-text-stroke-color: $silver; }
.s--light-silver { -webkit-text-stroke-color: $light-silver; }
.s--moon-gray {    -webkit-text-stroke-color: $moon-gray; }
.s--light-gray {   -webkit-text-stroke-color: $light-gray; }
.s--near-white {   -webkit-text-stroke-color: $near-white; }
.s--white {        -webkit-text-stroke-color: $white; }

.s--white-90 {   -webkit-text-stroke-color: $white-90; }
.s--white-80 {   -webkit-text-stroke-color: $white-80; }
.s--white-70 {   -webkit-text-stroke-color: $white-70; }
.s--white-60 {   -webkit-text-stroke-color: $white-60; }
.s--white-50 {   -webkit-text-stroke-color: $white-50; }
.s--white-40 {   -webkit-text-stroke-color: $white-40; }
.s--white-30 {   -webkit-text-stroke-color: $white-30; }
.s--white-20 {   -webkit-text-stroke-color: $white-20; }
.s--white-10 {   -webkit-text-stroke-color: $white-10; }
.s--white-05 {   -webkit-text-stroke-color: $white-05; }
.s--white-025 {   -webkit-text-stroke-color: $white-025; }
.s--white-0125 {   -webkit-text-stroke-color: $white-0125; }

.s--black-90 {   -webkit-text-stroke-color: $black-90; }
.s--black-80 {   -webkit-text-stroke-color: $black-80; }
.s--black-70 {   -webkit-text-stroke-color: $black-70; }
.s--black-60 {   -webkit-text-stroke-color: $black-60; }
.s--black-50 {   -webkit-text-stroke-color: $black-50; }
.s--black-40 {   -webkit-text-stroke-color: $black-40; }
.s--black-30 {   -webkit-text-stroke-color: $black-30; }
.s--black-20 {   -webkit-text-stroke-color: $black-20; }
.s--black-10 {   -webkit-text-stroke-color: $black-10; }
.s--black-05 {   -webkit-text-stroke-color: $black-05; }
.s--black-025 {   -webkit-text-stroke-color: $black-025; }
.s--black-0125 {   -webkit-text-stroke-color: $black-0125; }

.s--dark-red { -webkit-text-stroke-color: $dark-red; }
.s--red { -webkit-text-stroke-color: $red; }
.s--light-red { -webkit-text-stroke-color: $light-red; }
.s--orange { -webkit-text-stroke-color: $orange; }
.s--gold { -webkit-text-stroke-color: $gold; }
.s--yellow { -webkit-text-stroke-color: $yellow; }
.s--light-yellow { -webkit-text-stroke-color: $light-yellow; }
.s--purple { -webkit-text-stroke-color: $purple; }
.s--light-purple { -webkit-text-stroke-color: $light-purple; }
.s--dark-pink { -webkit-text-stroke-color: $dark-pink; }
.s--hot-pink { -webkit-text-stroke-color: $hot-pink; }
.s--pink { -webkit-text-stroke-color: $pink; }
.s--light-pink { -webkit-text-stroke-color: $light-pink; }
.s--dark-green { -webkit-text-stroke-color: $dark-green; }
.s--green { -webkit-text-stroke-color: $green; }
.s--light-green { -webkit-text-stroke-color: $light-green; }
.s--navy { -webkit-text-stroke-color: $navy; }
.s--dark-blue { -webkit-text-stroke-color: $dark-blue; }
.s--blue { -webkit-text-stroke-color: $blue; }
.s--light-blue { -webkit-text-stroke-color: $light-blue; }
.s--lightest-blue { -webkit-text-stroke-color: $lightest-blue; }
.s--washed-blue { -webkit-text-stroke-color: $washed-blue; }
.s--washed-green { -webkit-text-stroke-color: $washed-green; }
.s--washed-yellow { -webkit-text-stroke-color: $washed-yellow; }
.s--washed-red { -webkit-text-stroke-color: $washed-red; }

.s--transparent { -webkit-text-stroke-color: $transparent; }
.s--inherit { -webkit-text-stroke-color: inherit; }