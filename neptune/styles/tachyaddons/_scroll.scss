@import '../tachyons/_variables';


.scroll-snap-x {
   /* snap mandatory on horizontal axis  */
  scroll-snap-type: x mandatory;
  /* Enable Safari touch scrolling physics which is needed for scroll snap */
  -webkit-overflow-scrolling: touch;
}
.overscroll-contain {
  overscroll-behavior: contain;
}
.overscroll-x-contain {
  overscroll-behavior-x: contain;
}
.overscroll-y-contain {
  overscroll-behavior-y: contain;
}
.smooth-scroll {
  scroll-behavior:smooth;
}
.scroll-y {
  overflow-y: scroll;
}
.scroll-x {
  overflow-x: scroll;
}

.flex-slider > * {
   /* snap align center  */
  scroll-snap-align: center;
  position: relative;
}
.flex-slider[data-align="start"] > *,
.flex-slider.snap-align-start > * {
  scroll-snap-align: start;
}
.flex-slider[data-align="end"] > *,
.flex-slider.snap-align-end > * {
  scroll-snap-align: end;
}
.flex-slider[data-align="none"] > *,
.flex-slider.snap-align-none > * {
  scroll-snap-align: none;
}

.no-scrollbar {
  overflow: -moz-scrollbars-none;
  -ms-overflow-style: none;
}
.no-scrollbar::-webkit-scrollbar { 
  width: 0 !important; 
  background-color: transparent;
  height: 0 !important
}
.no-scrollbar::-webkit-scrollbar-track {
  background-color: transparent;
}
.no-scrollbar::-webkit-scrollbar-thumb {
  background-color: transparent;
}


@media #{$breakpoint-not-small} {
}

@media #{$breakpoint-medium} {
}

@media #{$breakpoint-large} {
  .overscroll-contain-l {
    overscroll-behavior: contain;
  }
  .overscroll-x-contain-l {
    overscroll-behavior-x: contain;
  }
  .overscroll-y-contain-l {
    overscroll-behavior-y: contain;
  }
}

@media #{$breakpoint-extra-large} {
}
