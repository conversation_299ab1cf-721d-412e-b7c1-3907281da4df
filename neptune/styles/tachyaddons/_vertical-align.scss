@import '../tachyons/_variables';



.v-center {top:50%; transform:translateY(-50%);}
.h-center {left:50%; transform:translateX(-50%);}
.v-center.h-center {  transform:translate(-50%, -50%); }

.v-center-s {top:50%;transform:translateY(-50%);}
.h-center-s {left:50%;transform:translateX(-50%);}
.f-super-s { font-size:12rem; line-height:.75; }
.top-0-s { top:0; }
.bottom-0-s { bottom:0; }


@media #{$breakpoint-not-small} {
  .v-center-l {top:50%;transform:translateY(-50%);}
  .h-center-l {left:50%;transform:translateX(-50%);}
  .v-center-l.h-center-l { transform:translate(-50%, -50%); }
}