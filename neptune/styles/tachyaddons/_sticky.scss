@import '../tachyons/_variables';

.sticky { position:sticky; }

@media #{$breakpoint-not-small} {
  .sticky-ns { position: sticky; }
}

@media #{$breakpoint-medium} {
  .sticky-m { position: sticky; }
}

@media #{$breakpoint-large} {
  .sticky-l { position: sticky; }
}

@media #{$breakpoint-extra-large} {
  .sticky-xl { position: sticky; }
}

[neptune-sentinel] {
display:block;
  position:absolute;
  visibility:hidden;
  .top-0 & { top:-1px; }
  .left-0 & { left:-1px; }
}

.sticky--stuck {
  &.stuck-bb { border-bottom-style: solid; }
  &.stuck-shadow-none { box-shadow: none; }
}


