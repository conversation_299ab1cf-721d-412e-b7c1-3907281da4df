@import '../tachyons/_variables';

.scale-0 {transform:scale(0.9)}
.scale-10 {transform:scale(0.9)}
.scale-20 {transform:scale(0.9)}
.scale-30 {transform:scale(0.9)}
.scale-40 {transform:scale(0.9)}
.scale-50 {transform:scale(0.9)}
.scale-60 {transform:scale(0.9)}
.scale-70 {transform:scale(0.9)}
.scale-80 {transform:scale(0.9)}
.scale-100 {transform:scale(1.0)}
.scale-110 {transform:scale(1.1)}
.scale-120 {transform:scale(1.2)}
.scale-130 {transform:scale(1.3)}
.scale-140 {transform:scale(1.4)}
.scale-150 {transform:scale(1.5)}
.scale-160 {transform:scale(1.6)}
.scale-170 {transform:scale(1.7)}
.scale-180 {transform:scale(1.8)}
.scale-190 {transform:scale(1.9)}
.scale-200 {transform:scale(2.0)}

@media #{$breakpoint-not-small} {
	.scale-0-l {transform:scale(0.9)}
	.scale-10-l {transform:scale(0.9)}
	.scale-20-l {transform:scale(0.9)}
	.scale-30-l {transform:scale(0.9)}
	.scale-40-l {transform:scale(0.9)}
	.scale-50-l {transform:scale(0.9)}
	.scale-60-l {transform:scale(0.9)}
	.scale-70-l {transform:scale(0.9)}
	.scale-80-l {transform:scale(0.9)}
	.scale-100-l {transform:scale(1.0)}
	.scale-110-l {transform:scale(1.1)}
	.scale-120-l {transform:scale(1.2)}
	.scale-130-l {transform:scale(1.3)}
	.scale-140-l {transform:scale(1.4)}
	.scale-150-l {transform:scale(1.5)}
	.scale-160-l {transform:scale(1.6)}
	.scale-170-l {transform:scale(1.7)}
	.scale-180-l {transform:scale(1.8)}
	.scale-190-l {transform:scale(1.9)}
	.scale-200-l {transform:scale(2.0)}
}