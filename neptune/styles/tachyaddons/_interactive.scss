@import '../tachyons/_variables';

/* 
  Interactivity
  =============
*/

input[type=checkbox]:checked + .checkbox-controlled,
input[type=radio]:checked + .checkbox-controlled,
.active {
  

  &.active-white { @extend .white; }
  &.active-black { @extend .black; }

  &.active-bg-black { @extend .bg-black; }
  &.active-bg-white { @extend .bg-white; }

  &.active-ba { @extend .ba }
  &.active-bw1 { @extend .bw1 }
  &.active-bw2 { @extend .bw2 }
  &.active-db { @extend .db }
  &.active-flex { @extend .flex }
  &.active-dib { @extend .dib }

  &span.active-show {display:inline-block;}
  &div.active-show, ul.active-show {display:block;}
  &.flex.active-show {display:flex;}
  &.dib.active-show {display:inline-block;}

  &.active-ba { @extend .ba }
  &.active-b--black { @extend .b--black; border-color:black !important; }
  &.active-b--red { @extend .b--red; border-color: #ff4136 !important;}
  &.active-shadow-down-muted { box-shadow:0px 3px 4px 0px rgba(0, 0, 0, 0.1) }

}

.active-show:not(.active) {
  display:none;
}
.active span.active-show {
  display:inline-block;
}
.active div.active-show,
.active ul.active-show {
  display:block;
}
.active .flex.active-show {
  display:flex;
}
.active .active-hide {
  display:none;
}
.active .active-h-auto {
  height:auto!important;
}

.active-b.active { font-weight:bold; }
.active.active-fw6,
.active .active-fw6 {
  font-weight:600
}
.active.active-black,
.active .active-black {
  color:#000000
}
.active.active-white,
.active .active-white {
  color:#FFFFFF
}
.active.active-underline,
.active .active-underline {
  text-decoration:underline;
}

.active.active-bb,
.active .active-bb {
  border-bottom:solid #000;
}
.active-bg-black.active {
  background:black;
}

.active .active-child-black { color:#000; }

.active-visible { visibility:hidden; }
.active-visible.active { visibility:visible; }

.active-shadow.active, :checked + .checkbox-activated.active-shadow { @extend .shadow-1 }
.active-shadow-1.active, :checked + .checkbox-activated.active-shadow-1{ @extend .shadow-1 }
.active-shadow-2.active, :checked + .checkbox-activated.active-shadow-2{ @extend .shadow-2 }
.active-shadow-3.active, :checked + .checkbox-activated.active-shadow-3{ @extend .shadow-3 }
.active-shadow-4.active, :checked + .checkbox-activated.active-shadow-4{ @extend .shadow-4 }
.active-shadow-5.active, :checked + .checkbox-activated.active-shadow-5{ @extend .shadow-5 }

.active-rise.active, :checked + .checkbox-activated.active-rise {
  box-shadow: 0 16px 16px rgba(0,0,0,.1);
 
}



.active-rise-down.active, :checked + .checkbox-activated.active-rise-down {
  box-shadow: 0 16px 16px rgba(0,0,0,.1);
  -webkit-transform: translateY(4px);
  transform: translateY(4px);
}


.checkbox-controlled-display {
  display:none;
}
input[type=checkbox]:checked ~ .checkbox-controlled-display,
input[type=radio]:checked ~ .checkbox-controlled-display,
input[type=radio]:checked ~ .checkbox-controlled-display {
  display:block;
}
input[type=checkbox]:checked ~ .checkbox-controlled-display.flex,
input[type=radio]:checked ~ .checkbox-controlled-display.flex {
  display:flex;
}
input[type=checkbox]:checked ~ .checkbox-controlled-display.dib,
input[type=radio]:checked ~ .checkbox-controlled-display.dib {
  display:inline-block;
}

input[type=checkbox]:checked ~ .checkbox-controlled-bold,
input[type=radio]:checked ~ .checkbox-controlled-bold {
  font-weight: 600;
  position: relative;
  z-index: 2;
}

.checkbox-controlled-height {
  max-height:0;
  overflow:hidden;
  transition:max-height 0.2s cubic-bezier(0.55, 0.055, 0.675, 0.19);
}


.checkbox-controlled {
	
	.active-show {
  	display: none;
  }

}

input[type=checkbox]:checked ~ .checkbox-controlled .active-show,
input[type=radio]:checked ~ .checkbox-controlled .active-show,
input[type=radio]:checked ~ .active-show,
input[type=radio]:checked ~ * .active-show{
  display:block;
}
input[type=checkbox]:checked ~ .checkbox-controlled .active-show.flex,
input[type=radio]:checked ~ .checkbox-controlled .active-show.flex,
input[type=radio]:checked ~ .active-show.flex,
input[type=radio]:checked ~ * .active-show.flex {
  display:flex;
}
input[type=checkbox]:checked ~ .checkbox-controlled .active-show.dib,
input[type=radio]:checked ~ .checkbox-controlled .active-show.dib,
input[type=radio]:checked ~ .active-show.dib,
input[type=radio]:checked ~ * .active-show.dib {
  display:inline-block;
}

input[type=checkbox]:checked ~ .checkbox-controlled .active-hide,
input[type=radio]:checked ~ .checkbox-controlled .active-hide,
input[type=radio]:checked ~ .active-hide,
input[type=radio]:checked ~ * .active-hide {
  display:none;
}

.checkbox-controlled .active-show {
  display: none;
}

input[type=checkbox]:checked ~ .checkbox-controlled .active-show,
input[type=radio]:checked ~ .checkbox-controlled .active-show,
input[type=radio]:checked ~ .active-show,
input[type=radio]:checked ~ * .active-show{
  display:block;
}
input[type=checkbox]:checked ~ .checkbox-controlled .active-show.flex,
input[type=radio]:checked ~ .checkbox-controlled .active-show.flex,
input[type=radio]:checked ~ .active-show.flex,
input[type=radio]:checked ~ * .active-show.flex {
  display:flex;
}
input[type=checkbox]:checked ~ .checkbox-controlled .active-show.dib,
input[type=radio]:checked ~ .checkbox-controlled .active-show.dib,
input[type=radio]:checked ~ .active-show.dib,
input[type=radio]:checked ~ * .active-show.dib {
  display:inline-block;
}

input[type=checkbox]:checked ~ .checkbox-controlled .active-hide,
input[type=radio]:checked ~ .checkbox-controlled .active-hide,
input[type=radio]:checked ~ .active-hide,
input[type=radio]:checked ~ * .active-hide {
  display:none;
}