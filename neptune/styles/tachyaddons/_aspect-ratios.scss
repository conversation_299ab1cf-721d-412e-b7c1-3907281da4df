
// Converted Variables


// Custom Media Query Variables


/*

   ASPECT RATIOS

*/

/* This is for fluid media that is embedded from third party sites like youtube, vimeo etc.
 * Wrap the outer element in aspect-ratio and then extend it with the desired ratio i.e
 * Make sure there are no height and width attributes on the embedded media.
 * Adapted from: https://github.com/suitcss/components-flex-embed
 *
 * Example:
 *
 * <div class="aspect-ratio aspect-ratio--16x9">
 *  <iframe class="aspect-ratio--object"></iframe>
 * </div>
 *
 * */



.aspect-ratio--5x4, %aspect-ratio--5x4 { padding-bottom: 80%; }
.aspect-ratio--4x5, %aspect-ratio--4x5 { padding-bottom: 125%; }




@media #{$breakpoint-not-small}{
    .aspect-ratio--5x4-ns, %aspect-ratio--5x4-ns { padding-bottom: 80%; }
    .aspect-ratio--4x5-ns, %aspect-ratio--4x5-ns { padding-bottom: 125%; }
}

@media #{$breakpoint-medium}{
    .aspect-ratio--5x4-m, %aspect-ratio--5x4-m { padding-bottom: 80%; }
    .aspect-ratio--4x5-m, %aspect-ratio--4x5-m { padding-bottom: 125%; }
}

@media #{$breakpoint-large}{
    .aspect-ratio--5x4-l, %aspect-ratio--5x4-l { padding-bottom: 80%; }
    .aspect-ratio--4x5-l, %aspect-ratio--4x5-l { padding-bottom: 125%; }
}
