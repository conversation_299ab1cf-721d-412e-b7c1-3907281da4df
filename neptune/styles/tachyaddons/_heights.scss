
// Converted Variables


// Custom Media Query Variables


/*

   HEIGHTS
   Docs: http://tachyons.io/docs/layout/heights/

   Base:
     h = height
     min-h = min-height
     min-vh = min-height vertical screen height
     vh = vertical screen height

   Modifiers
     1 = 1st step in height scale
     2 = 2nd step in height scale
     3 = 3rd step in height scale
     4 = 4th step in height scale
     5 = 5th step in height scale

     -25   = literal value 25%
     -50   = literal value 50%
     -75   = literal value 75%
     -100  = literal value 100%

     -auto = string value of auto
     -inherit = string value of inherit

   Media Query Extensions:
     -ns = not-small
     -m  = medium
     -l  = large

*/

/* Height Scale */

.height-noconflict {
  .h1 { height: auto; }
  .h2 { height: auto; }
  .h3 { height: auto; }
  .h4 { height: auto; }
  .h5 { height: auto; }
}

.h-no-c {
  .h-1 { height: $height-1; }
  .h-2 { height: $height-2; }
  .h-3 { height: $height-3; }
  .h-4 { height: $height-4; }
  .h-5 { height: $height-5; }
}

.height1 { height: $height-1; }
.height2 { height: $height-2; }
.height3 { height: $height-3; }
.height4 { height: $height-4; }
.height5 { height: $height-5; }

/* Height Percentages - Based off of height of parent */

.h-25 {  height:  25%; }
.h-50 {  height:  50%; }
.h-75 {  height:  75%; }
.h-100 { height: 100%; }

.tachy-height .h-25 {  height: auto; }
.tachy-height .h-50 {  height: auto; }
.tachy-height .h-75 {  height: auto; }
.tachy-height .h-100 { height: auto; }

.min-h-auto {     height: auto; }
.min-h-screen { min-height: 100dvh; }

/* String Properties */
.h-0 { height: 0px; }
.h-auto {     height: auto; }
.h-inherit {  height: inherit; }

@media #{$breakpoint-not-small} {
  .h-no-c {
    .h-1-ns { height: $height-1; }
    .h-2-ns { height: $height-2; }
    .h-3-ns { height: $height-3; }
    .h-4-ns { height: $height-4; }
    .h-5-ns { height: $height-5; }
  }
  .height1-ns {  height: $height-1; }
  .height2-ns {  height: $height-2; }
  .height3-ns {  height: $height-3; }
  .height4-ns {  height: $height-4; }
  .height5-ns {  height: $height-5; }
  .h-25-ns { height: 25%; }
  .h-50-ns { height: 50%; }
  .h-75-ns { height: 75%; }
  .h-100-ns { height: 100%; }
  .h-auto-ns { height: auto; }
  .h-inherit-ns { height: inherit; }
  .min-h-auto-ns {     height: auto; }
}

@media #{$breakpoint-medium} {
  .h-no-c {
    .h-1-m { height: $height-1; }
    .h-2-m { height: $height-2; }
    .h-3-m { height: $height-3; }
    .h-4-m { height: $height-4; }
    .h-5-m { height: $height-5; }
  }
  .height1-m { height: $height-1; }
  .height2-m { height: $height-2; }
  .height3-m { height: $height-3; }
  .height4-m { height: $height-4; }
  .height5-m { height: $height-5; }
  .h-25-m { height: 25%; }
  .h-50-m { height: 50%; }
  .h-75-m { height: 75%; }
  .h-100-m { height: 100%; }
  .h-auto-m { height: auto; }
  .h-inherit-m { height: inherit; }
  .min-h-auto-m {     height: auto; }
}

@media #{$breakpoint-large} {
  .h-no-c {
    .h-1-l { height: $height-1; }
    .h-2-l { height: $height-2; }
    .h-3-l { height: $height-3; }
    .h-4-l { height: $height-4; }
    .h-5-l { height: $height-5; }
  }
  .height1-l { height: $height-1; }
  .height2-l { height: $height-2; }
  .height3-l { height: $height-3; }
  .height4-l { height: $height-4; }
  .height5-l { height: $height-5; }
  .h-25-l { height: 25%; }
  .h-50-l { height: 50%; }
  .h-75-l { height: 75%; }
  .h-100-l { height: 100%; }
  .h-auto-l { height: auto; }
  .h-inherit-l { height: inherit; }
  .min-h-auto-l { height: auto; }
}

.h-set.h-0 {
  height: 0px !important;
}

.h-fill {
  min-height: 100vh;
  min-height: -webkit-fill-available;
}
