.hide-child .child {
	pointer-events:none;
}
.hide-child:hover .child {
	pointer-events:auto;
}


.rise, .rise-down, .rise-straight {
	@extend .animate;
}


@media #{$breakpoint-not-small} {
	.rise:hover {
		box-shadow: 0 16px 16px rgba(0,0,0,.1);
	  -webkit-transform: translateY(-4px);
	  transform: translateY(-4px);
	}
	.rise-down:hover {
		box-shadow: 0 16px 16px rgba(0,0,0,.1);
	  -webkit-transform: translateY(4px);
	  transform: translateY(4px);
	 }
	.rise-straight:hover {
		box-shadow: 0 16px 16px rgba(0,0,0,.1);
	 }
}


.brighten {
  opacity: .5;
  transition: opacity .15s ease-in;
}
.brighten:hover,
.brighten:focus {
  opacity: 1;
  transition: opacity .15s ease-in;
}
.brighten:active {
  opacity: .8; transition: opacity .15s ease-out;
}