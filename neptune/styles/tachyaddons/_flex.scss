@import '../tachyons/_variables';

.flex-1 {
  flex: 1;
}
.flex-2 {
  flex: 2;
}
.flex-fill         { flex: 1 1 auto !important; }
.flex-grow-0       { flex-grow: 0 !important; }
.flex-grow-1       { flex-grow: 1 !important; }
.flex-shrink-0     { flex-shrink: 0 !important; }
.flex-shrink-1     { flex-shrink: 1 !important; }
.flex-basis-0     { flex-basis: 0 !important; }
.flex-basis-100     { flex-basis: 100% !important; }

.order--1 { order: -1; }

.flex-dn { display: none; }
.flex-db { display: block; }
.flex-dib { display: inline-block; }
.flex-static { display: static; }
.flex { display: flex; }

@media #{$breakpoint-not-small} {
  .flex-1-ns {
	  flex: 1;
	}
	.flex-2-ns {
	  flex: 2;
	}
	.flex-fill-ns { flex: 1 1 auto !important; }
	.flex-grow-0-ns { flex-grow: 0 !important; }
	.flex-grow-1-ns { flex-grow: 1 !important; }
	.flex-shrink-0-ns { flex-shrink: 0 !important; }
	.flex-shrink-1-ns { flex-shrink: 1 !important; }
	.flex-basis-0-ns { flex-basis: 0 !important; }
	.flex-basis-100-ns     { flex-basis: 100% !important; }

	.order--1-ns { order: -1; }

	.flex-dn-ns { display: none; }
	.flex-db-ns { display: block; }
	.flex-dib-ns { display: inline-block; }
	.flex-static-ns { display: static; }
	.flex-ns { display: flex; }
}

@media #{$breakpoint-medium} {
  .flex-1-m {
	  flex: 1;
	}
	.flex-2-m {
	  flex: 2;
	}
	.flex-fill-m { flex: 1 1 auto !important; }
	.flex-grow-0-m { flex-grow: 0 !important; }
	.flex-grow-1-m { flex-grow: 1 !important; }
	.flex-shrink-0-m { flex-shrink: 0 !important; }
	.flex-shrink-1-m { flex-shrink: 1 !important; }
	.flex-basis-0-m { flex-basis: 0 !important; }
	.flex-basis-100-m     { flex-basis: 100% !important; }

	.order--1-m { order: -1; }

	.flex-dn-m { display: none; }
	.flex-db-m { display: block; }
	.flex-dib-m { display: inline-block; }
	.flex-static-m { display: static; }
	.flex-m { display: flex; }
}

@media #{$breakpoint-large} {
  .flex-1-l {
	  flex: 1;
	}
	.flex-2-l {
	  flex: 2;
	}
	.flex-fill-l { flex: 1 1 auto !important; }
	.flex-grow-0-l { flex-grow: 0 !important; }
	.flex-grow-1-l { flex-grow: 1 !important; }
	.flex-shrink-0-l { flex-shrink: 0 !important; }
	.flex-shrink-1-l { flex-shrink: 1 !important; }
	.flex-basis-0-l { flex-basis: 0 !important; }
	.flex-basis-100-l     { flex-basis: 100% !important; }

	.order--1-l { order: -1; }

	.flex-dn-l { display: none; }
	.flex-dn-l { display: none; }
	.flex-db-l { display: block; }
	.flex-dib-l { display: inline-block; }
	.flex-static-l { display: static; }
	.flex-l { display: flex; }
}

@media screen and (max-width: 59em) {
	.order--1-nl { order: -1; }
}
