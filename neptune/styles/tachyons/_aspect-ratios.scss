
// Converted Variables


// Custom Media Query Variables


/*

   ASPECT RATIOS

*/

/* This is for fluid media that is embedded from third party sites like youtube, vimeo etc.
 * Wrap the outer element in aspect-ratio and then extend it with the desired ratio i.e
 * Make sure there are no height and width attributes on the embedded media.
 * Adapted from: https://github.com/suitcss/components-flex-embed
 *
 * Example:
 *
 * <div class="aspect-ratio aspect-ratio--16x9">
 *  <iframe class="aspect-ratio--object"></iframe>
 * </div>
 *
 * */

.aspect-ratio, %aspect-ratio {
  height: 0;
  position: relative;
}

.aspect-ratio--16x9, %aspect-ratio--16x9 { padding-bottom: 56.25%; }
.aspect-ratio--9x16, %aspect-ratio--9x16 { padding-bottom: 177.77%; }

.aspect-ratio--4x3, %aspect-ratio--4x3 {  padding-bottom: 75%; }
.aspect-ratio--3x4, %aspect-ratio--3x4 {  padding-bottom: 133.33%; }

.aspect-ratio--6x4, %aspect-ratio--6x4 {  padding-bottom: 66.6%; }
.aspect-ratio--4x6, %aspect-ratio--4x6 {  padding-bottom: 150%; }

.aspect-ratio--8x5, %aspect-ratio--8x5 {  padding-bottom: 62.5%; }
.aspect-ratio--5x8, %aspect-ratio--5x8 {  padding-bottom: 160%; }

.aspect-ratio--7x5, %aspect-ratio--7x5 {  padding-bottom: 71.42%; }
.aspect-ratio--5x7, %aspect-ratio--5x7 {  padding-bottom: 140%; }

.aspect-ratio--1x1, %aspect-ratio--1x1 {  padding-bottom: 100%; }

.aspect-ratio--object, %aspect-ratio--object {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100;
}

@media #{$breakpoint-not-small}{
    .aspect-ratio-ns, %aspect-ratio-ns {
      height: 0;
      position: relative;
    }
    .aspect-ratio--16x9-ns, %aspect-ratio--16x9-ns { padding-bottom: 56.25%; }
    .aspect-ratio--9x16-ns, %aspect-ratio--9x16-ns { padding-bottom: 177.77%; }
    .aspect-ratio--4x3-ns, %aspect-ratio--4x3-ns {  padding-bottom: 75%; }
    .aspect-ratio--3x4-ns, %aspect-ratio--3x4-ns {  padding-bottom: 133.33%; }
    .aspect-ratio--6x4-ns, %aspect-ratio--6x4-ns {  padding-bottom: 66.6%; }
    .aspect-ratio--4x6-ns, %aspect-ratio--4x6-ns {  padding-bottom: 150%; }
    .aspect-ratio--8x5-ns, %aspect-ratio--8x5-ns {  padding-bottom: 62.5%; }
    .aspect-ratio--5x8-ns, %aspect-ratio--5x8-ns {  padding-bottom: 160%; }
    .aspect-ratio--7x5-ns, %aspect-ratio--7x5-ns {  padding-bottom: 71.42%; }
    .aspect-ratio--5x7-ns, %aspect-ratio--5x7-ns {  padding-bottom: 140%; }
    .aspect-ratio--1x1-ns, %aspect-ratio--1x1-ns {  padding-bottom: 100%; }
    .aspect-ratio--object-ns, %aspect-ratio--object-ns {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 100;
    }
}

@media #{$breakpoint-medium}{
    .aspect-ratio-m, %aspect-ratio-m {
      height: 0;
      position: relative;
    }
    .aspect-ratio--16x9-m, %aspect-ratio--16x9-m { padding-bottom: 56.25%; }
    .aspect-ratio--9x16-m, %aspect-ratio--9x16-m { padding-bottom: 177.77%; }
    .aspect-ratio--4x3-m, %aspect-ratio--4x3-m {  padding-bottom: 75%; }
    .aspect-ratio--3x4-m, %aspect-ratio--3x4-m {  padding-bottom: 133.33%; }
    .aspect-ratio--6x4-m, %aspect-ratio--6x4-m {  padding-bottom: 66.6%; }
    .aspect-ratio--4x6-m, %aspect-ratio--4x6-m {  padding-bottom: 150%; }
    .aspect-ratio--8x5-m, %aspect-ratio--8x5-m {  padding-bottom: 62.5%; }
    .aspect-ratio--5x8-m, %aspect-ratio--5x8-m {  padding-bottom: 160%; }
    .aspect-ratio--7x5-m, %aspect-ratio--7x5-m {  padding-bottom: 71.42%; }
    .aspect-ratio--5x7-m, %aspect-ratio--5x7-m {  padding-bottom: 140%; }
    .aspect-ratio--1x1-m, %aspect-ratio--1x1-m {  padding-bottom: 100%; }
    .aspect-ratio--object-m, %aspect-ratio--object-m {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 100;
    }
}

@media #{$breakpoint-large}{
    .aspect-ratio-l, %aspect-ratio-l {
      height: 0;
      position: relative;
    }
    .aspect-ratio--16x9-l, %aspect-ratio--16x9-l { padding-bottom: 56.25%; }
    .aspect-ratio--9x16-l, %aspect-ratio--9x16-l { padding-bottom: 177.77%; }
    .aspect-ratio--4x3-l, %aspect-ratio--4x3-l {  padding-bottom: 75%; }
    .aspect-ratio--3x4-l, %aspect-ratio--3x4-l {  padding-bottom: 133.33%; }
    .aspect-ratio--6x4-l, %aspect-ratio--6x4-l {  padding-bottom: 66.6%; }
    .aspect-ratio--4x6-l, %aspect-ratio--4x6-l {  padding-bottom: 150%; }
    .aspect-ratio--8x5-l, %aspect-ratio--8x5-l {  padding-bottom: 62.5%; }
    .aspect-ratio--5x8-l, %aspect-ratio--5x8-l {  padding-bottom: 160%; }
    .aspect-ratio--7x5-l, %aspect-ratio--7x5-l {  padding-bottom: 71.42%; }
    .aspect-ratio--5x7-l, %aspect-ratio--5x7-l {  padding-bottom: 140%; }
    .aspect-ratio--1x1-l, %aspect-ratio--1x1-l {  padding-bottom: 100%; }
    .aspect-ratio--object-l, %aspect-ratio--object-l {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 100;
    }
}
