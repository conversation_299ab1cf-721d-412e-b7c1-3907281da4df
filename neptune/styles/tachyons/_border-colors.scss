
// Converted Variables


// Custom Media Query Variables


/*

   BORDER COLORS
   Docs: http://tachyons.io/docs/themes/borders/

   Border colors can be used to extend the base
   border classes ba,bt,bb,br,bl found in the _borders.css file.

   The base border class by default will set the color of the border
   to that of the current text color. These classes are for the cases
   where you desire for the text and border colors to be different.

   Base:
     b = border

   Modifiers:
   --color-name = each color variable name is also a border color name

*/

.b--black {        border-color: $black; }
.b--near-black {   border-color: $near-black; }
.b--dark-gray {    border-color: $dark-gray; }
.b--mid-gray {     border-color: $mid-gray; }
.b--gray {         border-color: $gray; }
.b--silver {       border-color: $silver; }
.b--light-silver { border-color: $light-silver; }
.b--moon-gray {    border-color: $moon-gray; }
.b--light-gray {   border-color: $light-gray; }
.b--near-white {   border-color: $near-white; }
.b--white {        border-color: $white; }

.b--white-90 {   border-color: $white-90; }
.b--white-80 {   border-color: $white-80; }
.b--white-70 {   border-color: $white-70; }
.b--white-60 {   border-color: $white-60; }
.b--white-50 {   border-color: $white-50; }
.b--white-40 {   border-color: $white-40; }
.b--white-30 {   border-color: $white-30; }
.b--white-20 {   border-color: $white-20; }
.b--white-10 {   border-color: $white-10; }
.b--white-05 {   border-color: $white-05; }
.b--white-025 {   border-color: $white-025; }
.b--white-0125 {   border-color: $white-0125; }

.b--black-90 {   border-color: $black-90; }
.b--black-80 {   border-color: $black-80; }
.b--black-70 {   border-color: $black-70; }
.b--black-60 {   border-color: $black-60; }
.b--black-50 {   border-color: $black-50; }
.b--black-40 {   border-color: $black-40; }
.b--black-30 {   border-color: $black-30; }
.b--black-20 {   border-color: $black-20; }
.b--black-10 {   border-color: $black-10; }
.b--black-05 {   border-color: $black-05; }
.b--black-025 {   border-color: $black-025; }
.b--black-0125 {   border-color: $black-0125; }

.b--dark-red { border-color: $dark-red; }
.b--red { border-color: $red; }
.b--light-red { border-color: $light-red; }
.b--orange { border-color: $orange; }
.b--gold { border-color: $gold; }
.b--yellow { border-color: $yellow; }
.b--light-yellow { border-color: $light-yellow; }
.b--purple { border-color: $purple; }
.b--light-purple { border-color: $light-purple; }
.b--dark-pink { border-color: $dark-pink; }
.b--hot-pink { border-color: $hot-pink; }
.b--pink { border-color: $pink; }
.b--light-pink { border-color: $light-pink; }
.b--dark-green { border-color: $dark-green; }
.b--green { border-color: $green; }
.b--light-green { border-color: $light-green; }
.b--navy { border-color: $navy; }
.b--dark-blue { border-color: $dark-blue; }
.b--blue { border-color: $blue; }
.b--light-blue { border-color: $light-blue; }
.b--lightest-blue { border-color: $lightest-blue; }
.b--washed-blue { border-color: $washed-blue; }
.b--washed-green { border-color: $washed-green; }
.b--washed-yellow { border-color: $washed-yellow; }
.b--washed-red { border-color: $washed-red; }

.b--transparent { border-color: $transparent; }
.b--inherit { border-color: inherit; }
