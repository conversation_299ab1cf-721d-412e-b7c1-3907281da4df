<link rel="stylesheet" href="{{ 'section-main-page.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'component-rte.css' | asset_url }}" media="print" onload="this.media='all'">

<noscript>{{ 'section-main-page.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'component-rte.css' | asset_url | stylesheet_tag }}</noscript>

<div class="page-width page-width--narrow">
  <div class="py-8">
    <h1 class="main-page-title page-title h1 text-2xl poppins leading-snug mb-16 mt-0">
      {{ page.title | escape }}
    </h1>
    <div class="rte">
      {{ page.content }}
    </div>
  </div>
</div>

{% if section.settings.additional_scripts != blank %}
  {{ section.settings.additional_scripts }}
{% endif %}

{% schema %}
{
  "name": "t:sections.main-page.name",
  "tag": "section",
  "class": "spaced-section",
  "settings": [
    {
      "type": "header",
      "content": "Additional Scripts"
    },
    {
      "type": "html",
      "id": "additional_scripts",
      "label": "Additional Scripts"
    }
  ]
}
{% endschema %}
