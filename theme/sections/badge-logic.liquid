{% schema %}
{
  "name": "Badge Logic",
  "settings": [
    
  ],
  "blocks": [
    {
      "type": "rule",
      "name": "Rule",
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "Collection"
        },
        {
          "type": "text",
          "id": "hide_badges",
          "label": "Hide Badges"
        }
      ]
    }
  ]
}
{% endschema %}
<style>
    {% for block in section.blocks %}
    {% if block.settings.collection == collection.handle %}
    {% assign badges = block.settings.hide_badges | split: ',' %}
    {% for badge in badges %}
    {% assign badge = badge | strip %}
    #searchspring-content [data-badge="{{badge}}"] {display:none !important;}
    {% endfor %}
    {% endif %}
    {% endfor %}
</style>
