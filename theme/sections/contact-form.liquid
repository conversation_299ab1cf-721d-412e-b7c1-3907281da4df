{{ 'section-contact-form.css' | asset_url | stylesheet_tag }}

<div class="contact page-width page-width--narrow spaced-section">
  <h2 class="title">{{ section.settings.heading | escape }}</h2>
  {%- form 'contact', id: 'ContactForm' -%}
    {%- if form.posted_successfully? -%}
      <div class="form-status form-status-list form__message" tabindex="-1" autofocus>{{ 'templates.contact.form.post_success' | t }}</div>
    {%- elsif form.errors -%}
      <div class="form__message">
        <h2 class="form-status caption-large" role="alert" tabindex="-1" autofocus>{{ 'templates.contact.form.error_heading' | t }}</h2>
      </div>
      <ul class="form-status-list caption-large" role="list">
        <li>
          <a href="#ContactForm-email" class="link">
            {{ form.errors.translated_fields['email'] | capitalize }} {{ form.errors.messages['email'] }}
          </a>
        </li>
      </ul>
    {%- endif -%}
    <div class="contact__fields">
      <div class="field">
        <input class="field__input" autocomplete="name" type="text" id="ContactForm-name" name="contact[{{ 'templates.contact.form.name' | t }}]" value="{% if form.name %}{{ form.name }}{% elsif customer %}{{ customer.name }}{% endif %}" placeholder="{{ 'templates.contact.form.name' | t }}">
        <label class="field__label" for="ContactForm-name">{{ 'templates.contact.form.name' | t }}</label>
      </div>
      <div class="field field--with-error">
        <input
          autocomplete="email"
          type="email"
          id="ContactForm-email"
          class="field__input"
          name="contact[email]"
          spellcheck="false"
          autocapitalize="off"
          value="{% if form.email %}{{ form.email }}{% elsif customer %}{{ customer.email }}{% endif %}"
          aria-required="true"
          {% if form.errors contains 'email' %}
            aria-invalid="true"
            aria-describedby="ContactForm-email-error"
          {% endif %}
          placeholder="{{ 'templates.contact.form.email' | t }}"
        >
        <label class="field__label" for="ContactForm-email">{{ 'templates.contact.form.email' | t }} <span aria-hidden="true">*</span></label>
        {%- if form.errors contains 'email' -%}
          <small class="contact__field-error" id="ContactForm-email-error">
            <span class="sr-only">{{ 'accessibility.error' | t }}</span>
            <span class="form__message">{{ form.errors.translated_fields['email'] | capitalize }} {{ form.errors.messages['email'] }}</span>
          </small>
        {%- endif -%}
      </div>
    </div>
    <div class="field">
      <input type="tel" id="ContactForm-phone" class="field__input" autocomplete="tel" name="contact[{{ 'templates.contact.form.phone' | t }}]" pattern="[0-9\-]*" value="{% if form.phone %}{{ form.phone }}{% elsif customer %}{{ customer.phone }}{% endif %}" placeholder="{{ 'templates.contact.form.phone' | t }}">
      <label class="field__label" for="ContactForm-phone">{{ 'templates.contact.form.phone' | t }}</label>
    </div>
    <div class="field">
      <textarea
        rows="10"
        id="ContactForm-body"
        class="text-area field__input"
        name="contact[{{ 'templates.contact.form.comment' | t }}]"
        placeholder="{{ 'templates.contact.form.comment' | t }}"
      >
        {{- form.body -}}
      </textarea>
      <label class="form__label field__label" for="ContactForm-body">{{ 'templates.contact.form.comment' | t }}</label>
    </div>
    <div class="contact__button">
      <button type="submit" class="button">
        {{ 'templates.contact.form.send' | t }}
      </button>
    </div>
  {%- endform -%}
</div>

{% schema %}
{
  "name": "t:sections.contact-form.name",
  "tag": "section",
  "class": "spaced-section",
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "default": "Contact form",
      "label": "Heading"
    }
  ],
  "presets": [
    {
      "name": "t:sections.contact-form.presets.name"
    }
  ]
}
{% endschema %}
