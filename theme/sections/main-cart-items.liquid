
<div class="px-0 mx-auto cart__container container-narrow lg:px-16 max-w-screen-2xl">
  <section class="relative flex flex-col h-full lg:pt-4 lg:pb-16" neptune-liquid="{topic:cart, source:cart}">

    <div class="flex flex-row flex-wrap justify-between cart-wrapper lg:pt-8">

      <div class="relative w-full cart-body lg:w-3/5 lg:pr-4 lg:px-4 lg:pl-0">

        <h2 class="px-4 text-base tracking-normal font-body">{{ 'templates.cart.cart' | t }}</h2>

      {% raw %}

        {% unless items.size > 0 %}
        <p class="px-4 text-base tracking-normal font-body">
          {% endraw %}
          {{ 'templates.cart.cart_empty' | t }}
          {% raw %}
        </p>
        {% endunless %}

        {% for item in items %}

        {% unless item.properties._bundle == 'component' %}

        <article tabindex="0" class="flex flex-wrap items-start justify-start p-4 m-2 border-b border-light-gray lg:flex-nowrap" data-cart-item-index="{{forloop.index0}}">

          <div class="w-32 pb-4 cart_item_img">
            <a {{'h'}}ref="{{ item.url }}&edit={{item.key}}" class="block">
              {% if item.properties._image %}
                <img {{'s'}}rc="{{ item.properties._image }}" alt="{{ item.product_title }}" class="object-cover w-full inline-img">
              {% else %}
                <img {{'s'}}rc="{{ item.image }}" alt="{{ item.product_title }}" class="object-cover w-full inline-img">
              {% endif %}
            </a>
          </div>

          <div class="flex items-start justify-between w-full lg:pl-4">

            <a {{'h'}}ref="{{ item.url }}&edit={{item.key}}" class="flex flex-col no-underline black">

              {% assign product_title = item.product_title | split: " - " %}
              <div class="mb-3">
                <h2 class="m-0 text-sm tracking-normal uppercase">
                  {{ product_title | first }}
                </h2>
                <h3 class="m-0 text-sm font-normal font-body">{{ product_title[1] }}</h3>
              </div>

              <div class="mb-3">

                {% assign price = item.final_price %}
                {% endraw %}
                {% render 'price-removal' %}
                {% raw %}
                <h4 class="m-0 text-sm font-normal leading-normal font-body">{{price | money: 'local' | remove: removal }}</h4>

                {% for option in item.options_with_values %}
                  {% unless option.name == "Title" %}
                    <h3 class="m-0 text-xs font-normal leading-normal font-body">{{ option.name }}: {{ option.value }}</h3>
                  {% endunless %}
                {% endfor %}
              </div>
              {%- comment -%}
              {% for prop in item.properties %}
              {% assign pk1 = prop | first | slice: 0 %}
              {% unless pk1 == '_' %}
              <h3 class="leading-normal">{{prop | first}}: {{prop|last}}</h3>
              {% endunless %}
              {% endfor %}
              {%- endcomment -%}

              {%- comment -%}
              {% assign price = item.final_price %}

              {% if item.properties._bundle == 'primary' %}

              {% assign reverse_items = items | reverse %}
              {% assign showIncludes = false %}
              {% for subitem in reverse_items %}
                {% if subitem.properties._bundle == 'component' and subitem.properties._instance == item.properties._instance %}
                  {% assign showIncludes = true %}
                {% endif %}
              {% endfor %}
              {% if showIncludes == true %}
                <p class="mt-2 mb-0 font-normal">Includes</p>
              {% endif %}
              
              {% for subitem in reverse_items %}
                {% if subitem.properties._bundle == 'component' and subitem.properties._instance == item.properties._instance %}
                  
                  <p class="mb-0 text-sm silver">{{subitem.title}} + {{subitem.final_price | money }}</p>

                  {% assign price = price | plus: subitem.final_price %}

                {% endif %}
              {% endfor %}
              {% else %}

              {% endif %}

              <h4 class="mt-0 mb-4 text-base font-normal leading-normal">{{price | money  }}</h4>
              {%- endcomment -%}

            </a>

            {% unless item.properties._gift or item.selling_plan_allocation %}
            <div data-cart-item-tools class="flex flex-row items-center m-0 text-center border border-black rounded-sm" style="width: 86px; height: 28px;">
              <button class="w-2/6 h-full text-base border-none cursor-pointer bg-near-white flex-fill" 
                onclick="Neptune.cart.changeItem({{forloop.index0}},{{item.quantity}}-1)">-</button>
              <input class="w-2/6 h-full px-0 text-base text-center border-none bg-near-white flex-fill" type="text" 
                onblur="Neptune.cart.changeItem({{forloop.index0}},this.value)" value="{{item.quantity}}"></input>
              <button class="w-2/6 h-full text-base border-none cursor-pointer bg-near-white flex-fill" 
                onclick="Neptune.cart.changeItem({{forloop.index0}},{{item.quantity}}+1)">+</button>
            </div>
            {% endunless %}

            {% unless item.properties._gift and product_has_only_default_variant %}
            <button class="p-0 py-2 text-base text-black underline border-none cursor-pointer bg-near-white tracked" onclick="Neptune.cart.changeItem({{forloop.index0}},0)">
              <span class="">Remove</span>
            </button>
            {% endunless %}

          </div>

        </article>

        {% endunless %}
        {% endfor %}
      </div>

      <footer class="w-full p-4 cart-footer lg:w-1/3 lg:p-0">
        <div class="bg-light-grey">

          <!-- <h4 class="mt-0 mb-4 text-xl font-bold leading-none f-subheadline poppins lg:text-3xl lg:mb-16">Summary</h4> -->

          <article tabindex="0" class="flex flex-wrap items-center">

            {% assign cartHasSubscription = false %}
            {% for item in items %}
            {% if item.selling_plan_allocation %}
              {% assign cartHasSubscription = true %}
            {% endif %}
            {% endfor %}

            <!-- <p class="">Shipping and taxes calculated at checkout.</p> -->

            {% endraw %}
            {% render 'free-shipping-message', class: 'pt-4 text-sm lh-solid text-right underline ml-auto' %}
            {% raw %}

            {% endraw %}
            {% assign persistentMessage = 'general.persistent_messaging.persistent_message' | t %}

            {% if persistentMessage != blank %}
              <div id="persistentMessage" class="mb-4" style="color: {{ settings.persistent_messaging_color }}; font-weight: {{ settings.persistent_messaging_weight }};">{{ persistentMessage }}</div>
            {% endif %}
            {% raw %}

            {% if items.size > 0 %}
            <div class="flex items-center justify-between w-full no-underline black">
              <h4 class="my-0 leading-normal tracking-normal text-left uppercase">Subtotal</h4>
              <h4 class="my-0 font-normal leading-normal tracking-normal text-right font-body">{{items_subtotal_price | money: 'local' | remove: removal }}</h4>
            </div>
            {% endif %}


          </article>

          <div class="py-4">
            {% if items.size > 0 %}
            <button onclick="document.location='{% endraw %}{% unless routes.root_url == '/' %}{{ routes.root_url }}{% endunless %}{% raw %}/checkout'" class="block w-full mb-4 btn">Checkout</button>
            {% endif %}
            {% endraw %}
            <a href="/{{ routes.url }}" class="block w-full btn btn--outline hover:text-white">Keep Shopping</a>
            {% raw %}
          </div>

      </footer>

    </div>
    {% endraw %}

  </section>

</div>

{% style %}
  .template-cart .cart__close, .template-cart button.bg-secondary {
    display: none;
  }
{% endstyle %}

{% schema %}
  {
    "name": "Cart",
    "settings": [],
    "blocks": [
      { 
        "type": "gift",
        "name": "Gift",
        "settings": [
          {
            "type": "paragraph",
            "content": "Each gift must also be configured in the published Line Items script in the Shopify Script Editor. Product must also be tagged with GWP in order for Shopify Scripts to validate it's price reduction to avoid fradulent frontend hacking."
          },
          {
            "id": "title",
            "type": "text",
            "label": "Title",
            "info": "For internal organization only"
          },
          {
            "id": "product",
            "type": "product",
            "label": "Gift Product"
          },
          {
            "id": "threshold_cents",
            "type": "text",
            "label": "Price threshold (cents)",
            "info": "Leave blank for none"
          },
          {
            "id": "combineable",
            "type": "checkbox",
            "label": "May be combined with other GWP tiers"
          }
        ]
      }
    ]
  }
{% endschema %}

<script>
  CCS.Cart = {}
  CCS.Cart.settings = {{section.settings | json}};
  CCS.Cart.blocks = [
    {%- for block in section.blocks -%}
    {
      "type":{{block.type|json}},
      {% if block.settings.product != blank %}"product":{{ all_products[block.settings.product] | json}},{% endif %}
      "settings": {{ block.settings | json}}
    }{% unless forloop.last %},{% endunless %}
    {%- endfor -%}
  ];
</script>
