<script src="{{ 'searchspring.js' | asset_url }}" defer></script>
<h1 class="h5 text-center border-b-2 border-white m-0 p-1 {% unless section.settings.show_title %}sr-only{% endunless %}">{{ collection.title }}</h1>

{%- render 'unisex-collection' -%}
{%- render 'ss-localized-prices' -%}

<div skeleton class="collection-skeleton collection__product mx-0 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 bg-nearest-white p-4 gap-4" >
  {% for i in (1..12)  %}
    {% render 'product-item-skeleton' %}
  {% endfor %}
</div>

{% if settings.ss_enable %}
  <div id="searchspring-content" >
{% endif %}
      
  {%- paginate collection.products by section.settings.grid_items_per_page -%}

  <div class="collection-grid flex flex-row flex wrap items-start ph-4 lg:pl-8 lg:pr-0"></div>

  {%- render 'pagination' -%}

  {%- endpaginate -%}

{% if settings.ss_enable %}
  </div>
{% endif %} 


<!-- <div collection-injection="{page:1,position:4}">Inject me at page 1 position 4</div> -->
{% style %}
  [collection-injection]:not([injected]){display: none;}.pi__photo-hover {margin-top: 1px;}
  .quick-shop [data-available=""] ~ *,
  .quick-shop [data-available=""] ~ * *,
  .quick-shop [data-available].unavailable, 
  .quick-shop [data-available].unavailable ~ *,
  button[disabled] {
    opacity:.5;
    text-decoration: line-through;
    pointer-events: none;
  }

  {% if collection.handle contains 'new-arrivals' %}
    [data-badge="new"] {
      display: none !important;
    }
  {% endif %}


{% endstyle %}
<script>
  window.inventory = {
  message: {{settings.collection_lowstock_message|json}},
  threshold: {{settings.variant_lowstock_threshold}},
  products: {
    {%- for product in collection.products -%}
    {% if product.variants.size > 2 %}
    {{product.handle|json}}:{
      {%- for variant in product.variants -%}
      {{variant.option2|json}}:{{variant.inventory_quantity}}
      {%- unless forloop.last %},{%- endunless -%}
      {%- endfor -%}  
    }
    {%- unless forloop.last %},{%- endunless -%}
    {% endif %}
    {%- endfor -%}  
  }
};
</script>

{% schema %}
{
  "name": "Collections",
  "class": "ss-collection-section",
  "settings": [
    {
      "type": "header",
      "content": "Display Settings"
    },
    {
      "type": "checkbox",
      "id": "show_title",
      "label": "Show title",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "filter",
      "name": "Filter Set",
      "settings": [
        {
          "type": "text",
          "id": "background_color",
          "label": "Background Color"
        }
      ]
    }
  ]
}
{% endschema %}
