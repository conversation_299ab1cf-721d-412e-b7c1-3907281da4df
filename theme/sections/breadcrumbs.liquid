<div class="blog__breadcrumbs {% if section.settings.show_desktop == true and section.settings.show_mobile == true %} block {% elsif section.settings.show_desktop == true and section.settings.show_mobile == false %} hidden lg:block {% elsif section.settings.show_desktop == false and section.settings.show_mobile == false %} hidden {% elsif section.settings.show_desktop == false and section.settings.show_mobile == true %} block lg:hidden {% endif %}">
	{% render 'breadcrumbs' %}
</div>

{% style %}
	.blog__breadcrumbs, .blog__breadcrumbs a {
		color: {{ section.settings.color }};
	}
{% endstyle %}

{% schema %}
{
  "name": "Breadcrumbs",
  "settings": [
    {
      "type": "header",
      "content": "Display Settings"
    },
    {
      "type": "checkbox",
      "id": "show_desktop",
      "label": "Show desktop",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_mobile",
      "label": "Show mobile",
      "default": true
    },
    {
      "type": "header",
      "content": "Color Settings"
    },
    {
      "type": "color",
      "id": "color",
      "label": "Text color"
    }
  ]
}
{% endschema %}