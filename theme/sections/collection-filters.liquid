{% comment %} 


This is generated by webpack so don't edit this unless you want to be sad when your work gets overwritten in production.
You can edit this file in ../../components/** 


{% endcomment %}






<script> window.filter_delimiter = {{ section.settings.tag_delimiter | json }}</script>
<div id="collectionFilters" 
 x-neptune-permanent 
 neptune-transport="{ prepend:.collection__filters_wrap }" 
 class="-translate-x-full active:translate-x-0 bg-gray-100 md:bg-transparent fixed md:sticky md:self-start md:h-auto md:top-20 inset-0 md:p-0 md:transform-none md:w-1/4 p-4 transform transition-transform z-40 md:z-0 overflow-scroll md:overflow-auto"
 dynamic-aria-hidden
>
    <div class="">
      <button 
      class="filters__mobile_close md:hidden block ml-auto"
      neptune-engage='{
        on:click,
        targets:[
        {
          selector: "#collectionFilters",
          "classes":{
            "toggle":"active"
          },
          "attributes": [
          {
            "att": "aria-hidden",
            "toggle":["true", "false"]
          }]
        },
        {
          selector:.main-content,
          classes:add:z-1
        },
        {
          selector:html,
          attributes:[{
            att:data-active-modal-mobile
            set:_remove
          }]
        },
        {
          selector:"[data-return-focus]"
          attributes:[{
            att:data-return-focus 
            set:_remove
          }]
          focus:true
        }]
      }'>
      {% render 'icon', icon: 'close' %}
    </button>
      
    
    <aside class="collection-filters{% if search.performed %}o-0{% endif %}">  

      <form data-collection-filters id="collection-filters" class="active">
        <h2 class="font-body text-base"><span class="">Sort By:</span></h2>
        <select class="bg-transparent mb-4 border border-gray-400 font-body p-2 w-full" onchange="Collection.sort(this.value)">
          {% for option in collection.sort_options %}
          <!-- <option value="{{ option.value }}"{% if option.value == collection.sort_by %} selected{% endif %}>{{ option.name }}</option> -->
        {% endfor %}
          <option value="featured">Most Relevant</option>
          <option value="best-selling">Best Selling</option>
          <option value="created-descending">Newest</option>
          <option value="created-ascending">Oldest</option>
          <option value="title-ascending">A-Z</option>
          <option value="title-descending">Z-A</option>
          <option value="price-ascending">Low to High</option>
          <option value="price-descending">High to Low</option>
        </select>
     
        {% if section.settings.title != blank %}
        <h2 class="font-body text-base"><span class="">{{section.settings.title}}:</span></h2>
        {% endif %}

        <div class="ph3 flex flex-wrap mb-4" neptune-liquid="{topic: activeFilters}">
          {% raw %}
            {% assign current_filters_count = 0 %}
            {% for filter in Filters.data %}
              {% for tag in filter[1].tags %}
                {% assign current_filters_count = current_filters_count | plus: 1 %}
                <div class="bg-white flex items-center justify-center rounded-full mr-2 mb-2">
                  <span class="pl-4 py-2">
                    {{- tag | split: section.settings.tag_delimiter | last | url_decode -}}
                  </span>
                  <button
                    class="px-4 py-2"
                    onclick="Filters.clear({set: '{{ filter[0] }}', value: '{{ tag | url_decode}}' })"
                  >
                  <span class="sr-only">
                    Remove {{ tag | split: section.settings.tag_delimiter | last | url_decode }} Filter
                   </span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-x"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                </button>
                </div>
              {% endfor %}
            {% endfor %}

            {% if current_filters_count != 0 %}
              <button onclick="Filters.clear(); return false;" class="bn bg-near-white ph3 pv1 mb2 mr2">Clear All Filters</button>
            {% endif %}
          {% endraw %}
        </div>

        <div id="filterSets" class="flex flex-column flex-wrap animate overflow-y-hidden-l pa3">

          {% for link in linklists[section.settings.subnav].links %}
            {% if link.object == collection %}

            <div class="filter-set b--light-gray mb2 gray bb bg-white w-100 relative filter-format-{{block.settings.format}} f6 {% if block.settings.format == 'range' %} mb4 {% endif %}">

              <h3 
                id="{{ link.title | handle }}_filter_title"
                class="pointer filter-title tracked lh-solid flex flex-wrap db pv3"
                aria-controls="{{ link.title | handle }}_filter_container" aria-expanded="false"
                neptune-engage='{
                  on:click,
                  targets: [
                    {
                      selector: "_self",
                      "attributes": [
                        {
                          "att": "aria-expanded",
                          "toggle":["true", "false"]
                        }
                      ]
                    },
                    {
                      selector: "#{{ link.title | handle }}_filter_container",
                      "classes":{
                        "toggle":"active"
                      },
                      "attributes": [
                      {
                        "att": "aria-hidden",
                        "toggle":["true", "false"]
                      }]
                    }
                  ]
                }'
              >
                {{link.title}}
              </h3>

              <ul 
                id="{{ link.title | handle }}_filter_container" 
                class="filter-options {% if optionCount > 9 %}overflow-y-scroll-l h5-l {% endif %}ma0 list pa0 {% if filter_type == 'swatch' %}grid grid-cols-3 grid-gap-1{%endif%}"
                aria-controlledby="{{ link.title | handle }}_filter_title" aria-hidden="false"
                >
                {% for sub in link.links %}
                  <li>
                    <a href="{{ sub.url }}" class="ml-2 my-2">{{ sub.title }}</a>
                  </li>
                {% endfor %}
                
              </ul>

            </div>


             
            {% endif %}
          {% endfor %}

          {%- for block in section.blocks -%}
            
            {%- if block.type == 'set' %}

            {% unless search.performed %}

              {% if block.settings.collection_exclusion contains collection.handle %}
                {% continue %}
              {% endif %}

              {% if block.settings.collection_inclusion != blank %}
                {% unless block.settings.collection_inclusion  contains collection.handle %}
                  {% continue %}
                {% endunless %}
              {% endif %}

            {% endunless %}

            {% if block.settings.collection_exclusion contains 'search' and search.performed %}
              {% continue %}
            {% endif %}
              
              {%- assign optionCount = 0-%}

              {% capture filterSetContents %}
                
                {%- for filter in section.blocks offset:forloop.index-%}
                  
                  {%- if filter.type == 'set' %}{% break %}{%- endif -%}

                  {% assign format = 'checkbox' %}
                  
                  {% if block.settings.format == 'radio' %}
                  {% assign format = 'radio' %}
                  {%- endif -%}
                  {% if block.settings.format == 'links' %}
                  {% assign format = 'links' %}
                  {%- endif -%}
                  {% if block.settings.format == 'collection_links' %}
                  {% assign format = 'collection_links' %}
                  {%- endif -%}

                  {% assign sort = false %}

                  {% assign filter_type = filter.type %}
                  
                  {%- if filter.type == 'option' and collection.all_tags contains filter.settings.value %}

                    {%- assign optionCount = optionCount | plus:1-%}

                    {% assign tag = filter.settings.value %}
                    {% assign key = tag | split: section.settings.tag_delimiter | first %}
                    {% assign field = 'tags' %}
                    {% if tag contains section.settings.tag_delimiter %}
                      {% assign value = tag | split: section.settings.tag_delimiter | last %}
                    {% else %}
                      {% assign value = tag %}
                    {% endif %}
                    {% assign display = filter.settings.label %}
                    {% assign group = block.settings.title | handle %}

                    {% render 'collection-filter-option' format:format, value:value, field:field, key:key, units:block.settings.units, group:group, constraint:block.settings.product_type, display:display, strip:strip %}
                  
                  {%- endif -%}
                  
                  {%- if filter.type == 'swatch' and collection.all_tags contains filter.settings.value %}

                    {%- assign optionCount = optionCount | plus:1-%}

                      <li class="flex filter-option mb2 justify-stretch items-stretch  relative">
                        {% if format == 'links' %}
                          <a href="{{ collection_url }}/{{ filter.settings.value }}" class="ml-2 my-2">
                            <div class="w-50 br-pill mb2 center" style="background-color: {{ filter.settings.color }}; padding-bottom: 50%"></div>
                            <span>{{filter.settings.label}}</span>
                          </a>
                        {% else %}
                          <input type="{{format}}" data-field="{% if filter.settings.value contains section.settings.tag_delimiter %}{{ filter.settings.value | split: section.settings.tag_delimiter | first }}~tags{% endif %}" name="{{block.settings.title|handle}}" value="{{ filter.settings.value | split: section.settings.tag_delimiter | last }}" data-constraint="{{block.settings.product_type}}" id="{{block.settings.title | append: filter.settings.label | append: filter.settings.value | handle}}"  class="hidden">
                          <label for="{{block.settings.title | append: filter.settings.label | append: filter.settings.value | url_decode | handle}}" class="filter-swatch-wrapper my-2">
                            <div class="filter-swatch-dot" style="background-color: {{ filter.settings.color }};"></div>
                            <span>{{filter.settings.label}}</span>
                          </label>
                        {% endif %}
                      </li>
                  
                  {%- endif -%}

                  {{}}

                  {%- if filter.type == 'dynamic' %}


                    {% assign link_template = false %}
                    {% if block.settings.link_prefix != blank and format == 'links' %}
                      {% assign prefixed = collection.handle | prepend: block.settings.link_prefix | handle %}
                      {% for tag in collection.all_tags %}
                        {% assign handlized_tag = tag | handle %}
                        {% if handlized_tag == prefixed %}
                          {% assign link_template = '**VALUE**' | remove: block.settings.link_prefix | prepend: '/collections/' | append: '/' | append: collection.handle %}
                        {% break %}{% endif %}
                      {% endfor %}
                    {% endif %}

                    {% for tag in collection.all_tags %}

                      {% assign matchtag = tag | downcase %}
                      {% assign matchinc = filter.settings.inclusion | downcase %}
                    
                      {% if matchtag contains matchinc %}

                        {%- assign optionCount = optionCount | plus: 1 -%}

                        {% assign key = tag | split: section.settings.tag_delimiter | first %}
                        {% assign field = block.settings.field %}
                        {% if tag contains section.settings.tag_delimiter %}
                          {% assign value = tag | split: section.settings.tag_delimiter | last %}
                        {% else %}
                          {% assign value = tag %}
                        {% endif %}
                        {% assign valuearray = tag | split: filter.settings.delimiter %}
                        {% assign valueindex = filter.settings.display | minus: 1%}
                        {% assign display = valuearray[valueindex] | remove: filter.settings.strip_label %}

                        {% assign group = block.settings.title | handle %}
                        {% assign strip = filter.settings.strip %}

                        {% assign the_format = format %}

                        {% if link_template %}
                          {% assign value = value | handle %}
                          {% assign value = link_template | replace: '**VALUE**', value %}
                          {% assign the_format = 'templated_link' %}
                        {% endif %}

                        {% render 'collection-filter-option' format:the_format, value:value, field:field, key:key, units:block.settings.units, group:group, constraint:block.settings.product_type, display:display, strip:strip %}

                      {% endif %}
                
                    {%- endfor -%}

                    {% if filter.settings.sort_order != blank %}
                      {% assign sort = filter.settings.sort_order %}
                    {% endif %}
                  
                  {%- endif -%}

                  {%- if block.settings.field == 'vendor' %}

                    {% for vendor in collection.all_vendors %}

                      {%- assign optionCount = optionCount | plus: 1 -%}

                      {% render 'collection-filter-option' format:format, value:vendor field:block.settings.field, units:block.settings.units, group:'vendor' %}
                
                    {%- endfor -%}
                  
                  {%- endif -%}

                  {%- if block.settings.field == 'type' %}

                    {% for type in collection.all_types %}

                      {%- assign optionCount = optionCount | plus: 1 -%}

                      {% render 'collection-filter-option' format:format, value:type field:block.settings.field, units:block.settings.units, group:type %}
                
                    {%- endfor -%}
                  
                  {%- endif -%}

                {%- endfor -%}
                
              {% endcapture %}

              {% assign filterSetSplit = filterSetContents | split:'filter-option'  %}
              {% assign filterOptionCount = filterSetSplit.size | minus: 1 %}

              {% if filterOptionCount >= block.settings.minimum_values %}

              <details class="border-b group py-2 w-full" open>
                <summary class="cursor-pointer flex items-center justify-between w-full">
                  {{ block.settings.title }}
                  <span class="group-active:hidden">
                    {% render 'icon', icon: 'plus' %}
                  </span>
                  <span class="group-active:block hidden">
                    {% render 'icon', icon: 'minus' %}
                  </span>
                </summary>
                <ul 
                  id="{{ block.settings.title | handle }}_filter_container" 
                  class="filter-options m-0"
                  aria-controlledby="{{ block.settings.title | handle }}_filter_title" aria-hidden="false"
                  {% if sort %}neptune-sort='{on:innerText.trim(),as:alpha,reverse:false,order:{{sort|split:","|json}} }'{% endif %}
                  >
                  {{ filterSetContents }}
                </ul>
              </details>
              {%- endif -%}

            {%- endif -%}

          {%- endfor -%}

        </div>

      </form>


    </aside>

  </div>


{% schema %}
{
  "name": "Collection Filters",
  "settings": [
    {
      "type": "text",
      "label": "Title",
      "id": "title",
      "default": "Refine by"
    },
    {
      "type": "link_list",
      "label": "Subnavigation",
      "id": "subnav"
      },
      {
        "type": "text",
        "label": "Tag Delimiter",
        "id": "tag_delimiter",
        "default": ":",
        "info": "This is the character that separates the tag names for filters."
      }
  ],
  "blocks": [
    {
      "type": "set",
      "name": "Filter Set",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Filter Set Title"
        },
        {
          "type": "select",
          "label": "Field",
          "id": "field",
          "default": "tags",
          "options": [
            {
              "label": "Tags",
              "value": "tags"
            },
            {
              "label": "Vendor",
              "value": "vendor"
            },
            {
              "label": "Product Type",
              "value": "type"
            }
          ]
        },
        {
          "type": "text",
          "label": "Tag Prefix",
          "id": "namespace"
        },
        {
          "type": "text",
          "label": "Product Type Restriction",
          "id": "product_type"
        },
        {
          "type": "text",
          "label": "Collection Inclusion",
          "id": "collection_inclusion",
          "info": "Only display on Collections as listed above in comma-separated handles"
        },
        {
          "type": "text",
          "label": "Collection Exclusion",
          "id": "collection_exclusion",
          "info": "Do not display on Collections listed above in comma-separated handles"
        },
        {
          "type": "select",
          "label": "Format",
          "id": "format",
          "options": [
            {
              "label": "Checkboxes (select many)",
              "value": "checkboxes"
            },
            {
              "label": "Radio Buttons (select one)",
              "value": "radio"
            },
            {
              "label": "Tag Links",
              "value": "links"
            },
            {
              "label": "Collection Links",
              "value": "collection_links"
            }
          ],
          "info": "For large Collections, it is STRONGLY recommended to use Radio buttons or Tag Links to avoid slow product batch loads"
        },
        {
          "type": "text",
          "label": "Units",
          "id": "units"
        },
        {
          "type": "number",
          "label": "Minimum Values",
          "id": "minimum_values",
          "default": 1
        }
      ]
    },
    {
      "type": "option",
      "name": "Filter Option",
      "settings": [
        {
          "type": "text",
          "id": "label",
          "label": "Filter Label"
        },
        {
          "type": "text",
          "id": "value",
          "label": "Filter Value"
        }
      ]
    },
    {
      "type": "swatch",
      "name": "Filter Option Swatch",
      "settings": [
        {
          "type": "text",
          "id": "label",
          "label": "Filter Label"
        },
        {
          "type": "text",
          "id": "value",
          "label": "Filter Value"
        },
        {
          "type": "color",
          "id": "color",
          "label": "Filter Swatch Color",
          "default": "#000"
        }
      ]
    },
    {
      "type": "dynamic",
      "name": "Dynamic Filter Options",
      "settings": [
        {
          "type": "text",
          "id": "inclusion",
          "label": "Tag Inclusion"
        },
        {
          "type": "text",
          "id": "delimiter",
          "label": "Tag Delimeter",
          "default": ":"
        },
        {
          "type": "number",
          "id": "display",
          "label": "Display Segment"
        },
        {
          "type": "text",
          "id": "strip",
          "label": "Strip Segment from Link"
        },
        {
          "type": "text",
          "id": "strip_label",
          "label": "Strip Segment from Label"
        },
        {
          "type": "textarea",
          "id": "sort_order",
          "label": "Sort Order"
        }
      ]
    }
  ]
}
{% endschema %}
