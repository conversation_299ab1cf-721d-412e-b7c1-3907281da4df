<footer class="footer-section" data-section-id="{{ section.id }}" data-section-type="footer-section">
  {% style %}
    .footer-section {
      background-color: {{ section.settings.footer_bg_color }};
      color: {{ section.settings.footer_text_color }};
    }
    .footer-section a, .footer-section p, .footer-section .font-subheading {
      color: {{ section.settings.footer_text_color }};
    }
  {% endstyle %}
  <div id="footer-wrapper" class="mx-auto">

    <div id="footer" class="row flex lg:flex-row flex-col flex-wrap pt-10 px-0 lg:px-10 lg:pt-16 pb-0">
      <div class="w-full flex lg:flex-row flex-col justify items-stretch">
        {% assign count = 0 %}
        {% for block in section.blocks %}
          {% assign count = count | plus: 1 %}
          {% case block.type %}
          {% when 'menu' %}
          <div class="footer-menu flex flex-col lg:flex-grow lg:basis-0 {{ block.settings.item_order }} {{ block.settings.item_order_mobile }} {% if block.settings.apply_max_space %}lg:ml-auto{% endif %}">
            <ul class="list-none m-0 lg:mr-20 p-0" {{ block.shopify_attributes }}>
              {% for link in linklists[block.settings.footer_nav].links %}
                <li class="flex-auto flex-basis-0 p-0 lg:mb-4">
                  {% if link.links != blank %}
                  <div class="group w-full m-0 border-b-2 lg:border-b-0 border-white lg:mb-0" n:open="{% raw %} '_remove' | lg: 'true' {% endraw %}">
                    <div
                      neptune-engage="{on:click, targets: {selector:_parent, classes:toggle:active}}"
                      class="flex lg:cursor-default cursor-pointer items-center justify-between w-full border-none bg-transparent color-white font-normal accordion-title py-5 lg:py-0 lg:pb-1.5 lg:px-0 px-5"
                    >
                      <span class="block font-caption-caps leading-relaxed">{{ link.title }}</span>
                      <span class="w-8 text-right text-currentColor icon-plus lg:hidden flex justify-end group-active:hidden">
                        {% render 'icon', icon: 'plus' width:18 height:18 %}
                      </span>
                      <span class="w-8 text-right text-currentColor icon-minus lg:hidden hidden justify-end group-active:flex">
                        {% render 'icon', icon: 'minus' width:18 height:18 %}
                      </span>  
                    </div> 
                    <ul class="list-none m-0 p-0 max-h-0 lg:max-h-full group-active:max-h-full overflow-hidden lg:px-0 px-5">
                      {%- for child_link in link.links -%}
                        <li class="flex-auto flex-basis-0 p-0 last:pb-5 last:lg:pb-0">
                          {%- liquid
                            assign external_link = false
                            if child_link.type == "http_link" 
                              unless child_link.url contains '/vendors?' or child_link.url contains '/types?' or child_link.url contains '/pages/'
                                assign external_link = true
                              endunless
                            endif
                          -%}
                          {% if child_link.title == 'Accessibility' %}
                            {% if settings.accessibe %}
                            <span class="cursor-pointer block leading-relaxed text-base" data-acsb-custom-trigger="true">
                              {{ child_link.title }}
                            </span>
                            {% endif %}
                          {% elsif child_link.title == block.settings.highlight_link %}
                            <h4 class="text-base m-0">
                              <a class="block leading-relaxed text-base" href="{{ child_link.url }}" {% if child_link.active %}aria-current="page"{% endif %}{% if external_link %} target="_blank"{% endif %}>
                                {{ child_link.title }}
                              </a>
                            </h4>
                          {% else %}
                            <a class="block leading-relaxed text-base" href="{{ child_link.url }}" {% if child_link.active %}aria-current="page"{% endif %}{% if external_link %} target="_blank"{% endif %}>
                              {{ child_link.title }}
                            </a>
                          {% endif %}
                        </li>
                      {%- endfor -%}
                    </ul>
                  </div>
                  {% else %}
                    {%- liquid
                      assign external_link = false
                      if link.type == "http_link" 
                        unless link.url contains '/vendors?' or link.url contains '/types?' or link.url contains '/pages/'
                          assign external_link = true
                        endunless
                      endif
                    -%}
                    <a class="block hover:opacity-70 leading-normal text-sm" href="{{ link.url }}"{% if external_link %} target="_blank"{% endif %}>
                      {{ link.title }}
                    </a>
                  {% endif %}
                </li>
              {% endfor %}
            </ul>
          </div>
          {% when 'company' %}
            <section class="footer-company m-0 flex flex-col items-start lg:flex-grow lg:basis-0 lg:py-0 py-5 lg:px-0 px-5 {{ block.settings.item_order }} {{ block.settings.item_order_mobile }}">
              {% if block.settings.logo != blank or block.settings.logoSVG != blank  %}
                <div class="footer-logo mb-6" {% if block.settings.logo.width != blank %}style="max-width: {{ block.settings.logo.width }}"{% endif %}>
                  {% if block.settings.logo != blank %}
                    {% render 'lazy-image' image: block.settings.logo, image_class: "block w-full" %}
                  {% elsif block.settings.logoSVG != blank %}
                    {{ block.settings.logoSVG }}
                  {% endif %}
                </div>
              {% endif %}
              {% if block.settings.newsletter_title != blank %}
                <p class="h4 uppercase font-pop m-0 text-sm block w-full lg:text-left text-center">{{ block.settings.newsletter_title }}</p>
              {% endif %}
              {% if block.settings.newsletter != blank %}
              <div class="footer-company--newsletter flex flex-col w-full font-light lg:text-left text-center">
                {{ block.settings.newsletter }}
              </div>
              {% endif %}
              <div id="socialFooter" class="flex lg:flex-row flex-col lg:pt-8 pt-4 w-full">
                {% render 'social-media-links', classes:'m-0 p-0' %}
                {% render 'country-picker' %}
              </div>
            </section>
          {% when 'text' %}
          <details class="group w-full footer-menu lg:pb-4 lg:px-4 m-0 border-b lg:border-b-0 border-gray-400 mb-4 lg:mb-0 flex flex-col lg:flex-grow lg:basis-0 {{ block.settings.item_order }} {{ block.settings.item_order_mobile }}" n:open="{% raw %} '_remove' | lg: 'true' {% endraw %}">
            <summary
              neptune-engage="{on:click, targets: {selector:_parent, classes:toggle:active}}"
              class="flex lg:cursor-default items-center justify-between w-full border-none bg-transparent pb-4 color-white font-normal accordion-button"
            >
              <span class="leading-tight block text-left text-sm">{{ block.settings.title }}</span>
              <span class="w-4 text-right icon text-currentColor icon-plus lg:hidden opacity-0 group-active:opacity-100">
                -
              </span>
              <span class="w-4 text-right icon text-currentColor icon-minus lg:hidden opacity-100 group-active:opacity-0 group-active:hidden">
                +
              {% comment %} </span>   {% endcomment %}
            </summary>
            <div class="rte footer-block-content pb-4 accordion">
              <div class="leading-tight opacity-50 text-sm">{{ block.settings.footer_text }}</div>
            </div>
          </details>
          {% endcase %}
        {% endfor %}
      </div>
    </div>
    <div id="bottom-footer" class="sub-footer lg:flex-row flex-col flex-wrap items-center lg:items-start row lg:text-left text-center lg:pt-0 lg:p-10 py-5 px-4 justify-center lg:justify-start">
      {% if section.settings.show_copyright %}
        <div class="flex lg:flex-row flex-col items-center lg:justify-between">
          <p class="m-0 p-0 text-xs lg:order-1 order-2">
            <a class="inline-block white p-0" href="{{ routes.root_url }}">&copy; {{ shop.name }} {{ 'now' | date: "%Y" }}</a>. All Rights Reserved.
          </p>
          {% if section.settings.footer_copy_nav != blank %}
            <ul class="list-none m-0 p-0 flex lg:flex-row flex-col items-start lg:justify-end text-xs lg:order-2 order-1">
              {% for link in linklists[section.settings.footer_copy_nav].links %}
                <li class="lg:ml-4">
                  {%- liquid
                    assign external_link = false
                    if link.type == "http_link" 
                      unless link.url contains '/vendors?' or link.url contains '/types?' or link.url contains '/pages/'
                        assign external_link = true
                      endunless
                    endif
                  -%}
                  <a class="block hover:opacity-70 animate leading-normal py-3 lg:p-0 text-xs white hover:underline" href="{{ link.url }}"{% if external_link %}target="_blank"{% endif %}>
                    {{ link.title }}
                  </a>
                </li>
              {% endfor %}
            </ul>
          {% endif %}
        </div>
      {% endif %}
    </div>
  </div>
  {% style %}
    .icon-wrap .icon {
      max-width: 100%;
      height: auto;
    }
    @media (max-width: 1023px) {
      .footer-menu.active .list-none {
        max-height: 100%;
      }
    }

    .footer-company--newsletter .klaviyo-form .kl-private-quill-wrapper-Lkqws1.kl-private-quill-wrapper-Lkqws1.kl-private-quill-wrapper-Lkqws1 .ql-container.ql-container.ql-container p {
      letter-spacing: 0.1em !important;
      line-height: 1.375 !important;
      text-transform: uppercase !important;
      font-size: 0.875rem !important;
      font-family: var(--font-body-family) !important;
      margin-bottom: 1rem !important;
    }
    .footer-company--newsletter .klaviyo-form .kl-private-quill-wrapper-Lkqws1.kl-private-quill-wrapper-Lkqws1.kl-private-quill-wrapper-Lkqws1 .ql-container.ql-container.ql-container p strong {
      font-weight: normal !important;
    }
    .footer-company--newsletter .klaviyo-form [type="button"]{
      position: absolute;
      top: 1px;
      right: 100%;
      width: 30px;
      display: block;
      height: 0 !important;
      padding: 28px 0 0 !important;
      overflow: hidden;
    }
    .footer-company--newsletter .klaviyo-form [type="button"]:after{
      position: absolute;
      content: "";
      background-image: url('https://cdn.shopify.com/s/files/1/2094/5935/files/chevron-right_1.svg?v=1663606041');
      background-repeat: no-repeat;
      background-position: center;
      background-size: 20px;
      height: 100%;
      width: 100%;
      right: 0;
      top: 0;
    }
  {% endstyle %}
</footer>

{% schema %}
{
  "name": "Footer",
  "max_blocks": 5,
    "settings": [
     {
        "type": "select",
        "id": "footer_text_align",
        "label": "Footer text align",
        "options": [
          {
            "value": "left",
            "label": "Left"
          },
          {
            "value": "center",
            "label": "Center"
          },
          {
            "value": "right",
            "label": "Right"
          }
        ]
      },
      {
        "type": "checkbox",
        "id": "show_social_icons",
        "label": "Show social icons",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "show_copyright",
        "label": "Show site name and copyright",
        "default": true
      },
      {
        "type": "link_list",
        "id": "footer_copy_nav",
        "label": "Copyright menu",
        "info": "This menu won't show dropdown items"
      },
      {
        "type": "header",
        "content": "Footer Color Settings"
      },
      {
        "type": "color",
        "id": "footer_text_color",
        "label": "Footer text color",
        "default": "#000000"
      },
      {
        "type": "color",
        "id": "footer_bg_color",
        "label": "Footer background color",
        "default": "#F4F4F4"
      }
    ],
    "blocks":[
      {
        "type":"menu",
        "name":"Menu",
        "settings":[
          {
            "type": "text",
            "id": "title",
            "label": "Title",
            "info": "For admin label purposes only."
          },
          {
            "type": "link_list",
            "id": "footer_nav",
            "label": "Footer menu",
            "info": "This menu won't show dropdown items"
          },
          {
            "type": "text",
            "id": "highlight_link",
            "label": "Highlight link",
            "info": "An H4 tag will be added for SEO purposes."
          },
          {
            "type": "header",
            "content": "Item Order Settings"
          },
          {
            "type": "select",
            "id": "item_order",
            "label": "Grid Item Order (Desktop)",
            "options": [
              {
                "value": "lg:order-none",
                "label": "Default"
              },
              {
                "value": "lg:order-1",
                "label": "1"
              },
              {
                "value": "lg:order-2",
                "label": "2"
              },
              {
                "value": "lg:order-3",
                "label": "3"
              },
              {
                "value": "lg:order-4",
                "label": "4"
              },
              {
                "value": "lg:order-5",
                "label": "5"
              },
              {
                "value": "lg:order-6",
                "label": "6"
              }
            ],
            "default": "lg:order-none"
          },
          {
            "type": "select",
            "id": "item_order_mobile",
            "label": "Grid Item Order (Mobile)",
            "options": [
              {
                "value": "order-none",
                "label": "Default"
              },
              {
                "value": "order-1",
                "label": "1"
              },
              {
                "value": "order-2",
                "label": "2"
              },
              {
                "value": "order-3",
                "label": "3"
              },
              {
                "value": "order-4",
                "label": "4"
              },
              {
                "value": "order-5",
                "label": "5"
              },
              {
                "value": "order-6",
                "label": "6"
              }
            ],
            "default": "order-none"
          },
          {
            "type": "checkbox",
            "id": "apply_max_space",
            "label": "Push content right",
            "default": false,
            "info": "Push content to the right on desktop"
          }
        ]
      },
      {
        "type":"company",
        "name":"Company & Newsletter",
        "settings":[
          {
            "type": "header",
            "content": "Logo settings"
          },
          {
            "type": "image_picker",
            "id": "logo",
            "label": "Logo image"
          },
          {
            "id": "logoSVG",
            "label": "Logo SVG",
            "type": "textarea",
            "info": "Paste an inline SVG of your logo."
          },
          {
            "id": "logo_max_width",
            "label": "Logo Max Width",
            "type": "text",
            "info": "Add a pixel or percentage value."
          },
          {
            "type": "header",
            "content": "Newsletter Settings"
          },
          {
            "type": "liquid",
            "id": "newsletter",
            "label": "Newsletter Script"
          },
          {
            "type": "text",
            "id": "newsletter_title",
            "label": "Newsletter title",
            "default": "Sign Up for our Newsletter"
          },
          {
            "type": "header",
            "content": "Item Order Settings"
          },
          {
            "type": "select",
            "id": "item_order",
            "label": "Grid Item Order (Desktop)",
            "options": [
              {
                "value": "lg:order-none",
                "label": "Default"
              },
              {
                "value": "lg:order-1",
                "label": "1"
              },
              {
                "value": "lg:order-2",
                "label": "2"
              },
              {
                "value": "lg:order-3",
                "label": "3"
              },
              {
                "value": "lg:order-4",
                "label": "4"
              },
              {
                "value": "lg:order-5",
                "label": "5"
              },
              {
                "value": "lg:order-6",
                "label": "6"
              }
            ],
            "default": "lg:order-none"
          },
          {
            "type": "select",
            "id": "item_order_mobile",
            "label": "Grid Item Order (Mobile)",
            "options": [
              {
                "value": "order-none",
                "label": "Default"
              },
              {
                "value": "order-1",
                "label": "1"
              },
              {
                "value": "order-2",
                "label": "2"
              },
              {
                "value": "order-3",
                "label": "3"
              },
              {
                "value": "order-4",
                "label": "4"
              },
              {
                "value": "order-5",
                "label": "5"
              },
              {
                "value": "order-6",
                "label": "6"
              }
            ],
            "default": "order-none"
          }
        ]
      },
      {
        "type": "text",
        "name": "Text",
        "settings":[
          {
            "type": "text",
            "id": "title",
            "label": "Text title",
            "default": "About"
          },
          {
            "id": "footer_text",
            "type": "richtext",
            "label": "Text",
            "default": "<p>Adding text to your footer helps introduce your business to new customers.</p>"
          },
          {
            "type": "header",
            "content": "Item Order Settings"
          },
          {
            "type": "select",
            "id": "item_order",
            "label": "Grid Item Order (Desktop)",
            "options": [
              {
                "value": "lg:order-none",
                "label": "Default"
              },
              {
                "value": "lg:order-1",
                "label": "1"
              },
              {
                "value": "lg:order-2",
                "label": "2"
              },
              {
                "value": "lg:order-3",
                "label": "3"
              },
              {
                "value": "lg:order-4",
                "label": "4"
              },
              {
                "value": "lg:order-5",
                "label": "5"
              },
              {
                "value": "lg:order-6",
                "label": "6"
              }
            ],
            "default": "lg:order-none"
          },
          {
            "type": "select",
            "id": "item_order_mobile",
            "label": "Grid Item Order (Mobile)",
            "options": [
              {
                "value": "order-none",
                "label": "Default"
              },
              {
                "value": "order-1",
                "label": "1"
              },
              {
                "value": "order-2",
                "label": "2"
              },
              {
                "value": "order-3",
                "label": "3"
              },
              {
                "value": "order-4",
                "label": "4"
              },
              {
                "value": "order-5",
                "label": "5"
              },
              {
                "value": "order-6",
                "label": "6"
              }
            ],
            "default": "order-none"
          }
        ]
      }
    ]
  }
{% endschema %}