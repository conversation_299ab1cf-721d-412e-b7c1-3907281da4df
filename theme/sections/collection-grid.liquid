{% schema %}
{
  "name": "Collection Grid",
  "settings": [
    {
      "type": "header",
      "content": "A/B Testing"
    },
    {
      "type": "checkbox",
      "id": "remove_section_condition",
      "label": "Remove Section",
      "default": false,
      "info": "This will hide the section in one of the experiment variations."
    },
    {
      "type":"color",
      "default":"#000",
      "label":"Background Color",
      "id":"background_color"
    },
    {
      "type":"text",
      "label":"Background Video (Desktop)",
      "id":"background_video"
    },
    {
      "type":"text",
      "label":"Background Video (Mobile)",
      "id":"background_video_mobile"
    },
    {
      "type":"select",
      "default":"container",
      "label":"Container",
      "id":"container",
      "options": [
        {
          "value":"container",
          "label":"Container"
        },
        {
          "value":"hero w-full",
          "label":"Full Width"
        },
        {
          "value":"hero w-full px-4 lg:px-8",
          "label":"Padded"
        }
      ]
    },
    {
      "type":"number",
      "label":"Number of Products",
      "id":"product_limit",
      "default":12
    },
    {
      "type":"select",
      "default":"w-full",
      "label":"Mobile Columns",
      "id":"mobile-columns",
      "options": [
        {
          "value":"w-full",
          "label":"One Across"
        },
        {
          "value":"w-1/2",
          "label":"Two Across"
        }
      ]
    },
    {
      "type":"select",
      "default":"lg:w-1/6",
      "label":"Desktop Columns",
      "id":"desktop-columns",
      "options": [
        {
          "value":"lg:w-1/2",
          "label":"Two Across"
        },
        {
          "value":"lg:w-2/6",
          "label":"Three Across"
        },
        {
          "value":"lg:w-1/4",
          "label":"Four Across"
        },
        {
          "value":"lg:w-1/6",
          "label":"Six Across"
        }
      ]
    },
    {
      "type":"text",
      "label":"Section Title",
      "id":"title"
    },
    {
      "type": "font_picker",
      "id": "title_font_family",
      "label": "Title Font",
      "default": "work_sans_n6"
    },
    {
      "type":"text",
      "label":"Button Text",
      "id":"button_text"
    },
    {
      "type":"collection",
      "label":"Collection",
      "id":"collection"
    },
    {
      "type": "select",
      "id": "section_vertical_padding",
      "label": "Section vertical padding",
      "options": [
        {
          "value": "py-0",
          "label": "None"
        },
        {
          "value": "py-1",
          "label": "1"
        },
        {
          "value": "py-2",
          "label": "2"
        },
        {
          "value": "py-4",
          "label": "3"
        },
        {
          "value": "py-8",
          "label": "4"
        },
        {
          "value": "py-16",
          "label": "5"
        }
      ],
      "default": "py-16"
    }
  ],
  "presets": [
    {
      "name": "Collection Grid",
      "category": "Product"
    }
  ]
}
{% endschema %}

<article class="h-full p-0 m-0 w-full relative {% if section.settings.remove_section_condition %} vwo-hide{% endif %}" style="background-color:{{section.settings.background_color}}">

  {% if section.settings.background_video != blank or section.settings.background_video_mobile != blank %}
    {% if section.settings.background_video != blank and section.settings.background_video_mobile == blank %}
      <video autoplay playsinline mute muted loop style="object-fit: cover;" class="absolute w-full h-full top-0 left-0 z-0 pointer-events-none">
        <source src="{{ section.settings.background_video }}" type="video/mp4">
      </video>
    {% elsif section.settings.background_video != blank and section.settings.background_video_mobile != blank %}
      <video autoplay playsinline mute muted loop style="object-fit: cover;" class="absolute w-full h-full top-0 left-0 z-0 pointer-events-none lg:block hidden">
        <source src="{{ section.settings.background_video }}" type="video/mp4">
      </video>
      <video autoplay playsinline mute muted loop style="object-fit: cover;" class="absolute w-full h-full top-0 left-0 z-0 pointer-events-none lg:hidden block">
        <source src="{{ section.settings.background_video_mobile }}" type="video/mp4">
      </video>
    {% elsif section.settings.background_video == blank and section.settings.background_video_mobile != blank %}
      <video autoplay playsinline mute muted loop style="object-fit: cover;" class="absolute w-full h-full top-0 left-0 z-0 pointer-events-none">
        <source src="{{ section.settings.background_video_mobile }}" type="video/mp4">
      </video>
    {% endif %}
  {% endif %}

    {% assign collection = collections[section.settings.collection]%}
  
  <div class="{{section.settings.container}} {{section.settings.section_vertical_padding}}">
      {% if section.settings.title != blank %}
      <div class="flex justify-between items-center">
        <div class="mr-auto">
        <h2 class="text-5xl" style='font-family:{{section.settings.title_font_family.family}}'>{{section.settings.title}}</h2>
      </div>

        {% if section.settings.button_text != blank %}
        <div class="ml-auto">
        <a href="/collections/{{collection.handle}}" class="py-4 px-8 border text-black inline-block no-underline">{{section.settings.button_text}}</a>
      </div>
      {% endif %}

    </div>
    {% endif %}
    
    <div class="flex flex-wrap">
      {% for product in collection.products limit:section.settings.product_limit  %}
        <div class="product-grid-item w-1/2 lg:w-1/4 overflow-hidden">
          <div class="pr-1 pb-16 lg:pr-8 lg:pb-8">
            {%- include 'product-item'-%}
          </div>
        </div>
      {% endfor %}
    </div>

  </div>
  
</article>


