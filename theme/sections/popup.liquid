{% comment %} 


This is generated by webpack so don't edit this unless you want to be sad when your work gets overwritten in production.
You can edit this file in ../../components/** 


{% endcomment %}






{% schema %}
{
  "name": "Popup",
  "settings": [
  ],
  "blocks": [
    {
      "type": "popup",
      "name": "Popup",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "header",
          "content": "Display Settings"
        },
        {
          "type": "checkbox",
          "id": "show_desktop",
          "label": "Show desktop",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_mobile",
          "label": "Show mobile",
          "default": true
        },
        {
          "type": "header",
          "content": "Width Settings"
        },
        {
          "type": "text",
          "id": "width_desktop",
          "label": "Width (Desktop)",
          "default": "300px"
        },
        {
          "type": "text",
          "id": "width_mobile",
          "label": "Width (Mobile)",
          "default": "300px"
        },
        {
          "type": "header",
          "content": "Content Settings"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "url",
          "label": "Link",
          "id": "url"
        },
        {
          "type": "header",
          "content": "Popup Position Settings"
        },
        {
          "type": "range",
          "id": "position_x",
          "label": "X Position",
          "default": 50,
          "min": 0,
          "max": 100
        },
        {
          "type": "range",
          "id": "position_y",
          "label": "Y Position",
          "default": 50,
          "min": 0,
          "max": 100
        },
        {
          "type": "range",
          "id": "position_x_mobile",
          "label": "Mobile X Position",
          "default": 50,
          "min": 0,
          "max": 100
        },
        {
          "type": "range",
          "id": "position_y_mobile",
          "label": "Mobile Y Position",
          "default": 50,
          "min": 0,
          "max": 100
        }
      ]
    }
  ]
}
{% endschema %}

<section class="section-{{section.id}} section-popup">
  {% for block in section.blocks %}
    <article id="block--{{block.id}}" class="fixed z-5 {% if block.settings.show_desktop == true and block.settings.show_mobile == true %} block {% elsif block.settings.show_desktop == true and block.settings.show_mobile == false %} dn block-l {% elsif block.settings.show_desktop == false and block.settings.show_mobile == false %} flex-dn {% elsif block.settings.show_desktop == false and block.settings.show_mobile == true %} block dn-l {% endif %}">
      <button class="btn-none absolute activate btn-icon pa3 right-0 top-0" onclick="this.closest('article').remove();">
        <span class="screenreader sr-only">Close</span>
        <i class="icon icon--close"></i>
      </button>
      <a href="{{ block.settings.url }}" class="popup__img db w-100">
        {% render 'lazy-image' with image: block.settings.image, image_class: "w-100 h-auto" %}
      </a>
    </article>
    {% style %}
      #block--{{block.id}}  {
        left: {{ block.settings.position_x_mobile }}%;
        top: {{ block.settings.position_y_mobile }}%;
        transform: translate(-{{ block.settings.position_x_mobile }}%, -{{ block.settings.position_y_mobile }}%);
        width: {{ block.settings.width_mobile }};
        max-width: 100%;;
      }
      @media (min-width: 1024px) {
        #block--{{block.id}} {
          left: {{ block.settings.position_x }}%;
          top: {{ block.settings.position_y }}%;
        transform: translate(-{{ block.settings.position_x }}%, -{{ block.settings.position_y }}%);
          width: {{ block.settings.width_desktop }};
        }
      }
    {% endstyle %}
  {% endfor %}
</section>