{%- liquid
  for block in section.blocks
    if product.tags contains block.settings.tag_inclusion
      assign size_guide_data = block.settings
      break
    endif
  endfor
%}

{% capture modal_content %}
  <div class="lg:pt-[60px] lg:pb-9 py-[36px] h-full overflow-x-hidden">
    <div class="w-full flex text-right justify-end lg:pl-16 lg:pr-8 px-[22px]">
      <button class="" onclick="modal.close()">
        <span class="sr-only">X</span>
        {%  render 'icon' icon:'exit', height:15, width:15  %}
      </button>
    </div>
    {% render 'size-guide', section: section, settings: section.settings, guides_data: size_guide_data %}
  </div>
{% endcapture %}
{% render 'modal' topic: 'size-guide' position: 'right' content: modal_content %}

{% schema %}
  {
    "name": "Size Guide",
    "class": "size-guide-section",
    "settings": [
      {
        "type": "text",
        "id": "select_country_title",
        "label": "Select country label",
        "default": "Select conversion table"
      },
      {
        "type": "inline_richtext",
        "id": "note_text",
        "label": "Note text"
      },
      {
        "type": "header",
        "content": "Measurements guide"
      },
      {
        "type": "text",
        "id": "guide_title",
        "label": "Title",
        "default": "Measurements guide"
      },
      {
        "type": "image_picker",
        "id": "guide_image",
        "label": "Image (desktop)"
      },
      {
        "type": "image_picker",
        "id": "guide_image_mobile",
        "label": "Image (mobile)"
      },
      {
        "type": "header",
        "content": "Chest guide content"
      },
      {
        "type": "text",
        "id": "chest_title",
        "label": "Title"
      },
      {
        "type": "textarea",
        "id": "chest_text",
        "label": "Description"
      },
      {
        "type": "header",
        "content": "Hip guide content"
      },
      {
        "type": "text",
        "id": "hip_title",
        "label": "Title"
      },
      {
        "type": "textarea",
        "id": "hip_text",
        "label": "Description"
      },
      {
        "type": "header",
        "content": "Waist guide content"
      },
      {
        "type": "text",
        "id": "waist_title",
        "label": "Title"
      },
      {
        "type": "textarea",
        "id": "waist_text",
        "label": "Description"
      }
    ],
    "blocks": [
      {
        "name": "Size chart",
        "type": "chart",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Size chart title"
          },
          {
            "type": "text",
            "id": "tag_inclusion",
            "label": "Tag inclusion"
          },
          {
            "type": "textarea",
            "id": "mother_sizing_values",
            "label": "Mother sizing values"
          },
          {
            "type": "textarea",
            "id": "countries_values",
            "label": "Per country size values"
          },
          {
            "type": "textarea",
            "id": "chest_values",
            "label": "Chest measurements (in inches)"
          },
          {
            "type": "textarea",
            "id": "waist_values",
            "label": "Waist measurements (in inches)"
          },
          {
            "type": "textarea",
            "id": "hip_values",
            "label": "Hip measurements (in inches)"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Size Guide"
      }
    ]
  }
{% endschema %}