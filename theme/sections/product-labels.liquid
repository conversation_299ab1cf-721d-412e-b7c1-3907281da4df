<script>
window.productLabels = [];

{%- assign now = "now" | date: "%Y-%m-%d %H:%M" -%}
{%- for block in section.blocks -%}

  {%- assign include_label = true -%}

  {%- if block.settings.end != blank and block.settings.end < now -%}
    {%- assign include_label = false -%}
  {%- endif -%}

  {%- if block.settings.start != blank and block.settings.start > now -%}
    {%- assign include_label = false -%}
  {%- endif -%}

  {%- if include_label -%}
    productLabels.push({{ block.settings|json }});
  {%- endif -%}

{%- endfor -%}
</script>

{% schema %}
  {
    "name": "Product Labels",
    "settings": [],
    "blocks": [
      {
        "type":"tier",
        "name":"Label",
        "settings": [
          {
            "label": "Label Title",
            "id": "title",
            "type": "text"
          },
          {
            "label": "Product Tag",
            "id": "tag",
            "type": "text"
          },
          {
            "label": "Product Detail label Text",
            "id": "product_label",
            "type": "text"
          },
          {
            "type": "color",
            "id": "label_text_color",
            "label": "label text color"
          }
        ]
      }
    ]
  }
{% endschema %}