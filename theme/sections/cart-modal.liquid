<div data-modal="cart" class="cart__modal fixed top-0 right-0 z-50 h-screen max-h-full lg:max-h-screen transition-transform duration-300 ease-in-out bg-light modal-right animate lg:max-w-[28.125rem] lg:w-full w-11/12"  tabindex="-1" role="dialog" aria-modal="true" aria-label="Shopping Cart" neptune-brig="{}" neptune-engage="{
  on:keyup,
  which: 27,
  targets:[{
    selector:html,
    attributes:[{
      att:data-active-modal,
      set:_remove
    }]
  },
  { 
    selector:'[data-return-focus]',
    attributes:[{
      att:data-return-focus,
      set:_remove
    }],
    focus:true
  }]
}">

  <section class="relative flex flex-col h-full" neptune-liquid="{topic:cart, source:cart, append:[Shopify, freeShippingMessages, productBadges, Cart]}" data-flow-cart-container>
    <template>

      {% render 'cart-header' %}

    {% raw %}

      <main class="relative flex-grow h-full overflow-y-scroll cart-body smooth-scroll overscroll-contain">

        {% if item_count == 0 %}
          <p class="p-4 lg:px-8 font-light text-center text-xl leading-snug">
            {% endraw %}
            {{ 'templates.cart.cart_empty' | t }}
            {% raw %}
          </p> 
        {% endif %}

        {% for item in items %}

          {% unless item.properties._bundle == 'component' %}

            {% endraw %}{% render 'cart-item', show_scarcity_msg: section.settings.show_scarcity_msg %}{% raw %}

          {% endunless %}

        {% endfor %}

      </main>
    {% endraw %}

      {% render 'cart-offers' offers:offers %}
      {% render 'cart-footer' %}

    </template>

  </section>

</div>

{% schema %}
  {
    "name": "Cart",
    "settings": [
      {
        "id": "limits",
        "type": "textarea",
        "label": "Cart Maximums by Currency",
        "info": "Multiline logic map. Each line to follow format { currency_code }:{ limit } where limit is the amount in USD. Example EUR:500 would limit anyone paying in Euros to a maximum cart value of $500(USD)"
      },
      {
        "type": "checkbox",
        "id": "show_scarcity_msg",
        "label": "Show Scarcity Message",
        "default": true
      }
    ],
    "blocks": [
      { 
        "type": "offer",
        "name": "Offer",
        "settings": [
          {
            "id": "title",
            "type": "text",
            "label": "Title",
            "info": "For internal organization only"
          },
          {
            "id": "display_type",
            "type": "select",
            "label": "Display Type",
            "options": [
              {
                "label": "None",
                "value": ""
              },
              {
                "label": "Carousel",
                "value": "carousel"
              }
            ],
            "default": ""
          },
          {
            "id": "data_source",
            "type": "select",
            "label": "Data Source",
            "options": [
              {
                "label": "Shopify",
                "value": "shopify"
              },
              {
                "label": "Searchspring",
                "value": "searchspring"
              }
            ],
            "default": "shopify"
          },
          {
            "id": "product",
            "type": "product",
            "label": "Single Product"
          },
          {
            "id": "collection",
            "type": "collection",
            "label": "Multiple Products from Collection"
          },
          {
            "id": "searchspring_tags",
            "type": "liquid",
            "label": "Searchspring profiles",
            "default": "cross-sell",
            "info": "Required tags like \"cross-sell\", \"also-bought\", or \"most-popular\""
          },
          {
            "id": "threshold_cents",
            "type": "number",
            "label": "Price threshold (cents)",
            "info": "Leave blank for none",
            "default":1
          },
          {
            "id": "threshold_quantity",
            "type": "number",
            "label": "Quantity threshold",
            "info": "Leave blank for none"
          },
          {
            "id": "tag_requirement",
            "type": "text",
            "label": "Tag requirement to aply to threshold",
            "info": "Leave blank for none"
          },
          {
            "id": "metafield",
            "type": "text",
            "label": "Metafield",
            "info": "This will get the products from a metafield of the product added to cart."
          },
          {
            "id": "product_selection",
            "type": "select",
            "label": "Product Selection",
            "options": [
              {
                "value":"checkbox",
                "label":"Many"
              },
              {
                "value":"radio",
                "label":"One"
              },
              {
                "value":"all",
                "label":"All"
              }
            ],
            "default":"checkbox"
          },
          {
            "id": "discount",
            "type": "number",
            "label": "Discount Percent",
            "info": "Leave blank for none, 100 for free gift with purchase"
          },
          {
            "type": "paragraph",
            "content": "Each gift must also be configured in Automatic Discounts or in the published Line Items script in the Shopify Script Editor."
          },
          {
            "id": "automatic",
            "type": "checkbox",
            "label": "Add to Cart automatically"
          },
          {
            "id": "combineable",
            "type": "checkbox",
            "label": "May be combined with other offer tiers"
          },
          {
            "type": "header",
            "content": "Messaging"
          },
          {
            "id": "approach",
            "type": "text",
            "label": "Approach Banner text",
            "info": "To display as customer approaches threshold. Available variables include {{ balance }}"
          },
          {
            "id": "success",
            "type": "text",
            "label": "Success Banner text",
            "info": "To display after customer reaches threshold"
          },
          {
            "id": "prompt",
            "type": "textarea",
            "label": "Product Selection Prompt"
          },
          {
            "type": "header",
            "content": "Scheduling"
          },
          {
            "id": "start_date",
            "type": "text",
            "label": "Start Date",
            "info": "Format YYYY-MM-DD. Leave blank to allow anytime"
          },
          {
            "id": "end_date",
            "type": "text",
            "label": "End Date",
            "info": "Format YYYY-MM-DD. Leave blank to allow anytime"
          }
        ]
      }
    ]
  }
{% endschema %}

<script>
  window.Cart = {}
  window.Cart.settings = {{ section.settings | json }};
  window.Cart.blocks = [
    {% assign today = "now" | date: "%Y-%m-%d" %}
    {% capture cart_blocks %}
    {%- for block in section.blocks -%}
    {%- if block.settings.start_date != blank and block.settings.start_date > today %}{% continue %}{% endif -%}
    {%- if block.settings.end_date != blank and block.settings.end_date <= today %}{% continue %}{% endif -%}
    ,{
      "type":{{ block.type|json }},
      "today":{{ today | json }},
      {%- case block.settings.data_source -%}
        {%- when 'shopify' -%}
          {% if block.settings.product != blank -%}
            {% assign offerProduct = all_products[block.settings.product] %}
            {% if offerProduct.available %}
            "products":[{{ offerProduct | json }}],
            {% endif %}
          {% endif -%}

          {% if block.settings.collection != blank -%}
            "products":{{ collections[block.settings.collection].products | json }},
          {% endif -%}

        {%- when 'searchspring' -%}
          {% assign source = 'https://' | append: settings.ss_site_id | append: '.a.searchspring.io/boost/' | append: settings.ss_site_id | append: '/recommend?tags=' | append: block.settings.searchspring_tags %}
          {%-  liquid
            if block.settings.product != blank
              assign source = source | append: '&product=' | append: all_products[block.settings.product].id | replace: ' ', '' 
            endif

            if block.settings.collection != blank
              assign source = source | append: '&categories=' | append: collections[block.settings.collection].id | replace: ' ', '' 
            endif

            if cart.items.size > 0
              assign cart_skus = ''
              for item in cart.items
                assign cart_skus = cart_skus | append: item.sku | append: ','
              endfor
              assign cart_skus = cart_skus | remove_last: ','
              assign source = source | append: '&cart=' | append: cart_skus 
            endif
          -%}
          "source": {
            "url": {{ source | json }},
            "map": {id:'mappings.core.uid',title:'mappings.core.name', handle: 'mappings.core.url|.split(`products/`).reverse()[0]', price:'mappings.core.price|*100',compare_at_price:'mappings.core.msrp|*100',msrp:'mappings.core.msrp',featured_image:'mappings.core.imageUrl',alt_image:'attributes.ss_image_alt',hover_image:'attributes.ss_image_alt',hover_video:'attributes.mfield_product_gallery_video',images:'attributes.images',variants:'attributes.variants',siblings:'attributes.mfield_family_siblings'},
            "enhance": true,
            "transform": function (data) {
              return data[0].results.map(r => {
                try {
                  r.attributes.mfield_family_siblings = JSON.parse(r.attributes.mfield_family_siblings.replace(/&quot;/g, `"`)).products;
                } catch (err) {}

                try {
                  r.attributes.variants = JSON.parse(r.attributes.variants.replace(/&quot;/g, `"`));
                } catch (err) {}

                try {
                  r.attributes.images = r.attributes.images.split(`|`)
                  r.hover_image = r.attributes.images[1]
                } catch (err) {}

                return r
              })
            },
          },

      {%- endcase -%}

      "settings": {{ block.settings | json }}
    }{%- endfor -%}
    {% endcapture %}
    {{ cart_blocks | remove_first: ',' }}
  ];
</script>


{% render 'cart-limits' limits:section.settings.limits %}
