{%- liquid
  assign size_guide_countries = section.settings.countries_list
%}

<div class="size-guide-page lg:pt-[60px] lg:pb-9 py-[25px] h-full">
    {% if section.settings.title != blank %}
      <h2 class="size-guide-page__title text-center text-[36px] font-normal tracking-wider uppercase">{{ section.settings.title  }}</h2>
    {% endif %}

    {% render 'size-guide', section: section, settings: section.settings %}
</div>

{% schema %}
  {
    "name": "Standalone Size Guide",
    "class": "size-guide-section",
    "settings": [
      {
        "type": "header",
        "content": "Section settings"
      },
      {
        "type": "text",
        "id": "title",
        "label": "Title"
      },
      {
        "type": "textarea",
        "id": "countries_list",
        "label": "Countries list"
      },
      {
        "type": "text",
        "id": "select_country_title",
        "label": "Select country label",
        "default": "Select conversion table"
      },
      {
        "type": "header",
        "content": "Measurements guide"
      },
      {
        "type": "text",
        "id": "guide_title",
        "label": "Title",
        "default": "Measurements guide"
      },
      {
        "type": "image_picker",
        "id": "guide_image",
        "label": "Image (desktop)"
      },
      {
        "type": "image_picker",
        "id": "guide_image_mobile",
        "label": "Image (mobile)"
      },
      {
        "type": "header",
        "content": "Chest guide content"
      },
      {
        "type": "text",
        "id": "chest_title",
        "label": "Title"
      },
      {
        "type": "textarea",
        "id": "chest_text",
        "label": "Description"
      },
      {
        "type": "header",
        "content": "Hip guide content"
      },
      {
        "type": "text",
        "id": "hip_title",
        "label": "Title"
      },
      {
        "type": "textarea",
        "id": "hip_text",
        "label": "Description"
      },
      {
        "type": "header",
        "content": "Waist guide content"
      },
      {
        "type": "text",
        "id": "waist_title",
        "label": "Title"
      },
      {
        "type": "textarea",
        "id": "waist_text",
        "label": "Description"
      }
    ],
    "blocks": [
      {
        "name": "Size chart",
        "type": "chart",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Size chart title"
          },
          {
            "type": "textarea",
            "id": "mother_sizing_values",
            "label": "Mother sizing values"
          },
          {
            "type": "textarea",
            "id": "countries_values",
            "label": "Per country size values"
          },
          {
            "type": "inline_richtext",
            "id": "note_text",
            "label": "Note text"
          },
          {
            "type": "textarea",
            "id": "chest_values",
            "label": "Chest measurements (in inches)"
          },
          {
            "type": "textarea",
            "id": "waist_values",
            "label": "Waist measurements (in inches)"
          },
          {
            "type": "textarea",
            "id": "hip_values",
            "label": "Hip measurements (in inches)"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Standalone Size Guide"
      }
    ]
  }
{% endschema %}