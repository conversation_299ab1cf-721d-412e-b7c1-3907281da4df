<div class="fixed bottom-0 w-full left-0 right-0 z-30 bg-near-white recently-viewed"  neptune-liquid="{
      topic: recently-viewed,
      source:'recentlyViewed'
    }">
  <template>
  {% raw %}
  {% unless products.size == 0 %}
    <div
      id="RecentlyViewed" 
      role="region"
      aria-label="recently-viewed"
      class="hidden recently-viewed__panel"
    >
      <header class="recently-viewed__header border-b-2 border-t-2 border-white px-5 py-2">
        <a class="font-link font-light" href="/collections/recently-viewed">View All</a>
      </header>
      <div class="flex recently-viewed__products no-scrollbar">
        {% for product in products %}
        {% endraw %}
        <div class="recently-viewed__product-item">
          {%- render 'product-item' -%}
        </div>
        {% raw %}
        {% endfor %}
      </div>          
    </div>
    <div class="flex recently-viewed__persistent-footer">
      <button class="font-link uppercase py-2 font-light" onclick="_n.qs('#RecentlyViewed').classList.toggle('hidden')">Recently Viewed ({{products.size}})</button>
    </div>
    {% endunless %}
  {% endraw %}
  </template>
</div> 

<script>
  (function(){
    
    let rv = {products:[]}
    try{
      rv = JSON.parse(localStorage.getItem('recentlyViewed'));
    } catch(err) {}
    
    if(!rv)
      rv = {products:[]}

    window.recentlyViewed = rv;

    {% if template contains 'product' %}

      var handle = {{product.handle|json}};
      rv.products = JSON.parse(JSON.stringify(
          rv.products.filter(function(p, i) {return p.handle != handle})
        )
      ).slice(0,10);
      window.recentlyViewed = JSON.parse(JSON.stringify(rv));

      rv.products.unshift({{product|json}})
      localStorage.setItem('recentlyViewed', JSON.stringify(rv))

    {% endif %}

  })()
</script>