{% capture accordion_items %}
{% for block in section.blocks %}
{% if block.type == 'dynamic-metafields' %}
  {% include 'entity' %}
  {% assign namespace = block.settings.metafield | split: '.' | first %}
  {% assign key = block.settings.metafield | split: '.' | last %}
  {% for i in (1..block.settings.metafield-loop-max) %}
    {% assign key_ = key | append: forloop.index %}
    {% if entity.metafields[namespace][key_] %}
      {% assign title = entity.metafields[namespace][key_] | newline_to_br | split: '<br />' | first %}
      {% assign content = entity.metafields[namespace][key_] | remove: title | strip %}
      {% render 'accordion-item' key:forloop.index, title:title, content: content, fontSize: section.settings.font_size, fontSizeMobile: section.settings.font_size_mobile %}
    {% endif %}
  {% endfor %}
{% else %}
  {% render 'accordion-item' key:forloop.index, title: block.settings.title, content: block.settings.accordion-panel, fontSize: section.settings.font_size, fontSizeMobile: section.settings.font_size_mobile %}
{% endif %}
{% endfor %}
{% endcapture %}

{% if accordion_items contains 'accordion' %}

<div id="{{ section.id }}" class="accordion--section {{ section.settings.section_bottom_margin }} {{ section.settings.section_bottom_margin_mobile }} {{ section.settings.section_top_margin }} {{ section.settings.section_top_margin_mobile }}" {% if section.settings.section_background_color %}style="background-color: {{ section.settings.section_background_color }};"{% endif %}>
  <div class="{{ section.settings.container }} mx-auto">
    <div class="{{ section.settings.section_vertical_padding }} {{ section.settings.section_vertical_padding_mobile }} {{ section.settings.section_horizontal_padding }} {{ section.settings.section_horizontal_padding_mobile }}">
      {% if section.settings.title != blank %}
        <h2 class="{{ section.settings.title_font_size }} {{ section.settings.title_font_size_mobile }} px-4">{{ section.settings.title }}</h2>
      {% endif %}

      <ul class="pl-0 list list-none {{ section.settings.font_size }} {{ section.settings.font_size_mobile }}">
        {{ accordion_items }}
      </ul>
    </div>
  </div>
  {% style %}
    .accordion .active-show, .accordion.active .active-hide {
      display: none;
    }
    .accordion .active-hide, .accordion.active .active-show {
      display: block;
    }
    {% if section.settings.section_text_color %}
      #{{section.id}} .accordion button, #{{section.id}} .accordion a, #{{section.id}} .accordion p, #{{section.id}} .accordion {
        color: {{ section.settings.section_text_color }};
      }
    {% endif %}
    #{{section.id}} .accordion p {
      font-size: inherit;
    }
    .accordion-panel a {
      text-decoration:underline;
    }
    .accordion.active {
      scroll-margin-top: calc(var(--scrolled-header-height) + 20px);
    }
  {% endstyle %}

  <script>
    const scrollToActiveAccordion = () => {
      const urlParams = new URLSearchParams(window.location.search);
      const hasAccordionParam = urlParams.has('accordion'); 
      setTimeout(() => {
        if (hasAccordionParam) {
          const acc = document.querySelector('.accordion.active');
          try {
            acc.scrollIntoView({
              behavior: "smooth",
              block: "start",
              inline: "nearest"
            })
          } catch(err) {
            console.error('Animated scroll failed:', err);
          }
        }
      }, 100);
    };
    window.addEventListener('load', () => {
      scrollToActiveAccordion()
    });
  </script>
</div>

{% endif %}

{% schema %}
{
  "name": "Accordion",
  "tag": "section",
  "class": "accordion",
  "settings": [
    {
      "type": "header",
      "content": "Display Settings"
    },
    {
      "type": "select",
      "id": "container",
      "label": "Container",
      "options": [
        {
          "value": "max-w-4xl",
          "label": "Container Narrow"
        },
        {
          "value": "container",
          "label": "Container"
        },
        {
          "value": "w-full",
          "label": "Full width"
        }
      ],
      "default": "w-full"
    },
    {
      "type": "header",
      "content": "Title"
    },
    {
      "type":"text",
      "id":"title",
      "label": "Title"
    },
    {
      "type": "select",
      "id": "title_font_size",
      "label": "Font Size",
      "options": [
        {
          "value": "lg:text-xs",
          "label": "XS"
        },
        {
          "value": "lg:text-sm",
          "label": "SM"
        },
        {
          "value": "lg:text-base",
          "label": "Base"
        },
        {
          "value": "lg:text-lg",
          "label": "LG"
        },
        {
          "value": "lg:text-xl",
          "label": "XL"
        },
        {
          "value": "lg:text-2xl",
          "label": "2XL"
        },
        {
          "value": "title--secondary title--section",
          "label": "Section Title"
        },
        {
          "value": "title--secondary title--article",
          "label": "Article Title"
        }
      ],
      "default": "lg:text-2xl"
    },
    {
      "type": "header",
      "content": "Text Settings (Mobile)"
    },
    {
      "type": "select",
      "id": "title_font_size_mobile",
      "label": "Font Size",
      "options": [
        {
          "value": "text-xs",
          "label": "XS"
        },
        {
          "value": "text-sm",
          "label": "SM"
        },
        {
          "value": "text-base",
          "label": "Base"
        },
        {
          "value": "text-lg",
          "label": "LG"
        },
        {
          "value": "text-xl",
          "label": "XL"
        },
        {
          "value": "text-2xl",
          "label": "2XL"
        }
      ],
      "default": "text-2xl"
    },
    {
      "type": "header",
      "content": "Background Settings"
    },
    {
      "type": "color",
      "id": "section_background_color",
      "label": "Background color"
    },
    {
      "type": "color",
      "id": "section_text_color",
      "label": "Text color"
    },
    {
      "type": "header",
      "content": "Text Settings (Desktop)"
    },
    {
      "type": "select",
      "id": "font_size",
      "label": "Font Size",
      "options": [
        {
          "value": "lg:text-xs",
          "label": "XS"
        },
        {
          "value": "lg:text-sm",
          "label": "SM"
        },
        {
          "value": "lg:text-base",
          "label": "Base"
        },
        {
          "value": "lg:text-lg",
          "label": "LG"
        },
        {
          "value": "lg:text-xl",
          "label": "XL"
        }
      ],
      "default": "lg:text-base"
    },
    {
      "type": "header",
      "content": "Text Settings (Mobile)"
    },
    {
      "type": "select",
      "id": "font_size_mobile",
      "label": "Font Size",
      "options": [
        {
          "value": "text-xs",
          "label": "XS"
        },
        {
          "value": "text-sm",
          "label": "SM"
        },
        {
          "value": "text-base",
          "label": "Base"
        },
        {
          "value": "text-lg",
          "label": "LG"
        },
        {
          "value": "text-xl",
          "label": "XL"
        }
      ],
      "default": "text-base"
    },
    {
      "type": "header",
      "content": "Spacing Settings (Desktop)"
    },
    {
      "type": "select",
      "id": "section_top_margin",
      "label": "Margin top",
      "options": [
        {
          "value": "lg:mt-0",
          "label": "None"
        },
        {
          "value": "lg:mt-1",
          "label": ".25rem"
        },
        {
          "value": "lg:mt-2",
          "label": ".5rem"
        },
        {
          "value": "lg:mt-4",
          "label": "1rem"
        },
        {
          "value": "lg:mt-8",
          "label": "2rem"
        },
        {
          "value": "lg:mt-16",
          "label": "4rem"
        },
        {
          "value": "lg:mt-32",
          "label": "8rem"
        },
        {
          "value": "lg:mt-64",
          "label": "16rem"
        }
      ],
      "default": "lg:mt-0"
    },
    {
      "type": "select",
      "id": "section_bottom_margin",
      "label": "Margin bottom ",
      "options": [
        {
          "value": "lg:mb-0",
          "label": "None"
        },
        {
          "value": "lg:mb-1",
          "label": ".25rem"
        },
        {
          "value": "lg:mb-2",
          "label": ".5rem"
        },
        {
          "value": "lg:mb-4",
          "label": "1rem"
        },
        {
          "value": "lg:mb-8",
          "label": "2rem"
        },
        {
          "value": "lg:mb-16",
          "label": "4rem"
        },
        {
          "value": "lg:mb-32",
          "label": "8rem"
        },
        {
          "value": "lg:mb-64",
          "label": "16rem"
        }
      ],
      "default": "lg:mb-0"
    },
    {
      "type": "select",
      "id": "section_vertical_padding",
      "label": "Vertical Padding",
      "options": [
        {
          "value": "lg:py-0",
          "label": "None"
        },
        {
          "value": "lg:py-1",
          "label": ".25rem"
        },
        {
          "value": "lg:py-2",
          "label": ".5rem"
        },
        {
          "value": "lg:py-4",
          "label": "1rem"
        },
        {
          "value": "lg:py-8",
          "label": "2rem"
        },
        {
          "value": "lg:py-16",
          "label": "4rem"
        },
        {
          "value": "lg:py-32",
          "label": "8rem"
        },
        {
          "value": "lg:py-64",
          "label": "16rem"
        }
      ],
      "default": "lg:py-0"
    },
    {
      "type": "select",
      "id": "section_horizontal_padding",
      "label": "Horizontal Padding",
      "options": [
        {
          "value": "lg:px-0",
          "label": "None"
        },
        {
          "value": "lg:px-1",
          "label": ".25rem"
        },
        {
          "value": "lg:px-2",
          "label": ".5rem"
        },
        {
          "value": "lg:px-4",
          "label": "1rem"
        },
        {
          "value": "lg:px-8",
          "label": "2rem"
        },
        {
          "value": "lg:px-16",
          "label": "4rem"
        },
        {
          "value": "lg:px-32",
          "label": "8rem"
        },
        {
          "value": "lg:px-64",
          "label": "16rem"
        }
      ],
      "default": "lg:px-0"
    },
    {
      "type": "header",
      "content": "Spacing Settings (Mobile)"
    },
    {
      "type": "select",
      "id": "section_top_margin_mobile",
      "label": "Margin top",
      "options": [
        {
          "value": "mt-0",
          "label": "None"
        },
        {
          "value": "mt-1",
          "label": ".25rem"
        },
        {
          "value": "mt-2",
          "label": ".5rem"
        },
        {
          "value": "mt-4",
          "label": "1rem"
        },
        {
          "value": "mt-8",
          "label": "2rem"
        },
        {
          "value": "mt-16",
          "label": "4rem"
        },
        {
          "value": "mt-32",
          "label": "8rem"
        },
        {
          "value": "mt-64",
          "label": "16rem"
        }
      ],
      "default": "mt-0"
    },
    {
      "type": "select",
      "id": "section_bottom_margin_mobile",
      "label": "Margin bottom",
      "options": [
        {
          "value": "mb-0",
          "label": "None"
        },
        {
          "value": "mb-1",
          "label": ".25rem"
        },
        {
          "value": "mb-2",
          "label": ".5rem"
        },
        {
          "value": "mb-4",
          "label": "1rem"
        },
        {
          "value": "mb-8",
          "label": "2rem"
        },
        {
          "value": "mb-16",
          "label": "4rem"
        },
        {
          "value": "mb-32",
          "label": "8rem"
        },
        {
          "value": "mb-64",
          "label": "16rem"
        }
      ],
      "default": "mb-0"
    },
    {
          "type": "select",
          "id": "section_vertical_padding_mobile",
          "label": "Vertical Padding",
          "options": [
            {
              "value": "py-0",
              "label": "None"
            },
            {
              "value": "py-1",
              "label": ".25rem"
            },
            {
              "value": "py-2",
              "label": ".5rem"
            },
            {
              "value": "py-4",
              "label": "1rem"
            },
            {
              "value": "py-8",
              "label": "2rem"
            },
            {
              "value": "py-16",
              "label": "4rem"
            },
            {
              "value": "py-32",
              "label": "8rem"
            },
            {
              "value": "py-64",
              "label": "16rem"
            }
          ],
          "default": "py-0"
        },
        {
          "type": "select",
          "id": "section_horizontal_padding_mobile",
          "label": "Horizontal Padding",
          "options": [
            {
              "value": "px-0",
              "label": "None"
            },
            {
              "value": "px-1",
              "label": ".25rem"
            },
            {
              "value": "px-2",
              "label": ".5rem"
            },
            {
              "value": "px-4",
              "label": "1rem"
            },
            {
              "value": "px-8",
              "label": "2rem"
            },
            {
              "value": "px-16",
              "label": "4rem"
            },
            {
              "value": "px-32",
              "label": "8rem"
            },
            {
              "value": "px-64",
              "label": "16rem"
            }
          ],
          "default": "px-0"
        }
  ],
  "blocks": [
    {
      "type": "text-content",
      "name": "Text Content",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "default": "Button Text",
          "label": "Accordion Title"
        },
        {
          "type": "richtext",
          "id": "accordion-panel",
          "default": "<p>This is you your accordion content</p>",
          "label": "Accordion Panel"
        }
      ]
    },
    {
      "type": "liquid-content",
      "name": "Liquid Content",
      "settings": [
        {
          "type": "liquid",
          "id": "title",
          "default": "Button Text",
          "label": "Accordion Title"
        },
        {
          "type": "liquid",
          "id": "accordion-panel",
          "default": "This is your tab content. {{ product.details }}",
          "label": "Accordion Panel"
        }
      ]
    },
    {
      "type": "dynamic-metafields",
      "name": "Multi Metafield Content",
      "settings": [
        {
          "type": "text",
          "id": "metafield",
          "label": "Metafield Loop Prefix",
          "info": "Loops over metafields with this namespace.key plus a numeric index for content. First line of text is treated as Button Text, remainder is treated as Panel Content"
        },
        {
          "type": "number",
          "id": "metafield-loop-max",
          "default": 20,
          "label": "Metafield Loop Max"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Accordion"
    }
  ]
}
{% endschema %}
