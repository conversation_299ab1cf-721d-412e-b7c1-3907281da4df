{% schema %}
  {
  "name": "Collection Grid Banner",
  "max_blocks": 50,
  "settings": [
    {
      "type": "header",
      "content": "A/B Testing"
    },
    {
      "type": "checkbox",
      "id": "remove_section_condition",
      "label": "Remove Section",
      "default": false,
      "info": "This will hide the section in one of the experiment variations."
    }
  ],
  "blocks": [
    {
      "type": "grid-banner",
      "name": "Grid Banner",
      "settings": [
        {
          "type": "number",
          "id": "position",
          "label": "Position",
          "info": "Banner position among products",
          "default": 1
        },
        {
          "type": "number",
          "id": "page",
          "label": "Page",
          "info": "For collection pages. Set Banner position among pages.",
          "default": 1
        },
        {
          "type": "checkbox",
          "id": "filter",
          "label": "Persist on Filter",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "sort",
          "label": "Persist on Sort",
          "default": true
        },
        {
          "type": "liquid",
          "id": "inclusion",
          "label": "Inclusion Logic",
          "default": "true"
        },
        {
          "type": "textarea",
          "id": "collection_inclusion",
          "label": "Collection Inclusions",
          "info": "Add the handles of the specific collection(s) you would like this banner to display on. If this field is populated with a value and the value does not contain the current collection handle, the banner will not display."
        },
        {
          "type": "textarea",
          "id": "collection_exclusion",
          "label": "Collection Exclusions",
          "info": "Add the handles of the specific collection(s) you would like this banner to NOT display on. If this field is populated with a value and the value does not contain the current collection handle, the banner WILL display."
        },
        {
          "type": "url",
          "id": "link",
          "label": "Block Link"
        },
        {
          "type": "color",
          "id": "background_color",
          "label": "Background Color",
          "default": "#f7f7f7"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text Color",
          "default": "#ffffff"
        },
        {
          "type": "header",
          "content": "Width Settings"
        },
        {
          "type": "select",
          "id": "desktop_width",
          "label": "Desktop Column Width",
          "default": "lg:col-span-2",
          "options": [
            {
              "label": "1",
              "value": "lg:col-span-1"
            },
            {
              "label": "2",
              "value": "lg:col-span-2"
            },
            {
              "label": "3",
              "value": "lg:col-span-3"
            },
            {
              "label": "4",
              "value": "lg:col-span-4"
            }
          ]
        },
        {
          "type": "select",
          "id": "mobile_width",
          "label": "Mobile Column Width",
          "options": [
            {
              "value": "col-span-1",
              "label": "1"
            },
            {
              "value": "col-span-2",
              "label": "2"
            }
          ],
          "default": "col-span-2"
        },
        {
          "type": "header",
          "content": "Height Settings"
        },
        {
          "type": "select",
          "id": "desktop_row_span",
          "options": [
            {
              "value": "lg:row-span-1",
              "label": "1"
            },
            {
              "value": "lg:row-span-2",
              "label": "2"
            },
            {
              "value": "lg:row-span-3",
              "label": "3"
            },
            {
              "value": "lg:row-span-4",
              "label": "4"
            }
          ],
          "label": "Desktop Row Span",
          "default": "lg:row-span-1",
          "info": "Number of rows this banner will span on desktop"
        },
        {
          "type": "select",
          "id": "mobile_row_span",
          "options": [
            {
              "value": "row-span-1",
              "label": "1"
            },
            {
              "value": "row-span-2",
              "label": "2"
            },
            {
              "value": "row-span-3",
              "label": "3"
            },
            {
              "value": "row-span-4",
              "label": "4"
            }
          ],
          "label": "Mobile Row Span",
          "default": "row-span-1",
          "info": "Number of rows this banner will span on mobile"
        },
        {
          "type": "select",
          "id": "height",
          "label": "Height",
          "options": [
            {
              "value": "h-item",
              "label": "Product Image Height"
            },
            {
              "value": "h-full",
              "label": "Product Item Height"
            },
            {
              "value": "h-auto",
              "label": "Content Height"
            }
          ],
          "default": "h-full",
          "info": "Setting height to Content Height will allow the height of the banner to be set by the containing contents including image and text. This may affect the heights of adjacent grid items if the banner is not full width"
        },
        {
          "type": "header",
          "content": "Image Settings"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "Banner Image"
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "Mobile Image",
          "info": "Mobile Banner Image"
        },
        {
          "type": "checkbox",
          "id": "rounded",
          "label": "Rounded Corners",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "border",
          "label": "Image Border",
          "default": true
        },
        {
          "type": "select",
          "id": "image_position",
          "label": "Image Focal Position",
          "options": [
            {
              "value": "object-left-top",
              "label": "Top Left"
            },
            {
              "value": "object-top",
              "label": "Top Center"
            },
            {
              "value": "object-right-top",
              "label": "Top Right"
            },
            {
              "value": "object-left",
              "label": "Middle Left"
            },
            {
              "value": "object-center",
              "label": "Center"
            },
            {
              "value": "object-right",
              "label": "Middle Right"
            },
            {
              "value": "object-left-bottom",
              "label": "Bottom Left"
            },
            {
              "value": "object-bottom",
              "label": "Bottom Center"
            },
            {
              "value": "object-right-bottom",
              "label": "Bottom Right"
            }
          ],
          "default": "object-center"
        },
        {
          "type": "header",
          "content": "Video Settings"
        },
        {
          "type": "text",
          "id": "video_url",
          "label": "Video mp4 url",
          "info": "For section height settings of: Full height, Three Quarter Height, Half Height, and Quarter Height. To get mp4 file: 1) Go to vimeo.com, 2) Click on desired video, 3) Click on settings, 4) Click on Video file"
        },
        {
          "type": "text",
          "id": "video_url_mobile",
          "label": "Video mp4 url (Mobile)",
          "info": "For section height settings of: Full height, Three Quarter Height, Half Height, and Quarter Height. To get mp4 file: 1) Go to vimeo.com, 2) Click on desired video, 3) Click on settings, 4) Click on Video file"
        },
        {
          "type": "image_picker",
          "id": "poster_image",
          "label": "Video Poster Image"
        },
        {
          "type": "image_picker",
          "id": "poster_image_mobile",
          "label": "Video Poster Image (Mobile)"
        },
        {
          "type": "checkbox",
          "id": "show_video_controls",
          "label": "Show Video Controls",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_audio_controls",
          "label": "Show Audio Controls",
          "default": false
        },
        {
          "type": "header",
          "content": "Text Settings"
        },
        {
          "type": "select",
          "id": "text_align",
          "label": "Text Alignment (Desktop)",
          "options": [
            {
              "value": "lg:text-left",
              "label": "Left"
            },
            {
              "value": "lg:text-center",
              "label": "Center"
            },
            {
              "value": "lg:text-right",
              "label": "Right"
            },
            {
              "value": "lg:text-justify lg:text-justify-last",
              "label": "Justify"
            }
          ],
          "default": "lg:text-center"
        },
        {
          "type": "select",
          "id": "text_align_mobile",
          "label": "Text Alignment (Mobile)",
          "options": [
            {
              "value": "text-left",
              "label": "Left"
            },
            {
              "value": "text-center",
              "label": "Center"
            },
            {
              "value": "text-right",
              "label": "Right"
            },
            {
              "value": "text-justify text-justify-last",
              "label": "Justify"
            }
          ],
          "default": "text-center"
        },
        {
          "type": "select",
          "id": "text_type",
          "label": "Typeface",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "font-pop",
              "label": "ABC Monument Grotesk Black"
            },
            {
              "value": "font-highlight",
              "label": "ABC Monument Grotesk Light"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "MonumentGrotesk-Regular"
        },
        {
          "type": "text",
          "id": "text_leading",
          "label": "Text Line Height",
          "default": "1.38"
        },
        {
          "type": "text",
          "id": "text_letter_spacing",
          "label": "Letter Spacing"
        },
        {
          "type": "header",
          "content": "Pretitle Typeface Options"
        },
        {
          "type": "text",
          "id": "pretitle",
          "label": "Pretitle"
        },
        {
          "type": "select",
          "id": "pretitle_type",
          "label": "Typeface",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "font-pop",
              "label": "ABC Monument Grotesk Black"
            },
            {
              "value": "font-highlight",
              "label": "ABC Monument Grotesk Light"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "MonumentGrotesk-Regular"
        },
        {
          "type": "number",
          "id": "pretitle_size",
          "label": "Pretitle Font Size (Desktop)",
          "default": 11
        },
        {
          "type": "number",
          "id": "pretitle_size_mobile",
          "label": "Pretitle Font Size (Mobile)",
          "default": 11
        },
        {
          "type": "color",
          "id": "pretitle_color",
          "label": "Pretitle color",
          "default": "#504F4F"
        },
        {
          "type": "text",
          "id": "pretitle_leading",
          "label": "Preitle Line Height",
          "default": "1.38"
        },
        {
          "type": "text",
          "id": "pretitle_letter_spacing",
          "label": "Pretitle Letter Spacing"
        },
        {
          "type": "header",
          "content": "Title Typeface Options"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "select",
          "id": "title_type",
          "label": "Typeface",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "font-pop",
              "label": "ABC Monument Grotesk Black"
            },
            {
              "value": "font-highlight",
              "label": "ABC Monument Grotesk Light"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "MonumentGrotesk-Bold"
        },
        {
          "type": "number",
          "id": "title_size",
          "label": "Title Font Size (Desktop)",
          "default": 60
        },
        {
          "type": "number",
          "id": "title_size_mobile",
          "label": "Title Font Size (Mobile)",
          "default": 50
        },
        {
          "type": "color",
          "id": "title_color",
          "label": "Title color",
          "default": "#000000"
        },
        {
          "type": "text",
          "id": "title_leading",
          "label": "Title Line Height",
          "default": "1.38"
        },
        {
          "type": "text",
          "id": "title_letter_spacing",
          "label": "Title Letter Spacing"
        },
        {
          "type": "header",
          "content": "Subtitle Typeface Options"
        },
        {
          "type": "text",
          "id": "subtitle",
          "label": "Subtitle"
        },
        {
          "type": "select",
          "id": "subtitle_type",
          "label": "Typeface",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "font-pop",
              "label": "ABC Monument Grotesk Black"
            },
            {
              "value": "font-highlight",
              "label": "ABC Monument Grotesk Light"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "MonumentGrotesk-Regular"
        },
        {
          "type": "number",
          "id": "subtitle_size",
          "label": "Subtitle Font Size (Desktop)",
          "default": 20
        },
        {
          "type": "number",
          "id": "subtitle_size_mobile",
          "label": "Subtitle Font Size (Mobile)",
          "default": 20
        },
        {
          "type": "color",
          "id": "subtitle_color",
          "label": "Subtitle color",
          "default": "#000000"
        },
        {
          "type": "text",
          "id": "subtitle_leading",
          "label": "Subtitle Line Height",
          "default": "1.38"
        },
        {
          "type": "text",
          "id": "subtitle_letter_spacing",
          "label": "Subtitle Letter Spacing"
        },
        {
          "type": "header",
          "content": "Body Typeface Options"
        },
        {
          "type": "richtext",
          "id": "body",
          "label": "Body Text"
        },
        {
          "type": "select",
          "id": "body_type",
          "label": "Typeface",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "font-pop",
              "label": "ABC Monument Grotesk Black"
            },
            {
              "value": "font-highlight",
              "label": "ABC Monument Grotesk Light"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "font-pop"
        },
        {
          "type": "number",
          "id": "body_size",
          "label": "Body Font Size (Desktop)",
          "default": 16
        },
        {
          "type": "number",
          "id": "body_size_mobile",
          "label": "Body Font Size (Mobile)",
          "default": 16
        },
        {
          "type": "color",
          "id": "body_color",
          "label": "Body color"
        },
        {
          "type": "text",
          "id": "body_leading",
          "label": "Body Line Height",
          "default": "1.38"
        },
        {
          "type": "text",
          "id": "body_letter_spacing",
          "label": "Body Letter Spacing"
        },
        {
          "type": "header",
          "content": "Button Layout Settings"
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Button Text"
        },
        {
          "type": "url",
          "id": "btn_link",
          "label": "Button Link"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Button Style",
          "options": [
            {
              "value": "button button--primary",
              "label": "Button Primary"
            },
            {
              "value": "button button--secondary",
              "label": "Button Secondary"
            },
            {
              "value": "button button--tertiary",
              "label": "Button Tertiary"
            },
            {
              "value": "button button--light",
              "label": "Button Light"
            },
            {
              "value": "button button--higlight",
              "label": "Button Highlight"
            },
            {
              "value": "button button--link",
              "label": "Button Text"
            },
            {
              "value": "button button--text",
              "label": "Text link"
            }
          ],
          "default": "button button--primary"
        },
        {
          "type": "color",
          "id": "btn_color",
          "label": "Button Color",
          "default": "#f7f7f7"
        },
        {
          "type": "color",
          "id": "btn_text_color",
          "label": "Button text color",
          "default": "#222222"
        },
        {
          "type": "color",
          "id": "btn_border_color",
          "label": "Button border color",
          "default": "#222222"
        },
        {
          "type": "header",
          "content": "Desktop Content Layout Settings"
        },
        {
          "type": "select",
          "id": "content_width",
          "label": "Content Width",
          "options": [
            {
              "value": "lg:w-full",
              "label": "100%"
            },
            {
              "value": "lg:w-3/4",
              "label": "75%"
            },
            {
              "value": "lg:w-1/2",
              "label": "50%"
            },
            {
              "value": "lg:w-1/4",
              "label": "25%"
            }
          ],
          "default": "lg:w-1/2"
        },
        {
          "type": "select",
          "id": "content_position",
          "label": "Content Position",
          "options": [
            {
              "label": "Below Image or Text Only",
              "value": "relative"
            },
            {
              "label": "Overlay on Image",
              "value": "absolute"
            }
          ],
          "default": "absolute"
        },
        {
          "type": "select",
          "id": "justify",
          "label": "Content Justification",
          "options": [
            {
              "label": "Left",
              "value": "lg:justify-start"
            },
            {
              "label": "Center",
              "value": "lg:justify-center"
            },
            {
              "label": "Right",
              "value": "lg:justify-end"
            }
          ],
          "default": "lg:justify-center"
        },
        {
          "type": "select",
          "id": "content_vertical_padding",
          "label": "Content Vertical Padding",
          "options": [
            {
              "value": "lg:py-0",
              "label": "None"
            },
            {
              "value": "lg:py-1",
              "label": ".25rem"
            },
            {
              "value": "lg:py-2",
              "label": ".5rem"
            },
            {
              "value": "lg:py-4",
              "label": "1rem"
            },
            {
              "value": "lg:py-8",
              "label": "2rem"
            },
            {
              "value": "lg:py-16",
              "label": "4rem"
            },
            {
              "value": "lg:py-32",
              "label": "8rem"
            },
            {
              "value": "lg:py-64",
              "label": "16rem"
            }
          ],
          "default": "lg:py-4"
        },
        {
          "type": "select",
          "id": "content_horizontal_padding",
          "label": "Content Horizontal Padding",
          "options": [
            {
              "value": "lg:px-0",
              "label": "None"
            },
            {
              "value": "lg:px-1",
              "label": ".25rem"
            },
            {
              "value": "lg:px-2",
              "label": ".5rem"
            },
            {
              "value": "lg:px-4",
              "label": "1rem"
            },
            {
              "value": "lg:px-8",
              "label": "2rem"
            },
            {
              "value": "lg:px-16",
              "label": "4rem"
            },
            {
              "value": "lg:px-32",
              "label": "8rem"
            },
            {
              "value": "lg:px-64",
              "label": "16rem"
            }
          ],
          "default": "lg:px-4"
        },
        {
          "type": "header",
          "content": "Mobile Content Layout Settings"
        },
        {
          "type": "select",
          "id": "justify_mobile",
          "label": "Mobile Content Justification",
          "options": [
            {
              "label": "Left",
              "value": "justify-start"
            },
            {
              "label": "Center",
              "value": "justify-center"
            },
            {
              "label": "Right",
              "value": "justify-end"
            }
          ],
          "default": "justify-center"
        },
        {
          "type": "select",
          "id": "align",
          "label": "Content Alignment",
          "options": [
            {
              "label": "Top",
              "value": "items-start"
            },
            {
              "label": "Middle",
              "value": "items-center"
            },
            {
              "label": "Bottom",
              "value": "items-end"
            },
            {
              "label": "Spread",
              "value": "spread"
            }
          ],
          "default": "items-center"
        },
        {
          "type": "select",
          "id": "content_vertical_padding_mobile",
          "label": "Content Vertical Padding",
          "options": [
            {
              "value": "py-0",
              "label": "None"
            },
            {
              "value": "py-1",
              "label": ".25rem"
            },
            {
              "value": "py-2",
              "label": ".5rem"
            },
            {
              "value": "py-4",
              "label": "1rem"
            },
            {
              "value": "py-8",
              "label": "2rem"
            },
            {
              "value": "py-16",
              "label": "4rem"
            },
            {
              "value": "py-32",
              "label": "8rem"
            },
            {
              "value": "py-64",
              "label": "16rem"
            }
          ],
          "default": "py-4"
        },
        {
          "type": "select",
          "id": "content_horizontal_padding_mobile",
          "label": "Content Horizontal Padding",
          "options": [
            {
              "value": "px-0",
              "label": "None"
            },
            {
              "value": "px-1",
              "label": ".25rem"
            },
            {
              "value": "px-2",
              "label": ".5rem"
            },
            {
              "value": "px-4",
              "label": "1rem"
            },
            {
              "value": "px-8",
              "label": "2rem"
            },
            {
              "value": "px-16",
              "label": "4rem"
            },
            {
              "value": "px-32",
              "label": "8rem"
            },
            {
              "value": "px-64",
              "label": "16rem"
            }
          ],
          "default": "px-4"
        },
        {
          "type": "header",
          "content": "Modal Settings"
        },
        {
          "type": "checkbox",
          "id": "enable_modal",
          "label": "Enable Modal on Click",
          "default": false,
          "info": "When enabled, clicking the banner opens a modal instead of navigating to the link URL"
        },
        {
          "type": "metaobject",
          "id": "modal_content",
          "label": "Modal Content",
          "info": "Select a metaobject to use as the modal content. [Go to entries](https://admin.shopify.com/store/motherdenim/content/metaobjects/entries/modals)",
          "metaobject_type": "modals",
          "visible_if": "{{ block.settings.enable_modal == true }}"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Collection Grid Banner",
      "category": "Advanced",
      "settings": {},
      "blocks": []
    }
  ]
}
{% endschema %}


{% for block in section.blocks %}

{% liquid

  if block.settings.inclusion == blank
    continue
  endif

  if block.settings.collection_inclusion != blank
    assign inc_matched = false
    assign included_collections = block.settings.collection_inclusion | newline_to_br | split: '<br />'
    for inclusion in included_collections
      assign inc = inclusion | remove: "\n" | strip | handle
      if inc == collection.handle
        assign inc_matched = true
      endif
    endfor
    unless inc_matched
      continue
    endunless
  endif

  if block.settings.collection_exclusion != blank
    assign exc_matched = false
    assign excluded_collections = block.settings.collection_exclusion | newline_to_br | split: '<br />'
    for exclusion in excluded_collections
      assign exc = exclusion | remove: "\n" | strip | handle
      if exc == collection.handle
        assign exc_matched = true
      endif
    endfor
    if exc_matched
      continue
    endif
  endif

  if block.settings.collection_exclusion != blank
    assign excluded_collections = block.settings.collection_inclusion | newline_to_br | split: '<br />'
    if excluded_collections contains collection.handle
      continue
    endif
  endif

%}

<template class="collection-banner-template {% if section.settings.remove_section_condition %} vwo-hide{% endif %}" data-position="{{ block.settings.position }}">
  <article
    {% comment %}
    collection-injection="{page:{{block.settings.page | default: 1}},position:{{block.settings.position}},persist:{filter:{{ block.settings.filter}},sort:{{ block.settings.sort}}} }"
    {% endcomment %}
    data-position="{{ block.settings.position }}"
    data-page="{{ block.settings.page }}"
    class="
      in-grid in-grid--{{ block.id }}
      {{ block.settings.desktop_width }}
      {{ block.settings.mobile_width }}
      {% if block.settings.desktop_row_span != 'lg:row-span-1' %} {{ block.settings.desktop_row_span }}{% endif %}
      {% if block.settings.mobile_row_span != 'row-span-1' %} {{ block.settings.mobile_row_span }}{% endif %}
      {% unless block.settings.image != blank %} border-r-2{% endunless %} border-b-2 border-white
    "
    style="line-height:0;"
  >

    {%- liquid

      assign wrapper_open_tag = 'div'
      assign wrapper_close_tag = wrapper_open_tag

      if block.settings.enable_modal
        assign wrapper_open_tag = 'button tabindex="0" onclick="window.modal.open(`' | append: block.id | append: '-modal' | append: '`, {returnFocus: this, aria: true})"'
        assign wrapper_close_tag = 'button'

      elsif block.settings.link != blank
        assign wrapper_open_tag = 'a href="' | append: block.settings.link | append: '"'
        assign wrapper_close_tag = 'a'
      endif

    -%}

    <{{ wrapper_open_tag }} class="block relative w-full appearance-none {{ block.settings.height }}" style="background-color:{{ block.settings.background_color }}; color:{{ block.settings.text_color }};">

      {% liquid

        case block.settings.height
          when 'h-item'
            case block.settings.mobile_width
              when 'col-span-1'
                assign aspect = 'aspect-[18/11] '
              when 'col-span-2'
                assign aspect = 'aspect-[27/11] '
            endcase

            case block.settings.desktop_width
              when 'lg:col-span-1'
                assign aspect = aspect | append: 'lg:aspect-[9/11] '
              when 'lg:col-span-2'
                assign aspect = aspect | append: 'lg:aspect-[18/11] '
              when 'lg:col-span-3'
                assign aspect = aspect | append: 'lg:aspect-[27/11] '
              when 'lg:col-span-4'
                assign aspect = aspect | append: 'lg:aspect-[36/11] '
            endcase
          when 'h-full'
            assign aspect = 'h-full'
            assign image_aspect = 'absolute'
          else
            assign aspect = ''
            assign image_aspect = ''
        endcase

        assign style = ' '
        if block.settings.rounded
          assign style = style | append: 'rounded-xl '
        endif
        if block.settings.border
          assign style = style | append: 'border border-black '
        endif

        assign image_classes = 'w-full h-full object-cover ' | append: image_aspect | append: ' ' | append: block.settings.image_position

        if block.settings.mobile_image != blank or block.settings.video_url_mobile != blank
          assign mobile_image_classes = image_classes | append: ' ' | append: 'lg:hidden'
          assign image_classes = image_classes | append: ' ' | append: 'hidden lg:block'
        endif

      %}

      {% if block.settings.image != blank or block.settings.mobile_image != blank or block.settings.video_url_mobile != blank or block.settings.video_url != blank %}

        <div class="{{ aspect }} {{ style }} relative overflow-hidden">
          {% liquid
            if block.settings.image != blank
              render 'lazy-image' with image: block.settings.image, image_class: image_classes
            endif
          %}

          {% liquid
            if block.settings.mobile_image != blank
              render 'lazy-image' with image: block.settings.mobile_image, image_class: mobile_image_classes
            endif
          %}

          {%- if block.settings.video_url_mobile != blank -%}
            <video id="video-{{ block.id }}__mobile" autoplay playsinline muted loop {% if block.settings.poster_image_mobile != blank %}poster="https:{{ block.settings.poster_image_mobile | image_url: width: 500 }}"{% elsif block.settings.poster_image != blank %}poster="https:{{ block.settings.poster_image | image_url: width: 500 }}"{% endif %} class="z-0 top-0 left-0 {{ mobile_image_classes }}">
              <source src="{{ block.settings.video_url_mobile }}" type="video/mp4">
            </video>
          {%- endif -%}

          {%- if block.settings.video_url != blank -%}
            <video id="video-{{ block.id }}" autoplay playsinline muted loop {% if block.settings.poster_image != blank %}poster="https:{{ block.settings.poster_image | image_url: width: 500 }}"{% endif %} class="z-0 top-0 left-0 {{ image_classes }}">
              <source src="{{ block.settings.video_url }}" type="video/mp4">
            </video>
          {%- endif -%}

          {%- if block.settings.video_url != blank or block.settings.video_url_mobile != blank -%}
            {% render 'video-controls' data:block %}
          {%- endif -%}

        </div>

      {% endif %}

      <div class="w-full flex inset-0 overflow-hidden {{ block.settings.content_position }} {{ block.settings.align }} {{ block.settings.justify }} {{ block.settings.justify_mobile }} {{ block.settings.text_align }} {{ block.settings.text_align_mobile }}">
        <div class="{{ block.settings.content_width }} w-full {{ block.settings.content_vertical_padding }} {{ block.settings.content_horizontal_padding }} {{ block.settings.content_vertical_padding_mobile }} {{ block.settings.content_horizontal_padding_mobile }} flex flex-col">

          <div class="w-full flex-col {{ block.settings.align }} {{ block.settings.justify }} {{ block.settings.justify_mobile }} {{ block.settings.text_align }} {{ block.settings.text_align_mobile }}">
            {% if block.settings.pretitle != blank %}
              <p class="my-0 in-grid__pretitle {{ block.settings.pretitle_type }}"
                {% if block.settings.pretitle_color != blank %}style="color: {{ block.settings.pretitle_color }};"{% endif %}> {{ block.settings.pretitle }}
              </p>
            {% endif %}
            {% unless block.settings.title_html != blank %}
              <p class="my-0 in-grid__title {{ block.settings.title_type }}" {% if block.settings.title_color != blank %}style="color: {{ block.settings.title_color }};"{% endif %}>
                {{ block.settings.title }}
              </p>
            {% else %}
              <div class="my-0 in-grid__title_html {{ block.settings.title_type }}" {% if block.settings.title_color != blank %}style="color: {{ block.settings.title_color }};"{% endif %}>
                {{ block.settings.title_html }}
              </div>
            {% endunless %}
            {% unless block.settings.subtitle_html != blank %}
              <p class="my-0 in-grid__subtitle {{ block.settings.subtitle_type }}" {% if block.settings.subtitle_color != blank %}style="color: {{ block.settings.subtitle_color }};"{% endif %}>
                {{ block.settings.subtitle }}
              </p>
            {% else %}
              <div class="my-0 in-grid__subtitle_html {{ block.settings.subtitle_type }}" {% if block.settings.subtitle_color != blank %}style="color: {{ block.settings.subtitle_color }};"{% endif %}>
                {{ block.settings.subtitle_html }}
              </div>
            {% endunless %}
            {% if block.settings.body != blank %}
              <div class="mt-0 in-grid__body p-reset text-base {{ block.settings.body_type }}" {% if block.settings.body_color != blank %}style="color: {{ block.settings.body_color }};"{% endif %}>
                {{ block.settings.body }}
              </div>
            {% endif %}
          </div>

          {% style %}
            .in-grid--{{block.id}} .in-grid__pretitle {
              font-size: {{ block.settings.pretitle_size }}px;
              line-height: {{ block.settings.pretitle_leading }};
              letter-spacing: {{ block.settings.pretitle_letter_spacing }}px;
            }
            .in-grid--{{block.id}} .in-grid__title,
            .in-grid--{{block.id}} .in-grid__title_html {
              font-size: {{ block.settings.title_size }}px;
              line-height: {{ block.settings.title_leading }};
              letter-spacing: {{ block.settings.title_letter_spacing }}px;
            }
            .in-grid--{{block.id}} .in-grid__subtitle,
            .in-grid--{{block.id}} .in-grid__subtitle_html {
              font-size: {{ block.settings.subtitle_size }}px;
              line-height: {{ block.settings.subtitle_leading }};
              letter-spacing: {{ block.settings.subtitle_letter_spacing }}px;
            }
            .in-grid--{{block.id}} .in-grid__body {
              font-size: {{ block.settings.body_size }}px;
              line-height: {{ block.settings.body_leading }};
              letter-spacing: {{ block.settings.body_letter_spacing }}px;
            }
            @media (max-width: 1023px) {
              .in-grid--{{block.id}} .in-grid__pretitle {
                font-size: {{ block.settings.pretitle_size_mobile }}px;
              }
              .in-grid--{{block.id}} .in-grid__title,
              .in-grid--{{block.id}} .in-grid__title_html {
                font-size: {{ block.settings.title_size_mobile }}px;
              }
              .in-grid--{{block.id}} .in-grid__subtitle,
              .in-grid--{{block.id}} .in-grid__subtitle_html {
                font-size: {{ block.settings.subtitle_size_mobile }}px;
              }
            }
          {% endstyle %}

          {% if block.settings.button_text != blank %}
          <div class="{{ block.settings.align }} {{ block.settings.justify }} {{ block.settings.text_align }} {{ block.settings.text_align_mobile }}">
            <button {% if block.settings.btn_link != blank %}onclick="event.stopPropagation(); window.location.href = '{{ block.settings.btn_link }}';"{% endif %} class="{{ block.settings.button_style }}" style="background:{{ block.settings.btn_color}}; color:{{ block.settings.btn_text_color }}; border-color:{{ block.settings.btn_border_color }};">
              {{ block.settings.button_text }}
            </button>
          </div>
          {% endif %}
        </div>
      </div>

    </{{ wrapper_close_tag }}>

  </article>
</template>

{% if block.settings.enable_modal %}
  {% render 'universal-modal', _settings:block.settings, id:block.id %}
{% endif %}

{% endfor %}
