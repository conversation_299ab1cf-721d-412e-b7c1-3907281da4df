{{ 'component-cart.css' | asset_url | stylesheet_tag }}
{{ 'component-totals.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'component-discounts.css' | asset_url | stylesheet_tag }}

<div class="page-width{% if cart == empty %} is-empty{% endif %}" id="main-cart-footer" data-id="{{ section.id }}">
  <div>
    <div class="cart__footer">
      {%- if section.settings.show_cart_note -%}
        <cart-note class="cart__note field">
          <label for="Cart-note">{{ 'sections.cart.note' | t }}</label>
          <textarea class="text-area text-area--resize-vertical field__input" name="note" id="Cart-note" placeholder="{{ 'sections.cart.note' | t }}">{{ cart.note }}</textarea>
        </cart-note>
      {%- endif -%}

      <div class="cart__blocks">
        {% for block in section.blocks %}
          {%- case block.type -%}
            {%- when '@app' -%}
              {% render block %}
            {%- when 'subtotal' -%}
              <div class="js-contents" {{ block.shopify_attributes }}>
                <div class="totals">
                  <h3 class="totals__subtotal">{{ 'sections.cart.subtotal' | t }}</h3>
                  <p class="totals__subtotal-value">{{ cart.total_price | money_with_currency }}</p>
                </div>

                <div>
                  {%- if cart.cart_level_discount_applications.size > 0 -%}
                    <ul class="discounts list-unstyled" role="list" aria-label="{{ 'customer.order.discount' | t }}">
                      {%- for discount in cart.cart_level_discount_applications -%}
                        <li class="discounts__discount discounts__discount--end">
                          {%- render 'icon-discount' -%}
                          {{ discount.title }}
                          (-{{ discount.total_allocated_amount | money }})
                        </li>
                      {%- endfor -%}
                    </ul>
                  {%- endif -%}
                </div>

                <small class="tax-note caption-large rte">
                  {%- if cart.taxes_included and shop.shipping_policy.body != blank -%}
                    {{ 'sections.cart.taxes_included_and_shipping_policy_html' | t: link: shop.shipping_policy.url }}
                  {%- elsif cart.taxes_included -%}
                    {{ 'sections.cart.taxes_included_but_shipping_at_checkout' | t }}
                  {%- elsif shop.shipping_policy.body != blank -%}
                    {{ 'sections.cart.taxes_and_shipping_policy_at_checkout_html' | t: link: shop.shipping_policy.url }}
                  {%- else -%}
                    {{ 'sections.cart.taxes_and_shipping_at_checkout' | t }}
                  {%- endif -%}
                </small>
              </div>
            {%- else -%}
              <div class="cart__ctas" {{ block.shopify_attributes }}>
                <noscript>
                  <button type="submit" class="cart__update-button button button--secondary" form="cart">
                    {{ 'sections.cart.update' | t }}
                  </button>
                </noscript>

                <button type="submit" id="checkout" class="cart__checkout-button button" name="checkout"{% if cart == empty %} disabled{% endif %} form="cart">
                  {{ 'sections.cart.checkout' | t }}
                </button>
              </div>

              {%- if additional_checkout_buttons -%}
                <div class="cart__dynamic-checkout-buttons additional-checkout-buttons">
                  {{ content_for_additional_checkout_buttons }}
                </div>
              {%- endif -%}
          {%- endcase -%}
        {% endfor %}

        <div id="cart-errors"></div>
      </div>
    </div>
  </div>
</div>

{% javascript %}
  class CartNote extends HTMLElement {
    constructor() {
      super();

      this.addEventListener('change', debounce((event) => {
        const body = JSON.stringify({ note: event.target.value });
        fetch(`${routes.cart_update_url}`, {...fetchConfig(), ...{ body }});
      }, 300))
    }
  }

  customElements.define('cart-note', CartNote);
{% endjavascript %}

<script>
  document.addEventListener('DOMContentLoaded', function() {
    function isIE() {
      const ua = window.navigator.userAgent;
      const msie = ua.indexOf('MSIE ');
      const trident = ua.indexOf('Trident/');

      return (msie > 0 || trident > 0);
    }

    if (!isIE()) return;
    const cartSubmitInput = document.createElement('input');
    cartSubmitInput.setAttribute('name', 'checkout');
    cartSubmitInput.setAttribute('type', 'hidden');
    document.querySelector('#cart').appendChild(cartSubmitInput);
    document.querySelector('#checkout').addEventListener('click', function(event) {
      document.querySelector('#cart').submit();
    });
  });
</script>

{% schema %}
{
  "name": "t:sections.main-cart-footer.name",
  "class": "cart__footer-wrapper",
  "settings": [
    {
      "type": "checkbox",
      "id": "show_cart_note",
      "default": false,
      "label": "t:sections.main-cart-footer.settings.show_cart_note.label"
    }
  ],
  "blocks": [
    {
      "type": "subtotal",
      "name": "t:sections.main-cart-footer.blocks.subtotal.name",
      "limit": 1
    },
    {
      "type": "buttons",
      "name": "t:sections.main-cart-footer.blocks.buttons.name",
      "limit": 1
    },
    {
      "type": "@app"
    }
  ]
}
{% endschema %}
