{% comment %}
  Flex Pop Up Modal Section
  - Precisely positioned trigger element
  - Supports URL navigation or modal opening
  - Uses existing modal system for content display
  - Implements slide-in animations
{% endcomment %}

<div id="{{ section.id }}" class="flex-popup-container relative w-full h-full" data-flex-popup-container>
  {% render 'flex-popup-trigger', _settings:section.settings, section_id:section.id %}

  {% if section.settings.interaction_type == 'modal' %}
    {% render 'universal-modal', _settings:section.settings, id:section.id %}
  {% endif %}
</div>

{% schema %}
{
  "name": "Flex Pop Up",
  "settings": [
    {
      "type": "header",
      "content": "Trigger Positioning"
    },
    {
      "type": "select",
      "id": "trigger_class_origin",
      "label": "Position Origin",
      "default": "origin origin-center",
      "options": [
        {
          "value": "origin origin-top origin-left",
          "label": "◰",
        },
        {
          "value": "origin origin-top origin-right",
          "label": "◳",
        },
        {
          "value": "origin origin-bottom origin-right",
          "label": "◲",
        },
        {
          "value": "origin origin-bottom origin-left",
          "label": "◱",
        },
        {
          "value": "origin origin-center",
          "label": "⧈",
        },
      ]
    },
    {
      "type": "paragraph",
      "content": "–– Mobile –––––––"
    },
    {
      "type": "range",
      "id": "trigger_style___offset_x",
      "label": "X Offset",
      "min": -100,
      "max": 100,
      "step": 2,
      "unit": "%",
      "default": 0
    },
    {
      "type": "range",
      "id": "trigger_style___offset_y",
      "label": "Y Offset",
      "min": -100,
      "max": 100,
      "step": 2,
      "unit": "%",
      "default": 0
    },
    {
      "type": "paragraph",
      "content": "–– Desktop –––––––"
    },
    {
      "type": "range",
      "id": "trigger_style___offset_x__desktop",
      "label": "X Offset",
      "min": -100,
      "max": 100,
      "step": 2,
      "unit": "%",
      "default": 0
    },
    {
      "type": "range",
      "id": "trigger_style___offset_y__desktop",
      "label": "Y Offset",
      "min": -100,
      "max": 100,
      "step": 2,
      "unit": "%",
      "default": 0
    },
    {
      "type": "paragraph",
      "content": "Use negavite values with center positioning only."
    },
    {
      "type": "header",
      "content": "Trigger Settings"
    },
    {
      "type": "image_picker",
      "id": "trigger_image",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "trigger_text",
      "label": "Text",
      "default": "Click Me"
    },
    {
      "type": "range",
      "id": "trigger_style___width",
      "min": 50,
      "max": 300,
      "step": 10,
      "unit": "px",
      "label": "Width",
      "default": 150
    },
    {
      "type": "color",
      "id": "trigger_style_background_color",
      "label": "Background Color",
      "default": "#f7f7f7",
    },
    {
      "type": "color",
      "id": "trigger_style_color",
      "label": "Text Color",
      "default": "#121212",
    },
    {
      "type": "select",
      "id": "trigger_class_border_radius",
      "label": "Border Radius",
      "options": [
        {
          "label": "None",
          "value": ""
        },
        {
          "label": "Small",
          "value": "overflow-hidden rounded-sm"
        },
        {
          "label": "Medium",
          "value": "overflow-hidden rounded-md"
        },
        {
          "label": "Large",
          "value": "overflow-hidden rounded-lg"
        },
        {
          "label": "XL",
          "value": "overflow-hidden rounded-xl"
        },
        {
          "label": "2XL",
          "value": "overflow-hidden rounded-[15px]"
        }
      ],
      "default": ""
    },
    {
      "type": "select",
      "id": "trigger_class_text_align",
      "label": "Text Alignment",
      "options": [
        {
          "value": "text-left",
          "label": "Left"
        },
        {
          "value": "text-center",
          "label": "Center"
        },
        {
          "value": "text-right",
          "label": "Right"
        },
        {
          "value": "text-justify text-justify-last",
          "label": "Justify"
        }
      ],
      "default": "text-center"
    },
    {
      "type": "paragraph",
      "content": "–– Mobile –––––––"
    },
    {
      "type": "select",
      "id": "trigger_class_y_padding",
      "label": "Vertical Padding",
      "default": "py-unset",
      "options": [
        {
          "value": "py-unset",
          "label": "----------------"
        },
        {
          "value": "py-inherit",
          "label": "Inherit"
        },
        {
          "value": "py-0",
          "label": "None"
        },
        {
          "value": "py-2xs",
          "label": "2XS"
        },
        {
          "value": "py-xs",
          "label": "XS"
        },
        {
          "value": "py-sm",
          "label": "SM"
        },
        {
          "value": "py-md",
          "label": "MD"
        },
        {
          "value": "py-lg",
          "label": "LG"
        },
        {
          "value": "py-xl",
          "label": "XL"
        },
        {
          "value": "py-2xl",
          "label": "2XL"
        },
        {
          "value": "py-3xl",
          "label": "3XL"
        },
        {
          "value": "py-4xl",
          "label": "4XL"
        },
        {
          "value": "py-5xl",
          "label": "5XL"
        },
        {
          "value": "py-6xl",
          "label": "6XL"
        },
        {
          "value": "py-7xl",
          "label": "7XL"
        }
      ]
    },
    {
      "type": "select",
      "id": "trigger_class_x_padding",
      "label": "Horizontal Padding",
      "default": "px-unset",
      "options": [
        {
          "value": "px-unset",
          "label": "----------------"
        },
        {
          "value": "px-inherit",
          "label": "Inherit"
        },
        {
          "value": "px-0",
          "label": "None"
        },
        {
          "value": "px-2xs",
          "label": "2XS"
        },
        {
          "value": "px-xs",
          "label": "XS"
        },
        {
          "value": "px-sm",
          "label": "SM"
        },
        {
          "value": "px-md",
          "label": "MD"
        },
        {
          "value": "px-lg",
          "label": "LG"
        },
        {
          "value": "px-xl",
          "label": "XL"
        },
        {
          "value": "px-2xl",
          "label": "2XL"
        },
        {
          "value": "px-3xl",
          "label": "3XL"
        },
        {
          "value": "px-4xl",
          "label": "4XL"
        },
        {
          "value": "px-5xl",
          "label": "5XL"
        },
        {
          "value": "px-6xl",
          "label": "6XL"
        },
        {
          "value": "px-7xl",
          "label": "7XL"
        }
      ]
    },
    {
      "type": "select",
      "id": "trigger_class_gap",
      "label": "Spacing Gap",
      "default": "gap-unset",
      "options": [
        {
          "value": "gap-unset",
          "label": "----------------"
        },
        {
          "value": "gap-inherit",
          "label": "Inherit"
        },
        {
          "value": "gap-0",
          "label": "None"
        },
        {
          "value": "gap-2xs",
          "label": "2XS"
        },
        {
          "value": "gap-xs",
          "label": "XS"
        },
        {
          "value": "gap-sm",
          "label": "SM"
        },
        {
          "value": "gap-md",
          "label": "MD"
        },
        {
          "value": "gap-lg",
          "label": "LG"
        },
        {
          "value": "gap-xl",
          "label": "XL"
        },
        {
          "value": "gap-2xl",
          "label": "2XL"
        },
        {
          "value": "gap-3xl",
          "label": "3XL"
        },
        {
          "value": "gap-4xl",
          "label": "4XL"
        },
        {
          "value": "gap-5xl",
          "label": "5XL"
        },
        {
          "value": "gap-6xl",
          "label": "6XL"
        },
        {
          "value": "gap-7xl",
          "label": "7XL"
        }
      ]
    },
    {
      "type": "paragraph",
      "content": "–– Desktop –––––––"
    },
    {
      "type": "select",
      "id": "trigger_class_y_padding_desktop",
      "label": "Vertical Padding",
      "default": "lg:py-unset",
      "options": [
        {
          "value": "lg:py-unset",
          "label": "----------------"
        },
        {
          "value": "lg:py-[inherit]",
          "label": "Inherit"
        },
        {
          "value": "lg:py-0",
          "label": "None"
        },
        {
          "value": "lg:py-2xs",
          "label": "2XS"
        },
        {
          "value": "lg:py-xs",
          "label": "XS"
        },
        {
          "value": "lg:py-sm",
          "label": "SM"
        },
        {
          "value": "lg:py-md",
          "label": "MD"
        },
        {
          "value": "lg:py-lg",
          "label": "LG"
        },
        {
          "value": "lg:py-xl",
          "label": "XL"
        },
        {
          "value": "lg:py-2xl",
          "label": "2XL"
        },
        {
          "value": "lg:py-3xl",
          "label": "3XL"
        },
        {
          "value": "lg:py-4xl",
          "label": "4XL"
        },
        {
          "value": "lg:py-5xl",
          "label": "5XL"
        },
        {
          "value": "lg:py-6xl",
          "label": "6XL"
        },
        {
          "value": "lg:py-7xl",
          "label": "7XL"
        }
      ]
    },
    {
      "type": "select",
      "id": "trigger_class_x_padding_desktop",
      "label": "Horizontal Padding",
      "default": "lg:px-unset",
      "options": [
        {
          "value": "lg:px-unset",
          "label": "----------------"
        },
        {
          "value": "lg:px-[inherit]",
          "label": "Inherit"
        },
        {
          "value": "lg:px-0",
          "label": "None"
        },
        {
          "value": "lg:px-2xs",
          "label": "2XS"
        },
        {
          "value": "lg:px-xs",
          "label": "XS"
        },
        {
          "value": "lg:px-sm",
          "label": "SM"
        },
        {
          "value": "lg:px-md",
          "label": "MD"
        },
        {
          "value": "lg:px-lg",
          "label": "LG"
        },
        {
          "value": "lg:px-xl",
          "label": "XL"
        },
        {
          "value": "lg:px-2xl",
          "label": "2XL"
        },
        {
          "value": "lg:px-3xl",
          "label": "3XL"
        },
        {
          "value": "lg:px-4xl",
          "label": "4XL"
        },
        {
          "value": "lg:px-5xl",
          "label": "5XL"
        },
        {
          "value": "lg:px-6xl",
          "label": "6XL"
        },
        {
          "value": "lg:px-7xl",
          "label": "7XL"
        }
      ]
    },
    {
      "type": "select",
      "id": "trigger_class_gap_desktop",
      "label": "Spacing Gap",
      "default": "lg:gap-unset",
      "options": [
        {
          "value": "lg:gap-unset",
          "label": "----------------"
        },
        {
          "value": "lg:gap-[inherit]",
          "label": "Inherit"
        },
        {
          "value": "lg:gap-0",
          "label": "None"
        },
        {
          "value": "lg:gap-2xs",
          "label": "2XS"
        },
        {
          "value": "lg:gap-xs",
          "label": "XS"
        },
        {
          "value": "lg:gap-sm",
          "label": "SM"
        },
        {
          "value": "lg:gap-md",
          "label": "MD"
        },
        {
          "value": "lg:gap-lg",
          "label": "LG"
        },
        {
          "value": "lg:gap-xl",
          "label": "XL"
        },
        {
          "value": "lg:gap-2xl",
          "label": "2XL"
        },
        {
          "value": "lg:gap-3xl",
          "label": "3XL"
        },
        {
          "value": "lg:gap-4xl",
          "label": "4XL"
        },
        {
          "value": "lg:gap-5xl",
          "label": "5XL"
        },
        {
          "value": "lg:gap-6xl",
          "label": "6XL"
        },
        {
          "value": "lg:gap-7xl",
          "label": "7XL"
        }
      ]
    },
    {
      "type": "header",
      "content": "Interaction Settings"
    },
    {
      "type": "select",
      "id": "interaction_type",
      "label": "Interaction Type",
      "options": [
        {
          "value": "url",
          "label": "Navigate to URL"
        },
        {
          "value": "modal",
          "label": "Open Modal"
        }
      ],
      "default": "modal"
    },
    {
      "type": "url",
      "id": "destination_url",
      "label": "Destination URL",
      "info": "Used when 'Navigate to URL' is selected",
      "visible_if": "{{ section.settings.interaction_type == 'url' }}"
    },
    {
      "type": "header",
      "content": "Modal Settings",
      "visible_if": "{{ section.settings.interaction_type == 'modal' }}"
    },
    {
      "type": "metaobject",
      "id": "modal_content",
      "label": "Modal Content",
      "info": "Select a metaobject to use as the modal content. [Go to entries](https://admin.shopify.com/store/motherdenim/content/metaobjects/entries/modals)",
      "metaobject_type": "modals",
      "visible_if": "{{ section.settings.interaction_type == 'modal' }}"
    }
  ],
  "presets": [
    {
      "name": "Flex Popup",
      "category": "Advanced"
    }
  ]
}
{% endschema %}
