{% schema %}
{
  "name": "Panel Menu",
  "settings": [
    {
      "type": "header",
      "content": "A/B Testing"
    },
    {
      "type": "liquid",
      "label": "Remove Section If...",
      "id": "remove_section_condition",
      "info": "Use a JS conditional expression that if true, will remove this section."
    },
    {
      "type": "text",
      "id": "selected_tab",
      "label": "Select Tab by default",
      "info": "type the name of the tab you want to select by default in the mobile menu"
    },
    {
      "type": "header",
      "content": "Slider Mobile Nav"
    },
    {
      "type": "text",
      "id": "slides_pew_view",
      "label": "Tab Per View",
      "default": "5"
    },
    {
      "type": "text",
      "id": "space_tab",
      "label": "Space Between Tab",
      "default": "5"
    }
  ],
  "blocks": [
    {
      "type": "parent",
      "name": "Pane",
      "settings": [
        {
          "type":"text",
          "id":"title",
          "label": "Title",
          "info": "For admin purposes only. Name this Pane: Womens, or Pane: Mens to have a visual representation of this set of blocks."
        },
        {
          "type": "header",
          "content": "Connectivity"
        },
        {
          "type":"text",
          "id":"trigger",
          "label": "Panel Trigger",
          "info": "Must be handleized menu title (e.g. New Arrivals: new-arrivals). Pairs with menu item from header menu."
        },
        {
          "type": "header",
          "content": "Display Settings"
        },
        {
          "type":"color",
          "id":"background_color",
          "label": "Background Color",
          "default": "#ffffff"
        }
      ] 
    },
    {
      "type": "subpane-start",
      "name": "Subpane Start",
      "settings": [] 
    },
    {
      "type": "subpane-end",
      "name": "Subpane End",
      "settings": [] 
    },
    {
      "type": "divider",
      "name": "Divider",
      "settings": [] 
    },
    {
      "type": "search-bar",
      "name": "Search bar",
      "settings": [] 
    },
    {
      "type": "nav-item",
      "name": "Nav Item",
      "settings": [
        {
          "type":"text",
          "id":"title",
          "label": "Item Text"
        },
        {
          "type": "url",
          "id": "url",
          "label": "Link"
        },
        {
          "type": "header",
          "content": "Structure Settings"
        },
        {
          "type": "checkbox",
          "id": "subpane_start",
          "label": "Subpane Start",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "subpane_end",
          "label": "Subpane End",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_desktop",
          "label": "Show desktop",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_mobile",
          "label": "Show mobile",
          "default": true
        }
      ] 
    },
    {
      "type": "image-link",
      "name": "Image Link",
      "settings": [
        {
          "type":"text",
          "id":"title",
          "label": "Title",
          "info": "For admin purposes only. If subpane, start with Subpane:"
        },
        {
          "type": "header",
          "content": "Structure Settings"
        },
        {
          "type": "checkbox",
          "id": "subpane_start",
          "label": "Subpane Start",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "subpane_end",
          "label": "Subpane End",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_divder",
          "label": "Set bottom divider",
          "default": false
        },
        {
          "type": "header",
          "content": "Display Settings"
        },
        {
          "type": "select",
          "id": "mobile_width",
          "label": "Width (Mobile)",
          "options": [
            {
              "value": "col-span-1",
              "label": "25%"
            },
            {
              "value": "col-span-2",
              "label": "50%"
            },
            {
              "value": "col-span-3",
              "label": "75%"
            },
            {
              "value": "col-span-4",
              "label": "100%"
            }
          ],
          "default": "col-span-2"
        },
        {
          "type": "select",
          "id": "desktop_width",
          "label": "Width (Desktop)",
          "options": [
            {
              "value": "lg:col-span-1",
              "label": "25%"
            },
            {
              "value": "lg:col-span-2",
              "label": "50%"
            },
            {
              "value": "lg:col-span-3",
              "label": "75%"
            },
            {
              "value": "lg:col-span-4",
              "label": "100%"
            }
          ],
          "default": "lg:col-span-2"
        },
        {
          "type": "header",
          "content": "Group Image Settings"
        },
        {
          "type": "select",
          "id": "image_height",
          "label": "Image Aspect Ratio (Desktop)",
          "options": [
            {
              "value": "",
              "label": "auto"
            },
            {
              "value": "lg:aspect-w-16 lg:aspect-h-9",
              "label": "16x9"
            },
            {
              "value": "lg:aspect-w-9 lg:aspect-h-16",
              "label": "9x16"
            },
            {
              "value": "lg:aspect-w-5 lg:aspect-h-4",
              "label": "5x4"
            },
            {
              "value": "lg:aspect-w-4 lg:aspect-h-3",
              "label": "4x3"
            },
            {
              "value": "lg:aspect-w-3 lg:aspect-h-4",
              "label": "3x4"
            },
            {
              "value": "lg:aspect-w-6 lg:aspect-h-4",
              "label": "6x4"
            },
            {
              "value": "lg:aspect-w-4 lg:aspect-h-6",
              "label": "4x6"
            },
            {
              "value": "lg:aspect-w-8 lg:aspect-h-5",
              "label": "8x5"
            },
            {
              "value": "lg:aspect-w-5 lg:aspect-h-8",
              "label": "5x8"
            },
            {
              "value": "lg:aspect-w-7 lg:aspect-h-5",
              "label": "7x5"
            },
            {
              "value": "lg:aspect-w-5 lg:aspect-h-7",
              "label": "5x7"
            },
            {
              "value": "lg:aspect-w-1 lg:aspect-h-1",
              "label": "1x1"
            }
          ],
          "default": "lg:aspect-w-5 lg:aspect-h-4"
        },
        {
          "type": "select",
          "id": "image_height_mobile",
          "label": "Image Aspect Ratio (Mobile)",
          "options": [
            {
              "value": "",
              "label": "auto"
            },
            {
              "value": "aspect-w-16 aspect-h-9",
              "label": "16x9"
            },
            {
              "value": "aspect-w-9 aspect-h-16",
              "label": "9x16"
            },
            {
              "value": "aspect-w-5 aspect-h-4",
              "label": "5x4"
            },
            {
              "value": "aspect-w-4 aspect-h-3",
              "label": "4x3"
            },
            {
              "value": "aspect-w-3 aspect-h-4",
              "label": "3x4"
            },
            {
              "value": "aspect-w-6 aspect-h-4",
              "label": "6x4"
            },
            {
              "value": "aspect-w-4 aspect-h-6",
              "label": "4x6"
            },
            {
              "value": "aspect-w-8 aspect-h-5",
              "label": "8x5"
            },
            {
              "value": "aspect-w-5 aspect-h-8",
              "label": "5x8"
            },
            {
              "value": "aspect-w-7 aspect-h-5",
              "label": "7x5"
            },
            {
              "value": "aspect-w-5 aspect-h-7",
              "label": "5x7"
            },
            {
              "value": "aspect-w-1 aspect-h-1",
              "label": "1x1"
            }
          ],
          "default": "aspect-w-5 aspect-h-4"
        },
        {
          "type": "select",
          "id": "corners",
          "label": "Image Corners (Desktop)",
          "options": [
            {
              "value": "lg:rounded-none",
              "label": "Square"
            },
            {
              "value": "lg:rounded-3xl",
              "label": "Round Corners"
            }
          ],
          "default": "lg:rounded-none"
        },
        {
          "type": "select",
          "id": "corners_mobile",
          "label": "Image corners (Mobile)",
          "options": [
            {
              "value": "rounded-none",
              "label": "Square"
            },
            {
              "value": "rounded-3xl",
              "label": "Round Corners"
            }
          ],
          "default": "rounded-none"
        },
        {
          "type": "header",
          "content": "Image 1 Settings"
        },
        {
          "type": "image_picker",
          "id": "image_1",
          "label": "Image 1"
        },
        {
          "type": "select",
          "id": "image_position_1",
          "label": "Image Position 1",
          "options": [
            {
              "value": "object-left-top",
              "label": "Top Left"
            },
            {
              "value": "object-top",
              "label": "Top Center"
            },
            {
              "value": "object-right-top",
              "label": "Top Right"
            },
            {
              "value": "object-left",
              "label": "Middle Left"
            },
            {
              "value": "object-center",
              "label": "Center"
            },
            {
              "value": "object-right",
              "label": "Middle Right"
            },
            {
              "value": "object-left-bottom",
              "label": "Bottom Left"
            },
            {
              "value": "object-bottom",
              "label": "Bottom Center"
            },
            {
              "value": "object-right-bottom",
              "label": "Bottom Right"
            }
          ],
          "default": "object-center"
        }, 
        {
          "type": "select",
          "id": "image_position_mobile_1",
          "label": "Image Position 1",
          "options": [
            {
              "value": "object-left-top",
              "label": "Top Left"
            },
            {
              "value": "object-top",
              "label": "Top Center"
            },
            {
              "value": "object-right-top",
              "label": "Top Right"
            },
            {
              "value": "object-left",
              "label": "Middle Left"
            },
            {
              "value": "object-center",
              "label": "Center"
            },
            {
              "value": "object-right",
              "label": "Middle Right"
            },
            {
              "value": "object-left-bottom",
              "label": "Bottom Left"
            },
            {
              "value": "object-bottom",
              "label": "Bottom Center"
            },
            {
              "value": "object-right-bottom",
              "label": "Bottom Right"
            }
          ],
          "default": "object-center"
        },
        {
          "type": "header",
          "content": "Content 1 Settings"
        },
        {
          "type":"text",
          "id":"title_1",
          "label": "Title 1"
        },
        {
          "type": "url",
          "id": "url_1",
          "label": "Link 1"
        },
        {
          "type": "header",
          "content": "Image 2 Settings"
        },
        {
          "type": "image_picker",
          "id": "image_2",
          "label": "Image 2"
        },
        {
          "type": "select",
          "id": "image_position_2",
          "label": "Image Position 2",
          "options": [
            {
              "value": "object-left-top",
              "label": "Top Left"
            },
            {
              "value": "object-top",
              "label": "Top Center"
            },
            {
              "value": "object-right-top",
              "label": "Top Right"
            },
            {
              "value": "object-left",
              "label": "Middle Left"
            },
            {
              "value": "object-center",
              "label": "Center"
            },
            {
              "value": "object-right",
              "label": "Middle Right"
            },
            {
              "value": "object-left-bottom",
              "label": "Bottom Left"
            },
            {
              "value": "object-bottom",
              "label": "Bottom Center"
            },
            {
              "value": "object-right-bottom",
              "label": "Bottom Right"
            }
          ],
          "default": "object-center"
        }, 
        {
          "type": "select",
          "id": "image_position_mobile_2",
          "label": "Image Position 2",
          "options": [
            {
              "value": "object-left-top",
              "label": "Top Left"
            },
            {
              "value": "object-top",
              "label": "Top Center"
            },
            {
              "value": "object-right-top",
              "label": "Top Right"
            },
            {
              "value": "object-left",
              "label": "Middle Left"
            },
            {
              "value": "object-center",
              "label": "Center"
            },
            {
              "value": "object-right",
              "label": "Middle Right"
            },
            {
              "value": "object-left-bottom",
              "label": "Bottom Left"
            },
            {
              "value": "object-bottom",
              "label": "Bottom Center"
            },
            {
              "value": "object-right-bottom",
              "label": "Bottom Right"
            }
          ],
          "default": "object-center"
        },
        {
          "type": "header",
          "content": "Content 2 Settings"
        },
        {
          "type":"text",
          "id":"title_2",
          "label": "Title 2"
        },
        {
          "type": "url",
          "id": "url_2",
          "label": "Link 2"
        },
        {
          "type": "header",
          "content": "Image 3 Settings"
        },
        {
          "type": "image_picker",
          "id": "image_3",
          "label": "Image 3"
        },
        {
          "type": "select",
          "id": "image_position_3",
          "label": "Image Position 3",
          "options": [
            {
              "value": "object-left-top",
              "label": "Top Left"
            },
            {
              "value": "object-top",
              "label": "Top Center"
            },
            {
              "value": "object-right-top",
              "label": "Top Right"
            },
            {
              "value": "object-left",
              "label": "Middle Left"
            },
            {
              "value": "object-center",
              "label": "Center"
            },
            {
              "value": "object-right",
              "label": "Middle Right"
            },
            {
              "value": "object-left-bottom",
              "label": "Bottom Left"
            },
            {
              "value": "object-bottom",
              "label": "Bottom Center"
            },
            {
              "value": "object-right-bottom",
              "label": "Bottom Right"
            }
          ],
          "default": "object-center"
        }, 
        {
          "type": "select",
          "id": "image_position_mobile_3",
          "label": "Image Position 3",
          "options": [
            {
              "value": "object-left-top",
              "label": "Top Left"
            },
            {
              "value": "object-top",
              "label": "Top Center"
            },
            {
              "value": "object-right-top",
              "label": "Top Right"
            },
            {
              "value": "object-left",
              "label": "Middle Left"
            },
            {
              "value": "object-center",
              "label": "Center"
            },
            {
              "value": "object-right",
              "label": "Middle Right"
            },
            {
              "value": "object-left-bottom",
              "label": "Bottom Left"
            },
            {
              "value": "object-bottom",
              "label": "Bottom Center"
            },
            {
              "value": "object-right-bottom",
              "label": "Bottom Right"
            }
          ],
          "default": "object-center"
        },
        {
          "type": "header",
          "content": "Content 3 Settings"
        },
        {
          "type":"text",
          "id":"title_3",
          "label": "Title 3"
        },
        {
          "type": "url",
          "id": "url_3",
          "label": "Link 3"
        },
        {
          "type": "header",
          "content": "Image 4 Settings"
        },
        {
          "type": "image_picker",
          "id": "image_4",
          "label": "Image 4"
        },
        {
          "type": "select",
          "id": "image_position_4",
          "label": "Image Position 4",
          "options": [
            {
              "value": "object-left-top",
              "label": "Top Left"
            },
            {
              "value": "object-top",
              "label": "Top Center"
            },
            {
              "value": "object-right-top",
              "label": "Top Right"
            },
            {
              "value": "object-left",
              "label": "Middle Left"
            },
            {
              "value": "object-center",
              "label": "Center"
            },
            {
              "value": "object-right",
              "label": "Middle Right"
            },
            {
              "value": "object-left-bottom",
              "label": "Bottom Left"
            },
            {
              "value": "object-bottom",
              "label": "Bottom Center"
            },
            {
              "value": "object-right-bottom",
              "label": "Bottom Right"
            }
          ],
          "default": "object-center"
        }, 
        {
          "type": "select",
          "id": "image_position_mobile_4",
          "label": "Image Position 4",
          "options": [
            {
              "value": "object-left-top",
              "label": "Top Left"
            },
            {
              "value": "object-top",
              "label": "Top Center"
            },
            {
              "value": "object-right-top",
              "label": "Top Right"
            },
            {
              "value": "object-left",
              "label": "Middle Left"
            },
            {
              "value": "object-center",
              "label": "Center"
            },
            {
              "value": "object-right",
              "label": "Middle Right"
            },
            {
              "value": "object-left-bottom",
              "label": "Bottom Left"
            },
            {
              "value": "object-bottom",
              "label": "Bottom Center"
            },
            {
              "value": "object-right-bottom",
              "label": "Bottom Right"
            }
          ],
          "default": "object-center"
        },
        {
          "type": "header",
          "content": "Content 4 Settings"
        },
        {
          "type":"text",
          "id":"title_4",
          "label": "Title 4"
        },
        {
          "type": "url",
          "id": "url_4",
          "label": "Link 4"
        },
        {
          "type": "header",
          "content": "Image 5 Settings"
        },
        {
          "type": "image_picker",
          "id": "image_5",
          "label": "Image 5"
        },
        {
          "type": "select",
          "id": "image_position_5",
          "label": "Image Position 5",
          "options": [
            {
              "value": "object-left-top",
              "label": "Top Left"
            },
            {
              "value": "object-top",
              "label": "Top Center"
            },
            {
              "value": "object-right-top",
              "label": "Top Right"
            },
            {
              "value": "object-left",
              "label": "Middle Left"
            },
            {
              "value": "object-center",
              "label": "Center"
            },
            {
              "value": "object-right",
              "label": "Middle Right"
            },
            {
              "value": "object-left-bottom",
              "label": "Bottom Left"
            },
            {
              "value": "object-bottom",
              "label": "Bottom Center"
            },
            {
              "value": "object-right-bottom",
              "label": "Bottom Right"
            }
          ],
          "default": "object-center"
        }, 
        {
          "type": "select",
          "id": "image_position_mobile_5",
          "label": "Image Position 5",
          "options": [
            {
              "value": "object-left-top",
              "label": "Top Left"
            },
            {
              "value": "object-top",
              "label": "Top Center"
            },
            {
              "value": "object-right-top",
              "label": "Top Right"
            },
            {
              "value": "object-left",
              "label": "Middle Left"
            },
            {
              "value": "object-center",
              "label": "Center"
            },
            {
              "value": "object-right",
              "label": "Middle Right"
            },
            {
              "value": "object-left-bottom",
              "label": "Bottom Left"
            },
            {
              "value": "object-bottom",
              "label": "Bottom Center"
            },
            {
              "value": "object-right-bottom",
              "label": "Bottom Right"
            }
          ],
          "default": "object-center"
        },
        {
          "type": "header",
          "content": "Content 5 Settings"
        },
        {
          "type":"text",
          "id":"title_5",
          "label": "Title 5"
        },
        {
          "type": "url",
          "id": "url_5",
          "label": "Link 5"
        },
        {
          "type": "header",
          "content": "Image 6 Settings"
        },
        {
          "type": "image_picker",
          "id": "image_6",
          "label": "Image 6"
        },
        {
          "type": "select",
          "id": "image_position_6",
          "label": "Image Position 6",
          "options": [
            {
              "value": "object-left-top",
              "label": "Top Left"
            },
            {
              "value": "object-top",
              "label": "Top Center"
            },
            {
              "value": "object-right-top",
              "label": "Top Right"
            },
            {
              "value": "object-left",
              "label": "Middle Left"
            },
            {
              "value": "object-center",
              "label": "Center"
            },
            {
              "value": "object-right",
              "label": "Middle Right"
            },
            {
              "value": "object-left-bottom",
              "label": "Bottom Left"
            },
            {
              "value": "object-bottom",
              "label": "Bottom Center"
            },
            {
              "value": "object-right-bottom",
              "label": "Bottom Right"
            }
          ],
          "default": "object-center"
        }, 
        {
          "type": "select",
          "id": "image_position_mobile_6",
          "label": "Image Position 6",
          "options": [
            {
              "value": "object-left-top",
              "label": "Top Left"
            },
            {
              "value": "object-top",
              "label": "Top Center"
            },
            {
              "value": "object-right-top",
              "label": "Top Right"
            },
            {
              "value": "object-left",
              "label": "Middle Left"
            },
            {
              "value": "object-center",
              "label": "Center"
            },
            {
              "value": "object-right",
              "label": "Middle Right"
            },
            {
              "value": "object-left-bottom",
              "label": "Bottom Left"
            },
            {
              "value": "object-bottom",
              "label": "Bottom Center"
            },
            {
              "value": "object-right-bottom",
              "label": "Bottom Right"
            }
          ],
          "default": "object-center"
        },
        {
          "type": "header",
          "content": "Content 6 Settings"
        },
        {
          "type":"text",
          "id":"title_6",
          "label": "Title 6"
        },
        {
          "type": "url",
          "id": "url_6",
          "label": "Link 6"
        }
      ] 
    },
    {
      "type": "footer",
      "name": "Footer",
      "settings": [] 
    },
    {
      "type": "main-menu",
      "name": "Main Menu",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Menu Settings"
        },
        {
          "type": "link_list",
          "label": "Menu",
          "id": "menu"
        }
      ] 
    },
    {
      "name": "Link List",
      "type": "link-list",
      "settings": [
        {
          "type":"text",
          "id":"title",
          "label": "Title",
          "info": "For admin purposes only. If subpane, start with Subpane:"
        },
        {
          "type": "header",
          "content": "Structure Settings"
        },
        {
          "type": "checkbox",
          "id": "subpane_start",
          "label": "Subpane Start",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "subpane_end",
          "label": "Subpane End",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_divder",
          "label": "Set bottom divider",
          "default": false
        },
        {
          "type": "header",
          "content": "Display Settings"
        },
        {
          "type": "checkbox",
          "id": "show_desktop",
          "label": "Show desktop",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_mobile",
          "label": "Show mobile",
          "default": true
        },
        {
          "type": "header",
          "content": "Layout Settings"
        },
        {
          "type": "link_list",
          "label": "Link list-none",
          "id": "linklist_menu"
        },
        {
          "type": "header",
          "content": "Display Settings"
        },
        {
          "type": "checkbox",
          "id": "show_buttons",
          "label": "Show list as buttons"
        },
        {
          "type": "checkbox",
          "id": "linklist_columns",
          "label": "Linklist Columns",
          "default": false
        }
      ]
    },
    {
      "name": "Image with text",
      "type": "image-text",
      "settings": [
        {
          "type": "header",
          "content": "Structure Settings"
        },
        {
          "type": "checkbox",
          "id": "subpane_start",
          "label": "Subpane Start",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "subpane_end",
          "label": "Subpane End",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_divder",
          "label": "Set bottom divider",
          "default": false
        },
        {
          "type": "header",
          "content": "Display Settings"
        },
        {
          "type": "checkbox",
          "id": "show_desktop",
          "label": "Show desktop",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_mobile",
          "label": "Show mobile",
          "default": true
        },
        {
          "type": "header",
          "content": "Layout Settings"
        },
        {
          "type": "select",
          "id": "width",
          "label": "Width (Mobile)",
          "options": [
            {
              "value": "col-span-1",
              "label": "25%"
            },
            {
              "value": "col-span-2",
              "label": "50%"
            },
            {
              "value": "col-span-3",
              "label": "75%"
            },
            {
              "value": "col-span-4",
              "label": "100%"
            }
          ],
          "default": "col-span-4"
        },
        {
          "type": "select",
          "id": "desktop_width",
          "label": "Width (Desktop)",
          "options": [
            {
              "value": "lg:col-span-1",
              "label": "25%"
            },
            {
              "value": "lg:col-span-2",
              "label": "50%"
            },
            {
              "value": "lg:col-span-3",
              "label": "75%"
            },
            {
              "value": "lg:col-span-4",
              "label": "100%"
            }
          ],
          "default": "lg:col-span-4"
        },
        {
          "type": "header",
          "content": "Image Settings"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "select",
          "id": "image_position",
          "label": "Image Position",
          "options": [
            {
              "value": "object-left-top",
              "label": "Top Left"
            },
            {
              "value": "object-top",
              "label": "Top Center"
            },
            {
              "value": "object-right-top",
              "label": "Top Right"
            },
            {
              "value": "object-left",
              "label": "Middle Left"
            },
            {
              "value": "object-center",
              "label": "Center"
            },
            {
              "value": "object-right",
              "label": "Middle Right"
            },
            {
              "value": "object-left-bottom",
              "label": "Bottom Left"
            },
            {
              "value": "object-bottom",
              "label": "Bottom Center"
            },
            {
              "value": "object-right-bottom",
              "label": "Bottom Right"
            }
          ],
          "default": "object-center"
        },
        {
          "type": "select",
          "id": "image_position_mobile",
          "label": "Image Position",
          "options": [
            {
              "value": "object-left-top",
              "label": "Top Left"
            },
            {
              "value": "object-top",
              "label": "Top Center"
            },
            {
              "value": "object-right-top",
              "label": "Top Right"
            },
            {
              "value": "object-left",
              "label": "Middle Left"
            },
            {
              "value": "object-center",
              "label": "Center"
            },
            {
              "value": "object-right",
              "label": "Middle Right"
            },
            {
              "value": "object-left-bottom",
              "label": "Bottom Left"
            },
            {
              "value": "object-bottom",
              "label": "Bottom Center"
            },
            {
              "value": "object-right-bottom",
              "label": "Bottom Right"
            }
          ],
          "default": "object-center"
        },
        {
          "type": "header",
          "content": "Image Corner Settings"
        },
        {
          "type": "select",
          "id": "corners",
          "label": "Image Corners (Desktop)",
          "options": [
            {
              "value": "lg:rounded-none",
              "label": "Square"
            },
            {
              "value": "lg:rounded-3xl",
              "label": "Round Corners"
            }
          ],
          "default": "lg:rounded-none"
        },
        {
          "type": "select",
          "id": "corners_mobile",
          "label": "Image corners (Mobile)",
          "options": [
            {
              "value": "rounded-none",
              "label": "Square"
            },
            {
              "value": "rounded-3xl",
              "label": "Round Corners"
            }
          ],
          "default": "rounded-none"
        },
        {
          "type": "header",
          "content": "Content Settings"
        },
        {
          "type": "text",
          "label": "Title",
          "id": "title"
        },
        {
          "type": "textarea",
          "label": "Text",
          "id": "text"
        },
        {
          "type": "text",
          "label": "Button Text",
          "id": "button_text"
        },
        {
          "type": "url",
          "label": "Link",
          "id": "link"
        }
      ]
    }
  ]
}
{% endschema %} 

{% if section.settings.remove_section_condition != blank %}
  <script>
    window.addEventListener("load", (event) => {
      if ({{ section.settings.remove_section_condition }}) {
        document.getElementById('shopify-section-panel-menu').remove();
      }
    });
  </script>
{% endif %}


<aside data-modal="panel-nav" class="modal no-focus-penetration modal-left transition-transform duration-300 ease-in bottom-0 fixed left-0 pointer-events-none top-0 lg:top-main lg:h-main vh-full w-full lg:w-2/3 z-50 flex lg:flex-row flex-col justify-start">

  <section class="panes flex flex-col lg:w-1/2 w-full bg-near-white h-full overflow-y-scroll overscroll-contain lg:relative pointer-events-auto scroll-smooth">

    <div class="lg:hidden flex justify-end panel-close__container">
      {% render 'panel-close' %}
    </div>

    {% for pane in section.blocks %}

      {%- if pane.type == 'main-menu' %}
        
        {% render 'panel-item-main-menu' block:pane %}

      {%- elsif pane.type == 'parent' %}

        {% assign paneIndex = forloop.index %}

        {% assign dropdownTrigger = dropdown.settings.trigger | replace: '"','`' | prepend: '#mainMenu-' %}

        {% assign hasNavItems = false %}

        <script neptune-engage="{
          trigger:'{{ pane.settings.trigger }}',
          targets:[
            {
              selector:'#Pane_{{ pane.id }}',
              classes:{
                add:active
              },
              siblings:{
                classes:{
                  remove:active
                }
              }
            },
            {
              selector:'#Pane_{{ pane.id }} .panel__item--nav-item:first-of-type',
              classes:{
                add:active
              }
            },
            {
              selector:'.subpanes .pane.active',
              classes:{
                remove:active
              }
            },
            {% if section.blocks[forloop.index].type == 'nav-item' %}
              {% assign hasNavItems = true %}
              {% assign index = forloop.index | plus: 1 %}
              {% if section.blocks[index].settings.subpane_start == true %}
                {
                  selector:'#Pane_{{ section.blocks[index].id }}',
                  classes:{
                    add:active
                  },
                  siblings:{
                    classes:{
                      remove:active
                    }
                  }
                },
              {% endif %}
            {% endif %}
            {
              selector:'{{ pane.settings.trigger }}',
              classes:{
                add:active
              },
              siblings:{
                classes:{
                  remove:active
                }
              }
            },
            {
              selector:html,
              delay:10,
              attributes:[
                {
                  att:data-active-modal,
                  set:panel-nav
                }
              ]
            }
          ]
        }"></script>

        {% assign panelName = pane.settings.trigger |  remove: '#mainMenu-'  %}
        {% assign ab_selected_tab = section.settings.selected_tab | handleize |  strip %}

        <article id="Pane_{{ pane.id }}" class="pane {% if hasNavItems %}pane--has-nav-items{% endif %} hidden opacity-0 active-remove active:flex active:opacity-100 ease-in transition-opacity duration-150 delay-150 flex-wrap{% if cart.attributes['segment__test__default-menu'] and panelName == ab_selected_tab %} selected {% endif %} {% unless cart.attributes contains 'segment__test__default-menu' %}selected{% endunless %} ">
   
          {% render 'panel-items' offset:paneIndex breakon:'parent' blocks:section.blocks %}

        </article>

      {%- endif -%}

    {%- endfor -%} 

    {% render 'panel-item-search-bar' %}

    <div class="lg:hidden mt-auto flex flex-row items-center lg:p-10 p-5 text-xs uppercase pane__customer">
      <span>
        {% render 'icon' icon:'user', height:18, width:18 %}
      </span>
      {% unless customer %}
        <a href="/account/login" class="px-3 block leading-none">{{ 'customer.log_in' | t }}</a>
      {% else %}
        <a href="/account/logout" class="px-3 block leading-none">{{ 'customer.log_out' | t }}</a>
      {% endunless %}
    </div>

    <div class="absolute hidden lg:block top-0 right-0 panel-close">
      {% render 'panel-close' %}
    </div>
    
  </section>

  <section class="subpanes lg:w-1/2 w-full pointer-events-none lg:static absolute top-panel left-0 transition-transform ease-in duration-300 transform active:translate-x-0 translate-x-full lg:translate-x-0 h-panel lg:h-auto">

    {% for subpane in section.blocks %}
    
    {%- if subpane.settings.subpane_start == true %}
      
      {% assign subPaneIndex = forloop.index0 %}

      <article id="Pane_{{ subpane.id }}" class="pane hidden opacity-0 active-remove active:grid active:opacity-100 ease-in transition-opacity duration-150 delay-150 w-full grid-cols-4 gap-x-4 auto-rows-min lg:bg-white bg-near-white h-full overflow-y-scroll overscroll-contain pointer-events-auto pb-20 lg:pb-0">

        {% assign previous = forloop.index0 | minus: 1 %}
        
        <button 
          class="w-full col-span-4 border-b border-white bg-near-white flex justify-center px-5 py-2 text-xl font-highlight relative lg:hidden"
          neptune-engage="
            targets:{
              selector:'#Pane_{{ blocks[next].id }}',classes:remove:active,siblings:classes:remove:active,
              selector:'.subpanes',classes:remove:active
            }
        ">
          <span class="absolute h-full top-0 left-0 flex justify-center items-center">{% render 'icon' icon:'chevron-left' %}</span>
          <span>{{ section.blocks[previous].settings.title }}</span>
        </button>

        {% render 'panel-items' offset:subPaneIndex type:subpane.type blocks:section.blocks %}
        
      </article>
    {% endif %}
  {% endfor %}

    <div class="subpane__close pointer-events-auto lg:p-0 p-5 pb-0 lg:absolute lg:right-0 lg:top-0">
      {% render 'panel-close' %}
    </div>
    
  </section>

</aside>

