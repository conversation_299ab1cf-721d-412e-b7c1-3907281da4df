<!-- Searchspring IntelliSuggest Tracking -->
<script type="text/javascript" src="//cdn.searchspring.net/intellisuggest/is.min.js"></script>
<script type="text/javascript">
try{
    var product_code = "{{ product.variants[0].sku }}";
    IntelliSuggest.init({siteId:{{ settings.ss_site_id | json }}, context:"Product/" + product_code, seed:product_code});
    IntelliSuggest.viewItem({sku:product_code});
} catch(err) {}
</script>
<!-- END Searchspring IntelliSuggest -->