{% schema %}
{
	"name": "Flow",
	"settings": [
		{
			"type": "header",
			"content": "Flow Settings"
		},
    {
      "type": "header",
      "content": "Country info",
      "info": "All info for data below can be found here: https:\/\/docs.flow.io\/reference\/country#get-reference-countries"
    },
    {
      "type":"textarea",
      "label":"Countries",
      "id":"countries",
      "info": "Each country must be comma separate and in the follow format: iso_3166_3 (ex: AUS) | Currency (ex: AUD)  | Name (ex: Australia)  | Label (ex: Australia (AUD) ) | Language (ex: en)."
    }
	],
	"blocks": []
}
{% endschema %}

{% assign flowCountries = section.settings.countries | split: ',' %}

{%- capture flow_countries -%}
	{%- for country in flowCountries -%}
    {% assign country_data = country | split: '|' %}
		{
      "iso_3166_3": "{{ country_data[0] }}",
      "currency": "{{ country_data[1] }}",
      "name": "{{ country_data[2] }}",
      "label": "{{ country_data[3] }}",
      "language": "{{ country_data[4] }}"
    }{%- unless forloop.last == true -%},{%- endunless -%}
	{%- endfor -%}
{%- endcapture -%}

<script>
	//https://docs.flow.io/docs/enable-a-country-picker-shopify
	//https://docs.flow.io/reference/country#get-reference-countries
	//https://api.flow.io/reference/countries

	Flow.set('on', 'ready', function () {
	  window.flow.countryPicker.createCountryPicker({
	  	type: 'modal',
		  containerId: 'country-picker',
		  modalTitle: 'Country',
		  logo: false,
		  countries: [
		    {{ flow_countries }}
		  ]
		});
		Neptune.liquid.load('promos');
	});

</script>