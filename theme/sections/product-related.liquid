<div class="flex flex-wrap related-products pt-4 pb-8 lg:pt-0 px-4 px-8">

  {%- assign sets_with_products = 0 -%}

  {%- for block in section.blocks -%}

    {% capture swiperAttributes %}
      neptune-swiper
      data-swiper-loop="false"
      data-swiper-navigation='{
        "prevEl":".related-items-{{block.settings.label|handle}} .swiper-button-prev-unique",
        "nextEl":".related-items-{{block.settings.label|handle}} .swiper-button-next-unique",
        "disabledClass": "swiper-button-disabled"
      }'
      data-swiper-breakpoints='{
        "320": {
          "slidesPerView": 2.5,
          "spaceBetween": 20,
          "loop":false
        },
        "480": {
          "slidesPerView": 3.5,
          "spaceBetween": 20,
          "loop":false
        },
        "1024": {
          "slidesPerView": 5,
          "spaceBetween": 30,
          "loop":false
        }
      }'
    {% endcapture %}
    
    {%- if block.type == 'products' and sets_with_products < 2%}
    
      {%- assign tag_match = false -%}
      {%- assign meta_match = false -%}
      {%- assign has_products = false -%}
    
      {%- if block.settings.metafield != blank%}

        {%- assign namespace = block.settings.metafield | split: '.' | first -%}
        {%- assign key = block.settings.metafield | split: '.' | last -%}
        {%- assign meta_match = product.metafields[namespace][key] -%}

      {%- endif -%}

      {%- if block.settings.tags != blank%}
        {%- for tag in product.tags -%}
          {%- assign block_tags = block.settings.tags | split: ',' -%}
          {%- for block_tag in block_tags -%}
            {%- assign block_tag = block_tag | strip -%}
            {%- if tag == block_tag -%}
              {%- assign tag_match = tag -%}
              {%- break -%}
            {%- endif -%}
          {%- endfor -%}
          {%- if tag_match -%}{%- break -%}{%- endif -%}
        {%- endfor -%}
      {%- endif -%}

      {%- if tag_match or meta_match%}
      <div 
        role="region"
        aria-label="{{block.settings.label | remove: ': **value**' }}"
        class="relative w-full lg:pv-4 related-items-{{block.settings.label|handle}}">
        <h2 class="pt-4 pb-2 tracked-slight text-left">
          {%- assign label_value = tag_match -%}
          
          {%- if meta_match -%}
            {%- assign label_value = meta_match -%}
          {%- endif -%}
          
          {%- if label_value contains ':' -%}
            {%- assign label_value = label_value | split: ':' | last -%}
          {%- endif -%}
          
          {% if block.settings.link_title %}<a href="/collections/{{ block.settings.collection }}" class="link">{% endif %}
          <span class="block MonumentGrotesk-Bold leading-normal uppercase tracking-wide leading-tight text-base">{{ block.settings.label | remove: ': **value**' }}</span>
          {% if block.settings.link_title %}</a>{% endif %}
          <span class="block leading-tight text-base MonumentGrotesk-Regular">{{ label_value }}</span>
          {% unless block.settings.hide_view_all %}
            <a href="/collections/{{ block.settings.collection }}" class="block link underline leading-normal text-base">View All</a>
          {% endunless %}

        </h2>
        
        <div class="swiper-container w-full" {{ swiperAttributes }}>
            <div class="swiper-wrapper">
            {%- assign num_related_products = 0 -%}
                
            {%- assign meta_fallback = true -%} 
            
            {%- if meta_match -%}
              {%- paginate collections[block.settings.collection].products by 9999 -%}
              {%- for r_product in collections[block.settings.collection].products -%}
                {%- if block.settings.metafield and r_product.metafields[namespace][key] == meta_match and r_product.id != product.id -%}
                  {%- assign meta_fallback = false -%}
                  {%- assign tag_match = false -%}
                  
                  {%- assign has_products = true -%}
                  
                  <div class="swiper-slide">
                    {%- render 'product-item', product: r_product -%}
                    {%- assign num_related_products = num_related_products | plus: 1 -%}
                  </div>
                {%- endif -%}
              {%- endfor -%}
              {%- endpaginate -%}
            {%- endif -%}
                  
            {%- if tag_match and meta_fallback -%}
                  
              {%- paginate collections[block.settings.collection].products by 9999 -%}
                {%- for r_product in collections[block.settings.collection].products -%}
                  {%- if block.settings.tags and r_product.tags contains tag_match and r_product.id != product.id -%}
                    {%- assign has_products = true -%}
                    <div class="swiper-slide">
                      {%- render 'product-item', product: r_product -%}
                      {%- assign num_related_products = num_related_products | plus: 1 -%}
                    </div>
                  {%- endif -%}
                {%- endfor -%}
              {%- endpaginate -%}

          {%- endif -%}
                  
          </div>
          {% if block.settings.show_arrows %}
            {% render 'related-products-arrows' %}
          {% endif %}
        </div>
      </div>
      {%- endif -%}
    
      {%- if has_products -%}
      {%- assign sets_with_products = sets_with_products | plus: 1 -%}
      {%- endif -%}
    
    {%- else -%}
      {%- if product.tags contains block.settings.tag and sets_with_products < 2%}
          
      <div 
        role="region"
        aria-label="{{block.settings.label | remove: ': **value**' }}"
        class="relative w-full lg:pv-4 related-items-{{block.settings.label|handle}}">
        <h2 class="pt-4 pb-2 tracked-slight text-left">
          {% if block.settings.link_title %}<a href="/collections/{{ block.settings.collection }}" class="link">{% endif %}
          <span class="block MonumentGrotesk-Bold leading-normal uppercase tracking-wide leading-tight text-base">{{ block.settings.label }}</span>
          {% if block.settings.link_title %}</a>{% endif %}
          {% unless block.settings.hide_view_all %}
            <a href="/collections/{{ block.settings.collection }}" class="block link underline leading-normal text-base">View All</a>
          {% endunless %}
        </h2>
        
          <div class="swiper-container w-full" {{ swiperAttributes }}>
              <div class="swiper-wrapper">  
              {%- for r_product in collections[block.settings.collection].products limit: 12%}
                {%- if r_product.id != product.id -%}
                  <div class="swiper-slide">
                      {%- render 'product-item', product: r_product -%}
                  </div>
                  {%- assign num_related_products = num_related_products | plus: 1 -%}

                  {%- endif -%}
              {%- endfor -%}
              </div>
            {% if block.settings.show_arrows %}
              {% include 'related-products-arrows' %}
            {% endif %}
          </div>
        </div>
          
      {%- assign sets_with_products = sets_with_products | plus: 1 -%}

    {%- endif -%}
    
        {%- if block.settings.tag == blank and sets_with_products < 2%}

          <div 
            role="region"
            aria-label="{{block.settings.label | remove: ': **value**' }}"
            class="relative w-full lg:pv-4 related-items-{{block.settings.label|handle}}">
              <h2 class="pt-4 pb-2 tracked-slight text-left">
                {% if block.settings.link_title %}<a href="/collections/{{ block.settings.collection }}" class="link">{% endif %}
                <span class="block MonumentGrotesk-Bold leading-normal uppercase tracking-wide leading-tight text-base">{{ block.settings.label }}</span>
                {% if block.settings.link_title %}</a>{% endif %}
                {% unless block.settings.hide_view_all %}
                  <a href="/collections/{{ block.settings.collection }}" class="block link underline leading-normal text-base">View All</a>
                {% endunless %}
              </h2>
              
                <div class="swiper-container w-full" {{ swiperAttributes }}> 
                    <div class="swiper-wrapper">
                    {%- assign num_related_products = 0 -%}
                    {%- for r_product in collections[block.settings.collection].products limit: 12%}
                        {%- if r_product.id != product.id -%}
                            <div class="swiper-slide">
                                {%- include 'product-item', product: r_product -%}
                            </div>
                        {%- assign num_related_products = num_related_products | plus: 1 -%}
                        {%- endif -%}
                    {%- endfor -%}
                    </div>
                  {% if block.settings.show_arrows %}
                    {% include 'related-products-arrows' %}
                  {% endif %}
                </div>
            </div>
          
      {%- assign sets_with_products = sets_with_products | plus: 1 -%}
        {%- endif -%}
      {%- endif -%}

    {%- endfor -%}

  </div>


  <style>

    .related-products .quick-shop {
      display: none;
    }

    @media(min-width:320px){
      .related-products .swiper-slide:only-child {
        width: 40%;
        margin: 0;
      }
    }

    @media(min-width:480px){
      .related-products .swiper-slide:only-child {
        width: 28.57%;
        margin: 0;
      }
    }

    @media(min-width:1024px){
      .related-products .swiper-slide:only-child {
        width: 20%;
        margin: 0;
      }
    }
    
    @media(max-width:1023px){


      .related-products .product-item-info {
        padding-left: 1rem;
        opacity: 0;
        transition:opacity .2s ease;
      }
      .related-products .swiper-slide.swiper-slide-active .product-item-info {
        opacity:1;
      }
      .related-products button.swiper-prev,
      .related-products button.swiper-next{
        display:block;
        position: absolute;
        cursor: pointer;
        top: 50%;
        left: 0;
        transform: translateY(-40px);
        z-index: 4;
      }
      .related-products button.swiper-next{
        left:auto;
        right:0;
      }
    }
  </style>

  <script>
    setInterval(function(){
      document.querySelectorAll('.related-products .swiper-wrapper').forEach(function(wrapper){
        if(wrapper.innerHTML.trim() == '')
            wrapper.parentNode.parentNode.remove()
      })
    },200); 
  </script>

{% schema %}
  {
    "name": "Related Products",
    "settings": [
      
    ],
    "blocks": [
    {
      "type": "products",
      "name": "Related Products",
      "settings": [

        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "info": "For internal reference only, will not display on site"
        },
        {
          "type": "collection",
          "id": "collection",
          "label": "Collection Constraint",
          "info": "Relate only products from this collection for these tags"
        },

        {
          "type": "text",
          "id": "label",
          "label": "Product Set Label",
          "info": "Use **value** to include the tag/metafield value in the label"
        },
        {
          "type": "text",
          "id": "tags",
          "label": "Product Tags",
          "info": "If a product is tagged with any of these items, this set will display with products matching that tag."
        },
        {
          "type": "text",
          "id": "metafield",
          "label": "Metafield",
          "info": "If a metafield is specified, products that contain the same value in this metafield will display."
        },
        {
          "type": "checkbox",
          "id": "hide_view_all",
          "label": "Hide view all"
        },
        {
          "type": "checkbox",
          "id": "link_title",
          "label": "Link title"
        },
        {
          "type": "checkbox",
          "id": "show_arrows",
          "label": "Show arrows",
          "default": false
        }
      ]
    },
    {
      "type": "fallback",
      "name": "Fallback",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "info": "For internal reference only, will not display on site"
        },
        {
          "type": "text",
          "id": "label",
          "label": "Product Set Label"
        },
        {
          "type": "text",
          "id": "tag",
          "label": "Product Tag",
          "info": "If a product is tagged with any of this value, this set will display with Products from the designated Collection."
        },
        {
          "type": "collection",
          "id": "collection",
          "label": "Collection"
        },
        {
          "type": "checkbox",
          "id": "hide_view_all",
          "label": "Hide view all"
        },
        {
          "type": "checkbox",
          "id": "link_title",
          "label": "Link title"
        },
        {
          "type": "checkbox",
          "id": "show_arrows",
          "label": "Show arrows",
          "default": false
        }
      ]
    }
  ]
  }
{% endschema %}
