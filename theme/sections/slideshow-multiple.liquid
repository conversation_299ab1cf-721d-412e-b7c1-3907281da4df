{% schema %}
{
  "name": "Slideshow Multiple Slides",
  "settings": [
    {
      "type": "header",
      "content": "A/B Testing"
    },
    {
      "type": "checkbox",
      "id": "remove_section_condition",
      "label": "Remove Section",
      "default": false,
      "info": "This will hide the section in one of the experiment variations."
    },
    {
      "type": "header",
      "content": "Display Settings"
    },
    {
      "type": "checkbox",
      "id": "async",
      "label": "Asynchronously Load Section on Scroll",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_desktop",
      "label": "Show desktop",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_mobile",
      "label": "Show mobile",
      "default": true
    },
    {
      "type": "header",
      "content": "Slideshow Settings"
    },
    {
      "type": "number",
      "id": "slides_per_view",
      "label": "Slides per view (Desktop)",
      "default": 3
    },
    {
      "type": "number",
      "id": "spacebetween",
      "label": "Space between slides (Desktop)",
      "default": 20
    },
    {
      "type": "number",
      "id": "slides_per_view_mobile",
      "label": "Slides per view (Mobile)",
      "default": 2
    },
    {
      "type": "number",
      "id": "spacebetween_mobile",
      "label": "Space between slides (Mobile)",
      "default": 15
    },
    {
      "type": "number",
      "id": "limit",
      "label": "Number of Slides",
      "info": "For collections, products, and blog posts",
      "default": 12
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Autoplay"
    },
    {
      "type": "checkbox",
      "id": "loop",
      "label": "Loop"
    },
    {
      "type": "range",
      "id": "autoplay_slide_duration",
      "label": "Autoplay Slide Duration",
      "min": 3,
      "max": 8,
      "step": 1,
      "default": 3
    },
    {
      "type": "checkbox",
      "id": "arrows",
      "label": "Show arrows (Desktop)"
    },
    {
      "type": "checkbox",
      "id": "arrows_mobile",
      "label": "Show arrows (Mobile)"
    },
    {
      "type": "checkbox",
      "id": "arrows_outside",
      "label": "Show arrows outside slideshow"
    },
    {
      "type": "color",
      "label": "Arrow color",
      "id": "arrow_color"
    },
    {
      "type": "header",
      "content": "Section Display Settings"
    },
    {
      "type": "select",
      "id": "container",
      "label": "Container",
      "options": [
        {
          "value": "container",
          "label": "Container"
        },
        {
          "value": "w-full",
          "label": "Full width"
        }
      ],
      "default": "container"
    },
    {
      "type": "color",
      "label": "Background Color",
      "id": "background_color"
    },
    {
      "type": "header",
      "content": "Slide Display Settings"
    },
    {
      "type": "select",
      "id": "item_height",
      "label": "Image Banner Aspect Ratio (Desktop)",
      "info": "For sliders containing only Image Banners",
      "options": [
        {
          "value": "image",
          "label": "Image Height"
        },
        {
          "value": "lg:aspect-w-16 lg:aspect-h-9",
          "label": "16x9"
        },
        {
          "value": "lg:aspect-w-9 lg:aspect-h-16",
          "label": "9x16"
        },
        {
          "value": "lg:aspect-w-4 lg:aspect-h-3",
          "label": "4x3"
        },
        {
          "value": "lg:aspect-w-3 lg:aspect-h-4",
          "label": "3x4"
        },
        {
          "value": "lg:aspect-w-6 lg:aspect-h-4",
          "label": "6x4"
        },
        {
          "value": "lg:aspect-w-4 lg:aspect-h-6",
          "label": "4x6"
        },
        {
          "value": "lg:aspect-w-8 lg:aspect-h-5",
          "label": "8x5"
        },
        {
          "value": "lg:aspect-w-5 lg:aspect-h-8",
          "label": "5x8"
        },
        {
          "value": "lg:aspect-w-7 lg:aspect-h-5",
          "label": "7x5"
        },
        {
          "value": "lg:aspect-w-5 lg:aspect-h-7",
          "label": "5x7"
        },
        {
          "value": "lg:aspect-w-4 lg:aspect-h-5",
          "label": "4x5"
        },
        {
          "value": "lg:aspect-w-1 lg:aspect-h-1",
          "label": "1x1"
        }
      ],
      "default": "image"
    },
    {
      "type": "select",
      "id": "item_height_mobile",
      "label": "Image Banner Aspect Ratio (Mobile)",
      "info": "For sliders containing only Image Banners",
      "options": [
        {
          "value": "image",
          "label": "Image Height"
        },
        {
          "value": "aspect-w-16 aspect-h-9",
          "label": "16x9"
        },
        {
          "value": "aspect-w-9 aspect-h-16",
          "label": "9x16"
        },
        {
          "value": "aspect-w-4 aspect-h-3",
          "label": "4x3"
        },
        {
          "value": "aspect-w-3 aspect-h-4",
          "label": "3x4"
        },
        {
          "value": "aspect-w-6 aspect-h-4",
          "label": "6x4"
        },
        {
          "value": "aspect-w-4 aspect-h-6",
          "label": "4x6"
        },
        {
          "value": "aspect-w-8 aspect-h-5",
          "label": "8x5"
        },
        {
          "value": "aspect-w-5 aspect-h-8",
          "label": "5x8"
        },
        {
          "value": "aspect-w-7 aspect-h-5",
          "label": "7x5"
        },
        {
          "value": "aspect-w-5 aspect-h-7",
          "label": "5x7"
        },
        {
          "value": "aspect-w-4 aspect-h-5",
          "label": "4x5"
        },
        {
          "value": "aspect-w-1 aspect-h-1",
          "label": "1x1"
        }
      ],
      "default": "image"
    },
    {
      "type": "select",
      "id": "corners",
      "label": "Slide Corners (Desktop)",
      "options": [
        {
          "value": "br0-l",
          "label": "Square"
        },
        {
          "value": "br5-l",
          "label": "Round Corners"
        },
        {
          "value": "br5-l br--top-right-l",
          "label": "Top left Round"
        },
        {
          "value": "br5-l br--top-left-l",
          "label": "Top Right Round"
        },
        {
          "value": "br5-l br--bottom-right-l",
          "label": "Bottom Left Round"
        },
        {
          "value": "br5-l br--bottom-left-l",
          "label": "Bottom Right Round"
        },
        {
          "value": "br6-l",
          "label": "Large Round Corners"
        },
        {
          "value": "br6-l br--top-right-l",
          "label": "Large Top left Round"
        },
        {
          "value": "br6-l br--top-left-l",
          "label": "Large Top Right Round"
        },
        {
          "value": "br6-l br--bottom-right-l",
          "label": "Large Bottom Left Round"
        },
        {
          "value": "br6-l br--bottom-left-l",
          "label": "Large Bottom Right Round"
        }
      ],
      "default": "br0-l"
    },
    {
      "type": "select",
      "id": "corners_mobile",
      "label": "Slide corners (Mobile)",
      "options": [
        {
          "value": "br0",
          "label": "Square"
        },
        {
          "value": "br5",
          "label": "Round Corners"
        },
        {
          "value": "br5 br--top-right",
          "label": "Top left Round"
        },
        {
          "value": "br5 br--top-left",
          "label": "Top Right Round"
        },
        {
          "value": "br5 br--bottom-right",
          "label": "Bottom Left Round"
        },
        {
          "value": "br5 br--bottom-left",
          "label": "Bottom Right Round"
        },
        {
          "value": "br6",
          "label": "Large Round Corners"
        },
        {
          "value": "br6 br--top-right",
          "label": "Large Top left Round"
        },
        {
          "value": "br6 br--top-left",
          "label": "Large Top Right Round"
        },
        {
          "value": "br6 br--bottom-right",
          "label": "Large Bottom Left Round"
        },
        {
          "value": "br6 br--bottom-left",
          "label": "Large Bottom Right Round"
        }
      ],
      "default": "br0"
    },
    {
      "type": "header",
      "content": "Section Spacing Settings (Desktop)"
    },
    {
      "type": "select",
      "id": "section_top_margin",
      "label": "Margin top",
      "options": [
        {
          "value": "0",
          "label": "None"
        },
        {
          "value": "1",
          "label": ".25rem"
        },
        {
          "value": "2",
          "label": ".5rem"
        },
        {
          "value": "3",
          "label": "1rem"
        },
        {
          "value": "4",
          "label": "2rem"
        },
        {
          "value": "5",
          "label": "4rem"
        },
        {
          "value": "6",
          "label": "8rem"
        },
        {
          "value": "7",
          "label": "16rem"
        }
      ],
      "default": "2"
    },
    {
      "type": "select",
      "id": "section_bottom_margin",
      "label": "Margin bottom ",
      "options": [
        {
          "value": "0",
          "label": "None"
        },
        {
          "value": "1",
          "label": ".25rem"
        },
        {
          "value": "2",
          "label": ".5rem"
        },
        {
          "value": "3",
          "label": "1rem"
        },
        {
          "value": "4",
          "label": "2rem"
        },
        {
          "value": "5",
          "label": "4rem"
        },
        {
          "value": "6",
          "label": "8rem"
        },
        {
          "value": "7",
          "label": "16rem"
        }
      ],
      "default": "2"
    },
    {
      "type": "select",
      "id": "section_top_padding",
      "label": "Padding top",
      "options": [
        {
          "value": "0",
          "label": "None"
        },
        {
          "value": "1",
          "label": ".25rem"
        },
        {
          "value": "2",
          "label": ".5rem"
        },
        {
          "value": "3",
          "label": "1rem"
        },
        {
          "value": "4",
          "label": "2rem"
        },
        {
          "value": "5",
          "label": "4rem"
        },
        {
          "value": "6",
          "label": "8rem"
        },
        {
          "value": "7",
          "label": "16rem"
        }
      ],
      "default": "0"
    },
    {
      "type": "select",
      "id": "section_bottom_padding",
      "label": "Padding bottom ",
      "options": [
        {
          "value": "0",
          "label": "None"
        },
        {
          "value": "1",
          "label": ".25rem"
        },
        {
          "value": "2",
          "label": ".5rem"
        },
        {
          "value": "3",
          "label": "1rem"
        },
        {
          "value": "4",
          "label": "2rem"
        },
        {
          "value": "5",
          "label": "4rem"
        },
        {
          "value": "6",
          "label": "8rem"
        },
        {
          "value": "7",
          "label": "16rem"
        }
      ],
      "default": "0"
    },
    {
      "type": "header",
      "content": "Section Spacing Settings (Mobile)"
    },
    {
      "type": "select",
      "id": "section_top_margin_mobile",
      "label": "Margin top",
      "options": [
        {
          "value": "0",
          "label": "None"
        },
        {
          "value": "1",
          "label": ".25rem"
        },
        {
          "value": "2",
          "label": ".5rem"
        },
        {
          "value": "4",
          "label": "1rem"
        },
        {
          "value": "8",
          "label": "2rem"
        },
        {
          "value": "16",
          "label": "4rem"
        },
        {
          "value": "32",
          "label": "8rem"
        },
        {
          "value": "64",
          "label": "16rem"
        }
      ],
      "default": "2"
    },
    {
      "type": "select",
      "id": "section_bottom_margin_mobile",
      "label": "Margin bottom",
      "options": [
        {
          "value": "0",
          "label": "None"
        },
        {
          "value": "1",
          "label": ".25rem"
        },
        {
          "value": "2",
          "label": ".5rem"
        },
        {
          "value": "4",
          "label": "1rem"
        },
        {
          "value": "8",
          "label": "2rem"
        },
        {
          "value": "16",
          "label": "4rem"
        },
        {
          "value": "32",
          "label": "8rem"
        },
        {
          "value": "64",
          "label": "16rem"
        }
      ],
      "default": "2"
    },
    {
      "type": "select",
      "id": "section_top_padding_mobile",
      "label": "Padding top",
      "options": [
        {
          "value": "0",
          "label": "None"
        },
        {
          "value": "1",
          "label": ".25rem"
        },
        {
          "value": "2",
          "label": ".5rem"
        },
        {
          "value": "4",
          "label": "1rem"
        },
        {
          "value": "8",
          "label": "2rem"
        },
        {
          "value": "16",
          "label": "4rem"
        },
        {
          "value": "32",
          "label": "8rem"
        },
        {
          "value": "64",
          "label": "16rem"
        }
      ],
      "default": "0"
    },
    {
      "type": "select",
      "id": "section_bottom_padding_mobile",
      "label": "Padding bottom",
      "options": [
        {
          "value": "0",
          "label": "None"
        },
        {
          "value": "1",
          "label": ".25rem"
        },
        {
          "value": "2",
          "label": ".5rem"
        },
        {
          "value": "4",
          "label": "1rem"
        },
        {
          "value": "8",
          "label": "2rem"
        },
        {
          "value": "16",
          "label": "4rem"
        },
        {
          "value": "32",
          "label": "8rem"
        },
        {
          "value": "64",
          "label": "16rem"
        }
      ],
      "default": "0"
			},
			{
				"type": "header",
				"content": "Interactivity"
			},
			{
				"type": "select",
				"id": "image_effect",
				"label": "image effect",
				"options": [
					{
						"value": "",
						"label": "None"
					},
					{
						"value": "animate zoom-in",
						"label": "Zoom in"
					}
				],
				"default": ""
			}

  ],
  "blocks": [
    {
      "name": "Image Banner",
      "type": "image_banner",
      "settings": [
        {
          "type": "header",
          "content": "Image Banner Settings"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Banner Image (Desktop)"
        },
        {
          "type": "select",
          "id": "image_position",
          "label": "Image Position",
          "options": [
            {
              "value": "bg-top-left",
              "label": "Top Left"
            },
            {
              "value": "bg-top",
              "label": "Top Center"
            },
            {
              "value": "bg-top-right",
              "label": "Top Right"
            },
            {
              "value": "bg-left",
              "label": "Middle Left"
            },
            {
              "value": "bg-center",
              "label": "Center"
            },
            {
              "value": "bg-right",
              "label": "Middle Right"
            },
            {
              "value": "bg-bottom-left",
              "label": "Bottom Left"
            },
            {
              "value": "bg-bottom",
              "label": "Bottom Center"
            },
            {
              "value": "bg-bottom-right",
              "label": "Bottom Right"
            }
          ],
          "default": "bg-center"
        },
        {
          "type": "image_picker",
          "id": "image_mobile",
          "label": "Banner Image (Mobile)"
        }, 
        {
          "type": "select",
          "id": "image_position_mobile",
          "label": "Image Position",
          "options": [
            {
              "value": "bg-top-left",
              "label": "Top Left"
            },
            {
              "value": "bg-top",
              "label": "Top Center"
            },
            {
              "value": "bg-top-right",
              "label": "Top Right"
            },
            {
              "value": "bg-left",
              "label": "Middle Left"
            },
            {
              "value": "bg-center",
              "label": "Center"
            },
            {
              "value": "bg-right",
              "label": "Middle Right"
            },
            {
              "value": "bg-bottom-left",
              "label": "Bottom Left"
            },
            {
              "value": "bg-bottom",
              "label": "Bottom Center"
            },
            {
              "value": "bg-bottom-right",
              "label": "Bottom Right"
            }
          ],
          "default": "bg-center"
        },
        {
          "type": "header",
          "content": "Link Settings"
        },
        {
          "type": "url",
          "id": "content_url",
          "label": "Link URL"
        },
        {
          "type": "header",
          "content": "Video Banner Settings"
        },
        {
          "type": "text",
          "id": "video_url",
          "label": "Video mp4 url (Desktop)",
          "info": "For section height settings of: Full height, Three Quarter Height, Half Height, and Quarter Height. To get mp4 file: 1) Go to vimeo.com, 2) Click on desired video, 3) Click on settings, 4) Click on Video file"
        },
        {
          "type": "text",
          "id": "video_url_mobile",
          "label": "Video mp4 url (Mobile)",
          "info": "For section height settings of: Full height, Three Quarter Height, Half Height, and Quarter Height. To get mp4 file: 1) Go to vimeo.com, 2) Click on desired video, 3) Click on settings, 4) Click on Video file"
        },
        { 
          "type": "image_picker",
          "id": "poster_image",
          "label": "Video Poster Image"
        },
        {
          "type": "header",
          "content": "Content Item Settings"
        },
        {
          "type": "select",
          "id": "content_direction",
          "label": "Direction (Desktop)",
          "options": [
            {
              "value": "lg:flex-row",
              "label": "Row"
            },
            {
              "value": "lg:flex-row-reverse",
              "label": "Row Reverse"
            },
            {
              "value": "lg:flex-col",
              "label": "Column"
            },
            {
              "value": "lg:flex-col-reverse",
              "label": "Column Reverse"
            }
          ],
          "default": "lg:flex-col"
        },
        {
          "type": "select",
          "id": "content_direction_mobile",
          "label": "Direction (Mobile)",
          "options": [
            {
              "value": "flex-row",
              "label": "Row"
            },
            {
              "value": "flex-row-reverse",
              "label": "Row Reverse"
            },
            {
              "value": "flex-col",
              "label": "Column"
            },
            {
              "value": "flex-col-reverse",
              "label": "Column Reverse"
            }
          ],
          "default": "flex-col"
        },
        {
          "type": "select",
          "id": "content_gap",
          "label": "Gap (Desktop)",
          "options": [
            {
              "value": "lg:gap-0",
              "label": "None"
            },
            {
              "value": "lg:gap-xs",
              "label": "XS"
            },
            {
              "value": "lg:gap-sm",
              "label": "SM"
            },
            {
              "value": "lg:gap-base",
              "label": "MD / Base"
            },
            {
              "value": "lg:gap-lg",
              "label": "LG"
            },
            {
              "value": "lg:gap-xl",
              "label": "XL"
            },
            {
              "value": "lg:gap-2xl",
              "label": "2XL"
            },
            {
              "value": "lg:gap-3xl",
              "label": "3XL"
            },
            {
              "value": "lg:gap-4xl",
              "label": "4XL"
            },
            {
              "value": "lg:gap-5xl",
              "label": "5XL"
            },
            {
              "value": "lg:gap-6xl",
              "label": "6XL"
            },
            {
              "value": "lg:gap-7xl",
              "label": "7XL"
            }
          ],
          "default": "lg:gap-base"
        },
        {
          "type": "select",
          "id": "content_gap_mobile",
          "label": "Gap (Mobile)",
          "options": [
            {
              "value": "gap-0",
              "label": "None"
            },
            {
              "value": "gap-xs",
              "label": "XS"
            },
            {
              "value": "gap-sm",
              "label": "SM"
            },
            {
              "value": "gap-base",
              "label": "MD / Base"
            },
            {
              "value": "gap-lg",
              "label": "LG"
            },
            {
              "value": "gap-xl",
              "label": "XL"
            },
            {
              "value": "gap-2xl",
              "label": "2XL"
            }
          ],
          "default": "gap-base"
        },
        {
          "type": "header",
          "content": "Text Settings"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text color"
        },
        {
          "type": "select",
          "id": "text_align",
          "label": "Text Alignment (Desktop)",
          "options": [
            {
              "value": "lg:text-left",
              "label": "Left"
            },
            {
              "value": "lg:text-center",
              "label": "Center"
            },
            {
              "value": "lg:text-right",
              "label": "Right"
            },
            {
              "value": "lg:text-justify lg:text-justify-last",
              "label": "Justify"
            }
          ],
          "default": "lg:text-left"
        },
        {
          "type": "select",
          "id": "text_align_mobile",
          "label": "Text Alignment (Mobile)",
          "options": [
            {
              "value": "text-left",
              "label": "Left"
            },
            {
              "value": "text-center",
              "label": "Center"
            },
            {
              "value": "text-right",
              "label": "Right"
            },
            {
              "value": "text-justify text-justify-last",
              "label": "Justify"
            }
          ],
          "default": "text-left"
        },
        {
          "type": "header",
          "content": "Title Image Options"
        },
        {
          "type": "image_picker",
          "label": "Title Image",
          "id": "title_image"
        },
        {
          "type": "text",
          "label": "Title SVG",
          "id": "title_svg",
          "info": "Choose an SVG from your [files](/admin/settings/files) and paste the file name in the field above."
        },
        {
          "type": "text",
          "label": "Title Image/SVG Max width",
          "id": "title_image_width",
          "info": "Use a pixel or percentage value here.",
          "default": "100%"
        },
        {
          "type": "header",
          "content": "Pretitle Typeface Options"
        },
        {
          "type": "text",
          "id": "pretitle",
          "label": "Pretitle"
        },
        {
          "type": "select",
          "id": "pretitle_type",
          "label": "Typeface",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "font-pop",
              "label": "ABC Monument Grotesk Black"
            },
            {
              "value": "font-highlight",
              "label": "ABC Monument Grotesk Light"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "MonumentGrotesk-Regular"
        },
        {
          "type": "number",
          "id": "pretitle_size",
          "label": "Pretitle Font Size (Desktop)",
          "default": 11
        },
        {
          "type": "number",
          "id": "pretitle_size_mobile",
          "label": "Pretitle Font Size (Mobile)",
          "default": 11
        },
        {
          "type": "color",
          "id": "pretitle_color",
          "label": "Pretitle color",
          "default": "#504F4F"
        },
        {
          "type": "text",
          "id": "pretitle_leading",
          "label": "Preitle Line Height",
          "default": "1.38"
        },
        {
          "type": "text",
          "id": "pretitle_tracking",
          "label": "Pretitle Letter Spacing"
        },
        {
          "type": "header",
          "content": "Title Typeface Options"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "select",
          "id": "title_type",
          "label": "Typeface",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "font-pop",
              "label": "ABC Monument Grotesk Black"
            },
            {
              "value": "font-highlight",
              "label": "ABC Monument Grotesk Light"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "MonumentGrotesk-Bold"
        },
        {
          "type": "number",
          "id": "title_size",
          "label": "Title Font Size (Desktop)",
          "default": 60
        },
        {
          "type": "number",
          "id": "title_size_mobile",
          "label": "Title Font Size (Mobile)",
          "default": 50
        },
        {
          "type": "color",
          "id": "title_color",
          "label": "Title color",
          "default": "#000000"
        },
        {
          "type": "text",
          "id": "title_leading",
          "label": "Title Line Height",
          "default": "1.38"
        },
        {
          "type": "text",
          "id": "title_tracking",
          "label": "Title Letter Spacing"
        },
        {
          "type": "header",
          "content": "Subtitle Typeface Options"
        },
        {
          "type": "text",
          "id": "subtitle",
          "label": "Subtitle"
        },
        {
          "type": "select",
          "id": "subtitle_type",
          "label": "Typeface",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "font-pop",
              "label": "ABC Monument Grotesk Black"
            },
            {
              "value": "font-highlight",
              "label": "ABC Monument Grotesk Light"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "MonumentGrotesk-Regular"
        },
        {
          "type": "number",
          "id": "subtitle_size",
          "label": "Subtitle Font Size (Desktop)",
          "default": 20
        },
        {
          "type": "number",
          "id": "subtitle_size_mobile",
          "label": "Subtitle Font Size (Mobile)",
          "default": 20
        },
        {
          "type": "color",
          "id": "subtitle_color",
          "label": "Subtitle color",
          "default": "#000000"
        },
        {
          "type": "text",
          "id": "subtitle_leading",
          "label": "Subtitle Line Height",
          "default": "1.38"
        },
        {
          "type": "text",
          "id": "subtitle_tracking",
          "label": "Subtitle Letter Spacing"
        },
        {
          "type": "header",
          "content": "Featured Content Typeface Options"
        },
        {
          "type": "richtext",
          "id": "featured_content",
          "label": "Featured Content"
        },
        {
          "type": "select",
          "id": "featured_content_type",
          "label": "Typeface",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "font-pop",
              "label": "ABC Monument Grotesk Black"
            },
            {
              "value": "font-highlight",
              "label": "ABC Monument Grotesk Light"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "MonumentGrotesk-Regular"
        },
        {
          "type": "number",
          "id": "featured_content_size",
          "label": "Featured Content Font Size (Desktop)",
          "default": 16
        },
        {
          "type": "number",
          "id": "featured_content_size_mobile",
          "label": "Featured Content Font Size (Mobile)",
          "default": 16
        },
        {
          "type": "color",
          "id": "featured_content_color",
          "label": "Featured Content color",
          "default": "#000000"
        },
        {
          "type": "text",
          "id": "featured_leading",
          "label": "Featured Line Height",
          "default": "1.38"
        },
        {
          "type": "text",
          "id": "featured_tracking",
          "label": "Featured Letter Spacing"
        },
        {
          "type": "header",
          "content": "Content Typeface Options"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Content"
        },
        {
          "type": "header",
          "content": "HTML Options"
        },
        {
          "type": "html",
          "id": "html",
          "label": "HTML"
        },
        {
          "type": "checkbox",
          "id": "grid_styling",
          "label": "Grid Styling",
          "default": false,
          "info": "Display HTML content in a 2-column layout."
        },
        {
          "type": "header",
          "content": "Link Settings"
        },
        {
          "type": "select",
          "id": "link_element",
          "label": "Link Element",
          "options": [
            {
              "value": "",
              "label": "Button Only"
            },
            {
              "value": "block",
              "label": "Entire Block"
            }
          ],
          "default": ""
        },
        {
          "type": "header",
          "content": "Button Alignment Settings"
        },
        {
          "type": "select",
          "id": "button_horizontal_align",
          "label": "Justify (Desktop)",
          "options": [
            {
              "value": "lg:justify-start",
              "label": "Start"
            },
            {
              "value": "lg:justify-center",
              "label": "Center"
            },
            {
              "value": "lg:justify-end",
              "label": "End"
            },
            {
              "value": "lg:justify-between",
              "label": "Between"
            }
          ],
          "default": "lg:justify-start"
        },
        {
          "type": "select",
          "id": "button_horizontal_align_mobile",
          "label": "Justify (Mobile)",
          "options": [
            {
              "value": "justify-start",
              "label": "Start"
            },
            {
              "value": "justify-center",
              "label": "Center"
            },
            {
              "value": "justify-end",
              "label": "End"
            },
            {
              "value": "justify-between",
              "label": "Between"
            }
          ],
          "default": "justify-start"
        },
        {
          "type": "select",
          "id": "button_vertical_align",
          "label": "Self Align (Desktop)",
          "options": [
            {
              "value": "lg:self-start",
              "label": "Start"
            },
            {
              "value": "lg:self-center",
              "label": "Center"
            },
            {
              "value": "lg:self-end",
              "label": "End"
            }
          ],
          "default": "lg:self-center"
        },
        {
          "type": "select",
          "id": "button_vertical_align_mobile",
          "label": "Self Align (Mobile)",
          "options": [
            {
              "value": "self-start",
              "label": "Start"
            },
            {
              "value": "self-center",
              "label": "Center"
            },
            {
              "value": "self-end",
              "label": "End"
            }
          ],
          "default": "self-center"
        },
        {
          "type": "header",
          "content": "Button 1 Layout Settings"
        },
        {
          "type": "text",
          "id": "link_title",
          "label": "Link title"
        },
        {
          "type": "url",
          "id": "url",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "link_target",
          "label": "Link Target",
          "options": [
            {
              "value": "_blank",
              "label": "New Window"
            },
            {
              "value": "_self",
              "label": "Same Window"
            }
          ],
          "default": "_self"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Button Style",
          "options": [
            {
              "value": "button button--primary",
              "label": "Button Primary"
            },
            {
              "value": "button button--secondary",
              "label": "Button Secondary"
            },
            {
              "value": "button button--tertiary",
              "label": "Button Tertiary"
            },
            {
              "value": "button button--light",
              "label": "Button Light"
            },
            {
              "value": "button button--higlight",
              "label": "Button Highlight"
            },
            {
              "value": "button button--link",
              "label": "Button Text"
            },
            {
              "value": "button button--text",
              "label": "Text link"
            }
          ],
          "default": "button button--primary"
        },
        {
          "type": "color",
          "id": "btn_color",
          "label": "Button bg color",
          "default": "#f7f7f7"
        },
        {
          "type": "color",
          "id": "btn_text_color",
          "label": "Button text color",
          "default": "#222222"
        },
        {
          "type": "color",
          "id": "btn_border_color",
          "label": "Button border color",
          "default": "#222222"
        },
        {
          "type": "color",
          "id": "btn_hover_color",
          "label": "Button bg color (hover)"
        },
        {
          "type": "color",
          "id": "btn_hover_text_color",
          "label": "Button text color (hover)"
        },
        {
          "type": "color",
          "id": "btn_hover_border_color",
          "label": "Button border color (hover)"
        },
        {
          "type": "number",
          "id": "button_size",
          "label": "Button Font Size",
          "info": "Optional"
        },
        {
          "type": "number",
          "id": "button_size_mobile",
          "label": "Button Font Size (Mobile)",
          "info": "Optional"
        },
        {
          "type": "header",
          "content": "Button 2 Layout Settings"
        },
        {
          "type": "text",
          "id": "link_title_two",
          "label": "Link title"
        },
        {
          "type": "url",
          "id": "url_two",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "link_target_two",
          "label": "Link Target",
          "options": [
            {
              "value": "_blank",
              "label": "New Window"
            },
            {
              "value": "_self",
              "label": "Same Window"
            }
          ],
          "default": "_self"
        },
        {
          "type": "select",
          "id": "button_style_two",
          "label": "Button Style",
          "options": [
            {
              "value": "button button--primary",
              "label": "Button Primary"
            },
            {
              "value": "button button--secondary",
              "label": "Button Secondary"
            },
            {
              "value": "button button--tertiary",
              "label": "Button Tertiary"
            },
            {
              "value": "button button--light",
              "label": "Button Light"
            },
            {
              "value": "button button--higlight",
              "label": "Button Highlight"
            },
            {
              "value": "button button--link",
              "label": "Button Text"
            },
            {
              "value": "button button--text",
              "label": "Text link"
            }
          ],
          "default": "button button--primary"
        },
        {
          "type": "color",
          "id": "btn_color_two",
          "label": "Button bg color",
          "default": "#f7f7f7"
        },
        {
          "type": "color",
          "id": "btn_text_color_two",
          "label": "Button text color",
          "default": "#000000"
        },
        {
          "type": "color",
          "id": "btn_border_color_two",
          "label": "Button border color",
          "default": "#000000"
        },
        {
          "type": "color",
          "id": "btn_hover_color_two",
          "label": "Button bg color (hover)"
        },
        {
          "type": "color",
          "id": "btn_hover_text_color_two",
          "label": "Button text color (hover)"
        },
        {
          "type": "color",
          "id": "btn_hover_border_color_two",
          "label": "Button border color (hover)"
        },
        {
          "type": "number",
          "id": "button_size_two",
          "label": "Button Font Size",
          "info": "Optional"
        },
        {
          "type": "number",
          "id": "button_size_mobile_two",
          "label": "Button Font Size (Mobile)",
          "info": "Optional"
        },
        {
          "type": "header",
          "content": "Content Layout Settings"
        },
        {
          "type": "header",
          "content": "Desktop Content Layout Settings"
        },
        {
          "type": "select",
          "id": "content_width",
          "label": "Content Width",
          "options": [
            {
              "value": "lg:w-full",
              "label": "100%",
              "group": "Percentage"
            },
            {
              "value": "lg:w-3/4",
              "label": "75%",
              "group": "Percentage"
            },
            {
              "value": "lg:w-3/5",
              "label": "60%",
              "group": "Percentage"
            },
            {
              "value": "lg:w-1/2",
              "label": "50%",
              "group": "Percentage"
            },
            {
              "value": "lg:w-1/4",
              "label": "25%",
              "group": "Percentage"
            },
            {
              "value": "lg:max-w-xl",
              "label": "XS",
              "group": "Fixed"
            },
            {
              "value": "lg:max-w-2xl",
              "label": "SM",
              "group": "Fixed"
            },
            {
              "value": "lg:max-w-3xl",
              "label": "MD",
              "group": "Fixed"
            },
            {
              "value": "lg:max-w-4xl",
              "label": "LG",
              "group": "Fixed"
            },
            {
              "value": "lg:max-w-5xl",
              "label": "XL",
              "group": "Fixed"
            },
            {
              "value": "lg:max-w-6xl",
              "label": "2XL",
              "group": "Fixed"
            }
          ],
          "default": "lg:w-full"
        },
        {
          "type": "select",
          "id": "content_position",
          "label": "Content Position",
          "options": [
            {
              "value": "lg:items-start lg:justify-start",
              "label": "Top Left"
            },
            {
              "value": "lg:items-center lg:justify-start",
              "label": "Top Center"
            },
            {
              "value": "lg:items-end lg:justify-start",
              "label": "Top Right"
            },
            {
              "value": "lg:items-start lg:justify-center",
              "label": "Middle Left"
            },
            {
              "value": "lg:items-center lg:justify-center",
              "label": "Middle Center"
            },
            {
              "value": "lg:items-end lg:justify-center",
              "label": "Middle Right"
            },
            {
              "value": "lg:items-start lg:justify-end",
              "label": "Bottom Left"
            },
            {
              "value": "lg:items-center lg:justify-end",
              "label": "Bottom Center"
            },
            {
              "value": "lg:items-end lg:justify-end",
              "label": "Bottom Right"
            }
          ],
          "default": "lg:items-center lg:justify-center"
        },
        {
          "type": "select",
          "id": "content_vertical_padding",
          "label": "Content Vertical Padding",
          "options": [
            {
              "value": "lg:py-0",
              "label": "None"
            },
            {
              "value": "lg:py-1",
              "label": ".25rem"
            },
            {
              "value": "lg:py-2",
              "label": ".5rem"
            },
            {
              "value": "lg:py-4",
              "label": "1rem"
            },
            {
              "value": "lg:py-8",
              "label": "2rem"
            },
            {
              "value": "lg:py-16",
              "label": "4rem"
            },
            {
              "value": "lg:py-32",
              "label": "8rem"
            },
            {
              "value": "lg:py-64",
              "label": "16rem"
            }
          ],
          "default": "lg:py-0"
        },
        {
          "type": "select",
          "id": "content_horizontal_padding",
          "label": "Content Horizontal Padding",
          "options": [
            {
              "value": "lg:px-0",
              "label": "None"
            },
            {
              "value": "lg:px-1",
              "label": ".25rem"
            },
            {
              "value": "lg:px-2",
              "label": ".5rem"
            },
            {
              "value": "lg:px-4",
              "label": "1rem"
            },
            {
              "value": "lg:px-8",
              "label": "2rem"
            },
            {
              "value": "lg:px-16",
              "label": "4rem"
            },
            {
              "value": "lg:px-32",
              "label": "8rem"
            },
            {
              "value": "lg:px-64",
              "label": "16rem"
            }
          ],
          "default": "lg:px-0"
        },
        {
          "type": "header",
          "content": "Mobile Content Layout Settings"
        },
        {
          "type": "select",
          "id": "content_position_mobile",
          "label": "Content Position",
          "options": [
            {
              "value": "h-full",
              "label": "None"
            },
            {
              "value": "items-center justify-start",
              "label": "Top"
            },
            {
              "value": "items-center justify-center",
              "label": "Middle"
            },
            {
              "value": "items-center justify-end",
              "label": "Bottom"
            }
          ],
          "default": "items-center justify-start"
        },
        {
          "type": "select",
          "id": "content_vertical_padding_mobile",
          "label": "Content Vertical Padding",
          "options": [
            {
              "value": "py-0",
              "label": "None"
            },
            {
              "value": "py-1",
              "label": ".25rem"
            },
            {
              "value": "py-2",
              "label": ".5rem"
            },
            {
              "value": "py-4",
              "label": "1rem"
            },
            {
              "value": "py-8",
              "label": "2rem"
            },
            {
              "value": "py-16",
              "label": "4rem"
            },
            {
              "value": "py-32",
              "label": "8rem"
            },
            {
              "value": "py-64",
              "label": "16rem"
            }
          ],
          "default": "py-0"
        },
        {
          "type": "select",
          "id": "content_horizontal_padding_mobile",
          "label": "Content Horizontal Padding",
          "options": [
            {
              "value": "px-0",
              "label": "None"
            },
            {
              "value": "px-1",
              "label": ".25rem"
            },
            {
              "value": "px-2",
              "label": ".5rem"
            },
            {
              "value": "px-4",
              "label": "1rem"
            },
            {
              "value": "px-8",
              "label": "2rem"
            },
            {
              "value": "px-16",
              "label": "4rem"
            },
            {
              "value": "px-32",
              "label": "8rem"
            },
            {
              "value": "px-64",
              "label": "16rem"
            }
          ],
          "default": "px-0"
        },
        {
          "type": "header",
          "content": "Desktop Text Format Settings"
        },
        {
          "type": "select",
          "id": "title_element",
          "label": "Heading Type",
          "options": [
            {
              "value": "h1",
              "label": "H1"
            },
            {
              "value": "h2",
              "label": "H2"
            },
            {
              "value": "h3",
              "label": "H3"
            },
            {
              "value": "h4",
              "label": "H4"
            },
            {
              "value": "h5",
              "label": "H5"
            }
          ],
          "default": "h2"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Slideshow Multiple Slides",
      "category": "Advanced",
      "settings": {},
      "blocks": []
    }
  ]
}

{% endschema %}

{% include 'async-section' skeleton:'<div class="p-16 w-full h-50v bg-white"><div class="bg-gray-100 p-16 rounded-2xl h-full w-full drop-shadow-lg flex flex-col items-center justify-center"><span class="sr-only">Skeleton</span><span class="bg-gray-200 h-24 w-24"></span><span class="bg-gray-200 h-2 w-1/4 mt-4"></span><span class="bg-gray-200 h-2 w-1/4 mt-4"></span><span class="bg-gray-200 h-2 w-1/4 mt-4"></span><span class="bg-gray-200 h-2 w-1/4 mt-4"></span></div></div>' %}
{% unless defer_section %}

<div id="multipleSlidesWrapper-{{section.id}}" class="{% if section.settings.show_desktop == true and section.settings.show_mobile == true %} block {% elsif section.settings.show_desktop == true and section.settings.show_mobile == false %} hidden lg:block{% elsif section.settings.show_desktop == false and section.settings.show_mobile == true %} block lg:hidden{% endif %} {% if section.settings.remove_section_condition %} vwo-hide{% endif %}">
  <section class="section-{{section.id}} nav-transparent section-slideshow relative flex-col flex-wrap items-center justify-center lg:mt-{{ section.settings.section_top_margin }} lg:mb-{{ section.settings.section_bottom_margin }}  mt-{{ section.settings.section_top_margin_mobile }} mb-{{ section.settings.section_bottom_margin_mobile }} lg:pt-{{ section.settings.section_top_margin }} lg:pb-{{ section.settings.section_bottom_margin }} pt-{{ section.settings.section_top_margin_mobile }} pb-{{ section.settings.section_bottom_margin_mobile }}" {% if section.settings.background_color %}style="background-color: {{ section.settings.background_color }};"{% endif %}>

    <div class="multipleSlidesWrapper {{ section.settings.container }} w-full {% if section.settings.slides_per_view <= 1 %} h-full {% endif %}"> 

      <div class="relative">
        <div class="w-full h-full">
  
          <div 
            class="swiper-container h-full"
            neptune-swiper
            data-swiper-mousewheel='{ "forceToAxis": true}'
            data-swiper-update-on-images-ready="true" 
            data-swiper-effect="slide" 
            data-swiper-watch-verflow="true"
            data-swiper-preload-images="true"
            data-swiper-observer="true"
            data-swiper-observe-parents="true"
            data-swiper-observe-slide-children="true"
            data-swiper-update-on-images-ready="true"
            data-swiper-centered-slides="true"
            data-swiper-centered-slides-bounds="true"
            data-swiper-slide-change="console.log('Slide Change');"

            {% if section.settings.autoplay == true %}
              data-swiper-autoplay='{
                "delay": {{ section.settings.autoplay_slide_duration }}000
              }'
            {% endif %}
            data-swiper-loop="{{ section.settings.loop }}" 
            data-swiper-breakpoints='{
              "1": {
                "spaceBetween": {{ section.settings.spacebetween_mobile }},
                "slidesPerView": {{ section.settings.slides_per_view_mobile }},
                "autoHeight": true
                
              },
              "1025": {
                "spaceBetween": {{ section.settings.spacebetween }},
                "slidesPerView": {{ section.settings.slides_per_view }},
                "autoHeight": true
              }
            }'
            data-swiper-navigation='{
              "nextEl": ".section-{{section.id}} .swiper-button-next-unique", "prevEl": ".section-{{section.id}} .swiper-button-prev-unique" }'
            >

						<div class="swiper-wrapper h-full items-stretch"
							neptune-surface="{
								'windowEdge': 'bottom',
								'elementEdge': 'top',
								'targets': [
									{
										'engage:action': {
											'classes': {
												'add': ['active']
											}
										}
									}
								]
							}"
						>
              {% assign banner_aspect_ratio = true %}
              {% for block in section.blocks %}
                {% if block.type != "image_banner" %}
                  {% assign banner_aspect_ratio = false %}
                {% endif %}
              {% endfor %}

              {% for block in section.blocks %}
                {% if block.type == 'image_banner' %}
                  <article 
                    id="block-{{ block.id }}"
                    data-pretitle='{{ block.settings.pretitle | json }}'
                    data-title='{{ block.settings.title | json }}'
                    data-subtitle='{{ block.settings.subtitle | json }}'
                    data-featuredcontent='{{ block.settings.featured_content | json }}'
                    class="swiper-slide swiper-slide__card flex flex-col self-stretch w-full h-full"
                    {% if block.settings.cursor_image != blank or block.settings.cursor_svg != blank or block.settings.cursor != blank %}neptune-cursor="{text: 'this is my text', selector: '.custom-cursor'}"{% endif %}
                  >
                    {% if block.settings.link_element == 'block' and block.settings.url != blank %}
                      <a class="block w-full h-full link text-black relative {% if section.settings.height_mobile contains 'content-below-height' %}flex lg:block flex-col{% else %}block{% endif %}" target="{{ block.settings.link_target }}" href="{{ block.settings.url }}">
                    {% endif %}

                      {% render 'content-item', sectionType: 'flexible-grid', section: section.settings, block: block.settings, blockId: block.id %}
                    
                    {% if block.settings.link_element == 'block' and block.settings.url != blank %}
                      </a>
                    {% endif %}
                  </article>
                {% endif %}
              {% endfor %}
            
            </div>

          </div>

          <div class="slideshow-controls animate text-center relative flex items-center flex-col">
            <div class="slideshow-controls--arrows absolute top-0 h-full w-full">
              <button onclick="_n.qs('#multipleSlidesWrapper-{{section.id}} .swiper-container').swiper.slidePrev();" class="swiper-prev pointer swiper-button-prev-unique lg:px-5 pt-0 border-0 bg-transparent absolute z-3 left-0 top-0 h-full {% if section.settings.arrows_outside != blank %} swiper-button-out {% endif %} {% if section.settings.arrows != blank and section.settings.arrows_mobile != blank %} flex {% elsif section.settings.arrows != blank and section.settings.arrows_mobile == blank %} hidden lg:flex {% elsif section.settings.arrows == blank and section.settings.arrows_mobile != blank %} flex lg:hidden {% else %} hidden {% endif %} items-center flex-col justify-center">
                <span class="icon block w-8" style="color: {{ section.settings.arrow_color }}">
                  {%- render 'icon-arrow-long-left' -%}
                </span>
              </button>
              <button onclick="_n.qs('#multipleSlidesWrapper-{{section.id}} .swiper-container').swiper.slideNext();" class="swiper-next pointer swiper-button-next-unique lg:px-5 pt-0 border-0 bg-transparent absolute z-3 right-0 top-0 h-full {% if section.settings.arrows_outside != blank %} swiper-button-out {% endif %} {% if section.settings.arrows != blank and section.settings.arrows_mobile != blank %} flex {% elsif section.settings.arrows != blank and section.settings.arrows_mobile == blank %} hidden lg:flex {% elsif section.settings.arrows == blank and section.settings.arrows_mobile != blank %} flex lg:hidden {% else %} hidden {% endif %} items-center flex-col justify-center">
                <span class="icon block w-8" style="color: {{ section.settings.arrow_color }}">
                  {%- render 'icon-arrow-long-right' -%}
                </span>
              </button>
            </div>
            <div class="slideshow-controls--content animate"></div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>

{% endunless %}
