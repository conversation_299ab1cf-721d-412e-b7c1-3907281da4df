{% comment %} 


This is generated by webpack so don't edit this unless you want to be sad when your work gets overwritten in production.
You can edit this file in ../../components/** 


{% endcomment %}






<script>window.pseudoCollection = {{pseudo | json}};</script>
<script>window.tagFiltered = {% if current_tags.size == 1 %}true{% else %}false{% endif %};</script>
<script>window.searchPerformed = {{ search.performed | json}};</script>

<div class="collection__filters_wrap flex flex-row relative w-full container mx-auto pt-8">
  <div 
   id="collectionFilters" 
   neptune-permanent
   neptune-transport="{append: 'body', breakpoints:{960:{prepend:.collection__filters_wrap}}}" 
   class="lg:w-1/6"
  >
    <div class="grid mx-0 p-4 p-6 w-full">
    {% for i in (1..5) %}
      <div class="border-b gap-4 grid pb-4">
        <div class="h-4 bg-gray-100 w-full mb-0"></div>
        <div class="h-4 bg-gray-100 w-2/3 mb-0"></div>
        <div class="h-4 bg-gray-100 w-3/4 mb-0"></div>
        <div class="h-4 bg-gray-100 w-2/5 mb-0"></div>
      </div>
    {% endfor %}
    </div>

    <div class="w-full absolute static-l top-0 bottom-0 overflow-y-scroll overflow-y-auto-l mt4 pb5 hidden">
      <button 
      class="filters__mobile_close w3 pa3 icon bg-transparent black bn dn-l db fixed z-2 top-0 right-0"
      neptune-engage='{
        on:click,
        targets:[
        {
          selector: "#collectionFilters",
          "classes":{
            "toggle":"active"
          },
          "attributes": [
          {
            "att": "aria-hidden",
            "toggle":["true", "false"]
          }]
        },
        {
          selector:.main-content,
          classes:add:z-1
        },
        {
          selector:html,
          attributes:[{
            att:data-active-modal-mobile
            set:_remove
          }]
        },
        {
          selector:"[data-return-focus]"
          attributes:[{
            att:data-return-focus 
            set:_remove
          }]
          focus:true
        }]
      }'>
      {% include 'icon-close' %}
    </button>
    {% comment %}{% section 'collection-filters' %}{% endcomment %}
    </div>
  </div>
  <div id="collectionEndless" class="w-full animate active sticky-l top-0 bg-nearest-white">
    <div class="flex justify-between items-center px-4">
      <h1 class="capitalize headline ph3-l text-lg tracking-normal font-medium">
        {% if search.results.size > 1 %}
        {{ "general.search.heading.other" | t }}
        {% else %}
        {{ "general.search.heading.one" | t }}
        {% endif %}
        {{ search.terms }}
      </h1>

      <button 
        class="ml-auto mr-4 md:hidden"
        neptune-engage="{
          on:click,
          targets:[
            {selector:'.collection-products', classes:toggle:['grid-cols-1', 'grid-cols-2']}
          ]
        }"
      >
        {% render 'icon' icon: 'layout' %}
      </button>

      <p class="mb0 brand-bold mid-gray hidden md:block" neptune-liquid="{topic: AllProducts, source: Collection}">{% raw %}{% if products.length > 1 %}{{ products.length }} results{% endif %}{% endraw  %}</p>
        <div class="bg-nearest-white md:hidden">
        <button 
          class="button--secondary br-pill"
          neptune-engage='{
            on:click,
            targets: [
              {
                selector: "_self",
                "attributes": [
                  {
                    "att": "aria-expanded",
                    "toggle":["true", "false"]
                  }
                ]
              },
              {
                selector: "#collectionFilters",
                "classes":{
                  "toggle":"active"
                },
                "attributes": [
                {
                  "att": "aria-hidden",
                  "toggle":["true", "false"]
                }]
              },
              {
                selector:.header__nav,
                classes:remove:active
              },
              {
                selector:.main-content,
                classes:remove:z-1
              },
              {
                selector:html,
                attributes:[{
                att:data-active-modal-mobile,
                set:collectionFilters
                }]
              }
            ]
          }'
        >
          Filters
        </button>
      </div>
    </div>
    <script>
      window.collectionConfig = {{ section.settings | json }};
      window.collectionConfig.subsort = {{ collection.metafields.merch.subsort | default: false | json}};
    </script>

      <div class="{{ collection_classes | strip_newlines }} plp bg-white b--none">

          
        <div class="collection-container bg-nearest-white">

          <div 
            class="collection-products collection__product mx-0 grid grid-cols-2 md:grid-cols-4 lg:p-1 p-0-75 bg-nearest-white" 
            neptune-liquid="{
              topic:Products,
              source:Collection,
              append:[productBadges,Filters,collectionBanners],
              store:true
            }"
            >
            
            {% raw %}

            {% for product in loader.activeProducts %}        

              {% assign index = forloop.index %}
              {% for banner in collectionBanners %}
                {% if banner.position == index %}
                  <article
                    class="{{banner.mobile_width}} {{banner.desktop_width}} p-0-75"
                    >
                    <div class="relative flex items-{{banner.align}} justify-{{banner.justify}} {{banner.color}}  w-full h-full">
                      <img loading="lazy" {{'s'}}rc="{{banner.image}}" alt="" class="inline-img w-full h-full object-cover absolute top-0 left-0 right-0 bottom-0">
                      <div class="relative p-1 lg:p-1-5">
                        <h3>{{banner.heading}}</h3>
                        <h4>{{banner.subheading}}</h4>
                        <a {{'h'}}ref="{{banner.url}}" class="br-pill py-0-75 uppercase px-1 font-bold text-sm tracked mt-0-5 nl2-l nr2-l no-underline inline-block {{banner.button_color}}">{{banner.button}}</a>
                      </div>
                    </div>
                  </article>
                {% endif %}
              {% endfor %}
              
              {% endraw %}

              {% render 'product-item' %}

              {% raw %}

            {% endfor %}

            {% endraw %}
          
          </div>
          
          <div class="collection-pagination w-full flex items-center justify-center pointer-none sticky bottom-0" neptune-liquid="{topic:Pagination,source:Collection.pagination}">
            {% raw %}
            {% unless total_pages == 1  or total_pages == 0 %}
              <div class="pb-8">
                <div class="flex space-x-4">
                  {% assign active = 'dim' %}
                  {% if current_page == 1 %}
                    {% assign active = 'opacity-50' %}
                  {% endif %}
                  <button class="{{active}} pagination__arrow_prev w-0-75 h-0-75 border-none bg-nearest-white rounded-full flex items-center justify-center mr-0-5" onclick="Collection.page({{current_page | minus: 1}},1)">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-left"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></svg>
                  </button>
                  
                  {% for italic in pages %}
                  {% assign pageoffset = current_page | minus: italic | abs %}
                  {% if pageoffset != 0 and pageoffset != 1 and pageoffset != 2 and pageoffset != 3 %}{% continue %}{% endif %}
                  {% assign active = '' %}
                  {% if current_page == italic %}
                    {% assign active = 'active' %}
                  {% endif %}
                    <button class="{{active}} text-gray-400 active:text-black hover:text-black" onclick="Collection.page({{italic}},1)">{{italic}}</button>
                    {% assign last_page_shown = italic %}
                  {% endfor %}
                  {% assign active = 'dim' %}
                  {% if current_page == total_pages %}
                    {% assign active = 'o-50 pointer-none' %}
                  {% endif %}
                  {% if last_page_shown != total_pages %} 
                  <div class="w-0-75 h-0-75 border-none rounded-full flex items-center justify-center text-xs mx-0-5 font-bold">...</div>
                  {% endif %}

                  <button class="{{active}} pagination__arrow_next w-0-75 h-0-75 border-none bg-nearest-white rounded-full flex items-center justify-center ml-0-5" onclick="Collection.page({{current_page | plus: 1}},1)">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-right"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg>
                  </button>
                </div>
              </div>
            {% endunless %}
            {% endraw %}
          </div>

        <div data-next-products class="text-center pt-2 pb-4">
          <div class="grid grid-cols-2 md:grid-cols-4 lg:p-1 gap-6 p-0-75 bg-nearest-white" >
            {% for i in (1..8)  %}
              <div class="animate-pulse bg-gray-50 p-4">
                <div class="aspect-h-14 aspect-w-16 bg-gray-100">
                  <svg xmlns="http://www.w3.org/2000/svg" class="opacity-40 mx-auto w-10 absolute" viewBox="0 0 170.63 350.68"><defs><style>.cls-1{fill:#d4d4d4;}</style></defs><g id="Layer_2" data-name="Layer 2"><g id="Layer_1-2" data-name="Layer 1"><path class="cls-1" d="M170.61,75.27c-.2,2.29-.32,4.59-.59,6.87-.67,5.7-3.82,9.43-9.12,11.52-9.5,3.75-19.43,6.11-29.16,9.12-14.6,4.5-29.35,8.55-43.32,14.88-6.84,3.1-13.38,6.73-19,11.86-12.61,11.61-14.18,26.31-4.24,40.27,6,8.46,13.47,15.54,21.59,22,11.14,8.83,22.52,17.36,33.26,26.69a87.83,87.83,0,0,1,9,8.67c6.3,7.29,10,15.59,9.36,25.42a41.24,41.24,0,0,1-5.85,18.2c-6.25,10.78-15,19.32-24.59,27.07-23.63,19.13-50.45,32.59-78.5,43.78-7.56,3-15.27,5.69-22.85,8.69-2.34.93-3.64.17-4.87-1.68C.1,346-.12,343.23,0,340.3c.08-1.4,1-1.81,2.1-2.23,31.47-12.43,56.34-33.16,75.9-60.55a82.7,82.7,0,0,0,6.57-11.22c4.87-9.67,4.42-19.16-.7-28.58C79.72,230,74,223.51,68,217.18c-9.22-9.79-19.08-19-27.44-29.58-9-11.43-15.62-24-16.65-38.79-1.48-21.16,5.15-39.32,21.89-52.91C57.46,86.45,71,80.17,84.66,74.23A533.6,533.6,0,0,1,143.54,53c4.39-1.32,8.86-1.9,13.32-.62,8.24,2.35,11.83,8.6,13.22,16.49A29.48,29.48,0,0,1,170.61,75.27Z"/><path class="cls-1" d="M55,25a25,25,0,1,1,24.9,25.11A25,25,0,0,1,55,25Z"/></g></g></svg> 
                </div>
                <div class="gap-4 grid p-4">
                  <div class="bg-gray-100 h-4 w-full"></div>
                  <div class="bg-gray-100 h-4 w-1/3"></div>
                  <div class="bg-gray-100 h-4 w-2/5"></div>
                </div>
              </div>
            {% endfor %}
          </div>
        </div>

          <div class="text-center p-1 hidden" data-empty-products>
            <p class="block uppercase tracked moon-gray font-bold text-sm">No products match your selection</p>
          </div>

        </div>
      </div>

      <style>
        .spr-badge[data-rating*="0."],
        .spr-badge[data-rating*="1."],
        .spr-badge[data-rating*="2."] {
          display: none;
        }
      </style>
    </div>
  </div>
</div>
<script src="{{ 'collection-paginated.js' | asset_url }}" defer></script>
<style>
.htusb-ui-coll-boost {
	display: none !important;
}
</style>

{% schema %}
{
  "name": "Collection Products",
  "class": "collection--section",
  "settings": [
    {
      "id":"filter",
      "label":"Filter Method",
      "type":"select",
      "default": "server",
      "options":[
        {
          "label":"Server-Side",
          "value":"server"
        },
        {
          "label":"Client-Side",
          "value":"client"
        }
      ]
    },
    {
      "id":"page_size",
      "label":"Page Size",
      "type":"number",
      "default":60
    },
    {
      "id":"ignore_defer_sale",
      "label":"Mix Sale Products in Collections",
      "type":"text",
      "info":"comma-separated collection handles"
    },
    {
      "id":"session_storage",
      "label":"Store Collection Products in Browser Session",
      "type":"checkbox",
      "default":false
    }
  ]
}

{% endschema %}
