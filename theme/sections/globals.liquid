{% style %}
  {% if section.settings.transparent_header != false %}
    [scroll-direction="down"] #shopify-section-header, [scroll-direction="up"] #shopify-section-header, #shopify-section-header {
      position: fixed;
    }
    #siteHeader, .scrolled-top #siteHeader {
      background-color: transparent;
    }
    .scrolled #siteHeader, #siteHeader:hover, #siteHeader:focus {
      background-color: #f7f7f7;
    }

    :is(html:has(#shopify-section-search.active), html:has([data-modal="panel-nav"].active)) #siteHeader {
      background-color: #f7f7f7;
    }

    @media (min-width: 1040px) {
      .blog__breadcrumbs {
        top: 82px;
      }
    }
    {% if section.settings.header_text_color != blank %}

      #siteHeader a,
      #siteHeader a:hover,
      #siteHeader a:focus,
      #siteHeader button,
      .flow-country-picker-dropdown-trigger-text,
      .scrolled-top #siteHeader a,
      #siteHeader #searchForm,
      .scrolled-top #siteHeader #searchForm,
      .header--search-mobile {
        color: {{ section.settings.header_text_color }} !important;
      }
      #siteHeader .nav__tools svg, .scrolled-top #siteHeader .nav__tools svg {
        stroke: currentColor;
      }

      #siteHeader .header__logo svg, .scrolled-top #siteHeader .header__logo svg {
        fill: currentColor;
      }

      .scrolled #siteHeader a,
      .scrolled #siteHeader button,
      #siteHeader:hover a,
      #siteHeader:focus a,
      #siteHeader:hover button,
      #siteHeader:focus button,
      #siteHeader:hover #searchForm,
      #siteHeader:focus #searchForm,
      .scrolled #siteHeader #searchForm,
      .scrolled .flow-country-picker-dropdown-trigger-text,
      #siteHeader:hover .flow-country-picker-dropdown-trigger-text,
      #siteHeader:focus .flow-country-picker-dropdown-trigger-text,
      .scrolled .header--search-mobile,
      #siteHeader:hover .header--search-mobile {
        color: #000 !important;
      }

      :is(html:has(#shopify-section-search.active), html:has([data-modal="panel-nav"].active)) #siteHeader :is(a, button, #searchForm, .flow-country-picker-dropdown-trigger-text, .header--search-mobile) {
        color: #000 !important;
      }

      #siteHeader:hover .nav__tools svg, #siteHeader:focus .nav__tools svg {
        stroke: #000;
      }

      #siteHeader:hover .header__logo svg, #siteHeader:focus .header__logo svg {
        fill: #000;
      }

    {% endif %}
    
  {% endif %}
{% endstyle %}

{% schema %}
{
  "name": "Globals",
  "settings": [
    {
      "type": "header",
      "content": "Display Settings"
    },
    {
      "type": "checkbox",
      "id": "transparent_header",
      "label": "Transparent Header",
      "default": true
    },
    {
      "type": "color",
      "id": "header_text_color",
      "label": "Header text color",
      "default": "#ffffff"
    }
  ],
  "presets": [
    {
      "name": "Transparent Header",
      "category": "Advanced"
    }
  ]
}
{% endschema %}
