<div class="section--page {{ section.settings.container }} {{ section.settings.content_vertical_padding }} {{ section.settings.content_horizontal_padding }} {{ section.settings.content_vertical_padding_mobile }} {{ section.settings.content_horizontal_padding_mobile }} mx-auto">
  <div class="{% if section.settings.page_show_title != blank or section.settings.page_show_content != blank %}lg:py-8 pt-4{% endif %}">
    <div class="lg:py-8">
      <h1 class="text-title font-normal MonumentGrotesk-Regular lg:my-3 mt-0 mb-8 lg:border-0 border-b border-black py-2 lg:py-0 lg:text-left text-center uppercase lg:normal-case {% if section.settings.page_show_title == blank %}sr-only{% endif %}">
        {{page.title}}
      </h1>
    </div>
    <div class="rte px-8 lg:px-0 {% if section.settings.page_show_content == blank %}hidden{% endif %}">
      {{page.content}}    
    </div>
    {% if section.settings.additional_scripts != blank %}
      {{ section.settings.additional_scripts }}
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.main-page.name",
  "tag": "section",
  "settings": [
    {
      "type": "header",
      "content": "Display Settings"
    },
    {
      "type": "checkbox",
      "id": "page_show_title",
      "default": true,
      "label": "Show Title"
    },
    {
      "type": "checkbox",
      "id": "page_show_content",
      "default": true,
      "label": "Show Content"
    },
    {
      "type": "header",
      "content": "Width Settings"
    },
    {
      "type": "select",
      "id": "container",
      "label": "Container",
      "options": [
        {
          "value": "max-w-4xl",
          "label": "Slim"
        },
        {
          "value": "container",
          "label": "Container"
        },
        {
          "value": "w-full",
          "label": "Full width"
        }
      ],
      "default": "max-w-4xl"
    },
    {
      "type": "header",
      "content": "Layout Settings (Desktop)"
    },
    {
      "type": "select",
      "id": "content_vertical_padding",
      "label": "Content Vertical Padding",
      "options": [
        {
          "value": "lg:py-0",
          "label": "None"
        },
        {
          "value": "lg:py-1",
          "label": ".25rem"
        },
        {
          "value": "lg:py-2",
          "label": ".5rem"
        },
        {
          "value": "lg:py-4",
          "label": "1rem"
        },
        {
          "value": "lg:py-8",
          "label": "2rem"
        },
        {
          "value": "lg:py-16",
          "label": "4rem"
        },
        {
          "value": "lg:py-32",
          "label": "8rem"
        },
        {
          "value": "lg:py-64",
          "label": "16rem"
        }
      ],
      "default": "lg:py-0"
    },
    {
      "type": "select",
      "id": "content_horizontal_padding",
      "label": "Content Horizontal Padding",
      "options": [
        {
          "value": "lg:px-0",
          "label": "None"
        },
        {
          "value": "lg:px-1",
          "label": ".25rem"
        },
        {
          "value": "lg:px-2",
          "label": ".5rem"
        },
        {
          "value": "lg:px-4",
          "label": "1rem"
        },
        {
          "value": "lg:px-8",
          "label": "2rem"
        },
        {
          "value": "lg:px-16",
          "label": "4rem"
        },
        {
          "value": "lg:px-32",
          "label": "8rem"
        },
        {
          "value": "lg:px-64",
          "label": "16rem"
        }
      ],
      "default": "lg:px-0"
    },
    {
      "type": "header",
      "content": "Layout Settings (Mobile)"
    },
    {
      "type": "select",
      "id": "content_vertical_padding_mobile",
      "label": "Content Vertical Padding",
      "options": [
        {
          "value": "py-0",
          "label": "None"
        },
        {
          "value": "py-1",
          "label": ".25rem"
        },
        {
          "value": "py-2",
          "label": ".5rem"
        },
        {
          "value": "py-4",
          "label": "1rem"
        },
        {
          "value": "py-8",
          "label": "2rem"
        },
        {
          "value": "py-16",
          "label": "4rem"
        },
        {
          "value": "py-32",
          "label": "8rem"
        },
        {
          "value": "py-64",
          "label": "16rem"
        }
      ],
      "default": "py-0"
    },
    {
      "type": "select",
      "id": "content_horizontal_padding_mobile",
      "label": "Content Horizontal Padding",
      "options": [
        {
          "value": "px-0",
          "label": "None"
        },
        {
          "value": "px-1",
          "label": ".25rem"
        },
        {
          "value": "px-2",
          "label": ".5rem"
        },
        {
          "value": "px-4",
          "label": "1rem"
        },
        {
          "value": "px-8",
          "label": "2rem"
        },
        {
          "value": "px-16",
          "label": "4rem"
        },
        {
          "value": "px-32",
          "label": "8rem"
        },
        {
          "value": "px-64",
          "label": "16rem"
        }
      ],
      "default": "px-0"
    },
    {
      "type": "header",
      "content": "Additional Scripts"
    },
    {
      "type": "html",
      "id": "additional_scripts",
      "label": "Additional Scripts"
    }
  ]
}
{% endschema %}