{% include 'async-section',
  skeleton: '<div class="p-16 w-full h-50v bg-white"><div class="bg-gray-100 p-16 rounded-2xl h-full w-full drop-shadow-lg flex flex-col items-center justify-center"><span class="sr-only">Skeleton</span><span class="bg-gray-200 h-24 w-24"></span><span class="bg-gray-200 h-2 w-1/4 mt-4"></span><span class="bg-gray-200 h-2 w-1/4 mt-4"></span><span class="bg-gray-200 h-2 w-1/4 mt-4"></span><span class="bg-gray-200 h-2 w-1/4 mt-4"></span></div></div>'
%}
{% unless defer_section %}
  <section
    id="richText-{{ section.id }}"
    class="{{ section.settings.section_top_margin }} {{ section.settings.section_bottom_margin }} {{ section.settings.section_top_margin_mobile }} {{ section.settings.section_bottom_margin_mobile }} {{ section.settings.horizontal_padding }} {{ section.settings.horizontal_padding_mobile }} {% if section.settings.show_desktop == true and section.settings.show_mobile == true %} flex {% elsif section.settings.show_desktop == true and section.settings.show_mobile == false %} hidden lg:flex {% elsif section.settings.show_desktop == false and section.settings.show_mobile == false %} hidden {% elsif section.settings.show_desktop == false and section.settings.show_mobile == true %} flex lg:hidden {% endif %} {% if section.settings.remove_section_condition %} vwo-hide{% endif %}"
    neptune-surface="
      {
        'direction':'up',
        'windowEdge': 'bottom',
        'elementEdge': 'top',
        'targets': [
          {
            'selector':'_self .rich-text--container',
            'engage:action': {
              'classes': {
                'remove': ['active']
              }
            }
          }
        ]
      }
    "
  >
    <div
      class="rich-text--container p-0 {{ section.settings.container }} grid {{ section.settings.grid_column }} {{ section.settings.grid_column_mobile }} {{ section.settings.grid_gap }} {{ section.settings.grid_gap_mobile }} {{ section.settings.scroll_effect }} animate animate-slow"
      neptune-surface="
        {
          'windowEdge': 'bottom',
          'elementEdge': 'top',
          'delay': '800',
          'targets': [
            {
              'engage:action': {
                'classes': {
                  'add': ['active']
                }
              }
            }
          ]
        }
      "
    >
      {% for block in section.blocks %}
        <div
          id="block-{{ block.id }}"
          class="{{ block.settings.text_align }} {{ block.settings.text_align_mobile }}"
          {{ block.shopify_attributes }}
        >
          {% if block.type == 'text' %}
            {% if block.settings.title != blank %}
              <{{ block.settings.title_element }}
                class="block__title lh-solid mb4 {{ block.settings.title_type }}"
                {% if block.settings.title_color != blank %}
                  style="color: {{ block.settings.title_color }};"
                {% endif %}
              >
                {{ block.settings.title }}
              </{{ block.settings.title_element }}>
            {% endif %}
            {% unless block.settings.featured_content_html != blank %}
              <div
                class="block__featured_content {{ block.settings.featured_content_type }}"
                {% if block.settings.featured_content_color != blank %}
                  style="color: {{ block.settings.featured_content_color }};"
                {% endif %}
              >
                {{ block.settings.featured_content }}
              </div>
            {% else %}
              <div
                class="block__featured_content_html {{ block.settings.featured_content_type }}"
                {% if block.settings.featured_content_color != blank %}
                  style="color: {{ block.settings.featured_content_color }};"
                {% endif %}
              >
                {{ block.settings.featured_content_html }}
              </div>
            {% endunless %}

          {% style %}
            #richText-{{ section.id }} p:only-of-type {
              margin-bottom: 0;
            }
            #block-{{ block.id }} .block__title {
              font-size: {{ block.settings.title_size }}px;
              line-height: {{ block.settings.title_leading }};
              letter-spacing: {{ block.settings.title_letter_spacing }}px;
              {% if block.settings.title_font_override != blank %}
                font-family: {{block.settings.title_font_override}};
              {% endif %}
            }
            #block-{{ block.id }} .block__featured_content p,
            #block-{{ block.id }} .block__featured_content_html > p {
              font-family: inherit;
            }
            #block-{{ block.id }} .block__featured_content,
            #block-{{ block.id }} .block__featured_content p,
            #block-{{ block.id }} .block__featured_content_html,
            #block-{{ block.id }} .block__featured_content_html > * {
              font-size: {{ block.settings.featured_content_size }}px;
              line-height: {{ block.settings.featured_content_leading }};
              letter-spacing: {{ block.settings.featured_content_letter_spacing }}px;
              {% if block.settings.featured_content_font_override != blank %}
                font-family: {{ block.settings.featured_content_font_override }};
              {% endif %}
            }
            @media (max-width: 1023px){
              #block-{{ block.id }} .block__title {
                font-size: {{ block.settings.title_size }}px;
                line-height: {{ block.settings.title_leading }};
                letter-spacing: {{ block.settings.title_letter_spacing }}px;
                {% if block.settings.title_font_override == blank %}
                  font-family: {{ block.settings.title_type }};
                {% else %}
                  font-family: {{block.settings.title_font_override}};
                {% endif %}
              }
              #block-{{ block.id }} .block__featured_content,
              #block-{{ block.id }} .block__featured_content p,
              #block-{{ block.id }} .block__featured_content_html,
              #block-{{ block.id }} .block__featured_content_html > * {
                font-size: {{ block.settings.featured_content_size }}px;
                line-height: {{ block.settings.featured_content_leading }};
                letter-spacing: {{ block.settings.featured_content_letter_spacing }}px;
                {% if block.settings.featured_content_font_override == blank %}
                  font-family: {{ block.settings.featured_content_type }};
                {% else %}
                  font-family: {{ block.settings.featured_content_font_override }};
                {% endif %}
              }
              @media (max-width: 1023px){
                #block-{{ block.id }} .block__title {
                  font-size: {{ block.settings.title_size_mobile }}px;
                }
                #block-{{ block.id }} .block__featured_content,
                #block-{{ block.id }} .block__featured_content p,
                #block-{{ block.id }} .block__featured_content_html,
                #block-{{ block.id }} .block__featured_content_html > * {
                  font-size: {{ block.settings.featured_content_size_mobile }}px;
                }
              }
            {% endstyle %}
          {% endif %}
        </div>
      {% endfor %}
    </div>
  </section>
{% endunless %}

{% schema %}
{
  "name": "Rich Text",
  "class": "rich-text--section",
  "max_blocks": 2,
  "settings": [
    {
      "type": "header",
      "content": "A/B Testing"
    },
    {
      "type": "checkbox",
      "id": "remove_section_condition",
      "label": "Remove Section",
      "default": false,
      "info": "This will hide the section in one of the experiment variations."
    },
    {
      "type": "header",
      "content": "Display Settings"
    },
    {
      "type": "checkbox",
      "id": "async",
      "label": "Asynchronously Load Section on Scroll",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_desktop",
      "label": "Show desktop",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_mobile",
      "label": "Show mobile",
      "default": true
    },
    {
      "type": "header",
      "content": "Section Display Settings"
    },
    {
      "type": "select",
      "id": "container",
      "label": "Container",
      "options": [
        {
          "value": "container",
          "label": "Container"
        },
        {
          "value": "container--small container",
          "label": "Container small"
        },
        {
          "value": "w-full",
          "label": "Full width"
        }
      ],
      "default": "container--small container"
    },
    {
      "type": "header",
      "content": "Section Spacing Settings (Desktop)"
    },
    {
      "type": "select",
      "id": "section_top_margin",
      "label": "Margin top",
      "options": [
        {
          "value": "lg:mt-0",
          "label": "None"
        },
        {
          "value": "lg:mt-1",
          "label": ".25rem"
        },
        {
          "value": "lg:mt-2",
          "label": ".5rem"
        },
        {
          "value": "lg:mt-4",
          "label": "1rem"
        },
        {
          "value": "lg:mt-8",
          "label": "2rem"
        },
        {
          "value": "lg:mt-16",
          "label": "4rem"
        },
        {
          "value": "lg:mt-32",
          "label": "8rem"
        },
        {
          "value": "lg:mt-64",
          "label": "16rem"
        }
      ],
      "default": "lg:mt-0"
    },
    {
      "type": "select",
      "id": "section_bottom_margin",
      "label": "Margin bottom ",
      "options": [
        {
          "value": "lg:mb-0",
          "label": "None"
        },
        {
          "value": "lg:mb-1",
          "label": ".25rem"
        },
        {
          "value": "lg:mb-2",
          "label": ".5rem"
        },
        {
          "value": "lg:mb-4",
          "label": "1rem"
        },
        {
          "value": "lg:mb-8",
          "label": "2rem"
        },
        {
          "value": "lg:mb-16",
          "label": "4rem"
        },
        {
          "value": "lg:mb-32",
          "label": "8rem"
        },
        {
          "value": "lg:mb-64",
          "label": "16rem"
        }
      ],
      "default": "lg:mb-0"
    },
    {
      "type": "select",
      "id": "horizontal_padding",
      "label": "Horizontal Padding",
      "info": "Padding will only work with full width container",
      "options": [
        {
          "value": "lg:px-0",
          "label": "None"
        },
        {
          "value": "lg:px-1",
          "label": ".25rem"
        },
        {
          "value": "lg:px-2",
          "label": ".5rem"
        },
        {
          "value": "lg:px-4",
          "label": "1rem"
        },
        {
          "value": "lg:px-8",
          "label": "2rem"
        },
        {
          "value": "lg:px-16",
          "label": "4rem"
        },
        {
          "value": "lg:px-32",
          "label": "8rem"
        },
        {
          "value": "lg:px-64",
          "label": "16rem"
        }
      ],
      "default": "lg:px-0"
    },
    {
      "type": "header",
      "content": "Section Spacing Settings (Mobile)"
    },
    {
      "type": "select",
      "id": "section_top_margin_mobile",
      "label": "Margin top",
      "options": [
        {
          "value": "mt-0",
          "label": "None"
        },
        {
          "value": "mt-1",
          "label": ".25rem"
        },
        {
          "value": "mt-2",
          "label": ".5rem"
        },
        {
          "value": "mt-4",
          "label": "1rem"
        },
        {
          "value": "mt-8",
          "label": "2rem"
        },
        {
          "value": "mt-16",
          "label": "4rem"
        },
        {
          "value": "mt-32",
          "label": "8rem"
        },
        {
          "value": "mt-64",
          "label": "16rem"
        }
      ],
      "default": "mt-0"
    },
    {
      "type": "select",
      "id": "section_bottom_margin_mobile",
      "label": "Margin bottom",
      "options": [
        {
          "value": "mb-0",
          "label": "None"
        },
        {
          "value": "mb-1",
          "label": ".25rem"
        },
        {
          "value": "mb-2",
          "label": ".5rem"
        },
        {
          "value": "mb-4",
          "label": "1rem"
        },
        {
          "value": "mb-8",
          "label": "2rem"
        },
        {
          "value": "mb-16",
          "label": "4rem"
        },
        {
          "value": "mb-32",
          "label": "8rem"
        },
        {
          "value": "mb-64",
          "label": "16rem"
        }
      ],
      "default": "mb-0"
    },
    {
      "type": "select",
      "id": "horizontal_padding_mobile",
      "label": "Horizontal Padding (Mobile)",
      "info": "Padding will only work with full width container",
      "options": [
        {
          "value": "px-0",
          "label": "None"
        },
        {
          "value": "px-1",
          "label": ".25rem"
        },
        {
          "value": "px-2",
          "label": ".5rem"
        },
        {
          "value": "px-4",
          "label": "1rem"
        },
        {
          "value": "px-8",
          "label": "2rem"
        },
        {
          "value": "px-16",
          "label": "4rem"
        },
        {
          "value": "px-32",
          "label": "8rem"
        },
        {
          "value": "px-64",
          "label": "16rem"
        }
      ],
      "default": "px-0"
    },
    {
      "type": "header",
      "content": "Grid Settings"
    },
    {
      "type": "select",
      "id": "grid_column",
      "label": "Columns (Desktop)",
      "options": [
        {
          "value": "lg:grid-cols-1",
          "label": "1"
        },
        {
          "value": "lg:grid-cols-2",
          "label": "2"
        },
        {
          "value": "lg:grid-cols-3",
          "label": "3"
        },
        {
          "value": "lg:grid-cols-4",
          "label": "4"
        }
      ],
      "default": "lg:grid-cols-1"
    },
    {
      "type": "select",
      "id": "grid_gap",
      "label": "Gutter (Desktop)",
      "options": [
        {
          "value": "lg:gap-0",
          "label": "None"
        },
        {
          "value": "lg:gap-1",
          "label": ".25rem"
        },
        {
          "value": "lg:gap-2",
          "label": ".5rem"
        },
        {
          "value": "lg:gap-3",
          "label": "1rem"
        },
        {
          "value": "lg:gap-4",
          "label": "2rem"
        },
        {
          "value": "lg:gap-5",
          "label": "4rem"
        },
        {
          "value": "lg:gap-6",
          "label": "8rem"
        },
        {
          "value": "lg:gap-7",
          "label": "16rem"
        }
      ],
      "default": "lg:gap-0"
    },
    {
      "type": "select",
      "id": "grid_column_mobile",
      "label": "Columns (Mobile)",
      "options": [
        {
          "value": "grid-cols-1",
          "label": "1"
        },
        {
          "value": "grid-cols-2",
          "label": "2"
        },
        {
          "value": "grid-cols-3",
          "label": "3"
        },
        {
          "value": "grid-cols-4",
          "label": "4"
        }
      ],
      "default": "grid-cols-1"
    },
    {
      "type": "select",
      "id": "grid_gap_mobile",
      "label": "Gutter (Mobile)",
      "options": [
        {
          "value": "gap-0",
          "label": "None"
        },
        {
          "value": "gap-1",
          "label": ".25rem"
        },
        {
          "value": "gap-2",
          "label": ".5rem"
        },
        {
          "value": "gap-3",
          "label": "1rem"
        },
        {
          "value": "gap-4",
          "label": "2rem"
        },
        {
          "value": "gap-5",
          "label": "4rem"
        },
        {
          "value": "gap-6",
          "label": "8rem"
        },
        {
          "value": "gap-7",
          "label": "16rem"
        }
      ],
      "default": "gap-0"
    },
    {
      "type": "header",
      "content": "Interactivity"
    },
    {
      "type": "select",
      "id": "scroll_effect",
      "label": "Scroll Effect",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "fade-in in-view:fade-in",
          "label": "Fade In"
        },
        {
          "value": "fade-in-rise in-view:fade-in-rise",
          "label": "Fade In and Rise"
        }
      ],
      "default": ""
    }
  ],
  "blocks": [
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "header",
          "content": "Text Settings"
        },
        {
          "type": "select",
          "id": "text_align",
          "label": "Text Alignment (Desktop)",
          "options": [
            {
              "value": "lg:text-left",
              "label": "Left"
            },
            {
              "value": "lg:text-center",
              "label": "Center"
            },
            {
              "value": "lg:text-right",
              "label": "Right"
            },
            {
              "value": "lg:text-justify lg:text-justify-last",
              "label": "Justify"
            }
          ],
          "default": "lg:text-left"
        },
        {
          "type": "select",
          "id": "text_align_mobile",
          "label": "Text Alignment (Mobile)",
          "options": [
            {
              "value": "text-left",
              "label": "Left"
            },
            {
              "value": "text-center",
              "label": "Center"
            },
            {
              "value": "text-right",
              "label": "Right"
            },
            {
              "value": "text-justify text-justify-last",
              "label": "Justify"
            }
          ],
          "default": "text-left"
        },
        {
          "type": "header",
          "content": "Title Typeface Options"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "select",
          "id": "title_type",
          "label": "Title Typeface",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "font-pop",
              "label": "ABC Monument Grotesk Black"
            },
            {
              "value": "font-highlight",
              "label": "ABC Monument Grotesk Light"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "MonumentGrotesk-Regular"
        },
        {
          "type": "text",
          "id": "title_font_override",
          "label": "Title Font Family Override"
        },
        {
          "type": "number",
          "id": "title_size",
          "label": "Title Font Size (Desktop)",
          "default": 32
        },
        {
          "type": "number",
          "id": "title_size_mobile",
          "label": "Title Font Size (Mobile)",
          "default": 22
        },
        {
          "type": "color",
          "id": "title_color",
          "label": "Title Color",
          "default": "#000000"
        },
        {
          "type": "text",
          "id": "title_leading",
          "label": "Title Line Height",
          "default": "1.38"
        },
        {
          "type": "text",
          "id": "title_letter_spacing",
          "label": "Title Letter Spacing"
        },
        {
          "type": "header",
          "content": "Featured Content Typeface Options"
        },
        {
          "type": "richtext",
          "id": "featured_content",
          "label": "Featured Content"
        },
        {
          "type": "html",
          "id": "featured_content_html",
          "label": "Featured Content HTML"
        },
        {
          "type": "select",
          "id": "featured_content_type",
          "label": "Featured Content Typeface",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "font-pop",
              "label": "ABC Monument Grotesk Black"
            },
            {
              "value": "font-highlight",
              "label": "ABC Monument Grotesk Light"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "MonumentGrotesk-Regular"
        },
        {
          "type": "text",
          "id": "featured_content_font_override",
          "label": "Featured Content Font Family Override"
        },
        {
          "type": "number",
          "id": "featured_content_size",
          "label": "Featured Content Font Size (Desktop)",
          "default": 16
        },
        {
          "type": "number",
          "id": "featured_content_size_mobile",
          "label": "Featured Content Font Size (Mobile)",
          "default": 16
        },
        {
          "type": "color",
          "id": "featured_content_color",
          "label": "Featured Content color",
          "default": "#000000"
        },
        {
          "type": "text",
          "id": "featured_content_leading",
          "label": "Featured Content Line Height",
          "default": "1.38"
        },
        {
          "type": "text",
          "id": "featured_content_letter_spacing",
          "label": "Featured Content Letter Spacing"
        },
        {
          "type": "header",
          "content": "Desktop Text Format Settings"
        },
        {
          "type": "select",
          "id": "title_element",
          "label": "Heading Type",
          "options": [
            {
              "value": "h1",
              "label": "H1"
            },
            {
              "value": "h2",
              "label": "H2"
            },
            {
              "value": "h3",
              "label": "H3"
            },
            {
              "value": "h4",
              "label": "H4"
            },
            {
              "value": "h5",
              "label": "H5"
            }
          ],
          "default": "h2"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Rich text",
      "category": "Text",
      "blocks": []
    }
  ]
}
{% endschema %}
