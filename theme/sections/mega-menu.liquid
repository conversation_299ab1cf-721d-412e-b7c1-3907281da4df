{% schema %}
{
  "name": "Mega Menu",
  "settings": [
    {
      "type": "header",
      "content": "A/B Testing"
    },
    {
      "type": "liquid",
      "label": "Remove Section If...",
      "id": "remove_section_condition",
      "info": "Use a JS conditional expression that if true, will remove this section."
    }
  ],
  "blocks": [
    {
      "type": "parent",
      "name": "Dropdown",
      "settings": [
        {
          "type": "header",
          "content": "Interactivity"
        },
        {
          "type":"text",
          "id":"trigger",
          "label": "Dropdown Trigger",
          "info": "Must be handleized menu title (e.g. New Arrivals: new-arrivals). Pairs with menu item from header menu."
        },
        {
          "type": "header",
          "content": "Display Settings"
        },
        {
          "type": "select",
          "id": "container",
          "label": "Container",
          "options": [
            {
              "value": "container",
              "label": "Container"
            },
            {
              "value": "w-full",
              "label": "Full width"
            }
          ],
          "default": "w-full"
        },
        {
          "type": "select",
          "id": "padding",
          "label": "Padding",
          "options": [
            {
              "value": "lg:p-0",
              "label": "None"
            },
            {
              "value": "lg:p-4",
              "label": "1rem"
            },
            {
              "value": "lg:p-8",
              "label": "2rem"
            },
            {
              "value": "lg:p-12",
              "label": "3rem"
            },
            {
              "value": "lg:p-16",
              "label": "4rem"
            },
            {
              "value": "lg:p-20",
              "label": "5rem"
            }
          ],
          "default": "lg:p-0"
        },
        {
          "type":"color",
          "id":"background_color",
          "label": "Background Color",
          "default": "#f7f7f7"
        },
        {
          "type": "header",
          "content": "Layout Settings"
        },
        {
          "type": "select",
          "id": "justify",
          "label": "Horizontal Block Justification",
          "options": [
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "right",
              "label": "Right"
            },
            {
              "value": "between",
              "label": "Spread"
            },
            {
              "value": "around",
              "label": "Distribute"
            }
          ],
          "default": "center"
        },
        {
          "type": "select",
          "id": "align",
          "label": "Vertical Block Alignment",
          "options": [
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "start",
              "label": "Top"
            },
            {
              "value": "end",
              "label": "Bottom"
            },
            {
              "value": "stretch",
              "label": "Fill"
            }
          ],
          "default": "center"
        },
        {
          "type": "select",
          "id": "overflow",
          "label": "Overflow",
          "options": [
            {
              "value": "flex-wrap",
              "label": "Wrap"
            },
            {
              "value": "",
              "label": "Squeeze"
            }
          ],
          "default": "flex-wrap"
        },
        {
          "type": "select",
          "id": "gutters_desktop",
          "label": "Content Gutters (Desktop)",
          "options": [
            {
              "value": "lg:p-0",
              "label": "None"
            },
            {
              "value": "lg:p-4",
              "label": "1rem"
            },
            {
              "value": "lg:p-8",
              "label": "2rem"
            },
            {
              "value": "lg:p-12",
              "label": "3rem"
            },
            {
              "value": "lg:p-16",
              "label": "4rem"
            },
            {
              "value": "lg:p-20",
              "label": "5rem"
            }
          ],
          "default": "lg:p-4"
        }
      ] 
    },
    {
      "name": "Link List",
      "type": "link_list",
      "settings": [
        {
          "type": "header",
          "content": "Display Settings"
        },
        {
          "type": "checkbox",
          "id": "show_desktop",
          "label": "Show desktop",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_mobile",
          "label": "Show mobile",
          "default": true
        },
        {
          "type": "header",
          "content": "Layout Settings"
        },
        {
          "type": "select",
          "id": "width",
          "label": "Width (Mobile)",
          "options": [
            {
              "value": "w-1/3",
              "label": "33%"
            },
            {
              "value": "w-1/2",
              "label": "50%"
            },
            {
              "value": "w-2/3",
              "label": "66%"
            },
            {
              "value": "w-full",
              "label": "100%"
            }
          ],
          "default": "w-full"
        },
        {
          "type": "select",
          "id": "desktop_width",
          "label": "Width (Desktop)",
          "options": [
            {
              "value": "lg:w-[15.5%]",
              "label": "15.5%"
            },
            {
              "value": "lg:w-1/5",
              "label": "20%"
            },
            {
              "value": "lg:w-[23%]",
              "label": "23%"
            },
            {
              "value": "lg:w-1/4",
              "label": "25%"
            },
            {
              "value": "lg:w-[30%]",
              "label": "30%"
            },
            {
              "value": "lg:w-1/3",
              "label": "33%"
            },
            {
              "value": "lg:w-2/5",
              "label": "40%"
            },
            {
              "value": "lg:w-1/2",
              "label": "50%"
            },
            {
              "value": "lg:w-3/5",
              "label": "60%"
            },
            {
              "value": "w-2/3",
              "label": "66%"
            },
            {
              "value": "lg:w-[70%]",
              "label": "70%"
            },
            {
              "value": "lg:w-3/4",
              "label": "75%"
            },
            {
              "value": "lg:w-4/5",
              "label": "80%"
            },
            {
              "value": "lg:w-full",
              "label": "100%"
            }
          ],
          "default": "lg:w-full"
        },
        {
          "type": "select",
          "id": "linklist_border",
          "label": "Border",
          "options": [
            {
              "value": "border-0",
              "label": "None"
            },
            {
              "value": "border-2",
              "label": "Border all"
            },
            {
              "value": "border-t-2",
              "label": "Border top"
            },
            {
              "value": "border-r-2",
              "label": "Border right"
            },
            {
              "value": "border-b-2",
              "label": "Border bottom"
            },
            {
              "value": "border-l-2",
              "label": "Border left"
            }
          ],
          "default": "border-0"
        },
        {
          "type": "link_list",
          "label": "Link list-none",
          "id": "linklist_menu"
        },
        {
          "type": "header",
          "content": "Display Settings"
        },
        {
          "type": "checkbox",
          "id": "nested_links",
          "label": "Nested menu structure",
          "info": "Select this option to separate nested menus into columns.",
          "default": true
        },
        {
          "type":"color",
          "id":"linklist_background_color",
          "label": "Background Color",
          "default": "#f7f7f7"
        },
        {
          "type":"color",
          "id":"linklist_color",
          "label": "Text Color"
        },
        {
          "type": "select",
          "id": "padding",
          "label": "Padding (Mobile)",
          "options": [
            {
              "value": "p-0",
              "label": "None"
            },
            {
              "value": "p-4",
              "label": "1rem"
            },
            {
              "value": "p-8",
              "label": "2rem"
            },
            {
              "value": "p-12",
              "label": "3rem"
            },
            {
              "value": "p-16",
              "label": "4rem"
            },
            {
              "value": "p-20",
              "label": "5rem"
            }
          ],
          "default": "p-0"
        },
        {
          "type": "select",
          "id": "padding_desktop",
          "label": "Padding (Desktop)",
          "options": [
            {
              "value": "lg:p-0",
              "label": "None"
            },
            {
              "value": "lg:p-4",
              "label": "1rem"
            },
            {
              "value": "lg:p-8",
              "label": "2rem"
            },
            {
              "value": "lg:p-12",
              "label": "3rem"
            },
            {
              "value": "lg:p-16",
              "label": "4rem"
            },
            {
              "value": "lg:p-20",
              "label": "5rem"
            }
          ],
          "default": "lg:p-0"
        },
        {
          "type": "select",
          "id": "justify",
          "label": "List Justification",
          "options": [
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "right",
              "label": "Right"
            },
            {
              "value": "between",
              "label": "Spread"
            },
            {
              "value": "around",
              "label": "Distribute"
            }
          ],
          "default": "center"
        },
        {
          "type": "select",
          "id": "text_align",
          "label": "Text Alignment",
          "options": [
            {
              "value": "text-left",
              "label": "Left"
            },
            {
              "value": "text-center",
              "label": "Center"
            },
            {
              "value": "text-right",
              "label": "Right"
            }
          ],
          "default": "text-left"
        }
      ]
    },
    {
      "name": "Image with text",
      "type": "image_text",
      "settings": [
        {
          "type": "header",
          "content": "Display Settings"
        },
        {
          "type": "checkbox",
          "id": "show_desktop",
          "label": "Show desktop",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_mobile",
          "label": "Show mobile",
          "default": true
        },
        {
          "type": "header",
          "content": "Layout Settings"
        },
        {
          "type": "select",
          "id": "width",
          "label": "Width (Mobile)",
          "options": [
            {
              "value": "w-1/3",
              "label": "33%"
            },
            {
              "value": "w-1/2",
              "label": "50%"
            },
            {
              "value": "w-2/3",
              "label": "66%"
            },
            {
              "value": "w-full",
              "label": "100%"
            }
          ],
          "default": "w-full"
        },
        {
          "type": "select",
          "id": "desktop_width",
          "label": "Width (Desktop)",
          "options": [
            {
              "value": "lg:w-[15.5%]",
              "label": "15.5%"
            },
            {
              "value": "lg:w-1/5",
              "label": "20%"
            },
            {
              "value": "lg:w-[23%]",
              "label": "23%"
            },
            {
              "value": "lg:w-1/4",
              "label": "25%"
            },
            {
              "value": "lg:w-[30%]",
              "label": "30%"
            },
            {
              "value": "lg:w-1/3",
              "label": "33%"
            },
            {
              "value": "lg:w-2/5",
              "label": "40%"
            },
            {
              "value": "lg:w-1/2",
              "label": "50%"
            },
            {
              "value": "lg:w-3/5",
              "label": "60%"
            },
            {
              "value": "lg:w-2/3",
              "label": "66%"
            },
            {
              "value": "lg:w-[70%]",
              "label": "70%"
            },
            {
              "value": "lg:w-3/4",
              "label": "75%"
            },
            {
              "value": "lg:w-4/5",
              "label": "80%"
            },
            {
              "value": "lg:w-full",
              "label": "100%"
            }
          ],
          "default": "lg:w-full"
        },
        {
          "type": "header",
          "content": "Display Settings"
        },
        {
          "type": "image_picker",
          "label": "Image",
          "id": "image"
        },
        {
          "type": "inline_richtext",
          "label": "Title",
          "id": "title"
        },
        {
          "type": "inline_richtext",
          "label": "Text",
          "id": "text"
        },
        {
          "type": "select",
          "id": "text_style",
          "label": "Text style",
          "options": [
            {
              "value": "uppercase",
              "label": "Uppercase"
            },
            {
              "value": "lowercase",
              "label": "Lowercase"
            },
            {
              "value": "capitalize",
              "label": "Capitalize"
            }
          ],
          "default": "uppercase"
        },
        {
          "type": "url",
          "label": "Link",
          "id": "link"
        }
      ]
    }
  ]
}
{% endschema %}

{% for dropdown in section.blocks %}

  {%- if dropdown.type == 'parent' %}

    {% assign dropdownTrigger = dropdown.settings.trigger | replace: '"','`' | prepend: '#mainMenu-' %}

    <button 
      class="mega-trigger absolute right-0 top-1/2 -translate-y-1/2 flex items-center justify-center h-1/2 w-full p-0 bg-transparent cursor-pointer"
      neptune-transport="
        {
          append:'{{ dropdownTrigger }} .nav_item__wrap'
        }"
      neptune-engage='[
        {
          preventDefault: true,
          on:mouseover,
          targets: [
            {
              selector:"{{ dropdownTrigger }}",
              classes:add:active,
              siblings:classes:remove:active
            },
            {
              selector:.header__search-desktop,
              classes:remove:active
            },
            {
              selector:.header,
              classes:remove:search-active
            },
            {
              selector:html,
              classes:add:active-mega
            }
          ]
        },
        {
          preventDefault: true,
          on:click,
          targets: [
            {
              selector:"{{ dropdownTrigger }}",
              classes:toggle:active,
              siblings:classes:remove:active
            },
            {
              selector:.header__search-desktop,
              classes:remove:active
            },
            {
              selector:.header,
              classes:remove:search-active
            },
            {
              selector:html,
              classes:toggle:active-mega
            }
          ]
        }
      ]'>
      <span class="sr-only">Show {{ dropdown.settings.trigger }} menu</span>
    </button>

    <div 
      class="overlay mega-overlay h-screen pointer-events-none absolute left-0 top-0 z-40 w-full animate lg:invisible lg:group-active:visible lg:opacity-0 lg:group-active:opacity-100"
      neptune-transport="
        {
          append:'{{ dropdownTrigger }}'
        }
      "
    ></div>

    <div 
      class="mega-menu animate absolute z-40 lg:top-full top-0 left-0 lg:mt-[-2px] {{ dropdown.settings.padding }} w-full lg:max-h-[calc(100vh-var(--header-height))] lg:overflow-auto lg:invisible lg:group-active:visible lg:opacity-0 lg:group-active:opacity-100 lg:pointer-events-none lg:group-active:pointer-events-auto"
      neptune-transport="
        {
          append:'{{ dropdownTrigger }}'
        }
      "
      neptune-engage="[
      {
        trigger: '{{ dropdownTrigger }}',
        on: mouseover,
        preventDefault: true,
        targets:[
          { 
            selector:'{{ dropdownTrigger }}',
            attributes:[{
              att:data-return-focus,
              set:true
            }]
          }
        ]
      },
      {
        preventDefault: true,
        on: keyup,
        which:27,
        targets:[
          {
            selector:'[data-return-focus]',
            attributes:[{
              att:data-return-focus,
              set:_remove
            }],
            focus: true
          }
        ]
      }]" tabindex="0">

      <button 
        class="flex flex-row items-center w-full text-left bg-transparent text-black border-0 lg:hidden px-4 lg:px-0"
        neptune-engage='{
          preventDefault: true,
          on:click,
          targets: [
            {
              selector:"{{ dropdownTrigger }}",
              classes:toggle:active,
              siblings:classes:remove:active
            },
            {
              selector:.header__search-desktop,
              classes:remove:active
            },
            {
              selector:.header,
              classes:remove:search-active
            },
            {
              selector:html,
              classes:toggle:active-mega
            }
          ]
        }'
      >
        <span class="p-4 bg-transparent">
          {% render 'icon' icon:'arrow-left' height:16 width:16 %}
        </span>
        <span class="flex flex-row items-center flex-grow p-4 leading-loose lg:leading-normal">Back</span>
      </button>

      <div class="{{ dropdown.settings.container }} ml-auto mr-auto rounded-bl-3xl rounded-br-3xl border-y-2 border-solid border-white flex flex-grow items-{{ dropdown.settings.align }} justify-{{ dropdown.settings.justify }} {{ dropdown.settings.overflow }} lg:overflow-x-hidden overflow-y-lg:invisible animate" style="background-color: {{ dropdown.settings.background_color }};">

        <div class="{% if noFeature == true %}lg:w-3/4 {% endif %}w-full order-1 flex flex-wrap">
        
          {%- for block in section.blocks offset:forloop.index -%}
            
            {%- if block.type == 'parent' %}{% break %}{%- endif -%}

            {% if block.type == 'link_list' %}
              <div id="mega-{{ block.id }}" class="mega__link_list {{ block.settings.width }} {{ block.settings.desktop_width }} {% unless block.settings.linklist_border == 'border-0' %}{{ block.settings.linklist_border }} border-white{% endunless %}">
                <div class="{{ dropdown.settings.gutters_desktop }} {% if block.settings.show_desktop == true and block.settings.show_mobile == true %} block {% elsif block.settings.show_desktop == true and block.settings.show_mobile == false %} lg:block hidden {% elsif block.settings.show_desktop == false and block.settings.show_mobile == true %} lg:hidden block {% endif %}">
                  <div class="nav__list-columns nav-type-{{ block.type|handle }} flex-grow {{ block.settings.padding }} {{ block.settings.padding_desktop }} mha" style="{% if block.settings.linklist_background_color != blank %} background-color: {{ block.settings.linklist_background_color }}; {% endif %} {% if block.settings.linklist_color != blank %} color: {{block.settings.linklist_color}}; {% endif %}">
                    <div class="nav__list-column-wrap flex flex-col justify-{{ block.settings.justify }} {{ block.settings.text_align }}">

                      {% assign linkList = block.settings.linklist_menu %}

                      <ul class="list-none m-0 p-0 flex{% if block.settings.nested_links %} lg:flex-row nested_links divide-x-2 px-4 {% endif %} flex-col justify-between {% unless block.settings.nested_links %} pb-4 {% endunless %}">
                        {% for link in linklists[linkList].links %}
                          <li id="linklist-{{forloop.index}}" class="flex-1 flex lg:flex-col flex-row py-0 px-4 lg:px-6 lg:pt-6 lg:pb-10 lg:overflow-visible lg:w-1/{{ linklists[linkList].links | size }} text-sm border-white">
                            {%- liquid
                              assign external_link = false
                              if link.type == "http_link" 
                                unless link.url contains '/vendors?' or link.url contains '/types?'
                                  assign external_link = true
                                endunless
                              endif
                            -%}
                            <a 
                              {% if block.settings.linklist_color != blank %}style="color: {{ block.settings.linklist_color }};"{% endif %} 
                              class="block p-4 leading-normal tracking-wider text-black font-pop lg:px-0 lg:pt-0 lg:pb-1 {% if link.links != blank %} w-1/2 lg:w-full{% endif %} text-sm uppercase hover:font-bold focus:font-bold" 
                              href="{{ link.url }}"
                              {% if external_link %} target="_blank"{% endif %}
                            >
                              {{ link.title }}
                            </a>
                            {%- if link.links != blank -%}
                              <button class="flex items-center p-4 justify-center h-full p-0 bg-transparent cursor-pointer lg:hidden w-1/2 text-black"
                                neptune-engage='{
                                  preventDefault: true,
                                  on:click,
                                  targets: [
                                    {
                                      selector:"#linklist-{{forloop.index}} .nav__sublist",
                                      classes:toggle:active,
                                      siblings:classes:remove:active
                                    }
                                  ]
                                }'>
                                <span class="sr-only">Show {{ dropdown.settings.trigger }} menu</span>
                                <span class="flex items-center justify-center ml-auto lg:flex lg:hidden">
                                  {%  render 'icon' icon:'arrow-right' height:16 width:16  %}
                                </span>
                              </button>
                              <ul class="nav__sublist px-4 py-0 lg:px-0 m-0 list-none lg:w-auto w-full lg:min-w-1/8 absolute lg:relative lg:h-auto h-main top-0 left-0 bg-near-white">
                                <li>
                                  <button 
                                    class="flex flex-row justify-center w-full text-left bg-transparent text-black border-0 lg:hidden"
                                    neptune-engage='{
                                      preventDefault: true,
                                      on:click,
                                      targets: [
                                        {
                                          selector:"#linklist-{{forloop.index}} .nav__sublist",
                                          classes:toggle:active,
                                          siblings:classes:remove:active
                                        }
                                      ]
                                    }'
                                  >
                                    <span class="p-4 bg-transparent">
                                      {%  render 'icon' icon:'arrow-left' height:16 width:16 %}
                                    </span>
                                    <span class="flex flex-row items-center flex-grow p-4 leading-loose lg:leading-normal">Back</span>
                                  </button>
                                </li> 
                                {%- for child_link in link.links -%}
                                  <li>
                                    {%- liquid
                                      assign external_link = false
                                      if child_link.type == "http_link" 
                                        unless child_link.url contains '/vendors?' or child_link.url contains '/types?'
                                          assign external_link = true
                                        endunless
                                      endif
                                    -%}
                                    <a 
                                      {% if block.settings.linklist_color != blank %} style="color: {{block.settings.linklist_color}};" {% endif %} 
                                      class="block p-4 leading-loose lg:leading-normal text-black lg:text-xl hover:font-bold focus:font-bold animate lg:py-1 lg:px-0 whitespace-normal" 
                                      href="{{ child_link.url }}" 
                                      {% if child_link.active %}aria-current="page"{% endif %} 
                                      {% if external_link %}target="blank"{% endif %}
                                    >
                                      {{ child_link.title }}
                                    </a>
                                  </li>
                                {%- endfor -%}
                              </ul>
                            {%- endif -%}
                          </li>
                        {% endfor %}
                      </ul>

                    </div>
                  </div>
                </div>
              </div>
            {% endif %}

            {% if block.type == 'image_text' %}
              <div class="{{ block.settings.width }} {{ block.settings.desktop_width }} {{ dropdown.settings.gutters_desktop }} {% if block.settings.show_desktop == true and block.settings.show_mobile == true %} block {% elsif block.settings.show_desktop == true and block.settings.show_mobile == false %} lg:block hidden {% elsif block.settings.show_desktop == false and block.settings.show_mobile == true %} lg:hidden block {% endif %}">
                <a class="block navy" href="{{ block.settings.link }}">
                  {% assign block_image = block.settings.image %}
                  
                  <div class="relative rounded-sm" style="max-width: 750px;">
                    <img srcset="{%- if block_image.width >= 375 -%}{{ block_image | img_url: '375x' }} 375w,{%- endif -%}
                      {%- if block_image.width >= 750 -%}{{ block_image | img_url: '750x' }} 750w,{%- endif -%}
                      {%- if block_image.width >= 1100 -%}{{ block_image | img_url: '1100x' }} 1100w{%- endif -%}"
                      sizes="100vw"
                      src="{{ block_image | img_url: '750x' }}"
                      loading="lazy"
                      alt="{{ block_image.alt | escape }}"
                      width="{{ block_image.width }}"
                      height="{{ block_image.width | divided_by: block_image.aspect_ratio }}"
                      class="top-0 left-0 block object-contain object-top w-full h-full max-w-full"
                    >
                  </div>

                  <div class="py-1 lg:px-4">
                    {% if block.settings.title != blank %}
                      <p class="block px-0 pt-0 text-sm leading-normal text-black lg:py-1 m-0 lg:mb-1 {{ block.settings.text_style }}">{{ block.settings.title }}</h4>
                    {% endif %}
                    {% if block.settings.text != blank %}
                      <p class="block px-0 pt-2 pb-0 leading-normal text-black opacity-50 hover:opacity-100 focus:opacity-100 animate lg:py-1">{{ block.settings.text }}</p>
                    {% endif %}
                  </div>
                </a>
              </div>
            {% endif %}

          {%- endfor -%}

        </div>
   
      </div>

    </div>

  {%- endif -%}

{%- endfor -%}

<script src="{{ 'mega-menu.js' | asset_url }}"></script>

{% if section.settings.remove_section_condition != blank %}
  <script>
    window.addEventListener("load", (event) => {
      if ({{ section.settings.remove_section_condition }}) {
        _n.qsa('.mega-trigger').forEach(el=>el.remove());
        _n.qsa('.mega-overlay').forEach(el=>el.remove());
        _n.qsa('.mega-menu').forEach(el=>el.remove());
        document.getElementById('shopify-section-mega-menu').remove()
      }
    });
  </script>
{% endif %}
