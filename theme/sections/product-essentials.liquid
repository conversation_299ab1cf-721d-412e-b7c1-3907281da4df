<section class="relative lg:flex lg:{{section.settings.gallery_position}} {% if section.settings.remove_section_condition %} vwo-hide{% endif %}" neptune-product>

  <script type="application/json" neptune-product-data>
    {% render 'product-data' variants:true options:true %}
  </script>

  <article class="product-essentials__gallery relative flex flex-col w-full lg:w-2/3 lg:overflow-x-hidden user-select-none">


    <div class="w-full group lg:border-r-2 lg:border-b-2 border-white border-inset">
      
        {% include 'product-essentials-media' %}

    </div>

  </article>


  <article class="product-essentials__buy-box lg:w-1/3 w-full relative lg:flex lg:flex-col">
    
    <div class="lg:flex lg:flex-col h-full">

      <div class="product-essentials--buy-box py-5 lg:py-10 lg:sticky lg:top-20">

          {% include 'product-essentials-buy-box' %}

      </div>

      <div id="SidebarTop" class="lg:h-full relative pointer-events-none"></div>

      {% comment %}{% render 'sticky-add' product:product %}{% endcomment %}

    </div>

    <div id="SidebarBottom" class="product-essentials__extras mt-auto sticky top-20">

    </div>

  </article>

  </div>

</section>

{% render 'schema' %}


{% schema %}
{
  "name": "Product Essentials",
  "tag": "section",
  "class": "product-essentials",
  "settings": [
    {
      "type": "header",
      "content": "A/B Testing"
    },
    {
      "type": "checkbox",
      "id": "remove_section_condition",
      "label": "Remove Section",
      "default": false,
      "info": "This will hide the section in one of the experiment variations."
    },
    {
      "type": "header",
      "content": "Product Gallery"
    },
    {
      "type": "text",
      "id": "image_hide_alt",
      "label": "Image Hide",
      "info": "Matches text found in the alt text of the images to hide"
    },
    {
      "type": "select",
      "id": "pagination_type",
      "label": "Pagination Type",
      "options": [
        {
          "value": "none",
          "label": "None"
        },
        {
          "value": "fraction",
          "label": "Fraction"
        },
        {
          "value": "bullets",
          "label": "Bullets"
        },
        {
          "value": "thumbnail",
          "label": "Thumbnails"
        }
      ],
      "default": "bullets"
    },
    {
      "type": "select",
      "id": "gallery_position",
      "label": "Gallery Position",
      "options": [
        {
          "value": "flex-row-reverse",
          "label": "Right"
        },
        {
          "value": "flex-row",
          "label": "Left"
        }
      ],
      "default": "flex-row"
    },
    {
      "type": "checkbox",
      "id": "desktop_scroll",
      "label": "Scroll through product gallery on desktop",
      "default": true
    },
    {
      "type": "header",
      "content": "Visitor counter settings"
    },
    {
      "type": "checkbox",
      "id": "show_icon_vc",
      "label": "Show visitor counter icon",
      "default": false
    },
    {
      "type": "textarea",
      "id": "visitor_counter_inclusion_tag",
      "label": "Inclusion tag",
      "info": "Products with this tag will show the visitor counter."
    },
    {
      "type": "color",
      "id": "visitor_counter_text_color",
      "label": "Color text",
      "default": "#000000"
    },
    {
      "type": "select",
      "id": "visitor_counter_text_aligment",
      "label": "Text alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "title",
      "name": "t:sections.main-product.blocks.title.name",
      "limit": 1
    },
    {
      "type": "liquid_title",
      "name": "Liquid Title",
      "limit": 1,
      "settings": [
        {
          "type": "liquid",
          "id": "liquid",
          "label": "Liquid Text Field"
        }
      ]
    },
    {
      "type": "liquid_title_price",
      "name": "Liquid Title w/ Price",
      "limit": 1,
      "settings": [
        {
          "type": "liquid",
          "id": "liquid",
          "label": "Liquid Text Field"
        }
      ]
    },
    {
      "type": "liquid_subtitle",
      "name": "Subtitle",
      "limit": 2,
      "settings": [
        {
          "type": "liquid",
          "id": "liquid",
          "label": "Liquid Text Field"
        }
      ]
    },
    {
      "type": "liquid_vendor",
      "name": "Vendor",
      "limit": 1,
      "settings": [
        {
          "type": "liquid",
          "id": "liquid",
          "label": "Liquid Text Field"
        }
      ]
    },
    {
      "type": "product_sku",
      "name": "Product SKU",
      "limit": 1      
    },
    {
      "type": "breadcrumbs",
      "name": "Breadcrumbs",
      "limit": 1      
    },
    {
      "type": "promo_messaging",
      "name": "Promo Messaging",
      "limit": 1      
    },
    {
      "type": "liquid_block",
      "name": "Liquid",
      "limit": 4,
      "settings": [
        {
          "type": "inline_richtext",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "liquid",
          "id": "liquid",
          "label": "Liquid Block"
        }
      ]
    },
    {
      "type": "price",
      "name": "Price",
      "limit": 1
    },
    {
      "type": "product_form",
      "name": "Product Form",
      "limit": 1
    },
    {
      "type": "regional_messaging",
      "name": "Regional messaging",
      "limit": 1,
      "settings": [
        {
          "type": "textarea",
          "id": "country",
          "label": "Country Specific Messaging",
          "default": "US",
          "info": "For each country, use the 2 character country code or the currency code. Use 'ALL' for all international countries. Each country annoucement bar must be separated by a line break and in the follow format: AU | Free Shipping on orders over $50 AUD"
        }
      ]
    },
    {
      "type": "product_siblings",
      "name": "Product Siblings",
      "limit": 1
    },
    {
      "type": "description",
      "name": "Product Description",
      "limit": 1,
      "settings": [
       {
         "type": "text",
         "id": "description_title",
         "label": "Description Title",
         "default": "PRODUCT DETAILS"
       }
      ]
    },
    {
      "type": "review_summary",
      "name": "Reviews Summary",
      "limit": 1
    },
    {
      "type": "motivator",
      "name": "Motivator",
      "limit": 4,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "number",
          "id": "title_size",
          "label": "Title Font Size",
          "default": 20
        },
        {
          "type": "text",
          "id": "link_prefix",
          "label": "Link Prefix"
        },
        {
         "type": "text",
          "id": "link_text",
          "label": "Link Text"
        },
        {
          "type": "number",
          "id": "link_text_size",
          "label": "Link Font Size",
          "default": 16
        },
        {
          "type": "product",
          "id": "link_product",
          "label": "Link Product",
          "info": "If left blank, Link URL will be used."
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link URL"
        },
        {
          "type": "text",
          "id": "tags",
          "label": "Tag Requirement"
        }
      ]
    },
    {
      "type": "accordion",
      "name": "Accordion",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Accordion Title"
        },
        {
          "type": "richtext",
          "id": "panel",
          "label": "Accordion Panel"
        },
        {
          "type": "liquid",
          "id": "panel_liquid",
          "label": "Accordion Panel (Liquid)"
        }
      ]
    },
    {
      "type": "payment_widget",
      "name": "Payment Widgets",
      "settings": [
        {
          "type": "number",
          "id": "payment_count",
          "label": "Payment Count",
          "default": 4
        },
        {
          "type": "text",
          "id": "text",
          "label": "Widget Text",
          "info": "Or [ count ] interest-free payments of [ payment ] with ((klarna 40x16)) or ((afterpay 16x16))"
        },
        {
          "type": "header",
          "content": "Icon 1 settings"
        },
        {
          "type": "image_picker",
          "label": "Icon Image",
          "id": "image_1"
        },
        {
          "type": "url",
          "label": "Icon Image URL",
          "id": "link_1"
        },
        {
          "type": "header",
          "content": "Icon 2 settings"
        },
        {
          "type": "image_picker",
          "label": "Icon Image",
          "id": "image_2"
        },
        {
          "type": "url",
          "label": "Icon Image URL",
          "id": "link_2"
        },
        {
          "type": "header",
          "content": "Icon 3 settings"
        },
        {
          "type": "image_picker",
          "label": "Icon Image",
          "id": "image_3"
        },
        {
          "type": "url",
          "label": "Icon Image URL",
          "id": "link_3"
        },
        {
          "type": "header",
          "content": "Display Settings"
        },
        {
          "type": "textarea",
          "id": "country_inclusion",
          "label": "Country inclusion",
          "info": "Type the prefixes of the countries to be included separated by comma"
        }
      ]
    }
  ]
}
{% endschema %}
