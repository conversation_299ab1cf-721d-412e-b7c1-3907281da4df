<div data-modal="search" aria-expanded="false" class="relative right-0 bg-white z-40 modal w-screen active" tabindex="0" neptune-brig="{}" neptune-engage="{
  on:keyup,
  which: 27,
  targets:[{
    selector:html,
    attributes:[{
      att:data-active-modal,
      set:_remove
    }
  ]
  }]
}">
    
  <section class="search-section w-full z-30 bg-near-white text-black h-auto justify-center">

  <form action="{{ routes.search_url }}" method="get" role="search" class="search-form br-pill items-center justify-center w-full group modal-search-form">
    <label for="Search-header" class="hidden">
      Search
    </label>
    <input type="search" name="q" id="Search-header" value="" placeholder="Search..." class="search__input m-0 py-4 px-9 leading-relaxed text-lg bg-near-white shadow-none ui-autocomplete-input w-full" autocomplete="off" autocorrect="off" autocapitalize="off" aria-label="Search" aria-autocomplete="list">

      <input type="submit" value="go" class="sr-only">
      <button class="mr-8" 
        onclick="modal.close(); document.querySelectorAll('[search-suggestions]').forEach(el => el.innerHTML = ''); document.querySelectorAll('[search-results]').forEach(el => el.innerHTML = ''); return false;"
        x-neptune-engage="{
          preventDefault:true,
          targets:[
            {
              selector:html,
              attributes:[
                {
                  att:data-active-modal,
                  set:_remove
                }
              ]
            },
            {
              selector:'[data-modal=search]',
              attributes:[
                {
                  att: 'aria-expanded',
                  set:'false'
                }
              ]
            },
            {
              selector:'#Search-Trigger-header',
              attributes:[
                {
                  att: 'aria-expanded',
                  set:'false'
                }
              ]
            }
          ]
        }">
          {% render 'icon' icon:'x' %}
      </button>           
    </form>
    <div class="auto__results bg-transparent">
      <header 
        neptune-liquid="{topic:SearchSuggestions}" 
        class="empty:hidden bg-light" 
        search-suggestions
      >
        <template>
          {% raw %}
            {% if search.results != false and search.results.keywords.size > 0 %}
              <div class="pb-[0px] pt-[19px] bg-light">

                <p class="px-9 font-body text-[20px]  font-normal m-0 pb-2 border-b-2 border-white leading-normal">Search suggestions</p>
              
                <div class="mt-[15px] pb-[17px] px-9 suggested-search-words">
                  {% for item in search.results.keywords %}
					{% if forloop.index <= 10 %}
                    {% assign terms = search.term %}
                    {% for term in terms %}
                      <a href="{% if item.redirect %}{{ item.redirect | url }}{% else %}{{ Shopify.routes.root }}search?q={{ item.keyword }}{% endif %}" class="mr-8 text-sm hover:underline">
                        {%- if item.keyword contains term -%}
                          {%- capture term_bold -%}
                            <span class="predictive-word">{{ term }}</span>
                          {%- endcapture -%}
                          
                          <span class="text-[16px]">{{ item.keyword | replace: term, term_bold }}</span>
                        {%- else -%}
                          <span>{{ item.keyword }}</span>
                        {%- endif -%}
                      </a>         
                    {% endfor %}
					{% endif %}
                  {% endfor %}
                </div>  
              </div>
            {% endif %}
          {% endraw %}
        </template>
      </header>

      <div
      neptune-liquid="{topic:SearchResults}"
      search-results
      class="search-results empty:hidden bg-light h-full"
    >
      <template>
        {% raw %}
          {% if search.results != false and search.results.products.size > 0  %}
            <div class="px-0 pb-5 pt-[17px] bg-light">

              <p class="px-9 font-body text-[20px] capitalize m-0 pb-2">products</p>
              
              <div class="grid grid-cols-2 lg:grid-cols-4 search-results__container">
                {% for product in search.results.products limit: 4 %}
                  <div class="search-results__item {% endraw %} empty:hidden">
                    
                    {% render 'product-item' excluded_item_badge:false %}
                    {% raw %}
                  </div>
                {% endfor %}
              </div>

              <a href="/search?q={{ search.term }}" class="block font-body text-body my-4 px-9">See {{ search.results.total_products }} results for "{{ search.term }}" </a>

            </div>
          {% endif %}
        {% endraw %}
      </template>
    </div> 
    </div>
  </section>

</div>

<script type="text/javascript">
  window.addEventListener('Search:loaded', (e) => {
    Neptune.liquid.load('SearchResults');
    if(search.suggestions) Neptune.liquid.load('SearchSuggestions');
  });
</script>

{% schema %}
  {
    "name": "Search",
    "settings": [
    ],
    "blocks": [
    ]
  }
{% endschema %}
