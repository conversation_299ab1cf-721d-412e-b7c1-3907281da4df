{% schema %}
  {
  "name": "Custom Cursor",
  "settings": [
    {
      "type": "header",
      "content": "Cursor Settings"
    },
    {
      "type": "text",
      "label": "Cursor SVG",
      "id": "cursor_svg",
      "info": "Choose an SVG from your [files](/admin/settings/files) and paste the file name in the field above."
    },
    {
      "type": "text",
      "label": "Cursor width",
      "id": "cursor_image_width",
      "info": "Use a pixel value here."
    },
    {
      "type": "text",
      "label": "Cursor Position",
      "id": "position",
      "info": "X,Y Position of image relative to actual pointer",
      "default": "-90%,-100%"
    },
    {
      "type": "checkbox",
      "label": "Disable native cursor",
      "id": "disable_native",
      "default": true
    },
    {
      "type": "text",
      "label": "Blend mode",
      "id": "blend_mode",
      "default": "overlay"
    }
  ],
  "presets": [
    {
      "name": "Custom Cursor",
      "category": "Advanced",
      "settings": {},
      "blocks": []
    }
  ]
}
{% endschema %}

<div class="custom-cursor custom-cursor--{{ section.id }} hidden lg:block">
  <img src="{{ section.settings.cursor_svg | file_url }}" alt="custom cursor" style="width:{{section.settings.cursor_image_width}}px; max-width:none;" />
</div>

{% if section.settings.cursor_svg != blank %}
<script defer async>
  window.addEventListener('DOMContentLoaded', ()=>{
    var cursor = _n.qs('.custom-cursor--{{ section.id }}')
    window.addEventListener('mousemove', function(e){
      cursor.style.top = `${e.y}px`
      cursor.style.left = `${e.x}px`
    })
  })
</script>
{% endif %}

<style>
  @media(min-width:1000px) {
    .custom-cursor--{{ section.id }} {
      position:fixed;
      pointer-events:none;
      z-index:9000;
      transform:translate({{section.settings.position}});
      mix-blend-mode:{{section.settings.blend_mode}};
    }
    {% if section.settings.disable_native %}
      * {
        cursor: none!important;
      }
      
      #onetrust-banner-sdk * {
        cursor: auto!important;
      }

      #onetrust-accept-btn-handler {
        cursor: pointer!important;
      }
    {% endif %}
  }
</style>
