{% comment %} 


This is generated by webpack so don't edit this unless you want to be sad when your work gets overwritten in production.
You can edit this file in ../../components/** 


{% endcomment %}






<script>
  {%- capture CollectionBanners -%}
  {%- for block in section.blocks -%}
  {%- if collection.handle == block.settings.collection -%}
    {{ block.settings | json }},
  {%- endif -%}
  {%- endfor -%}
  {%- endcapture -%}
  {%- assign size = CollectionBanners | size | minus: 1 -%}
  window.collectionBanners = [{{ CollectionBanners | slice: 0, size }}]
</script>


  
{% schema %}
{
  "name": "Collection Banners",
  "settings": [
  ],
  "blocks": [
    {
      "type": "banner",
      "name": "Banner",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Banner Title"
        },
        {
          "type": "select",
          "label": "Mobile Width",
          "id": "mobile_width",
          "default": "col-span-1",
          "options": [
            {
              "label": "Full",
              "value": "col-span-2"
            },
            {
              "label": "Half",
              "value": "col-span-1"
            }
          ]
        },
        {
          "type": "select",
          "label": "Desktop Width",
          "id": "desktop_width",
          "default": "md:col-span-4",
          "options": [
            {
              "label": "Full",
              "value": "md:col-span-4"
            },
            {
              "label": "Three Quarters",
              "value": "md:col-span-3"
            },
            {
              "label": "Half",
              "value": "md:col-span-2"
            },
            {
              "label": "Quarter",
              "value": "md:col-span-1"
            }
          ]
        },
        {
          "type": "select",
          "label": "Horizontal Content Justification",
          "id": "justify",
          "default": "justify-center text-center",
          "options": [
            {
              "label": "Left",
              "value": "justify-start text-left"
            },
            {
              "label": "Center",
              "value": "justify-center text-center"
            },
            {
              "label": "Right",
              "value": "justify-end text-right"
            }
          ]
        },
        {
          "type": "select",
          "label": "Vertical Content Justification",
          "id": "align",
          "default": "items-center",
          "options": [
            {
              "label": "Top",
              "value": "items-start"
            },
            {
              "label": "Middle",
              "value": "items-center"
            },
            {
              "label": "Bottom",
              "value": "items-end"
            }
          ]
        },
        {
          "type": "text",
          "id": "heading",
          "label": "Heading"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "Sub Heading"
        },
        {
          "type": "text",
          "id": "button",
          "label": "Button Text"
        },
        {
          "type": "select",
          "label": "Button Color",
          "id": "button_color",
          "default": "bg-near-black text-white",
          "options": [
            {
              "label": "White",
              "value": "button--white"
            },
            {
              "label": "Black",
              "value": "bg-near-black text-white"
            }
          ]
        },
        {
          "type": "select",
          "label": "Text Color",
          "id": "color",
          "default": "text-white",
          "options": [
            {
              "label": "White",
              "value": "text-white"
            },
            {
              "label": "Black",
              "value": "text-near-black"
            }
          ]
        },
        {
          "type": "collection",
          "label": "Collection",
          "id": "collection"
        },
        {
          "type": "number",
          "label": "Position",
          "id": "position"
        },
        {
          "type": "url",
          "label": "Link",
          "id": "url"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        }
      ]
    }
  ]
}
{% endschema %}
