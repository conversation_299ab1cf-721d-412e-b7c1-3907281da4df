{% schema %}
{
  "name": "Slideshow Items",
  "settings": [
    {
      "type": "header",
      "content": "A/B Testing"
    },
    {
      "type": "checkbox",
      "id": "remove_section_condition",
      "label": "Remove Section",
      "default": false,
      "info": "This will hide the section in one of the experiment variations."
    },
    {
      "type": "header",
      "content": "Display Settings"
    },
    {
      "type": "checkbox",
      "id": "async",
      "label": "Asynchronously Load Section on Scroll",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_desktop",
      "label": "Show desktop",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_mobile",
      "label": "Show mobile",
      "default": true
    },
    {
      "type": "header",
      "content": "Slideshow Settings"
    },
    {
      "type": "number",
      "id": "slides_per_view",
      "label": "Slides per view (Desktop)",
      "default": 4
    },
    {
      "type": "number",
      "id": "spacebetween",
      "label": "Space between slides (Desktop)",
      "default": 16
    },
    {
      "type": "number",
      "id": "slides_per_view_mobile",
      "label": "Slides per view (Mobile)",
      "default": 2
    },
    {
      "type": "number",
      "id": "spacebetween_mobile",
      "label": "Space between slides (Mobile)",
      "default": 16
    },
    {
      "type": "number",
      "id": "limit",
      "label": "Number of Slides",
      "info": "For collections, products, and blog posts",
      "default": 12
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Autoplay"
    },
    {
      "type": "checkbox",
      "id": "loop",
      "label": "Loop"
    },
    {
      "type": "range",
      "id": "autoplay_slide_duration",
      "label": "Autoplay Slide Duration",
      "min": 3,
      "max": 8,
      "step": 1,
      "default": 8
    },
    {
      "type": "checkbox",
      "id": "arrows",
      "label": "Show arrows (Desktop)",
      "default":true
    },
    {
      "type": "checkbox",
      "id": "arrows_mobile",
      "label": "Show arrows (Mobile)",
      "default":true
    },
    {
      "type": "checkbox",
      "id": "show_pagination",
      "label": "Show Pagination (Mobile)",
      "default": false
    },
    {
      "type": "header",
      "content": "Section Display Settings"
    },
    {
      "type": "select",
      "id": "container",
      "label": "Container",
      "options": [
        {
          "value": "container",
          "label": "Container"
        },
        {
          "value": "w-full",
          "label": "Full width"
        }
      ],
      "default": "container"
    },
    {
      "type": "select",
      "id": "layout",
      "label": "Layout",
      "options": [
        {
          "value": "lg:flex-col",
          "label": "Info on top"
        },
        {
          "value": "lg:flex-row",
          "label": "Info on side"
        }
      ],
      "default": "lg:flex-row"
    },
    {
      "type": "select",
      "id": "item_height",
      "label": "Grid Item Aspect Ratio (Desktop)",
      "info": "To use Grid Item Aspect Ratio height, set section height for desktop and mobile to text content height.",
      "options": [
        {
          "value": "",
          "label": "auto"
        },
        {
          "value": "lg:aspect-w-16 lg:aspect-h-9",
          "label": "16x9"
        },
        {
          "value": "lg:aspect-w-9 lg:aspect-h-16",
          "label": "9x16"
        },
        {
          "value": "lg:aspect-w-4 lg:aspect-h-3",
          "label": "4x3"
        },
        {
          "value": "lg:aspect-w-3 lg:aspect-h-4",
          "label": "3x4"
        },
        {
          "value": "lg:aspect-w-6 lg:aspect-h-4",
          "label": "6x4"
        },
        {
          "value": "lg:aspect-w-4 lg:aspect-h-6",
          "label": "4x6"
        },
        {
          "value": "lg:aspect-w-8 lg:aspect-h-5",
          "label": "8x5"
        },
        {
          "value": "lg:aspect-w-5 lg:aspect-h-8",
          "label": "5x8"
        },
        {
          "value": "lg:aspect-w-7 lg:aspect-h-5",
          "label": "7x5"
        },
        {
          "value": "lg:aspect-w-5 lg:aspect-h-7",
          "label": "5x7"
        },
        {
          "value": "lg:aspect-w-1 lg:aspect-h-1",
          "label": "1x1"
        }
      ],
      "default": ""
    },
    {
      "type": "select",
      "id": "item_height_mobile",
      "label": "Grid Item Aspect Ratio (Mobile)",
      "info": "To use Grid Item Aspect Ratio height, set section height for desktop and mobile to text content height. If you set for mobile, this will also default for desktop.",
      "options": [
        {
          "value": "",
          "label": "auto"
        },
        {
          "value": "aspect-w-16 aspect-h-9",
          "label": "16x9"
        },
        {
          "value": "aspect-w-9 aspect-h-16",
          "label": "9x16"
        },
        {
          "value": "aspect-w-4 aspect-h-3",
          "label": "4x3"
        },
        {
          "value": "aspect-w-3 aspect-h-4",
          "label": "3x4"
        },
        {
          "value": "aspect-w-6 aspect-h-4",
          "label": "6x4"
        },
        {
          "value": "aspect-w-4 aspect-h-6",
          "label": "4x6"
        },
        {
          "value": "aspect-w-8 aspect-h-5",
          "label": "8x5"
        },
        {
          "value": "aspect-w-5 aspect-h-8",
          "label": "5x8"
        },
        {
          "value": "aspect-w-7 aspect-h-5",
          "label": "7x5"
        },
        {
          "value": "aspect-w-5 aspect-h-7",
          "label": "5x7"
        },
        {
          "value": "aspect-w-1 aspect-h-1",
          "label": "1x1"
        }
      ],
      "default": ""
    },
    {
      "type": "color",
      "label": "Background Color",
      "id": "background_color"
    },
    {
      "type": "header",
      "content": "Section Spacing Settings (Desktop)"
    },
    {
      "type": "select",
      "id": "section_top_margin",
      "label": "Margin top",
      "options": [
        {
          "value": "lg:mt-0",
          "label": "None"
        },
        {
          "value": "lg:mt-1",
          "label": ".25rem"
        },
        {
          "value": "lg:mt-2",
          "label": ".5rem"
        },
        {
          "value": "lg:mt-4",
          "label": "1rem"
        },
        {
          "value": "lg:mt-8",
          "label": "2rem"
        },
        {
          "value": "lg:mt-16",
          "label": "4rem"
        },
        {
          "value": "lg:mt-32",
          "label": "8rem"
        },
        {
          "value": "lg:mt-64",
          "label": "16rem"
        }
      ],
      "default": "lg:mt-16"
    },
    {
      "type": "select",
      "id": "section_bottom_margin",
      "label": "Margin bottom ",
      "options": [
        {
          "value": "lg:mb-0",
          "label": "None"
        },
        {
          "value": "lg:mb-1",
          "label": ".25rem"
        },
        {
          "value": "lg:mb-2",
          "label": ".5rem"
        },
        {
          "value": "lg:mb-4",
          "label": "1rem"
        },
        {
          "value": "lg:mb-8",
          "label": "2rem"
        },
        {
          "value": "lg:mb-16",
          "label": "4rem"
        },
        {
          "value": "lg:mb-32",
          "label": "8rem"
        },
        {
          "value": "lg:mb-64",
          "label": "16rem"
        }
      ],
      "default": "lg:mb-16"
    },
    {
      "type": "select",
      "id": "section_horizontal_padding",
      "label": "Horizontal Padding",
      "options": [
        {
          "value": "lg:px-0",
          "label": "None"
        },
        {
          "value": "lg:px-1",
          "label": ".25rem"
        },
        {
          "value": "lg:px-2",
          "label": ".5rem"
        },
        {
          "value": "lg:px-4",
          "label": "1rem"
        },
        {
          "value": "lg:px-8",
          "label": "2rem"
        },
        {
          "value": "lg:px-16",
          "label": "4rem"
        },
        {
          "value": "lg:px-32",
          "label": "8rem"
        },
        {
          "value": "lg:px-64",
          "label": "16rem"
        }
      ],
      "default": "lg:px-0"
    },
    {
      "type": "select",
      "id": "section_vertical_padding",
      "label": "Vertical Padding",
      "options": [
        {
          "value": "lg:py-0",
          "label": "None"
        },
        {
          "value": "lg:py-1",
          "label": ".25rem"
        },
        {
          "value": "lg:py-2",
          "label": ".5rem"
        },
        {
          "value": "lg:py-4",
          "label": "1rem"
        },
        {
          "value": "lg:py-8",
          "label": "2rem"
        },
        {
          "value": "lg:py-16",
          "label": "4rem"
        },
        {
          "value": "lg:py-32",
          "label": "8rem"
        },
        {
          "value": "lg:py-64",
          "label": "16rem"
        }
      ],
      "default": "lg:py-0"
    },
    {
      "type": "header",
      "content": "Section Spacing Settings (Mobile)"
    },
    {
      "type": "select",
      "id": "section_top_margin_mobile",
      "label": "Margin top",
      "options": [
        {
          "value": "mt-0",
          "label": "None"
        },
        {
          "value": "mt-1",
          "label": ".25rem"
        },
        {
          "value": "mt-2",
          "label": ".5rem"
        },
        {
          "value": "mt-4",
          "label": "1rem"
        },
        {
          "value": "mt-8",
          "label": "2rem"
        },
        {
          "value": "mt-16",
          "label": "4rem"
        },
        {
          "value": "mt-32",
          "label": "8rem"
        },
        {
          "value": "mt-64",
          "label": "16rem"
        }
      ],
      "default": "mt-16"
    },
    {
      "type": "select",
      "id": "section_bottom_margin_mobile",
      "label": "Margin bottom",
      "options": [
        {
          "value": "mb-0",
          "label": "None"
        },
        {
          "value": "mb-1",
          "label": ".25rem"
        },
        {
          "value": "mb-2",
          "label": ".5rem"
        },
        {
          "value": "mb-4",
          "label": "1rem"
        },
        {
          "value": "mb-8",
          "label": "2rem"
        },
        {
          "value": "mb-16",
          "label": "4rem"
        },
        {
          "value": "mb-32",
          "label": "8rem"
        },
        {
          "value": "mb-64",
          "label": "16rem"
        }
      ],
      "default": "mb-16"
    },
    {
      "type": "select",
      "id": "section_horizontal_padding_mobile",
      "label": "Horizontal Padding",
      "options": [
        {
          "value": "px-0",
          "label": "None"
        },
        {
          "value": "px-1",
          "label": ".25rem"
        },
        {
          "value": "px-2",
          "label": ".5rem"
        },
        {
          "value": "px-4",
          "label": "1rem"
        },
        {
          "value": "px-8",
          "label": "2rem"
        },
        {
          "value": "px-16",
          "label": "4rem"
        },
        {
          "value": "px-32",
          "label": "8rem"
        },
        {
          "value": "px-64",
          "label": "16rem"
        }
      ],
      "default": "px-0"
    },
    {
      "type": "select",
      "id": "section_vertical_padding_mobile",
      "label": "Vertical Padding",
      "options": [
        {
          "value": "py-0",
          "label": "None"
        },
        {
          "value": "py-1",
          "label": ".25rem"
        },
        {
          "value": "py-2",
          "label": ".5rem"
        },
        {
          "value": "py-4",
          "label": "1rem"
        },
        {
          "value": "py-8",
          "label": "2rem"
        },
        {
          "value": "py-16",
          "label": "4rem"
        },
        {
          "value": "py-32",
          "label": "8rem"
        },
        {
          "value": "py-64",
          "label": "16rem"
        }
      ],
      "default": "py-0"
    },
    {
      "type": "header",
      "content": "Text Settings"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color"
    },
    {
      "type": "select",
      "id": "text_type",
      "label": "Typeface",
      "options": [
        {
          "value": "MonumentGrotesk-Regular",
          "label": "Monument Grotesk Regular"
        },
        {
          "value": "MonumentGrotesk-Medium",
          "label": "Monument Grotesk Medium "
        },
        {
          "value": "MonumentGrotesk-Medium-Italic",
          "label": "Monument Grotesk Medium Italic"
        },
        {
          "value": "MonumentGrotesk-Bold",
          "label": "Monument Grotesk Bold"
        },
        {
          "value": "ABC-Monument",
          "label": "ABC Monument"
        },
        {
          "value": "font-pop",
          "label": "ABC Monument Grotesk Black"
        },
        {
          "value": "font-highlight",
          "label": "ABC Monument Grotesk Light"
        },
        {
          "value": "Libre-Caslon",
          "label": "Libre Caslon"
        }
      ],
      "default": "MonumentGrotesk-Regular"
    },
    {
      "type": "text",
      "id": "text_leading",
      "label": "Text Line Height",
      "default": "1.38"
    },
    {
      "type": "text",
      "id": "text_letter_spacing",
      "label": "Letter Spacing"
    },
    {
      "type": "select",
      "id": "text_align",
      "label": "Text Alignment (Desktop)",
      "options": [
        {
          "value": "lg:text-left",
          "label": "Left"
        },
        {
          "value": "lg:text-center",
          "label": "Center"
        },
        {
          "value": "lg:text-right",
          "label": "Right"
        },
        {
          "value": "lg:text-justify lg:text-justify-last",
          "label": "Justify"
        }
      ],
      "default": "lg:text-left"
    },
    {
      "type": "select",
      "id": "text_align_mobile",
      "label": "Text Alignment (Mobile)",
      "options": [
        {
          "value": "text-left",
          "label": "Left"
        },
        {
          "value": "text-center",
          "label": "Center"
        },
        {
          "value": "text-right",
          "label": "Right"
        },
        {
          "value": "text-justify text-justify-last",
          "label": "Justify"
        }
      ],
      "default": "text-left"
    },
    {
      "type": "header",
      "content": "Title Typeface Options"
    },
    {
      "type": "header",
      "content": "Title Typeface Options"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title"
    },
    {
      "type": "select",
      "id": "title_type",
      "label": "Typeface",
      "options": [
        {
          "value": "type--primary",
          "label": "Primary"
        },
        {
          "value": "type--secondary",
          "label": "Secondary"
        },
        {
          "value": "type--page",
          "label": "Page"
        },
        {
          "value": "type--section",
          "label": "Section"
        },
        {
          "value": "type--article",
          "label": "Article"
        },
        {
          "value": "type--headline",
          "label": "Headline"
        }
      ],
      "default": "type--secondary"
    },
    {
      "type": "select",
      "id": "title_element",
      "label": "Heading Type",
      "options": [
        {
          "value": "h1",
          "label": "H1"
        },
        {
          "value": "h2",
          "label": "H2"
        },
        {
          "value": "h3",
          "label": "H3"
        },
        {
          "value": "h4",
          "label": "H4"
        },
        {
          "value": "h5",
          "label": "H5"
        }
      ],
      "default": "h2"
    },
    {
      "type": "text",
      "id": "title_leading",
      "label": "Title Line Height",
      "default": "1.38"
    },
    {
      "type": "text",
      "id": "title_letter_spacing",
      "label": "Title Letter Spacing"
    },
    {
      "type": "header",
      "content": "Content Typeface Options"
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "Content"
    },
    {
      "id": "text_size",
      "label": "Font Size Override",
      "type": "text",
      "info": "Use a pixel or rem value for font size"
    },
    {
      "id": "text_weight",
      "label": "Font Weight Override",
      "type": "text",
      "info": "Use a value for font weight"
    },
    {
      "type": "header",
      "content": "Link Settings"
    },
    {
      "type": "select",
      "id": "link_element",
      "label": "Link Element",
      "options": [
        {
          "value": "",
          "label": "Button Only"
        },
        {
          "value": "block",
          "label": "Entire Block"
        }
      ],
      "default": ""
    },
    {
      "type": "header",
      "content": "Button Layout Settings"
    },
    {
      "type": "text",
      "id": "link_title",
      "label": "Link title"
    },
    {
      "type": "url",
      "id": "url",
      "label": "Link"
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "Button Style",
      "options": [
        {
          "value": "button button--primary",
          "label": "Button Primary"
        },
        {
          "value": "button button--secondary",
          "label": "Button Secondary"
        },
        {
          "value": "button button--tertiary",
          "label": "Button Tertiary"
        },
        {
          "value": "button button--light",
          "label": "Button Light"
        },
        {
          "value": "button button--higlight",
          "label": "Button Highlight"
        },
        {
          "value": "button button--link",
          "label": "Button Text"
        },
        {
          "value": "button button--text",
          "label": "Text link"
        }
      ],
      "default": "button button--primary"
    },
    {
      "type": "color",
      "id": "btn_color",
      "label": "Custom Background Color"
    },
    {
      "type": "color",
      "id": "btn_border_color",
      "label": "Custom Border Color"
    },
    {
      "type": "color",
      "id": "btn_text_color",
      "label": "Custom Text Color"
    },
    {
      "type": "color",
      "id": "btn_hover_color",
      "label": "Button bg color (hover)"
    },
    {
      "type": "color",
      "id": "btn_hover_text_color",
      "label": "Button text color (hover)"
    },
    {
      "type": "color",
      "id": "btn_hover_border_color",
      "label": "Button border color (hover)"
    }
  ],
  "blocks": [
    {
      "name": "Slide Item",
      "type": "item_content",
      "settings": [
        {
          "type": "header",
          "content": "Item settings"
        },
        {
          "type": "image_picker",
          "id": "item_image",
          "label": "Content Image"
        },
        {
          "type": "header",
          "content": "Button Layout Settings"
        },
        {
          "type": "text",
          "id": "link_title",
          "label": "Link title"
        },
        {
          "type": "url",
          "id": "url",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Button Style",
          "options": [
            {
              "value": "button button--primary",
              "label": "Button Primary"
            },
            {
              "value": "button button--secondary",
              "label": "Button Secondary"
            },
            {
              "value": "button button--tertiary",
              "label": "Button Tertiary"
            },
            {
              "value": "button button--light",
              "label": "Button Light"
            },
            {
              "value": "button button--higlight",
              "label": "Button Highlight"
            },
            {
              "value": "button button--link",
              "label": "Button Text"
            },
            {
              "value": "button button--text",
              "label": "Text link"
            }
          ],
          "default": "button button--primary"
        },
        {
          "type": "select",
          "id": "button_align",
          "label": "Button Alignment (Desktop)",
          "options": [
            {
              "value": "lg:items-start",
              "label": "Left"
            },
            {
              "value": "lg:items-center",
              "label": "Center"
            },
            {
              "value": "lg:items-right",
              "label": "Right"
            }
          ],
          "default": "lg:items-center"
        },
        {
          "type": "select",
          "id": "button_align_mobile",
          "label": "Button Alignment (Mobile)",
          "options": [
            {
              "value": "items-start",
              "label": "Left"
            },
            {
              "value": "items-center",
              "label": "Center"
            },
            {
              "value": "items-end",
              "label": "Right"
            }
          ],
          "default": "items-center"
        },
        {
          "type": "color",
          "id": "btn_color",
          "label": "Button bg color",
          "default": "#f7f7f7"
        },
        {
          "type": "color",
          "id": "btn_border_color",
          "label": "Button border color",
          "default": "#222222"
        },
        {
          "type": "color",
          "id": "btn_text_color",
          "label": "Button text color",
          "default": "#222222"
        },
        {
          "type": "color",
          "id": "btn_hover_color",
          "label": "Button bg color (hover)"
        },
        {
          "type": "color",
          "id": "btn_hover_text_color",
          "label": "Button text color (hover)"
        },
        {
          "type": "color",
          "id": "btn_hover_border_color",
          "label": "Button border color (hover)"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Slideshow Items",
      "category": "Advanced",
      "settings": {},
      "blocks": []
    }
  ]
}

{% endschema %}

{% include 'async-section' skeleton:'<div class="p-16 w-full h-50v bg-white"><div class="bg-gray-100 p-16 rounded-2xl h-full w-full drop-shadow-lg flex flex-col items-center justify-center"><span class="sr-only">Skeleton</span><span class="bg-gray-200 h-24 w-24"></span><span class="bg-gray-200 h-2 w-1/4 mt-4"></span><span class="bg-gray-200 h-2 w-1/4 mt-4"></span><span class="bg-gray-200 h-2 w-1/4 mt-4"></span><span class="bg-gray-200 h-2 w-1/4 mt-4"></span></div></div>' %}
{% unless defer_section %}

<section class="section-{{section.id}} slideshow-items {% if section.settings.show_desktop == true and section.settings.show_mobile == true %} block {% elsif section.settings.show_desktop == true and section.settings.show_mobile == false %} hidden lg:block{% elsif section.settings.show_desktop == false and section.settings.show_mobile == true %} block lg:hidden{% endif %} section-slideshow section-slideshow-collection {{ section.settings.section_top_margin }} {{ section.settings.section_bottom_margin }} {{ section.settings.section_top_margin_mobile }} {{ section.settings.section_bottom_margin_mobile }} {{ section.settings.section_horizontal_padding }} {{ section.settings.section_horizontal_padding_mobile }} {{ section.settings.section_vertical_padding }} {{ section.settings.section_vertical_padding_mobile }} {% if section.settings.remove_section_condition %} vwo-hide{% endif %}" {% if section.settings.background_color %}style="background-color: {{ section.settings.background_color }};"{% endif %}>

  <div class="{{ section.settings.container }} mx-auto flex flex-col {{ section.settings.layout }} lg:px-8 px-4">

    {% if section.settings.title != blank %}
      <aside class="{{ section.settings.text_align }} {{ section.settings.text_align_mobile }} flex flex-col w-full px-4 pb-8 {% if section.settings.layout contains 'lg:flex-row' %}lg:w-1/4 lg:pl-0 lg:pr-8 lg:pb-0 lg:justify-center{% else %}lg:px-0{% endif %}">
        <div>
          {% if section.settings.title != blank %}
            <{{ section.settings.title_element }} 
              class="{{ section.settings.title_type }} mb-2 mt-0" 
              {% if section.settings.text_color != blank or section.settings.title_size != blank or section.settings.title_weight != blank %}
              style="{% if section.settings.text_color != blank %}color: {{ section.settings.text_color }}; {% endif %}{% if section.settings.title_size != blank %}font-size: {{ section.settings.title_size }}; {% endif %}{% if section.settings.title_weight != blank %}font-weight: {{ section.settings.title_weight }}; {% endif %}"
              {% endif %}
            >
                {{ section.settings.title }}
            </{{ section.settings.title_element }}>
          {% endif %}

          {% if section.settings.text != blank %}
            <div 
              class="block__copy relative mb-2" 
              {% if section.settings.text_color != blank %}style="color: {{ section.settings.text_color }};"{% endif %}
            >
              {{ section.settings.text }}
            </div>
          {% endif %}  
          {% if section.settings.link_title != blank %}
            <a 
              class="mt-8 link {{ section.settings.button_style }} inline-flex"
              href="{{ section.settings.url }}" 
              style="{% if section.settings.btn_color != blank %}background-color:{{ section.settings.btn_color }};{% endif %}{% if section.settings.btn_text_color != blank %}color:{{ section.settings.btn_text_color }};{% endif %}{% if section.settings.btn_border != blank %}border-color:{{ section.settings.btn_border }};{% endif %}">
                <span>{{ section.settings.link_title }}</span>
                {% if section.settings.button_style contains 'button--custom' %}<span>{% render 'icon' icon: 'chevron-right' width:26 height:26 strokeWidth:2 %}</span>{% endif %}
            </a>
          {% endif %}          
        </div>

      </aside>
    {% endif %}

    <div class="w-full {% if section.settings.layout contains 'lg:flex-row' %}lg:w-3/4{% endif %}">
      <div class="w-full h-full relative">

        <div 
          class="swiper-container h-full"
          neptune-swiper
          data-swiper-mousewheel='{ "forceToAxis": true}'
          data-swiper-effect="slide" 
          data-swiper-watch-overflow="true"
          data-swiper-preload-images="true",
          data-swiper-update-on-images-ready="true",
          {% if section.settings.autoplay == true %}
            data-swiper-autoplay='{
              "delay": {{ section.settings.autoplay_slide_duration }}000
            }'
          {% endif %}
          data-swiper-loop="{{ section.settings.loop }}" 
          data-swiper-breakpoints='{
            "1": {
              "spaceBetween": {{ section.settings.spacebetween_mobile }},
              "slidesPerView": {{ section.settings.slides_per_view_mobile }},
              "autoHeight": true,
              "centeredSlides": true
              
            },
            "1025": {
              "spaceBetween": {{ section.settings.spacebetween }},
              "slidesPerView": {{ section.settings.slides_per_view }},
              "autoHeight": true
            }
          }'
          data-swiper-navigation='{
            "nextEl": ".section-{{section.id}} .swiper-button-next-unique",
            "prevEl": ".section-{{section.id}} .swiper-button-prev-unique"
          }'
          data-swiper-pagination='{
            "el": ".section-{{section.id}} .pagination"
          }'
          >

          <div class="swiper-wrapper h-full items-stretch">
            {% assign banner_aspect_ratio = true %}
            {% for block in section.blocks %}
              {% if block.type != "image_banner" %}
                {% assign banner_aspect_ratio = false %}
              {% endif %}
            {% endfor %}

            {% for block in section.blocks %}

              {% if block.type == 'item_content' %}
                  <div id="slide-{{block.id}}" class="swiper-slide swiper-slide__card lg:w-1/{{ section.settings.slides_per_view | split: '.' | first }} w-1/{{ section.settings.slides_per_view_mobile | split: '.' | first }} h-full" style="{% if block.settings.block_background_color != blank %}background-color: {{ block.settings.block_background_color }};{% endif %}{% if block.settings.block_text_color != blank %}text-color: {{ block.settings.block_text_color }};{% endif %}">
                    <a href="{{ block.settings.url}}" class="w-full flex flex-col items-center">
                      <div class="w-full flex flex-col {{ block.settings.button_align_mobile }} {{ block.settings.button_align }}">
                        {% render 'lazy-image' with image: block.settings.item_image, image_class: "w-full" %}
                        {% if block.settings.link_title != blank %}
                          <button class="inline-block min-h-0 my-6 mx-2 {{ block.settings.button_style }}"
                            onclick="window.location.href='{{ block.settings.url }}'">
                            {{ block.settings.link_title }}
                          </button>
                          {% style %}
                            #slide-{{block.id}} a button {
                              background-color: {{ block.settings.btn_color }}; 
                              border-color: {{ block.settings.btn_border_color }}; 
                              color: {{ block.settings.btn_text_color }};
                            }
                            #slide-{{block.id}} a:hover button, #slide-{{block.id}} a:focus button {
                              background-color: {{ block.settings.btn_text_color }}; 
                              border-color: {{ block.settings.btn_text_color }}; 
                              color: {{ block.settings.btn_color }};
                            }
                          {% endstyle %}
                        {% endif %}
                      </div>
                    </a>
                  </div>
              {% endif %}

            {% endfor %}
          
          </div>

        </div>

        <div class="flex items-center p-0">

          {% if section.settings.show_pagination == true %}
            <div class="pagination lg:hidden"></div>
          {% endif %}

          <div class="flex pl-8">
            <button class="absolute z-2 left-0 top-0 h-full items-center flex-col justify-center {% unless section.settings.arrows_mobile %}hidden{% endunless %} lg:flex {% unless section.settings.arrows %}lg:hidden{% endunless %} swiper-prev swiper-button-prev-unique btn-control bg-transparent text-dark p-2" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
              {% render 'icon' icon:'chevron-left' width:20 height:20 strokeWidth:1 %}
            </button>
            <button class="absolute z-2 right-0 top-0 h-full items-center flex-col justify-center {% unless section.settings.arrows_mobile %}hidden{% endunless %} lg:flex {% unless section.settings.arrows %}lg:hidden{% endunless %} swiper-next swiper-button-next-unique btn-control bg-transparent text-dark p-2" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
              {% render 'icon' icon:'chevron-right' width:20 height:20 strokeWidth:1 %}
            </button>
          </div>

        </div>

      </div>
    </div>

  </div>
  {% style %}
    .section-slideshow-multiple .productitem--extra {
      padding-left: 1rem;
      padding-right: 1rem;
    }
    .section-slideshow-multiple .productitem--wrap {
      height: 100%;
    }
    .section-slideshow-collection .swiper-button-disabled {
      opacity: .5;
    }
  {% endstyle %}
</section>

{% endunless %}