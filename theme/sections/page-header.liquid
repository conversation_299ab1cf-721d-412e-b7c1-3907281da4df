{% schema %}
{
  "name": "Page Header",
  "settings": [
    {
      "type": "header",
      "content": "Container Settings"
    },
    {
      "type": "select",
      "id": "container",
      "label": "Container",
      "options": [
        {
          "value": "max-w-4xl",
          "label": "Slim"
        },
        {
          "value": "container",
          "label": "Container"
        },
        {
          "value": "w-full",
          "label": "Full width"
        }
      ],
      "default": "max-w-4xl"
    },
    {
      "type": "header",
      "content": "Layout Settings (Desktop)"
    },
    {
      "type": "select",
      "id": "content_top_padding",
      "label": "Content Top Padding",
      "options": [
        {
          "value": "lg:pt-0",
          "label": "None"
        },
        {
          "value": "lg:pt-1",
          "label": ".25rem"
        },
        {
          "value": "lg:pt-2",
          "label": ".5rem"
        },
        {
          "value": "lg:pt-4",
          "label": "1rem"
        },
        {
          "value": "lg:pt-8",
          "label": "2rem"
        },
        {
          "value": "lg:pt-16",
          "label": "4rem"
        },
        {
          "value": "lg:pt-32",
          "label": "8rem"
        },
        {
          "value": "lg:pt-64",
          "label": "16rem"
        }
      ],
      "default": "lg:pt-0"
    },
    {
      "type": "select",
      "id": "content_bottom_padding",
      "label": "Content Bottom Padding",
      "options": [
        {
          "value": "lg:pb-0",
          "label": "None"
        },
        {
          "value": "lg:pb-1",
          "label": ".25rem"
        },
        {
          "value": "lg:pb-2",
          "label": ".5rem"
        },
        {
          "value": "lg:pb-4",
          "label": "1rem"
        },
        {
          "value": "lg:pb-8",
          "label": "2rem"
        },
        {
          "value": "lg:pb-16",
          "label": "4rem"
        },
        {
          "value": "lg:pb-32",
          "label": "8rem"
        },
        {
          "value": "lg:pb-64",
          "label": "16rem"
        }
      ],
      "default": "lg:pb-0"
    },
    {
      "type": "select",
      "id": "content_horizontal_padding",
      "label": "Content Horizontal Padding",
      "options": [
        {
          "value": "lg:px-0",
          "label": "None"
        },
        {
          "value": "lg:px-1",
          "label": ".25rem"
        },
        {
          "value": "lg:px-2",
          "label": ".5rem"
        },
        {
          "value": "lg:px-4",
          "label": "1rem"
        },
        {
          "value": "lg:px-8",
          "label": "2rem"
        },
        {
          "value": "lg:px-16",
          "label": "4rem"
        },
        {
          "value": "lg:px-32",
          "label": "8rem"
        },
        {
          "value": "lg:px-64",
          "label": "16rem"
        }
      ],
      "default": "lg:px-0"
    },
    {
      "type": "header",
      "content": "Layout Settings (Mobile)"
    },
    {
      "type": "select",
      "id": "content_top_padding_mobile",
      "label": "Content Top Padding",
      "options": [
        {
          "value": "pt-0",
          "label": "None"
        },
        {
          "value": "pt-1",
          "label": ".25rem"
        },
        {
          "value": "pt-2",
          "label": ".5rem"
        },
        {
          "value": "pt-4",
          "label": "1rem"
        },
        {
          "value": "pt-8",
          "label": "2rem"
        },
        {
          "value": "pt-16",
          "label": "4rem"
        },
        {
          "value": "pt-32",
          "label": "8rem"
        },
        {
          "value": "pt-64",
          "label": "16rem"
        }
      ],
      "default": "pt-0"
    },
    {
      "type": "select",
      "id": "content_bottom_padding_mobile",
      "label": "Content Bottom Padding",
      "options": [
        {
          "value": "pb-0",
          "label": "None"
        },
        {
          "value": "pb-1",
          "label": ".25rem"
        },
        {
          "value": "pb-2",
          "label": ".5rem"
        },
        {
          "value": "pb-4",
          "label": "1rem"
        },
        {
          "value": "pb-8",
          "label": "2rem"
        },
        {
          "value": "pb-16",
          "label": "4rem"
        },
        {
          "value": "pb-32",
          "label": "8rem"
        },
        {
          "value": "pb-64",
          "label": "16rem"
        }
      ],
      "default": "pb-0"
    },
    {
      "type": "select",
      "id": "content_horizontal_padding_mobile",
      "label": "Content Horizontal Padding",
      "options": [
        {
          "value": "px-0",
          "label": "None"
        },
        {
          "value": "px-1",
          "label": ".25rem"
        },
        {
          "value": "px-2",
          "label": ".5rem"
        },
        {
          "value": "px-4",
          "label": "1rem"
        },
        {
          "value": "px-8",
          "label": "2rem"
        },
        {
          "value": "px-16",
          "label": "4rem"
        },
        {
          "value": "px-32",
          "label": "8rem"
        },
        {
          "value": "px-64",
          "label": "16rem"
        }
      ],
      "default": "px-0"
    },
    {
      "type": "header",
      "content": "Pretitle Typeface Options"
    },
    {
      "type": "liquid",
      "id": "pretitle_1",
      "label": "Pretitle 1 (Liquid)"
    },
    {
      "type": "text",
      "id": "pretitle_2",
      "label": "Pretitle 2 (Liquid)"
    },
    {
      "type": "select",
      "id": "pretitle_type",
      "label": "Typeface",
      "options": [
        {
          "value": "Monument-Regular",
          "label": "Monument Grotesk Regular"
        },
        {
          "value": "MonumentGrotesk-Bold",
          "label": "Monument Grotesk Bold"
        },
        {
          "value": "ABC-Monument",
          "label": "ABC Monument"
        },
        {
          "value": "font-pop",
          "label": "ABC Monument Grotesk Black"
        },
        {
          "value": "font-highlight",
          "label": "ABC Monument Grotesk Light"
        },
        {
          "value": "Libre-Caslon",
          "label": "Libre Caslon"
        }
      ],
      "default": "Monument-Regular"
    },
    {
      "type": "number",
      "id": "pretitle_size",
      "label": "Pretitle Font Size (Desktop)",
      "default": 60
    },
    {
      "type": "number",
      "id": "pretitle_size_mobile",
      "label": "Pretitle Font Size (Mobile)",
      "default": 50
    },
    {
      "type": "text",
      "id": "pretitle_leading",
      "label": "Pretitle Line Height (Desktop)",
      "default": "1.38"
    },
    {
      "type": "text",
      "id": "pretitle_leading_mobile",
      "label": "Pretitle Line Height (Mobile)",
      "default": "1.38"
    },
    {
      "type": "text",
      "id": "pretitle_tracking",
      "label": "Pretitle Tracking",
      "default": "0.1",
      "info": "Em value"
    },
    {
      "type": "color",
      "id": "pretitle_color",
      "label": "Pretitle color",
      "default": "#000000"
    },
    {
      "type": "text",
      "id": "pretitle_font_override",
      "label": "Pretitle font family overide"
    },
    {
      "type": "header",
      "content": "Title Typeface Options"
    },
    {
      "type": "text",
      "id": "title_1",
      "label": "Title"
    },
    {
      "type": "text",
      "id": "title_2",
      "label": "Title"
    },
    {
      "type": "select",
      "id": "title_type",
      "label": "Typeface",
      "options": [
        {
          "value": "Monument-Regular",
          "label": "Monument Grotesk Regular"
        },
        {
          "value": "MonumentGrotesk-Bold",
          "label": "Monument Grotesk Bold"
        },
        {
          "value": "ABC-Monument",
          "label": "ABC Monument"
        },
        {
          "value": "font-pop",
          "label": "ABC Monument Grotesk Black"
        },
        {
          "value": "font-highlight",
          "label": "ABC Monument Grotesk Light"
        },
        {
          "value": "Libre-Caslon",
          "label": "Libre Caslon"
        }
      ],
      "default": "Monument-Regular"
    },
    {
      "type": "number",
      "id": "title_size",
      "label": "Title Font Size (Desktop)",
      "default": 60
    },
    {
      "type": "number",
      "id": "title_size_mobile",
      "label": "Title Font Size (Mobile)",
      "default": 50
    },
    {
      "type": "text",
      "id": "title_leading",
      "label": "Title Line Height (Desktop)",
      "default": "1.38"
    },
    {
      "type": "text",
      "id": "title_leading_mobile",
      "label": "Title Line Height (Mobile)",
      "default": "1.38"
    },
    {
      "type": "text",
      "id": "title_tracking",
      "label": "Title Tracking (Mobile)",
      "default": "0.1",
      "info": "Em value"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#000000"
    },
    {
      "type": "text",
      "id": "title_font_override",
      "label": "Title font family overide"
    },
    {
      "type": "checkbox",
      "id": "show_animation",
      "label": "Show Animation"
    }
  ],
  "presets": [
    {
      "name": "Page Header",
      "category": "Text",
      "settings": {
      },
      "blocks": [
      ]
    }
  ]
}
{% endschema %}

<div id="section-{{section.id}}" class="page-header {{ section.settings.content_top_padding }} {{ section.settings.content_top_padding_mobile }} {{ section.settings.content_bottom_padding }} {{ section.settings.content_bottom_padding_mobile }}"
  neptune-surface="{
    'windowEdge': 'bottom',
    'elementEdge': 'top',
    'targets': [
      {
        'selector':'_self',
        'engage:action': {
          'classes': {
            'add': ['active']
          }
        }
      }
    ]
  }"
  >
  <div class="w-full border-t border-b border-black">
    <div class="{{ section.settings.container }} {{ section.settings.content_horizontal_padding }}  {{ section.settings.content_horizontal_padding_mobile }} mx-auto">
      <div class="flex justify-between items-center">
        {% if section.settings.pretitle_1 %}
          <h4 class="page-header__pretitle my-0 py-3 {{ section.settings.pretitle_type }}">{{ section.settings.pretitle_1 }}</h4>
        {% endif %}
        {% if section.settings.pretitle_2 %}
          <h4 class="page-header__pretitle my-0 py-3 {{ section.settings.pretitle_type }}">{{ section.settings.pretitle_2 }}</h4>
        {% endif %}
      </div>
    </div>
  </div>

  <div class="page-header__title-wrap relative {{ section.settings.container }}  {{ section.settings.content_horizontal_padding }} {{ section.settings.content_horizontal_padding_mobile }} px-auto">
    {% if section.settings.title_1 or section.settings.title_2 %}
      <h1 class="page-header__title relative flex items-center {% unless section.settings.show_animation %}justify-between{% endunless %} {{ section.settings.title_type }}">
        {% if section.settings.title_1 %}
          <span class="page-header__title-1">{{ section.settings.title_1 }}</span>
        {% endif %}
        <span class="animated-spread {% if section.settings.show_animation %}animate animate-slowest delay-1{% endif %}">
        {% if section.settings.title_2 %}
          <span class="page-header__title-2">{{ section.settings.title_2 }}</span>
        {% endif %}
        </span>
      </h1>
    {% endif %}
  </div>
</div>

<script>
</script>

{% style %}
  #section-{{section.id}} .page-header__pretitle {
    font-size: {{ section.settings.pretitle_size }}px;
    line-height: {{ section.settings.pretitle_leading }};
    letter-spacing: {{ section.settings.pretitle_tracking }}em;
    color: {{ section.settings.pretitle_color }};
    {% if section.settings.pretitle_font_override != blank %}font-family: {{ section.settings.pretitle_font_override }};{% endif %}
  }
  #section-{{section.id}} .page-header__title {
    font-size: {{ section.settings.title_size }}px;
    line-height: {{ section.settings.title_leading }};
    letter-spacing: {{ section.settings.title_tracking }}em;
    color: {{ section.settings.title_color }};
    {% if section.settings.title_font_override != blank %}font-family: {{ section.settings.title_font_override }};{% endif %}
  }
  @media (max-width: 767px) {
    #section-{{ section.id }} .page-header__pretitle {
      font-size: {{ section.settings.pretitle_size_mobile }}px;
      line-height: {{ section.settings.pretitle_leading_mobile }};
      letter-spacing: {{ section.settings.pretitle_tracking_mobile }}em;
      {% if section.settings.pretitle_font_override != blank %}font-family: {{ section.settings.pretitle_font_override_mobile }};{% endif %}
    }
    #section-{{ section.id }} .page-header__title {
      font-size: {{ section.settings.title_size_mobile }}px;
      line-height: {{ section.settings.title_leading_mobile }};
      letter-spacing: {{ section.settings.title_tracking_mobile }}em;
      {% if section.settings.title_font_override != blank %}font-family: {{ section.settings.title_font_override_mobile }};{% endif %}
    }
  }
{% endstyle %}
