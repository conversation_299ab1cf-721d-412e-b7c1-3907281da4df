<script src="{{ 'searchspring.js' | asset_url }}" defer></script>
{%- render 'ss-localized-prices' -%}
<section class="collection" style="background: #f7f7f7;">

  <div skeleton class="collection-skeleton collection__product mx-0 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 bg-nearest-white p-4 gap-4" >
    {% for i in (1..8)  %}
      {% render 'product-item-skeleton' %}
    {% endfor %}
  </div>

  <div id="shopify-section-collection" class="shopify-section search--results">
    <div id="searchspring-content">
      <ul class="legacy-products hide">
        {% for product in collection.products %}
        <li><a href="{{ product.url }}">{{ product.title }}</a></li>
        {% endfor %}
      </ul>          
    </div>
  </div>  
</section>
<script>
  window.inventory = {
  message: {{settings.collection_lowstock_message|json}},
  threshold: {{settings.variant_lowstock_threshold}},
  products: {
    {%- for product in collection.products -%}
    {% if product.variants.size > 2 %}
    {{product.handle|json}}:{
      {%- for variant in product.variants -%}
      {{variant.option2|json}}:{{variant.inventory_quantity}}
      {%- unless forloop.last %},{%- endunless -%}
      {%- endfor -%}  
    }
    {%- unless forloop.last %},{%- endunless -%}
    {% endif %}
    {%- endfor -%}  
  }
};
</script>
