{% comment %} 


This is generated by webpack so don't edit this unless you want to be sad when your work gets overwritten in production.
You can edit this file in ../../components/** 


{% endcomment %}






<script>
window.productBadges = [];

{%- assign now = "now" | date: "%Y-%m-%d %H:%M" -%}
{%- for block in section.blocks -%}

  {%- assign include_badge = true -%}

  {%- if block.settings.end != blank and block.settings.end < now -%}
    {%- assign include_badge = false -%}
  {%- endif -%}

  {%- if block.settings.start != blank and block.settings.start > now -%}
    {%- assign include_badge = false -%}
  {%- endif -%}

  {%- if include_badge -%}
    productBadges.push({{ block.settings|json }});
  {%- endif -%}

{%- endfor -%}
</script>

{% schema %}
  {
    "name": "Product Badges",
    "settings": [],
    "blocks": [
      {
        "type":"tier",
        "name":"Badge",
        "settings": [
          {
            "label": "Product Tag",
            "id": "tag",
            "type": "text"
          },
          {
            "label": "Collection Grid Badge Text",
            "id": "collection_badge",
            "type": "text"
          },
          {
            "label": "Product Detail Badge Text",
            "id": "product_badge",
            "type": "text"
          },
          {
            "type": "color",
            "id": "badge_text_color",
            "label": "Badge text color"
          },
          {
            "type": "color",
            "id": "badge_bg_color",
            "label": "Badge background color"
          }
        ]
      }
    ]
  }
{% endschema %}
