<div class="main-blog bg-near-white flex flex-col">
  {%- paginate blog.articles by 12 -%}

  <div class="main-blog {{ section.settings.content_vertical_padding }} {{ section.settings.content_horizontal_padding }} {{ section.settings.content_vertical_padding_mobile }} {{ section.settings.content_horizontal_padding_mobile }} {% if section.settings.show_desktop == true and section.settings.show_mobile == true %} block {% elsif section.settings.show_desktop == true and section.settings.show_mobile == false %} hidden lg:block {% elsif section.settings.show_desktop == false and section.settings.show_mobile == false %} hidden {% elsif section.settings.show_desktop == false and section.settings.show_mobile == true %} block lg:hidden {% endif %}">
    <h1 class="title--primary sr-only">{{ blog.title | escape }}</h1>

    <div class="blog-articles container flex flex-row flex-wrap">
      {%- for article in blog.articles -%}
        {% unless article.tags contains 'featured' %}
          <div 
            class="blog-articles__article article"
            neptune-surface="{
              'direction':'up',
              'windowEdge': 'bottom',
              'elementEdge': 'top',
              'targets': [
                {
                  'selector':'_self .article-card',
                  'engage:action': {
                    'classes': {
                      'remove': ['active']
                    }
                  }
                }
              ]
            }"
            >
            {%- render 'article-card', article: article, show_image: section.settings.show_image, show_date: section.settings.show_date, show_excerpt: section.settings.show_excerpt, show_category: section.settings.show_category -%}
          </div>
        {% endunless %}
      {%- endfor -%}
    </div>

    {% if paginate.pages > 1 %}
      <input type="hidden" id="current-page" value="{{ paginate.current_page }}">
      <input type="hidden" id="total-pages" value="{{ paginate.pages }}">
    {% endif %}
  </div>
  {%- endpaginate -%}
</div>

{% comment %} Infinite scroll logic {% endcomment %}
<script>
  (function() {
    let currentPage = parseInt(document.querySelector('#current-page').value);
    const totalPages = parseInt(document.querySelector('#total-pages').value);
    const blogPostsContainer = document.querySelector('.blog-articles');
    let loading = false;

    // Safari doesn't update the srcset attribute of images when fetching new content,
    // causing images to be blurry without reloading the images.
    
    function forceSrcsetUpdateForSafari(posts) {
      if (!/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) return; // Check if the browser is Safari
    
      Array.from(posts.querySelectorAll('img')).forEach(img => {
        img.addEventListener('load', () => {
          img.outerHTML = img.outerHTML;
        }, { once: true });
      });
    }
  
    function fetchNextPage() {
      if (loading) return;
      loading = true;
  
      currentPage += 1;
      const url = window.location.href.split('?')[0] + '?page=' + currentPage;
  
      fetch(url)
        .then(response => response.text())
        .then(html => {
          const parser = new DOMParser();
          const doc = parser.parseFromString(html, 'text/html');
          const newBlogPosts = doc.querySelector('.blog-articles').children;
      
          Array.from(newBlogPosts).forEach(posts => {
            blogPostsContainer.appendChild(posts);
            forceSrcsetUpdateForSafari(posts);
          });

          loading = false;
        })
        .catch(error => console.error('Error fetching next page:', error));
    }
  
    function handleScroll() {
      const scrollPosition = window.scrollY + window.innerHeight;
      const threshold = document.body.scrollHeight - 500;
  
      if (scrollPosition >= threshold && currentPage < totalPages) {
        fetchNextPage();
      }
    }  
  
    if (totalPages > 1) {
      window.addEventListener('scroll', handleScroll);
    }
  })();
</script>

{% schema %}
{
  "name": "Blog",
  "tag": "section",
  "class": "spaced-section",
  "settings": [
    {
      "type": "header",
      "content": "Display Settings"
    },
    {
      "type": "checkbox",
      "id": "show_desktop",
      "label": "Show desktop",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_mobile",
      "label": "Show mobile",
      "default": false
    },
    {
      "type": "header",
      "content": "Blog Content"
    },
    {
      "type": "checkbox",
      "id": "show_image",
      "default": true,
      "label": "Show Image"
    },
    {
      "type": "checkbox",
      "id": "show_category",
      "default": true,
      "label": "Show Category"
    },
    {
      "type": "checkbox",
      "id": "show_date",
      "default": true,
      "label": "Show Date"
    },
    {
      "type": "checkbox",
      "id": "show_excerpt",
      "default": true,
      "label": "Show Excerpt"
    },
    {
      "type": "number",
      "id": "truncate_length",
      "label": "Truncate Length",
      "default": 130,
      "info": "The number of characters to truncate the article excerpt to."
    },
    {
      "type": "header",
      "content": "Desktop Content Layout Settings"
    },
    {
      "type": "select",
      "id": "content_vertical_padding",
      "label": "Content Vertical Padding",
      "options": [
        {
          "value": "lg:py-0",
          "label": "None"
        },
        {
          "value": "lg:py-1",
          "label": ".25rem"
        },
        {
          "value": "lg:py-2",
          "label": ".5rem"
        },
        {
          "value": "lg:py-4",
          "label": "1rem"
        },
        {
          "value": "lg:py-8",
          "label": "2rem"
        },
        {
          "value": "lg:py-16",
          "label": "4rem"
        },
        {
          "value": "lg:py-32",
          "label": "8rem"
        },
        {
          "value": "lg:py-64",
          "label": "16rem"
        }
      ],
      "default": "lg:py-0"
    },
    {
      "type": "select",
      "id": "content_horizontal_padding",
      "label": "Content Horizontal Padding",
      "options": [
        {
          "value": "lg:px-0",
          "label": "None"
        },
        {
          "value": "lg:px-1",
          "label": ".25rem"
        },
        {
          "value": "lg:px-2",
          "label": ".5rem"
        },
        {
          "value": "lg:px-4",
          "label": "1rem"
        },
        {
          "value": "lg:px-8",
          "label": "2rem"
        },
        {
          "value": "lg:px-16",
          "label": "4rem"
        },
        {
          "value": "lg:px-32",
          "label": "8rem"
        },
        {
          "value": "lg:px-64",
          "label": "16rem"
        }
      ],
      "default": "lg:px-0"
    },
    {
      "type": "header",
      "content": "Mobile Content Layout Settings"
    },
    {
      "type": "select",
      "id": "content_position_mobile_",
      "label": "Content Position",
      "options": [
        {
          "value": "items-start justify-start",
          "label": "Top Left"
        },
        {
          "value": "items-center justify-start",
          "label": "Top Center"
        },
        {
          "value": "items-end justify-start",
          "label": "Top Right"
        },
        {
          "value": "items-start justify-center",
          "label": "Middle Left"
        },
        {
          "value": "items-center justify-center",
          "label": "Middle Center"
        },
        {
          "value": "items-end justify-center",
          "label": "Middle Right"
        },
        {
          "value": "items-start justify-end",
          "label": "Bottom Left"
        },
        {
          "value": "items-center justify-end",
          "label": "Bottom Center"
        },
        {
          "value": "items-end justify-end",
          "label": "Bottom Right"
        }
      ],
      "default": "items-center justify-center"
    },
    {
      "type": "select",
      "id": "content_vertical_padding_mobile",
      "label": "Content Vertical Padding",
      "options": [
        {
          "value": "py-0",
          "label": "None"
        },
        {
          "value": "py-1",
          "label": ".25rem"
        },
        {
          "value": "py-2",
          "label": ".5rem"
        },
        {
          "value": "py-4",
          "label": "1rem"
        },
        {
          "value": "py-8",
          "label": "2rem"
        },
        {
          "value": "py-16",
          "label": "4rem"
        },
        {
          "value": "py-32",
          "label": "8rem"
        },
        {
          "value": "py-64",
          "label": "16rem"
        }
      ],
      "default": "py-0"
    },
    {
      "type": "select",
      "id": "content_horizontal_padding_mobile",
      "label": "Content Horizontal Padding",
      "options": [
        {
          "value": "px-0",
          "label": "None"
        },
        {
          "value": "px-1",
          "label": ".25rem"
        },
        {
          "value": "px-2",
          "label": ".5rem"
        },
        {
          "value": "px-4",
          "label": "1rem"
        },
        {
          "value": "px-8",
          "label": "2rem"
        },
        {
          "value": "px-16",
          "label": "4rem"
        },
        {
          "value": "px-32",
          "label": "8rem"
        },
        {
          "value": "px-64",
          "label": "16rem"
        }
      ],
      "default": "px-0"
    }
  ],
  "blocks": [
    {
      "type": "title",
      "name": "Title",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_date",
          "default": false,
          "label": "Show Date"
        },
        {
          "type": "checkbox",
          "id": "show_author",
          "default": false,
          "label": "Show Author"
        }
      ]
    },
    {
      "type": "summary",
      "name": "Summary",
      "limit": 1
    },
    {
      "type": "link",
      "name": "Link",
      "limit": 1
    }
  ]
}
{% endschema %}
