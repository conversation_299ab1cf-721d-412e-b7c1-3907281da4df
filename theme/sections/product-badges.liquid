{% schema %}
{
  "name": "Product Badges",
	"settings": [],
  "blocks": [
    {
      "name": "Badge",
      "type": "badge",
      "settings": [
        {
          "label": "Title",
          "id": "title",
          "type": "text",
          "info": "Internal reference only"
        },
        {
          "label": "Tag",
          "id": "tag",
          "type": "text"
        },
        {
          "label": "Excluded collections",
          "id": "excluded_collection",
          "type": "textarea"
        },
        {
          "type": "header",
          "content": "Badge Label Settings"
        },
        {
          "label": "Grid Item Badge",
          "id": "item",
          "type": "text"
        },
         {
          "label": "Cart Item Badge",
          "id": "cart_badge",
          "type": "textarea"
        },
        {
          "type": "header",
          "content": "Badge Display Settings"
        },
        {
          "label": "Badge Color",
          "id": "color",
          "type": "color",
          "default": "#000"
        },
        {
          "label": "Badge Background Color",
          "id": "bg_color",
          "type": "color"
        }
      ]
		},
		{
      "name": "Image Badge",
      "type": "image-badge",
      "settings": [
        {
          "label": "Title",
          "id": "title",
          "type": "text",
          "info": "Internal reference only"
        },
        {
          "label": "Tag",
          "id": "tag",
          "type": "text"
        },
        {
          "label": "Excluded collections",
          "id": "excluded_collection",
          "type": "textarea"
        },
        {
          "type": "header",
          "content": "Badge Display Settings"
        },
				{
          "label": "Image File",
          "id": "image",
          "type": "text"
        },
        {
          "type": "select",
          "id": "position",
          "label": "Position",
          "options": [
            {
							"value": "top-left",
              "label": "Top Left"
            },
            {
              "value": "top-right",
              "label": "Top Right"
            },
            {
              "value": "bottom-left",
              "label": "Bottom Left"
            },
            {
              "value": "bottom-right",
              "label": "Bottom Right"
            }
          ],
          "default": "top-right"
        },
        {
          "type": "number",
          "id": "image_width",
          "label": "Image Width (Desktop)",
          "info": "Value in pixels. If the value is 0, the default behavior will be applied.",
          "default": 0
        },
        {
          "type": "number",
          "id": "image_width_mobile",
          "label": "Image Width (Mobile)",
          "info": "Value in pixels. If the value is 0, the default behavior will be applied.",
          "default": 0
        }
      ]
    }
  ]
}

{% endschema %}

<script>
  window.productBadges = [];

  {% liquid 

    assign now = "now" | date: "%Y-%m-%d %H:%M"

    assign badges = section.blocks
    
    for block in badges
      assign include_badge = true

      if block.settings.end != blank and block.settings.end < now
        assign include_badge = false
      endif

      if block.settings.start != blank and block.settings.start > now
        assign include_badge = false
      endif

      if include_badge
        echo block.settings | json | prepend: 'productBadges.push(' | append: ');'
      endif
    endfor
  %}

  productBadges = productBadges.map(b => {b.image ? b.type='image' : b.type='text'; return b;}).concat({{ section.settings.map | newline_to_br | split: '<br />'| json }}.map(l => {return {type:'icon',tag:l.split('>>>')[0].trim(),icon:l.split('>>>')[1].trim()} }));

  productBadges = productBadges.concat({{ section.settings.map_graphic | newline_to_br | split: '<br />'| json }}.map(l => {return {type:'graphic',tag:l.split('>>>')[0].trim(),icon:l.split('>>>')[1].trim()} }));

  productBadges = productBadges.concat({{ section.settings.map_options | newline_to_br | split: '<br />'| json }}.map(l => {return {type:'option',tag:l.split('>>>')[0].trim(),label:l.split('>>>')[1].trim()} }));

  //console.log('productBadges', productBadges);
  sessionStorage.setItem('productBadges', JSON.stringify(productBadges));
</script>
