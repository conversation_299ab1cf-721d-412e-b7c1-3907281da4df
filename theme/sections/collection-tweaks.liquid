{% schema %}
  {
  "name": "Collection Tweaks",
  "settings": [
    {
      "type": "header",
      "content": "Display Settings"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background Color"
    },
    {
      "type": "text",
      "id": "filter_trigger_text",
      "label": "Filter Trigger Text",
      "default": "Filter"
    },
    {
      "type": "select",
      "id": "product_items_per_row",
      "label": "Product Items per Row (Desktop)",
      "options": [
        {
          "value": "50%",
          "label": "2"
        },
        {
          "value": "25%",
          "label": "4"
        }
      ],
      "default": "25%"
    },
    {
      "type": "select",
      "id": "product_items_per_row_mobile",
      "label": "Product Items per Row (Mobile)",
      "options": [
        {
          "value": "100%",
          "label": "1"
        },
        {
          "value": "50%",
          "label": "2"
        }
      ],
      "default": "50%"
    },
    {
      "type": "checkbox",
      "id": "show_title",
      "label": "Show Title",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_tools",
      "label": "Show Tools & Filters",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_badges",
      "label": "Show Badges",
      "default": true
    },
    {
      "type": "header",
      "content": "Product Item Settings"
    },
    {
      "type": "text",
      "id": "image_override_alt",
      "label": "Image Override",
      "info": "Matches text found in the alt text of the images to display",
      "default":"Front detail view"
    },
    {
      "type": "select",
      "id": "item_height",
      "label": "Grid Item Aspect Ratio",
      "options": [
        {
          "value": "",
          "label": "auto"
        },
        {
          "value": "9 | 11",
          "label": "9x11"
        },
        {
          "value": "6 | 8",
          "label": "6x8"
        },
        {
          "value": "1 | 1",
          "label": "1x1"
        }
      ],
      "default": ""
    },
    {
      "type": "select",
      "id": "image_display",
      "label": "Image Display",
      "options": [
        {
          "value": "bg-contain",
          "label": "Contain"
        },
        {
          "value": "bg-cover",
          "label": "Cover"
        }
      ],
      "default": "bg-contain"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color"
    },
    {
      "type": "header",
      "content": "Product Item Button Settings"
    },
    {
      "type": "color",
      "id": "btn_color",
      "label": "Button bg color"
    },
    {
      "type": "color",
      "id": "btn_text_color",
      "label": "Button text color"
    },
    {
      "type": "color",
      "id": "btn_border_color",
      "label": "Button border color"
    }
  ],
  "presets": [
    {
      "name": "Collection Tweaks",
      "category": "Advanced",
      "settings": {},
      "blocks": []
    }
  ]
}
{% endschema %}

{% if section.settings.image_override_alt != blank %}
<script>
  window.collectionTweaks = {
    image_overrides: {
      {%- paginate collection.products by 1000 %}
        {%- for product in collection.products %}
          {%- for image in product.images %}
            {%- if image.alt contains section.settings.image_override_alt %}
              "{{- product.handle }}":"{{ image.src | img_url:'original' }}",
              {%- break %}
            {%- endif %}
          {%- endfor %}
        {%- endfor %}
        end:true
      {%- endpaginate %}
    }
  }
  window.addEventListener('searchspring.motherdenim.domReady', function(){
    Object.entries(collectionTweaks.image_overrides).forEach(io=>{
        try{
            _n.qs(`.product-item__link[href*="${io[0]}"] .product-item__image img`).src=io[1]
        } catch(err) {}
    })
  })
</script>

{% endif %}

{% style %}
  {% if section.settings.background_color != blank %}
    .collection {
      background-color: {{ section.settings.background_color }};
    }
  {% endif %}

  {% if section.settings.show_tools == blank %}
    .collection__tools--filters, .collection__tools {
      display: none;
    }
  {% endif %}

  {% if section.settings.show_badges == blank %}
    .product-item__badges {
      display: none;
    }
  {% endif %}

  {% unless section.settings.product_items_per_row == '25%' %}
    @media (min-width: 1024px) {
      .collection-grid .product-item__wrapper {
        width: {{ section.settings.product_items_per_row }};
      }
      .collection-skeleton {
        grid-template-columns: repeat(2,minmax(0,1fr));
      }
    }
  {% endunless %}

  {% unless section.settings.product_items_per_row_mobile == '50%' %}
    @media (max-width: 1023px) {
      .collection-grid .product-item__wrapper {
        width: {{ section.settings.product_items_per_row_mobile }};
      }
    }  
  {% endunless %}

  {% if section.settings.btn_color != blank or section.settings.btn_text_color != blank or section.settings.btn_border_color != blank %}
    .quick-add__button {
      {% if section.settings.btn_color != blank %}background-color: {{ section.settings.btn_color}};{% endif %}
      {% if section.settings.btn_text_color != blank %}color: {{ section.settings.btn_text_color}};{% endif %}
      {% if section.settings.btn_border_color != blank %}border-color: {{ section.settings.btn_border_color}};{% endif %}
    }
  {% endif %}

  {% if section.settings.text_color != blank %}
    .product-item__meta {
      color: {{ section.settings.text_color }};
    }
  {% endif %}

  {% if section.settings.image_display == 'bg-cover' %}
    .product-item__image--main {
      object-fit: cover;
    }
    .product-item__image--hover {
      background-color: var(--color-light) !important;
    }
  {% endif %}
  {% if section.settings.item_height != '' %}
    .product-item__image--hover, .product-item__image {
      --tw-aspect-w: {{ section.settings.item_height | split: '|' | first }};
      --tw-aspect-h: {{ section.settings.item_height | split: '|' | last }};
    }
  {% endif %}

  .ss-pagination {
    display: none;
  }
{% endstyle %}
  
</style>