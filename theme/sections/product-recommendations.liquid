{% schema %}
{
  "name": "Product Recommendations",
  "class":"hidden",
  "settings": [
    {
      "type": "header",
      "content": "Display Settings"
    },
    {
      "type": "checkbox",
      "id": "show_desktop",
      "label": "Show desktop",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_mobile",
      "label": "Show mobile",
      "default": true
    },
    {
      "type": "select",
      "id": "container",
      "label": "Container",
      "options": [
        {
          "value": "container",
          "label": "Container"
        },
        {
          "value": "container container--product",
          "label": "Product Container"
        },
        {
          "value": "w-full",
          "label": "Full width"
        }
      ],
      "default": "container container--product"
    },
    {
      "type": "select",
      "id": "layout",
      "label": "Layout (Desktop)",
      "options": [
        {
          "value": "lg:flex-col",
          "label": "Content on top"
        },
        {
          "value": "lg:flex-row",
          "label": "Content on side"
        }
      ],
      "default": "lg:flex-col"
    },
    {
      "type": "select",
      "id": "display",
      "label": "Display products as",
      "options": [
        {
          "value": "slider",
          "label": "Slider"
        },
        {
          "value": "grid",
          "label": "Grid"
        }
      ],
      "default": "slider"
    },
    {
      "type": "color",
      "label": "Background Color",
      "id": "background_color"
    },
    {
      "type": "header",
      "content": "Product Settings"
    },
    {
      "type": "paragraph",
      "content": "To select data source for products, add a block to this Section"
    },
    {
      "type": "number",
      "id": "limit",
      "label": "Max Number of Products",
      "default": 8
    },
    {
      "type": "checkbox",
      "id": "hide_out_of_stock",
      "label": "Hide Out of Stock Products",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "siblings",
      "label": "Include Sibling Selector when Available",
      "default": true
    },
    {
      "type": "select",
      "id": "action",
      "label": "Product Action",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "quick-add-toggle",
          "label": "Quick Add Toggle"
        },
        {
          "value": "variant-selection",
          "label": "Exposed Variant Selection"
        }
      ],
      "default": ""
    },
    {
      "type": "checkbox",
      "id": "show_bis_form",
      "label": "Show Back in stock form",
      "default": true
    },
    {
      "type": "number",
      "id": "slides_per_view",
      "label": "Items per row/view (Desktop)",
      "default": 4
    },
    {
      "type": "number",
      "id": "slides_per_view_mobile",
      "label": "Items per row/view (Mobile)",
      "default": 2
    },
    {
      "type": "header",
      "content": "Slideshow Settings"
    },
    {
      "type": "number",
      "id": "spacebetween",
      "label": "Space between slides (Desktop)",
      "default": 16
    },
    {
      "type": "number",
      "id": "spacebetween_mobile",
      "label": "Space between slides (Mobile)",
      "default": 16
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Autoplay"
    },
    {
      "type": "checkbox",
      "id": "loop",
      "label": "Loop"
    },
    {
      "type": "range",
      "id": "autoplay_slide_duration",
      "label": "Autoplay Slide Duration",
      "min": 3,
      "max": 8,
      "step": 1,
      "default": 8
    },
    {
      "type": "checkbox",
      "id": "arrows",
      "label": "Show arrows (Desktop)",
      "default":true
    },
    {
      "type": "checkbox",
      "id": "arrows_mobile",
      "label": "Show arrows (Mobile)",
      "default":true
    },
    {
      "type": "checkbox",
      "id": "show_pagination",
      "label": "Show pagination",
      "default": false
    },
    {
      "type": "header",
      "content": "Section Spacing Settings (Desktop)"
    },
    {
      "type": "select",
      "id": "section_top_margin",
      "label": "Margin top",
      "options": [
        {
          "value": "lg:mt-0",
          "label": "None"
        },
        {
          "value": "lg:mt-1",
          "label": ".25rem"
        },
        {
          "value": "lg:mt-2",
          "label": ".5rem"
        },
        {
          "value": "lg:mt-4",
          "label": "1rem"
        },
        {
          "value": "lg:mt-8",
          "label": "2rem"
        },
        {
          "value": "lg:mt-16",
          "label": "4rem"
        },
        {
          "value": "lg:mt-32",
          "label": "8rem"
        },
        {
          "value": "lg:mt-64",
          "label": "16rem"
        }
      ],
      "default": "lg:mt-16"
    },
    {
      "type": "select",
      "id": "section_bottom_margin",
      "label": "Margin bottom",
      "options": [
        {
          "value": "lg:mb-0",
          "label": "None"
        },
        {
          "value": "lg:mb-1",
          "label": ".25rem"
        },
        {
          "value": "lg:mb-2",
          "label": ".5rem"
        },
        {
          "value": "lg:mb-4",
          "label": "1rem"
        },
        {
          "value": "lg:mb-8",
          "label": "2rem"
        },
        {
          "value": "lg:mb-16",
          "label": "4rem"
        },
        {
          "value": "lg:mb-32",
          "label": "8rem"
        },
        {
          "value": "lg:mb-64",
          "label": "16rem"
        }
      ],
      "default": "lg:mb-16"
    },
    {
      "type": "select",
      "id": "section_left_padding",
      "label": "Left Padding",
      "options": [
        {
          "value": "lg:pl-0",
          "label": "None"
        },
        {
          "value": "lg:pl-1",
          "label": ".25rem"
        },
        {
          "value": "lg:pl-2",
          "label": ".5rem"
        },
        {
          "value": "lg:pl-4",
          "label": "1rem"
        },
        {
          "value": "lg:pl-8",
          "label": "2rem"
        },
        {
          "value": "lg:pl-16",
          "label": "4rem"
        },
        {
          "value": "lg:pl-32",
          "label": "8rem"
        },
        {
          "value": "lg:pl-64",
          "label": "16rem"
        }
      ],
      "default": "lg:pl-0"
    },
    {
      "type": "select",
      "id": "section_right_padding",
      "label": "Right Padding",
      "options": [
        {
          "value": "lg:pr-0",
          "label": "None"
        },
        {
          "value": "lg:pr-1",
          "label": ".25rem"
        },
        {
          "value": "lg:pr-2",
          "label": ".5rem"
        },
        {
          "value": "lg:pr-4",
          "label": "1rem"
        },
        {
          "value": "lg:pr-8",
          "label": "2rem"
        },
        {
          "value": "lg:pr-16",
          "label": "4rem"
        },
        {
          "value": "lg:pr-32",
          "label": "8rem"
        },
        {
          "value": "lg:pr-64",
          "label": "16rem"
        }
      ],
      "default": "lg:pr-0"
    },
    {
      "type": "select",
      "id": "section_vertical_padding",
      "label": "Vertical Padding",
      "options": [
        {
          "value": "lg:py-0",
          "label": "None"
        },
        {
          "value": "lg:py-1",
          "label": ".25rem"
        },
        {
          "value": "lg:py-2",
          "label": ".5rem"
        },
        {
          "value": "lg:py-4",
          "label": "1rem"
        },
        {
          "value": "lg:py-8",
          "label": "2rem"
        },
        {
          "value": "lg:py-16",
          "label": "4rem"
        },
        {
          "value": "lg:py-32",
          "label": "8rem"
        },
        {
          "value": "lg:py-64",
          "label": "16rem"
        }
      ],
      "default": "lg:py-0"
    },
    {
      "type": "header",
      "content": "Section Spacing Settings (Mobile)"
    },
    {
      "type": "select",
      "id": "section_top_margin_mobile",
      "label": "Margin top",
      "options": [
        {
          "value": "mt-0",
          "label": "None"
        },
        {
          "value": "mt-1",
          "label": ".25rem"
        },
        {
          "value": "mt-2",
          "label": ".5rem"
        },
        {
          "value": "mt-4",
          "label": "1rem"
        },
        {
          "value": "mt-8",
          "label": "2rem"
        },
        {
          "value": "mt-16",
          "label": "4rem"
        },
        {
          "value": "mt-32",
          "label": "8rem"
        },
        {
          "value": "mt-64",
          "label": "16rem"
        }
      ],
      "default": "mt-16"
    },
    {
      "type": "select",
      "id": "section_bottom_margin_mobile",
      "label": "Margin bottom",
      "options": [
        {
          "value": "mb-0",
          "label": "None"
        },
        {
          "value": "mb-1",
          "label": ".25rem"
        },
        {
          "value": "mb-2",
          "label": ".5rem"
        },
        {
          "value": "mb-4",
          "label": "1rem"
        },
        {
          "value": "mb-8",
          "label": "2rem"
        },
        {
          "value": "mb-16",
          "label": "4rem"
        },
        {
          "value": "mb-32",
          "label": "8rem"
        },
        {
          "value": "mb-64",
          "label": "16rem"
        }
      ],
      "default": "mb-16"
    },
    {
      "type": "select",
      "id": "section_left_padding_mobile",
      "label": "Left Padding",
      "options": [
        {
          "value": "pl-0",
          "label": "None"
        },
        {
          "value": "pl-1",
          "label": ".25rem"
        },
        {
          "value": "pl-2",
          "label": ".5rem"
        },
        {
          "value": "pl-4",
          "label": "1rem"
        },
        {
          "value": "pl-8",
          "label": "2rem"
        },
        {
          "value": "pl-16",
          "label": "4rem"
        },
        {
          "value": "pl-32",
          "label": "8rem"
        },
        {
          "value": "pl-64",
          "label": "16rem"
        }
      ],
      "default": "pl-0"
    },
    {
      "type": "select",
      "id": "section_right_padding_mobile",
      "label": "Right Padding",
      "options": [
        {
          "value": "pr-0",
          "label": "None"
        },
        {
          "value": "pr-1",
          "label": ".25rem"
        },
        {
          "value": "pr-2",
          "label": ".5rem"
        },
        {
          "value": "pr-4",
          "label": "1rem"
        },
        {
          "value": "pr-8",
          "label": "2rem"
        },
        {
          "value": "pr-16",
          "label": "4rem"
        },
        {
          "value": "pr-32",
          "label": "8rem"
        },
        {
          "value": "pr-64",
          "label": "16rem"
        }
      ],
      "default": "pr-0"
    },
    {
      "type": "select",
      "id": "section_vertical_padding_mobile",
      "label": "Vertical Padding",
      "options": [
        {
          "value": "py-0",
          "label": "None"
        },
        {
          "value": "py-1",
          "label": ".25rem"
        },
        {
          "value": "py-2",
          "label": ".5rem"
        },
        {
          "value": "py-4",
          "label": "1rem"
        },
        {
          "value": "py-8",
          "label": "2rem"
        },
        {
          "value": "py-16",
          "label": "4rem"
        },
        {
          "value": "py-32",
          "label": "8rem"
        },
        {
          "value": "py-64",
          "label": "16rem"
        }
      ],
      "default": "py-0"
    },
    {
      "type": "header",
      "content": "Section Text Settings"
    },
    {
      "type": "select",
      "id": "text_align",
      "label": "Text Alignment (Desktop)",
      "options": [
        {
          "value": "lg:text-left",
          "label": "Left"
        },
        {
          "value": "lg:text-center",
          "label": "Center"
        },
        {
          "value": "lg:text-right",
          "label": "Right"
        }
      ],
      "default": "lg:text-left"
    },
    {
      "type": "select",
      "id": "text_align_mobile",
      "label": "Text Alignment (Mobile)",
      "options": [
        {
          "value": "text-left",
          "label": "Left"
        },
        {
          "value": "text-center",
          "label": "Center"
        },
        {
          "value": "text-right",
          "label": "Right"
        }
      ],
      "default": "text-left"
    },
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title"
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "Content"
    },
    {
      "type": "header",
      "content": "Advanced Settings"
    },
    {
      "type": "select",
      "id": "title_element",
      "label": "Heading Type",
      "options": [
        {
          "value": "h1",
          "label": "H1"
        },
        {
          "value": "h2",
          "label": "H2"
        },
        {
          "value": "h3",
          "label": "H3"
        },
        {
          "value": "h4",
          "label": "H4"
        },
        {
          "value": "h5",
          "label": "H5"
        },
        {
          "value": "p",
          "label": "p"
        }
      ],
      "default": "h2"
    },
    {
      "type": "text",
      "id": "transport",
      "label": "Transport Location",
      "info": "Query selector for transport location, using append method"
    },
    {
      "type":"liquid",
      "id": "inclusion",
      "label": "Inclusion Logic",
      "default": "1"
    },
    {
      "type":"liquid",
      "id": "exclusion",
      "label": "Exclusion Logic"
    },
    {
      "type":"text",
      "id": "custom_class",
      "label": "Custom classes"
    }
  ],
  "max_blocks": 1,
  "blocks": [
    {
      "name": "Specific Products",
      "type": "products",
      "limit": 4,
      "settings": [
        {
          "label": "Products",
          "id": "products",
          "type": "product_list"
        },
        {
          "label": "Siblings Swatches",
          "id": "siblings",
          "type": "checkbox",
          "default": true
        },
        {
          "label": "Details LInk",
          "id": "details_link",
          "type": "checkbox",
          "default": true
        }
      ]
    },
    {
      "name": "Shopify Collection",
      "type": "shopify-collection",
      "limit": 4,
      "settings": [
        {
          "type": "header",
          "content": "Collection settings"
        },
        {
          "type": "paragraph",
          "content": "Selecting a collection will display the first (section-specifid limit) products. If this section is loaded on a product template, the current product will be omitted from the results."
        },
        {
          "type": "paragraph",
          "content": "Collection Logic allows for a liquid-programmed dynamic collection (same Wash, same Fit, etc). If there is no logic populated or if there are no products returned when the logic is populated, the products from the standard Collection field will display instead."
        },
        {
          "label": "Collection Logic",
          "id": "liquid_collection",
          "type": "liquid"
        },
        {
          "label": "Collection",
          "id": "collection",
          "type": "collection"
        }
      ]
    },
    {
      "name": "Shopify Recommendations",
      "type": "shopify-recommendations",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Collection settings"
        },
        {
          "label": "Product ID",
          "id": "product_id",
          "type": "liquid",
          "default": "{{ product.id }}"
        }
      ]
    },
    {
      "name": "Recently Viewed",
      "type": "recently-viewed",
      "limit": 1,
      "settings": []
    },
    {
      "name": "SearchSpring Recs",
      "type": "searchspring-recs",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Collection settings" 
        },
        {
          "label": "Product ID",
          "id": "product_id",
          "type": "liquid",
          "default": "{{ product.id }}"
        },
        {
          "label": "Profile handle",
          "id": "tags",
          "type": "liquid",
          "default": "cross-sell",
          "info": "Tags like, \"cross-sell\", \"also-bought\", or \"most-popular\""
        },
        {
          "label": "Collection ID(s)",
          "id": "collection",
          "type": "liquid",
          "info": "Add a single collection id or multiple separated by commas"
        },
        {
          "label": "Include Customer?",
          "id": "customer",
          "type": "checkbox"
        },
        {
          "label": "Include Cart?",
          "id": "cart",
          "type": "checkbox"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Product Recommendations",
      "category": "Product",
      "settings": {},
      "blocks": []
    }
  ]
}
{% endschema %}

{% if section.settings.inclusion != blank and section.settings.exclusion == blank %}

{% liquid 

  assign topic = section.id | prepend: 'ProductRecommendations-'

  assign data = section.blocks[0]

  unless routes.root_url == '/'
    assign routes_url = routes.root_url
  else 
    assign routes_url = ''
  endunless

  case data.type

    when 'shopify-recommendations'
      assign source = 'url:' | append: routes_url | append: '/recommendations/products.json?product_id=' | append: data.settings.product_id | append: '&limit=' | append: section.settings.limit
      assign map = "{products:{from:products,each:{title:title,handle:handle,price:price,compare_at_price:compare_at_price,featured_image:featured_image,hover_image:'images.1',id:id,variants:variants}}}"

    when 'shopify-collection'
      assign recs_collection = data.settings.liquid_collection | default: data.settings.collection 
      assign source = 'url:' | append: routes_url | append: '/collections/' | append: recs_collection | append: '?view=data&limit=' | append: section.settings.limit
      if data.settings.collection != blank
        assign fallback = 'url:' | append: routes_url | append: '/collections/' | append: data.settings.collection | append: '?view=data&limit=' | append: section.settings.limit
      endif
      assign fallback_title = data.settings.collection.title

    when 'recently-viewed'
      assign source = 'recentlyViewed'

    when 'products'
      assign source = data.id

    when 'searchspring-recs'
      assign source = 'SSRecs-' | append: section.id
      render 'ss-recommendations' data: data, source: source, topic: topic

  endcase

%}

{% if data.type == 'products' %}
<script>
  document.addEventListener('DOMContentLoaded', function (e) {
    window['{{ source }}'] = {products:[
      {% liquid 
        for product in data.settings.products
          assign siblings = false
          if data.settings.siblings
            assign siblings = product.metafields.family.siblings.products
          endif
          render 'product-data' product:product siblings:siblings variants:true
          unless forloop.last 
            echo ','
          endunless
        endfor
      %}
    ]}
  })
</script>
{% endif %}

{% if fallback != blank %}
<script>
  document.addEventListener('template:rendered', e=> {
    if(e.detail.info.topic == '{{ topic }}') {
      if(e.detail.info.data.products.filter(p=>p.handle!='{{ product.handle }}').length===0){
        Neptune.liquid.load('ProductRecommendations-{{ section.id }}','{{ fallback }}')
        _n.qs('.section-{{ section.id }} .product-recommendations-title').textContent = '{{ fallback_title }}'
      }
    }
  })
</script>
{% endif %}

<section data-section-id="{{ section.id }}" class="section-{{ section.id }} {% if section.settings.show_desktop == true and section.settings.show_mobile == true %} block {% elsif section.settings.show_desktop == true and section.settings.show_mobile == false %} hidden lg:block{% elsif section.settings.show_desktop == false and section.settings.show_mobile == true %} block lg:hidden{% endif %} section-slideshow section-slideshow-collection {{ section.settings.section_top_margin }} {{ section.settings.section_bottom_margin }} {{ section.settings.section_top_margin_mobile }} {{ section.settings.section_bottom_margin_mobile }} {{ section.settings.section_left_padding }} {{ section.settings.section_left_padding_mobile }} {{ section.settings.section_right_padding }} {{ section.settings.section_right_padding_mobile }} {{ section.settings.section_vertical_padding }} {{ section.settings.section_vertical_padding_mobile }} {{ section.settings.custom_class }}" {% if section.settings.background_color %}style="background-color: {{ section.settings.background_color }};"{% endif %} {% if section.settings.transport != blank %}neptune-transport="{append:'{{ section.settings.transport }}'}"{% endif %}>

  <div class="{{ section.settings.container }} mx-auto flex flex-col {{ section.settings.layout }}">

    {% if section.settings.title != blank %}
      <aside class="section-title {{ section.settings.text_align }} {{ section.settings.text_align_mobile }} flex flex-col w-full lg:px-4 pb-5 {% if section.settings.layout contains 'lg:flex-row' %}lg:w-1/4 lg:pl-0 lg:pr-8 lg:pb-0 lg:justify-center{% else %}lg:px-0{% endif %} {% if section.settings.section_right_padding == 'lg:pr-0' %}{{ section.settings.section_left_padding | replace: 'pl', 'pr' }}{% endif %} {% if section.settings.section_right_padding_mobile == 'pr-0' %}{{ section.settings.section_left_padding_mobile | replace: 'pl', 'pr' }}{% endif %}">
        <div>
          {% if section.settings.title != blank %}
            <{{ section.settings.title_element }} 
              class="mb-2 mt-0 h5 product-recommendations-title" 
              {% if section.settings.text_color != blank or section.settings.title_size != blank or section.settings.title_weight != blank %}
              style="{% if section.settings.text_color != blank %}color: {{ section.settings.text_color }}; {% endif %}{% if section.settings.title_size != blank %}font-size: {{ section.settings.title_size }}; {% endif %}{% if section.settings.title_weight != blank %}font-weight: {{ section.settings.title_weight }}; {% endif %}"
              {% endif %}
            >
                {{ section.settings.title }}
            </{{ section.settings.title_element }}>
          {% endif %}

          {% if section.settings.text != blank %}
            <div 
              class="block__copy relative mb-2" 
              {% if section.settings.text_color != blank %}style="color: {{ section.settings.text_color }};"{% endif %}
            >
              {{ section.settings.text }}
            </div>
          {% endif %}
        </div>
      </aside>
    {% endif %}

    <div class="w-full{% if section.settings.layout contains 'lg:flex-row' %} lg:w-3/4{% endif %}">

			<div id="{{ topic }}" class="product-recommendations product-recommendations--{{ section.settings.display }}" aria-live="polite" neptune-liquid="{topic:{{ topic }},source:'{{ source }}'{% if map != blank %},map:{{ map }}{% endif %},append:[productBadges]}">
        <template>

          <div 
            class="swiper-container h-full"
            {% if section.settings.display == 'slider' %}
            neptune-swiper="{}"
            data-swiper-mousewheel='{ "forceToAxis": true}'
            data-swiper-effect="slide" 
            data-swiper-autoheight="true"
            data-swiper-watch-overflow="true"
            data-swiper-preload-images="true"
            data-swiper-update-on-images-ready="true"
            {% if section.settings.autoplay == true %}
              data-swiper-autoplay='{
                "delay": {{ section.settings.autoplay_slide_duration }}000
              }'
            {% endif %}
            data-swiper-loop="{{ section.settings.loop }}" 
            data-swiper-breakpoints='{
              "1": {
                "spaceBetween": {{ section.settings.spacebetween_mobile }},
                "slidesPerView": {{ section.settings.slides_per_view_mobile }},
                "autoHeight": true,
                "centeredSlides": true
                
              },
              "1025": {
                "spaceBetween": {{ section.settings.spacebetween }},
                "slidesPerView": {{ section.settings.slides_per_view }},
                "autoHeight": true
              }
            }'
            data-swiper-navigation='{
              "nextEl": ".section-{{ section.id }} .swiper-button-next-unique",
              "prevEl": ".section-{{ section.id }} .swiper-button-prev-unique"
            }'
            data-swiper-pagination='{
              "el": ".section-{{ section.id }} .pagination"
            }'
            {% endif %}
          >

            <div class="swiper-wrapper items-stretch{% if section.settings.display == 'grid' %} flex-wrap{% endif %}">
              {% raw %}

                {% for product in products limit: {% endraw %}{{ section.settings.limit }}{% raw %} %}
                  {% if product.handle == '{% endraw %}{{ product.handle }}{% raw %}' %}{% continue %}{% endif %}
                  {% endraw %}
                  <div class="swiper-slide lg:w-1/{{ section.settings.slides_per_view | split: '.' | first }} w-1/{{ section.settings.slides_per_view_mobile | split: '.' | first }} h-full" style="{% if block.settings.block_background_color != blank %}background-color: {{ block.settings.block_background_color }};{% endif %}{% if block.settings.block_text_color != blank %}text-color: {{ block.settings.block_text_color }};{% endif %}">
                    {% render 'product-item' details_link:section.blocks[0].settings.details_link action:section.settings.action siblings:section.settings.siblings topic:topic __settings:section.settings %}
                    {% style %}#shopify-section-{{ section.id }}{display:block;}{% endstyle %}
                  </div>
                  {% raw %}
                {% endfor %}
              {% endraw %}
            </div>

          </div>

          {% if section.settings.display == 'slider' %}
            {% if section.settings.show_pagination %}
              <div class="swiper-pagination flex items-center w-full p-4 lg:p-0">
                <div class="pagination"></div>
              </div>
            {% endif %}

            <button class="top-1/2 transform -translate-y-1/2 absolute left-1 {% unless section.settings.arrows_mobile %}hidden{% endunless %} lg:block {% unless section.settings.arrows %}lg:hidden{% endunless %} swiper-prev swiper-button-prev-unique btn-control btn-control--dark p-2" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
              {% render 'icon' icon:'chevron-left' width:30 height:30 %}
            </button>
            <button class="top-1/2 transform -translate-y-1/2 absolute right-1 {% unless section.settings.arrows_mobile %}hidden{% endunless %} lg:block {% unless section.settings.arrows %}lg:hidden{% endunless %} swiper-next swiper-button-next-unique btn-control btn-control--dark p-2" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
              {% render 'icon' icon:'chevron-right' width:30 height:30 %}
            </button>
          {% endif %}
        </template>

      </div>
    </div>

  </div>

</section>

{% style %}
  @media (max-width: 1024px) {
    .section-{{ section.id }} .swiper-slide:only-of-type {
      width: 54%;
      margin: 0 auto;
    }
    .section-{{ section.id }} .swiper-slide:only-of-type .product-item__wrapper {
      border-left: 2px solid #fff;
    }
    #SidebarBottom .section-{{ section.id }} .swiper-slide:only-of-type {
      width: 100%;
    }
    #SidebarBottom .section-{{ section.id }} .swiper-slide:only-of-type .product-item__wrapper {
      border-left: none;
    }
  }

  {% if section.settings.display == 'grid' %}
    @media (max-width: 1023px) {
      .section-{{ section.id }} .swiper-slide:nth-child(n + {{ section.settings.slides_per_view_mobile | plus: 1 }}) .product-item__wrapper {
        border-top-width: 0;
      }
    }

    @media (min-width: 1024px) {
      .section-{{ section.id }} .swiper-slide:nth-child(n + {{ section.settings.slides_per_view | plus: 1 }}) .product-item__wrapper {
        border-top-width: 0;
      }
    }
  {% endif %}

  {% unless section.settings.show_bis_form %}
    .section-{{ section.id }} .back-in-stock {
      display: none;
    }
 
    .section-{{ section.id }} button[disabled],
    .section-{{ section.id }} [data-available="0"],
    .section-{{ section.id }} [data-available].unavailable, 
    .section-{{ section.id }} [data-available].unavailable~* {
      cursor: auto;
      pointer-events: none;
    }
  {% endunless %}
{% endstyle %}

{% comment %}
  BELOW SCRIPT IS FOR RECENTLY VIEWED PRODUCTS
{% endcomment %}

<script>
  (function(){
    
    let rv = {products:[]}
    try{
      rv = JSON.parse(localStorage.getItem('recentlyViewed'));
    } catch(err) {}
    
    if(!rv)
      rv = {products:[]}

    window.recentlyViewed = rv;

    {% if template contains 'product' %}

      var handle = {{product.handle|json}};
      rv.products = JSON.parse(JSON.stringify(
          rv.products.filter(function(p, i) {return p.handle != handle})
        )
      ).slice(0,{{ section.settings.limit }});
      window.recentlyViewed = JSON.parse(JSON.stringify(rv));

      rv.products.unshift({{product|json}})
      localStorage.setItem('recentlyViewed', JSON.stringify(rv))

    {% endif %}

  })()
</script>

{% endif %}

{% comment %}
  BELOW SCRIPT IS FOR AB TEST
{% endcomment %}

