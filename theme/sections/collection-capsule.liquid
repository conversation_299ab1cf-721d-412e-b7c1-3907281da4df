{{ 'searchspring.js' | asset_url | script_tag }}
<link rel="stylesheet" href="https://use.typekit.net/nof1kit.css">
{% style %}
  .aw-conqueror-didot {
    font-family: aw-conqueror-didot,serif;
    font-weight: 400;
    font-style: italic;
  }
  .btn-quickshop {
    display: none;
  }
{% endstyle %}

{% paginate collection.products by section.settings.pagination_size %}
  <h1 class="sr-only">{{ collection.title }}</h1>

  <div class="flex flex-row flex-wrap collection-grid collection--capsule-grid animate animate-slow">
  	{% assign count = 0 | plus: paginate.current_offset %}
    {% for product in collection.products %}
    	{% assign count = count | plus: 1 %}
      <div class="product-item {{ section.settings.items_per_row }} {{ section.settings.items_per_row_mobile }} flex flex-col lg:p-4 py-4 relative product-grid-item group" data-tags="{% for tag in product.tags %}{{ tag | handleize }}{% unless forloop.last %},{% endunless %}{% endfor %}">
        
        <a class="block overflow-hidden" href="{{ product.url | within: collection }}" neptune-cursor="{text: '{{ product.title }}', selector: '.custom-cursor'}">
        	<span class="absolute top-0 left-0 z-20 block p-4 text-lg text-xl aw-conqueror-didot">{{ count }}</span>
        	{% if product.featured_image != blank %}
            {% render 'lazy-image' with image: product.featured_image, image_class: "block product--img-xl -translate-x-capsule" %}
          {% endif %}
          <div class="absolute top-0 left-0 w-full h-full transition-opacity delay-1000 opacity-0 pi__photo-hover group-hover:opacity-100">          
	        	{%- if product.images[1].alt contains 'https' -%}
			        <div class="w-full h-full" style="background-color: #f7f7f7;" neptune-uncomment="mouseenter">
			        	<!--<video class="product-video plyr--setup" preload="true" src="{{product.images[1].alt}}" muted mute autoplay loop="" style="width: calc(100% + 20px);
						    margin-left: -10px;
						    margin-right: -10px;
						    height: 100%;
						    object-fit: contain;" poster=""><source src="{{product.images[1].alt}}"></video>-->
			        </div>
		      	{%- else -%}
              {% render 'lazy-image' with image: product.images[1].src, image_class: "block product--img-xl" %}
		        {%- endif -%}
		      </div>  
          <div class="custom-cursor">
          	<h2 class="m-0 text-base leading-tight tracking-wide uppercase MonumentGrotesk-Bold">
          		{% if isThirdParty or template == 'product.thirdparty'%}
								{% assign productTitle = product.title | remove: product.vendor %}

								{% if productSubtitle and productTitle contains productSubtitle %}
									{{ 
										productTitle 
										| split: ' - ' 
										| first 
										| remove: product.metafields.product['sub-title']
									}}
								{% else %}
									{{ 
										productTitle 
										| remove: ' -'  
										| remove: product.metafields.product['sub-title'] 
									}}
								{% endif %}

				      {% else %}
								
								{% assign productTitle = product.title | split: ' - ' %}
				        {% assign title_handle = productTitle.first | handle-%}
								{% assign title_collection = collections[title_handle] %}
				          
				       	{% if title_collection.products_count != blank%}
				          {% assign title_link = title_handle | prepend: '/collections/' %}
				        {% endif %}

				        {% for block in section.blocks %}
				          {% if block.type == 'title-map' %}
									
										{% if productTitle.first contains block.settings.inclusion %}
											{% assign title_link = block.settings.collection | prepend: '/collections/'%}
											{% break %}
										{% endif %}

				          {% endif %}
				        {% endfor %}

				        {{ productTitle.first}}
							
							{% endif %}
          	</h2>
          	
        		{%- if product.metafields.product['sub-title'] != '' -%}
							<div class="font-normal leading-tight">
				        {% assign subtitle_handle = product.metafields.product['sub-title'] | handle-%}
				        {% assign wash_collection = collections[subtitle_handle] %}
				        {% if wash_collection.products_count != blank%}
				        	{% assign subtitle_link = subtitle_handle | prepend: '/collections/' %}
				        {% endif %}

				        {% for block in section.blocks %}
				          {% if block.type == 'title-map' %}
									
										{% if productTitle.last contains block.settings.inclusion  or product.metafields.product['sub-title'] contains  block.settings.inclusion%}
											{% assign subtitle_link = block.settings.collection | prepend: '/collections/'%}
											{% break %}
										{% endif %}

				          {% endif %}
				        {% endfor %}

				        {{ product.metafields.product['sub-title'] }}

				      </div>
						{%- endif -%}

          </div>
          <p class="sr-only">{{ product.title }}</p>
        </a>
    
      </div>

    {% else %}
      {% if collection.handle == 'all' and collection.all_vendors.size == 0 and collection.all_types.size == 0 %}
        {% for i in (1..8) %}
          <a href="#">
            {% capture current %}{% cycle 1, 2, 3, 4, 5, 6 %}{% endcapture %}
            {{ 'product-' | append: current | placeholder_svg_tag: 'placeholder-svg placeholder-svg--small' }}
          </a>

          <p>
            <a href="#">{{ 'homepage.onboarding.product_title' | t }}</a>
          </p>

          <p>
            {{ 1999 | money }}
          </p>
        {% endfor %}
      {% else %}
        <p>{{ 'collections.general.no_matches' | t }}</p>
      {% endif %}

    {% endfor %}
  </div>

  {% if paginate.pages > 1 %}
    {% include 'pagination' %}
  {% endif %}
{% endpaginate %}

<script type="text/javascript" defer>

	var moveIt = {
		init: function() {
			moveIt.bannerInterval = window.setInterval(function(){
				if (_n.exists('.section-image-text-overlay .block__image_wrap .lazyloaded')) {
					//console.log('ok')
					setTimeout( function(){
						_n.qs('.template-collection').classList.add('block-text-active');
					}, 3000)
					window.clearInterval(moveIt.bannerInterval);
				}
			}, 10);
		}
	}

	window.addEventListener('DOMContentLoaded', moveIt.init);

	document.addEventListener("DOMContentLoaded", function (e) {
		console.log('loaded');
		Collection.init();
	});
	
</script>

<style>[collection-injection]:not([injected]){display: none;}</style>

{% schema %}
{
	"name": "Capsule Collections",
	"settings": [
		{
      "type": "header",
      "content": "Grid Item Display Settings"
    },
    {
      "type": "select",
      "id": "items_per_row",
      "label": "Grid Items Per Row (Desktop)",
      "options": [
        {
          "value": "lg:w-1/4",
          "label": "4 Across"
        },
        {
          "value": "lg:w-1/3",
          "label": "3 Across"
        }
      ],
      "default": "lg:w-1/4"
    },
    {
      "type": "select",
      "id": "items_per_row_mobile",
      "label": "Grid Items Per Row (Mobile)",
      "options": [
        {
          "value": "w-1/2",
          "label": "2 Across"
        },
        {
          "value": "w-full",
          "label": "1 Across"
        }
      ],
      "default": "w-1/2"
    },
		{
			"type": "number",
			"id": "pagination_size",
			"label": "Pagination Size",
			"default": 30
		}
	],
	"blocks": [
		{
			"type": "filter_set",
			"name": "Filter Set",
			"settings": [
				{
					"id": "title",
					"type": "text",
					"label": "Filter Set Reference"
				},
				{
					"id": "tags",
					"type": "text",
					"label": "Tags",
					"info":"Comma separated list of tags to be included in filter set"
				},
				{
					"id": "swatch",
					"type": "checkbox",
					"label": "Display as Swatches",
					"info":"Upload swatch images to settings/files with file name {{handlized tag}}.jpg"
				}
			]
		}
	]
}
{% endschema %}