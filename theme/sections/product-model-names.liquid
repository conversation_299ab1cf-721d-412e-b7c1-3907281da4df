{% assign tagModelName = false %}
{%- capture modelNameDesc -%}
	{%- for tag in product.tags -%}
		{%- if tag contains 'model_' -%}
			{% assign tagModelName = true %}
			{%- capture modelTag -%}{{- tag | remove: 'model_' | strip -}}{%- endcapture -%}
			{{ modelTag }}
		{%- endif -%}
	{%- endfor -%}
{%- endcapture -%}

{% for block in section.blocks %}

	{% assign bs = block.settings %}
	{% assign title = bs.model_name | handleize %}
	{% assign desc = bs.model_desc  %}

	{%- if title == modelTag -%}
		<p class="mt-4" neptune-transport="{ prepend:.pv__sizeguide }">{{ desc }}</p>
		{%- break -%}
	{%- endif -%}

{% endfor %}

{% schema %}
{
	"name": "Model Names",
	"class": "model-name",
	"settings": [],
	"blocks": [
		{
			"type": "model-name",
			"name": "Model",
			"settings": [
				{
					"type": "header",
					"content": "Models"
				},
				{
					"id": "title",
					"type": "text",
					"label": "Title",
					"info": "For admin purposes only."
				},
				{
					"id": "model_name",
					"type": "text",
					"label": "Name"
				},
				{
					"id": "model_desc",
					"type": "textarea",
					"label": "Description"
				}
			]
		}
	]
}
{% endschema %}