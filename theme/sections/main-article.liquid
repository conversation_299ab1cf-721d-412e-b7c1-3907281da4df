<article class="article-template {{ section.settings.container }} {{ section.settings.section_top_margin }} {{ section.settings.section_bottom_margin }} {{ section.settings.section_top_margin_mobile }} {{ section.settings.section_bottom_margin_mobile }} {{ section.settings.horizontal_padding }} {{ section.settings.horizontal_padding_mobile }} {% if section.settings.show_desktop == true and section.settings.show_mobile == true %} block {% elsif section.settings.show_desktop == true and section.settings.show_mobile == false %} dn block-l {% elsif section.settings.show_desktop == false and section.settings.show_mobile == false %} dn {% elsif section.settings.show_desktop == false and section.settings.show_mobile == true %} block dn-l {% endif %}" itemscope itemtype="http://schema.org/BlogPosting"
  neptune-surface="{
    'direction':'up',
    'windowEdge': 'bottom',
    'elementEdge': 'top',
    'targets': [
      {
        'selector':'_self .article-template--wrap',
        'engage:action': {
          'classes': {
            'remove': ['active']
          }
        }
      }
    ]
  }"
>
  <div 
  class="article-template--wrap {{ section.settings.scroll_effect }} animate animate-slow"
  neptune-surface="{
    'windowEdge': 'bottom',
    'elementEdge': 'top',
    'delay': '800',
    'targets': [
      {
        'engage:action': {
          'classes': {
            'add': ['active']
          }
        }
      }
    ]
  }">
  {%- for block in section.blocks -%}
    {%- case block.type -%}
      {%- when '@app' -%}
        <div class="page-width page-width--narrow">
          {% render block %}
        </div>

        {%- when 'title'-%}
          <header class="page-width page-width--narrow" {{ block.shopify_attributes }}>
            <h1 class="article-template__title {% if block.settings.blog_show_title == blank %}clip{% endif %}" itemprop="headline">{{ article.title | escape }}</h1>
            {%- if block.settings.blog_show_date -%}
              <span class="circle-divider caption-with-letter-spacing" itemprop="dateCreated pubdate datePublished">{{ article.published_at | date: "%B %d, %Y" }}</span>
            {%- endif -%}
            {%- if block.settings.blog_show_author -%}
              <span class="caption-with-letter-spacing" itemprop="author" itemscope itemtype="http://schema.org/Person">
                <span itemprop="name">{{ article.author }}</span>
              </span>
            {%- endif -%}
          </header>

        {%- when 'content'-%}
          <div class="article-template__content page-width page-width--narrow rte {{ section.settings.font_family}} lh-copy" itemprop="articleBody" {{ block.shopify_attributes }}>
              {{ article.content }}
          </div>

          {% style %}
            .article-template__content , .article-template__content p {
              font-size: {{ block.settings.article_size }}px;
            }
            .article-template__content a {
              text-decoration: underline;
            }
            @media (max-width: 767px) {
              .article-template__content , .article-template__content p {
                font-size: {{ block.settings.article_size_mobile }}px;
              }
            }
          {% endstyle %}

        {%- when 'social_sharing'-%}
          <div class="article-template__social-sharing page-width page-width--narrow" {{ block.shopify_attributes }}>
            {% render 'social-sharing', share_title: article.title, share_permalink: article.url, share_image: article.image %}
          </div>
    {%- endcase -%}
  {%- endfor -%}
  </div>
</article>


{% schema %}
{
  "name": "Main Article",
  "tag": "section",
  "settings": [
    {
      "type": "header",
      "content": "Display Settings"
    },
    {
      "type": "checkbox",
      "id": "show_desktop",
      "label": "Show desktop",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_mobile",
      "label": "Show mobile",
      "default": true
    },
    {
      "type": "header",
      "content": "Section Display Settings"
    },
    {
      "type": "select",
      "id": "container",
      "label": "Container",
      "options": [
        {
          "value": "container",
          "label": "Container"
        },
        {
          "value": "container--small container",
          "label": "Container small"
        },
        {
          "value": "w-full",
          "label": "Full width"
        }
      ],
      "default": "container--small container"
    },
    {
      "type": "select",
      "id": "font_family",
      "label": "Typeface",
      "options": [
        {
          "value": "MonumentGrotesk-Regular",
          "label": "Monument Grotesk Regular"
        },
        {
          "value": "MonumentGrotesk-Medium",
          "label": "Monument Grotesk Medium "
        },
        {
          "value": "MonumentGrotesk-Medium-Italic",
          "label": "Monument Grotesk Medium Italic"
        },
        {
          "value": "MonumentGrotesk-Bold",
          "label": "Monument Grotesk Bold"
        },
        {
          "value": "Libre-Caslon",
          "label": "Libre Caslon"
        }
      ],
      "default": "Libre-Caslon"
    },
    {
      "type": "header",
      "content": "Section Spacing Settings (Desktop)"
    },
    {
      "type": "select",
      "id": "section_top_margin",
      "label": "Margin top",
      "options": [
        {
          "value": "lg:mt-0",
          "label": "None"
        },
        {
          "value": "lg:mt-1",
          "label": ".25rem"
        },
        {
          "value": "lg:mt-2",
          "label": ".5rem"
        },
        {
          "value": "lg:mt-4",
          "label": "1rem"
        },
        {
          "value": "lg:mt-8",
          "label": "2rem"
        },
        {
          "value": "lg:mt-16",
          "label": "4rem"
        },
        {
          "value": "lg:mt-32",
          "label": "8rem"
        },
        {
          "value": "lg:mt-64",
          "label": "16rem"
        }
      ],
      "default": "lg:mt-0"
    },
    {
      "type": "select",
      "id": "section_bottom_margin",
      "label": "Margin bottom ",
      "options": [
        {
          "value": "lg:mb-0",
          "label": "None"
        },
        {
          "value": "lg:mb-1",
          "label": ".25rem"
        },
        {
          "value": "lg:mb-2",
          "label": ".5rem"
        },
        {
          "value": "lg:mb-4",
          "label": "1rem"
        },
        {
          "value": "lg:mb-8",
          "label": "2rem"
        },
        {
          "value": "lg:mb-16",
          "label": "4rem"
        },
        {
          "value": "lg:mb-32",
          "label": "8rem"
        },
        {
          "value": "lg:mb-64",
          "label": "16rem"
        }
      ],
      "default": "lg:mb-0"
    },
    {
      "type": "select",
      "id": "horizontal_padding",
      "label": "Horizontal Padding",
      "options": [
        {
          "value": "lg:px-0",
          "label": "None"
        },
        {
          "value": "lg:px-1",
          "label": ".25rem"
        },
        {
          "value": "lg:px-2",
          "label": ".5rem"
        },
        {
          "value": "lg:px-4",
          "label": "1rem"
        },
        {
          "value": "lg:px-8",
          "label": "2rem"
        },
        {
          "value": "lg:px-16",
          "label": "4rem"
        },
        {
          "value": "lg:px-32",
          "label": "8rem"
        },
        {
          "value": "lg:px-64",
          "label": "16rem"
        }
      ],
      "default": "lg:px-0"
    },
    {
      "type": "header",
      "content": "Section Spacing Settings (Mobile)"
    },
    {
      "type": "select",
      "id": "section_top_margin_mobile",
      "label": "Margin top",
      "options": [
        {
          "value": "mt-0",
          "label": "None"
        },
        {
          "value": "mt-1",
          "label": ".25rem"
        },
        {
          "value": "mt-2",
          "label": ".5rem"
        },
        {
          "value": "mt-4",
          "label": "1rem"
        },
        {
          "value": "mt-8",
          "label": "2rem"
        },
        {
          "value": "mt-16",
          "label": "4rem"
        },
        {
          "value": "mt-32",
          "label": "8rem"
        },
        {
          "value": "mt-64",
          "label": "16rem"
        }
      ],
      "default": "mt-0"
    },
    {
      "type": "select",
      "id": "section_bottom_margin_mobile",
      "label": "Margin bottom",
      "options": [
        {
          "value": "mb-0",
          "label": "None"
        },
        {
          "value": "mb-1",
          "label": ".25rem"
        },
        {
          "value": "mb-2",
          "label": ".5rem"
        },
        {
          "value": "mb-4",
          "label": "1rem"
        },
        {
          "value": "mb-8",
          "label": "2rem"
        },
        {
          "value": "mb-16",
          "label": "4rem"
        },
        {
          "value": "mb-32",
          "label": "8rem"
        },
        {
          "value": "mb-64",
          "label": "16rem"
        }
      ],
      "default": "mb-0"
    },
    {
      "type": "select",
      "id": "horizontal_padding_mobile",
      "label": "Horizontal Padding (Mobile)",
      "options": [
        {
          "value": "px-0",
          "label": "None"
        },
        {
          "value": "px-1",
          "label": ".25rem"
        },
        {
          "value": "px-2",
          "label": ".5rem"
        },
        {
          "value": "px-4",
          "label": "1rem"
        },
        {
          "value": "px-8",
          "label": "2rem"
        },
        {
          "value": "px-16",
          "label": "4rem"
        },
        {
          "value": "px-32",
          "label": "8rem"
        },
        {
          "value": "px-64",
          "label": "16rem"
        }
      ],
      "default": "px-0"
    },
    {
      "type": "header",
      "content": "Interactivity"
    },
    {
      "type": "select",
      "id": "scroll_effect",
      "label": "Scroll Effect",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "fade-in in-view:fade-in",
          "label": "Fade In"
        },
        {
          "value": "fade-in-rise in-view:fade-in-rise",
          "label": "Fade In and Rise"
        }
      ],
      "default": ""
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "featured_image",
      "name": "Featured Image",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "image_height",
          "options": [
            {
              "value": "adapt",
              "label": "Adapt"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "Large"
            }
          ],
          "default": "adapt",
          "label": "Image Height",
          "info": "Image Height Info"
        }
      ]
    },
    {
      "type": "title",
      "name": "Title",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "blog_show_title",
          "default": false,
          "label": "Show Title"
        },
        {
          "type": "checkbox",
          "id": "blog_show_date",
          "default": false,
          "label": "Show Date"
        },
        {
          "type": "checkbox",
          "id": "blog_show_author",
          "default": false,
          "label": "Show Author"
        }
      ]
    },
    {
      "type": "content",
      "name": "Content",
      "limit": 1,
      "settings": [
        {
          "type": "number",
          "id": "article_size",
          "label": "Article Font Size (Desktop)",
          "default": 15
        },
        {
          "type": "number",
          "id": "article_size_mobile",
          "label": "Article Font Size (Mobile)",
          "default": 15
        }
      ]
    },
    {
      "type": "social_sharing",
      "name": "Social Sharing",
      "limit": 1
    }
  ]
}
{% endschema %}
