{% schema %}
{
  "name": "Modal",
  "settings": [
		{
	    "type": "header",
	    "content": "Modal"
	  },
	  {
	    "type":"text",
	    "id":"topic",
	    "label": "Modal Topic",
	    "info":"Open your modal with modal.open(topic);"
	  },
	  {
      "type": "select",
      "id": "position",
      "label": "Position",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "right",
          "label": "Right"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "top",
          "label": "Top"
        },
        {
          "value": "bottom",
          "label": "Bottom"
        },
        {
          "value": "full",
          "label": "Full"
        }
      ],
      "default": "center"
    },
	  {
	    "type":"liquid",
	    "id":"content",
	    "label": "Modal Content"
	  }
  ],
  "blocks": [
  ],
  "presets": [
    {
      "name": "Modal",
      "category": "Advanced",
      "settings": {},
      "blocks": []
    }
  ]
}
{% endschema %}
{% 
	render 'modal' 
	topic:section.settings.topic 
	position:section.settings.position 
	content:section.settings.content 
%}
