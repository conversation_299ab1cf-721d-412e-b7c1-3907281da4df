<script>
  window.promoBar = [];{%- for block in section.blocks -%}
    promoBar.push({{ block.settings|json }});{%- endfor -%}

  function scrollHandler() {
    if (window.scrollY < 100) {
      document.querySelector('body').classList.remove('scrolled')
    } else {
      document.querySelector('body').classList.add('scrolled')
    }
    if (window.scrollY === 0) {
      document.querySelector('body').classList.add('scrolled-top')
    } else {
      document.querySelector('body').classList.remove('scrolled-top')
    }
  }
  window.addEventListener('scroll', scrollHandler)
  window.addEventListener('DOMContentLoaded', scrollHandler)
</script>

<header class="section-{{section.id}} flex items-center justify-center h-8 lg:px-4 announcement-header transition-colors text-base" style="background-color: {{ section.settings.announcement_bg_color }};color: {{ section.settings.announcement_text_color }};">
  <div class="flex items-center justify-center w-full">
    <button class="px-2 py-2 announcement--prev {% if section.settings.arrows != false and section.settings.arrows_mobile != false %} flex {% elsif section.settings.arrows != false and section.settings.arrows_mobile == false %} hidden lg:flex {% elsif section.settings.arrows == false and section.settings.arrows_mobile != false %} flex lg:hidden {% else %} hidden {% endif %}" onclick="this.nextElementSibling.swiper.slidePrev()">
      {% render 'icon' icon: 'chevron-left' height: 16 width: 16 stroke: section.settings.arrow_color %}
    </button>

    <main
      neptune-swiper
      {% if section.settings.autoplay == true %}
      data-swiper-autoplay='{
                    "delay": {{ section.settings.autoplay_slide_duration }}000
                  }'
      {% endif %}
      data-swiper-loop="{{ section.settings.loop }}"
      class="swiper swiper-container" id="announcementSlider">
      <div class="swiper-wrapper flex items-center">
        {% for block in section.blocks %}
          {% liquid
            assign inclusions = block.settings.inclusions
          %}
          <div class="swiper-slide flex gap-2 justify-center items-center" {% if inclusions != blank %}data-international="{{inclusions}}" data-international-remove{% endif %}>
            {% if block.settings.text != blank and block.settings.link != blank and block.settings.text_link == true %}
              <a href="{{ block.settings.link }}" style="color: {{ section.settings.announcement_text_color }};"
              class="text-2xs uppercase flex gap-2">

                {% assign message_announcement = block.settings.text | replace: "<p", "<span" | replace: "/p>", "/span>" %}
                {% render 'country-specific-content' messages:block.settings.country, bold_style:block.settings.csm_bold, italic_style:block.settings.csm_italic  fallback: message_announcement %}

                {%- if block.settings.button != blank -%}
                {{ section.settings.button_underline }}
                  <span
                    style="color:{{ block.settings.button_color }}"
                    class="{{ block.settings.button_type }} {% if block.settings.button_underline != false %} underline{% endif %} text-2xs uppercase {% if block.settings.button_type == 'font-heading' %}transform -translate-y-px{% endif %}">
                    {{ block.settings.button }}
                  </span>
                {%- endif -%}
              </a>
            {% else %}
              <span class="text-2xs uppercase">
                {% assign message_announcement = block.settings.text | replace: "<p", "<span" | replace: "/p>", "/span>" %}
                {% render 'country-specific-content' messages:block.settings.country, bold_style:block.settings.csm_bold, italic_style:block.settings.csm_italic  fallback: message_announcement %}
              </span>

              {% if block.settings.button != blank %}
                {{ section.settings.button_underline }}
                <a
                  style="color:{{ block.settings.button_color }}"
                  class="{{ block.settings.button_type }} {% if block.settings.button_underline != false %} underline{% endif %} text-2xs uppercase {% if block.settings.button_type == 'font-heading' %}transform -translate-y-px{% endif %}"
                  href="{{ block.settings.link }}">
                  {{ block.settings.button }}
                </a>
              {% endif %}
  
            {% endif %}
            </div>
          {% endfor %}
        </div>
      </main>

      <button class="px-2 py-2 announcement--next {% if section.settings.arrows != false and section.settings.arrows_mobile != false %} flex {% elsif section.settings.arrows != false and section.settings.arrows_mobile == false %} hidden lg:flex {% elsif section.settings.arrows == false and section.settings.arrows_mobile != false %} flex lg:hidden {% else %} hidden {% endif %}" onclick="this.previousElementSibling.swiper.slideNext()">
        {% render 'icon' icon: 'chevron-right' height: 16 width: 16 stroke: section.settings.arrow_color %}
      </button>
    </div>

  </header>

  <header
    id="siteHeader"
    role="banner"
    class="{% if section.settings.mega-menu != blank %}header__mega-menu{% endif %} border-b-2 border-solid border-white flex flex-row justify-center w-full h-auto py-0 pl-2 pr-1 text-xs header lg:px-10 bg-near-white">

    <div class="header--logo flex flex-col items-center justify-center flex-grow-0 flex-shrink-0 lg:w-36 w-1/3 lg:items-start">
      <a href="/" class="flex flex-col items-center justify-center w-full lg:items-start header__logo lg:py-4 lg:px-0">
        {{ section.settings.logoSVG }}
        {% if template == "index" %}
          <h1 class="sr-only">{{ shop.name }}</h1>
        {% else %}
          <span class="sr-only">{{ shop.name }} - return to home page</span>
        {% endif %}
      </a>
    </div>

    {% render 'header-nav' megaMenu: section.settings.mega-menu %}

    {% render 'modal-overlay-insert' %}

    <aside class="nav__tools px-2 ml-auto flex justify-end lg:flex-grow-0 lg:flex-shrink-0 gray-800 items-center w-1/3 lg:w-auto lg:order-3">

      <div class="flex items-center uppercase nav__tools--search">

        <div
          class="hidden lg:flex justify-center header__search-desktop"
          tabindex="0"
          neptune-brig="{keys:[38,40]}">

          {% render 'search-trigger' show_search_text: section.settings.show_search_text %}

        </div>

        <div class="header--search-mobile flex lg:hidden justify-center px-3 py-4" tabindex="0">
          {% render 'search-trigger-mobile' megaMenu: section.settings.mega-menu %}
        </div>

      </div>

      <a class="p-4 uppercase login lg:block hidden" href="{% unless customer %}{{ routes.account_login_url }}{% else %}{{ routes.account_url }}{% endunless %}">
        <span class="sr-only">{{ 'customer.account.title' | t }}</span>
        <span>
          {% render 'icon' icon: 'user', height: 17, width: 17 %}
        </span>
      </a>

      <div class="px-3 py-4 lg:p-4 lg:pr-0">
        <button
          class="relative flex items-center p-0 m-0 bg-transparent border-none"
          neptune-liquid="{topic:cart, source:cart}"
          neptune-engage="{targets:[
            {
              attributes:[{
                att:data-return-focus 
                set:active
              }]
            },
            {
              selector:html,
              attributes:[{
                att:data-active-modal,
                set:cart
              }]
            }
          ]}"
          onclick="Neptune.cart.getCart();">
          <template>
            {% render 'icon' icon: 'cart', height: 17, width: 17 %}
            <span class="absolute top-0 right-0 flex flex-col items-center justify-end text-[10px] w-full h-full leading-none cart-bubble" style="padding-bottom: 2px;">
              {% raw %}
                <span>
                  {% unless item_count == 0 %}
                    {{ item_count }} <span class="sr-only">items in cart</span>
                  {% endunless %}
                </span>
              {% endraw %}
            </span>
          </template>
        </button>
      </div>

      <div class="header--menu flex items-center lg:hidden">
        <button
          id="navTrigger"
          class="w-10 pl-3 py-4 pt-5 pb-3 m-0 bg-transparent border-none"
          onclick="
          window.scrollTo(0,0);
          window.scrollTo(0,1);
          document.querySelector('html').setAttribute('data-active-modal', 'panel-nav');
          document.querySelector('[data-modal=panel-nav]').classList.add('active');
          document.querySelector('.panes article.selected').classList.add('active'); 
          document.querySelector('.panes article.selected').nextElementSibling.classList.remove('active'); 
          document.querySelector('.mobile--nav-item.selected').classList.add('active'); 
          document.querySelector('.mobile--nav-item.selected').nextElementSibling.classList.remove('active');
          document.querySelector('#mobileSearchForm').reset();
          document.querySelector('[search-results]').innerHTML = '';
          document.querySelector('[search-suggestions]').innerHTML = '';
          document.querySelector('body').classList.remove('search-in-focus');
        ">
          {% render 'icon' icon: 'menu', height: 14 width: 25 %}
        </button>
      </div>
    </aside>

  </header>

  <script>
    let announcementSlider = document.querySelector('#announcementSlider');
    announcementSlider.addEventListener("slideChange", function (e) {
      const el = e.detail.info.el;
      Neptune.liquid.init(announcementSlider);
    });
  </script>

  <style>
    .announcement-header {
      background: {{ section.settings.announcement_bar_color}}
    }

    {% if section.settings.announcement_bar_hover_color %}
      .announcement-header:hover {
        background: {{ section.settings.announcement_bar_hover_color}}
      }
    {% endif %}
  </style>

  {% schema %}
    {
      "name": "Header",
      "settings": [
        {
          "type": "header",
          "content": "Preferences"
        },
        {
          "type": "header",
          "content": "Logo settings"
        },
        {
          "type": "image_picker",
          "id": "logo",
          "label": "Logo image"
        },
        {
          "id": "logoSVG",
          "label": "Logo SVG",
          "type": "textarea",
          "info": "Paste an inline SVG of your logo."
        },
        {
          "id": "logo_max_width",
          "label": "Logo Max Width",
          "type": "text",
          "info": "Add a pixel or percentage value."
        },
        {
          "type": "header",
          "content": "Main menu"
        },
        {
          "type": "link_list",
          "id": "menu",
          "label": "Menu",
          "default": "main-menu"
        },
        {
          "type": "checkbox",
          "id": "mega-menu",
          "label": "Use mega-menu"
        },
        {
          "type": "header",
          "content": "Mobile additional menu"
        },
        {
          "type": "link_list",
          "id": "additional_menu",
          "label": "Menu"
        },
        {
          "type": "header",
          "content": "My account menu"
        },
        {
          "type": "link_list",
          "id": "account_menu",
          "label": "Menu"
        },
        {
          "type": "header",
          "content": "Header extras"
        },
        {
          "type": "text",
          "id": "exclusive_link_handle",
          "label": "Exclusive link handle",
          "info": "Add the handle for the exclusive text color to apply"
        },
        {
          "type": "color",
          "id": "nav_exclusive_color",
          "label": "Nav exclusive text color",
          "default": "#E7352B"
        },
        {
          "type": "header",
          "content": "Announcement Bar Settings"
        },
        {
          "type": "color",
          "id": "announcement_text_color",
          "label": "Announcement text color",
          "default": "#ffffff"
        },
        {
          "type": "color",
          "id": "announcement_bar_color",
          "label": "Announcement Bar Color",
          "default": "#000000"
        },
        {
          "type": "color",
          "id": "announcement_bar_hover_color",
          "label": "Announcement Bar Hover olor",
          "default": "#000000"
        },
        {
          "type": "color",
          "id": "announcement_button_color",
          "label": "Announcement button color",
          "default": "#ffffff"
        },
        {
          "type": "color",
          "id": "announcement_button_text_color",
          "label": "Announcement button text color",
          "default": "#000000"
        },
        {
          "type": "header",
          "content": "Announcement Slideshow Settings"
        },
        {
          "type": "checkbox",
          "id": "autoplay",
          "label": "Autoplay"
        },
        {
          "type": "checkbox",
          "id": "loop",
          "label": "Loop"
        },
        {
          "type": "range",
          "id": "autoplay_slide_duration",
          "label": "Autoplay Slide Duration",
          "min": 3,
          "max": 8,
          "step": 1,
          "default": 8
        },
        {
          "type": "checkbox",
          "id": "arrows",
          "label": "Show arrows (Desktop)",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "arrows_mobile",
          "label": "Show arrows (Mobile)",
          "default": true
        },
        {
          "type": "color",
          "label": "Arrow color",
          "id": "arrow_color",
          "default": "#ffffff"
        },
        {
          "type": "header",
          "content": "Search Label"
        },
        {
          "type": "checkbox",
          "id": "show_search_text",
          "label": "Show Search Text",
          "default": false
        }
      ],
      "blocks": [
        {
          "type": "announcement",
          "name": "Announcement",
          "settings": [
            {
              "id": "title",
              "label": "Title",
              "type": "text",
              "info": "For internal use purposes only. Gives the block the proper title."
            },
            {
              "id": "text",
              "label": "Announcement Text",
              "type": "richtext"
            },
            {
              "type": "checkbox",
              "id": "text_link",
              "label": "Apply link to full announcement",
              "default": false
            },
            {
              "id": "button",
              "label": "Annoucement Button",
              "type": "text"
            },
            {
              "type": "select",
              "label": "Button Style",
              "id": "button_type",
              "options": [
                {
                  "value": "font-body",
                  "label": "Normal"
                },
                {
                  "value": "font-heading",
                  "label": "Bold"
                },
                {
                  "value": "italic",
                  "label": "Italic"
                }
              ],
              "default": "font-body"
            },
            {
              "type": "checkbox",
              "id": "button_underline",
              "label": "Button underline",
              "default": false
            },
            {
              "id": "button_color",
              "label": "Button Color",
              "type": "color"
            },
            {
              "id": "link",
              "label": "Announcement Link",
              "type": "url"
            },
            {
              "id": "inclusions",
              "label": "Country Inclusions",
              "type": "text",
              "info": "Add the ISO code for the country or currency that this announcement should appear for. If left blank, it will appear for all countries. Separate each country code with a comma."
            },
            {
              "type": "header",
              "content": "International Announcement"
            },
            {
              "type": "textarea",
              "id": "country",
              "label": "Country Specific Messaging",
              "info": "Each country annoucement bar must be separated by a line break and in the follow format: AU _ Annoucement Text"
            },
            {
              "type": "checkbox",
              "id": "csm_bold",
              "label": "Bold",
              "default": false
            },
            {
              "type": "checkbox",
              "id": "csm_italic",
              "label": "Italic",
              "default": false
            }
          ]
        }
      ]
    }
  {% endschema %}

  <script type="application/ld+json">
    {
    "@context": "http://schema.org",
      "@type": "Organization",
      "name": "{{ shop.name }}",
    {% if section.settings.logo %}
      {% assign image_size = section.settings.logo.width | append: 'x' %}
      "logo": "https:{{ section.settings.logo | img_url: image_size }}",
    {% endif %}
    "sameAs": [
    "{{ settings.social_twitter_link }}",
    "{{ settings.social_facebook_link }}",
    "{{ settings.social_pinterest_link }}",
    "{{ settings.social_instagram_link }}",
    "{{ settings.social_tumblr_link }}",
    "{{ settings.social_snapchat_link }}",
    "{{ settings.social_youtube_link }}",
    "{{ settings.social_vimeo_link }}"
    ],
      "url": "{{ shop.url }}{{ page.url }}"
    }
  </script>

  {% if template.name == 'index' %}
    <script type="application/ld+json">
      {
      "@context": "http://schema.org",
            "@type": "WebSite",
            "name": "{{ shop.name }}",
      "potentialAction": {
              "@type": "SearchAction",
              "target": "{{ shop.url }}/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
            },
            "url": "{{ shop.url }}{{ page.url }}"
      }
    </script>
  {% endif %}
