<section 
  {% if section.settings.image_alt_tag %} aria-label="{{ section.settings.image_alt_tag }}" {% endif %}
  class="{{ section.settings.container }} custom-content-{{ section.id }} relative {% if section.settings.remove_section_condition %} vwo-hide{% endif %}" 
  data-section-id="{{ section.id }}" 
  data-section-type="custom-content" 
  style="
    {% if section.settings.background_color%} background-color:{{section.settings.background_color}}; {%  endif %}
    padding-bottom: {{ section.settings.section_vertical_padding }};
    padding-top: {{ section.settings.section_vertical_padding }};
    "
  >

  {% assign image01 = section.settings.image %}
  {% assign mobile_image01 = section.settings.mobile_image %}  
  {% assign bg_img = 'false' %}
  {% assign bg_img_mobile = 'false' %}
  {% assign height = section.settings.height %}
  {% assign mobile_height = section.settings.mobile_height %}

  {%- if image01 != blank and height != 'h-auto-img-l' -%}
    {% assign bg_img = 'true' %}
  {%- else -%}
    {% assign bg_img = 'false' %}
  {%- endif -%}

  {%- if mobile_image01 != blank -%}
    {% assign bg_img_mobile = 'true' %}
    {%- if mobile_height == 'h-auto-img' or mobile_height == 'h-auto-img-content' -%}
      {% assign bg_img_mobile = 'true' %}
    {%- else -%}
      {% assign bg_img_mobile = 'false' %}
    {%- endif -%}
  {%- endif -%}

  {% if bg_img == 'true' %}    
    <style type="text/css">
      .custom-content-{{ section.id }} {
        {% if image01 != blank %}
          background-image: url({{ image01 | img_url: '2400x' }});  
        {%- endif-%}
        background-size: {{ section.settings.background_size }}; 
        background-attachment: {{ section.settings.background_attachment }}; 
        background-repeat: no-repeat; 
        background-position:{{ section.settings.background_position }}; 
      }
    </style>
  {%- endif-%}
  {% if bg_img_mobile == 'true' %}
    <style type="text/css">
      @media (max-width: 960px) {
        .custom-content-{{ section.id }} {
          background-image: url({{ mobile_image01 | img_url: '1024x' }}); 
        }
      }
    </style>
  {% elsif mobile_height == 'h-auto-img' or mobile_height == 'h-auto-img-content' %}
    <style type="text/css">
      @media (max-width: 960px) {
        .custom-content-{{ section.id }} {
          background-image: none; 
        }
      }
    </style>
  {% endif %}


  {% if section.settings.video_url != blank or section.settings.video_url_mobile != blank %}
    {% if section.settings.video_url != blank and section.settings.video_url_mobile == blank %}
      <video autoplay playsinline mute muted loop style="top:0;left:0;object-fit: cover;width:100%;height: 100%;" class="absolute">
        <source src="{{ section.settings.video_url }}" type="video/mp4">
      </video>
    {% elsif section.settings.video_url != blank and section.settings.video_url_mobile != blank %}
      <video autoplay playsinline mute muted loop style="top:0;left:0;object-fit: cover;width:100%;height: 100%;" class="absolute db-l dn">
        <source src="{{ section.settings.video_url }}" type="video/mp4">
      </video>
      <video autoplay playsinline mute muted loop style="top:0;left:0;object-fit: cover;width:100%;height: 100%;" class="absolute db dn-l">
        <source src="{{ section.settings.video_url_mobile }}" type="video/mp4">
      </video>
    {% elsif section.settings.video_url == blank and section.settings.video_url_mobile != blank %}
      <video autoplay playsinline mute muted loop style="top:0;left:0;object-fit: cover;width:100%;height: 100%;" class="absolute">
        <source src="{{ section.settings.video_url_mobile }}" type="video/mp4">
      </video>
    {% endif %}
  {% endif %}


  {% comment %} Adds Image to the Section {% endcomment %} 
  {% if height == 'h-auto-img-l' %} 
    <img class="w-100 dn db-l" style="margin: 0;" src="{{ image01 | img_url: '2400x' }}" alt="{{ image_alt_tag }}">
  {% endif %}
  {% if bg_img_mobile == 'true' or bg_img == 'true' %}
    {% if mobile_height == 'h-auto-img' or mobile_height == 'h-auto-img-content' %}
      {% if mobile_image01 != blank %}
        <img class="w-100 dn-l db" style="margin: 0;" src="{{ mobile_image01 | img_url: '1024x' }}" alt="{{ image_alt_tag }}">
      {% else %}
        <img class="w-100 dn-l db" style="margin: 0;" src="{{ image01 | img_url: '1024x' }}" alt="{{ image_alt_tag }}">
      {% endif %}
    {% endif %}
  {% endif %}
  
  <div class="flex flex-wrap {{ section.settings.block_justify }} section-height relative">
    
    {%- assign inclusions = ''-%}
    {%- assign hotspots = ''-%}
    {% assign count = 0 %}

    {%- for block in section.blocks-%}

      {% assign count = count | plus: 1 %}

      {%- if block.type == 'floating-item' and block.settings.relative_to == 'block'%}
        {%- capture floater-%}
          {%- include 'custom-content-floater'-%}
        {%- endcapture-%}
        {%- assign inclusions = inclusions | append: floater-%}  
        {%- continue-%}
      {%- endif-%}

     
      {%- if block.type == 'hotspot' and block.settings.relative_to == 'block'%}
        {%- capture hotspot-%}
          {%- include 'hotspot'-%}
        {%- endcapture-%}
        {%- assign hotspots = hotspots | append: hotspot-%}  
        {%- continue-%}
      {%- endif-%}
      {%- if block.type == 'Block'-%}
          <style>
            .block-{{ section.id }}-{{ forloop.index }} {
              padding-left:  {{block.settings.background_horizontal_padding}}; 
              padding-right: {{block.settings.background_horizontal_padding}}; 
            }
            @media (max-width: 960px) {
              .block-{{ section.id }}-{{ forloop.index }} {
                padding-left: {{block.settings.background_horizontal_padding_mobile}};
                padding-right: {{block.settings.background_horizontal_padding_mobile}};
              }
            }
        </style>
        <div class="block block-{{ section.id }}-{{ forloop.index }} relative {% if block.settings.mobile_width != blank %} {{ block.settings.mobile_width }} {% else %} w-100 {% endif %} {{ block.settings.width }} {{ block.settings.text_align_mobile }} {{ block.settings.text_align }}" data-content-position-mobile="{{block.settings.content_mobile_position}}">
          {%- if block.settings.link_element == 'block'%}
          <a target="{{ block.settings.link_target }}" class="db no-underline h-100 w-100 ada-outline-fix absolute h-100 top-0 left-0" href="{{ block.settings.url }}">
          {%- endif-%}
          
          <div class="h-100 {% if section.settings.height != 'auto'%}absolute{% endif %} w-100 top-0 left-0 overflow-hidden {{block.settings.background_entrance}}" surface-direction="down" style="background-color: {{ block.settings.bg_color }}">

            {%- if block.settings.overlay != blank-%}
            <div class="overlay absolute top-0 left-0 w-100 h-100 z-2" style="background-color: {{ block.settings.overlay }};opacity:.{{block.settings.overlay_opacity}}"></div>
            {%- endif-%}
            
            {%- if block.settings.image-%}
            
              {%- assign image = block.settings.image -%}
              {%- assign img_url = image | img_url: '2400x'-%}
              {%- assign mobileImage = block.settings.mobile_image -%}
              {%- assign mobile_img_url = mobileImage | img_url: '1024x'-%}
            
              <div class="block-bg block-bg-{{count}} animate absolute top-0 left-0 h-100 w-100 {{block.settings.background_hover}}"> </div>
              <style>
                .custom-content-{{ section.id }} .block-bg-{{count}} {
                  background-image: url({{ img_url }});
                  background-size: {{block.settings.background_size}};
                  background-attachment: {{block.settings.background_attachment}};
                  background-repeat: no-repeat;
                  background-position:{{block.settings.background_position}};
                }
                @media (max-width:960px) {
                  {%- if block.settings.mobile_image-%}
                  .custom-content-{{ section.id }} .block-bg-{{count}} {
                    background-image: url({{ mobile_img_url }});
                    background-size: {{block.settings.mobile_background_size}};
                  }
                  {%- endif-%}
                }
              </style>
            
            {%- elsif block.settings.video_url != blank-%}

              {% if block.settings.video_url != blank or block.settings.video_url_mobile != blank %}
                {% if block.settings.video_url != blank and block.settings.video_url_mobile == blank %}
                  <video autoplay playsinline mute muted loop style="object-fit: cover;width:100%;height: 100%;" class="">
                    <source src="{{ block.settings.video_url }}" type="video/mp4">
                  </video>
                {% elsif block.settings.video_url != blank and block.settings.video_url_mobile != blank %}
                  <video autoplay playsinline mute muted loop style="object-fit: cover;width:100%;height: 100%;" class=" db-l dn">
                    <source src="{{ block.settings.video_url }}" type="video/mp4">
                  </video>
                  <video autoplay playsinline mute muted loop style="object-fit: cover;width:100%;height: 100%;" class=" db dn-l">
                    <source src="{{ block.settings.video_url_mobile }}" type="video/mp4">
                  </video>
                {% elsif block.settings.video_url == blank and block.settings.video_url_mobile != blank %}
                  <video autoplay playsinline mute muted loop style="object-fit: cover;width:100%;height: 100%;" class="">
                    <source src="{{ block.settings.video_url_mobile }}" type="video/mp4">
                  </video>
                {% endif %}
              {% endif %}

            {%- endif-%} 

            <div>
              <style>
                .cc_content-{{ section.id }}-{{ forloop.index }} {
                  margin: {{ block.settings.content_vertical_margin }} {{ block.settings.content_horizontal_margin }};
                  padding: {{ block.settings.content_vertical_padding }} {{ block.settings.content_horizontal_padding }};
                }
                @media (max-width: 960px) {
                  .cc_content-{{ section.id }}-{{ forloop.index }} {
                    margin: {{ block.settings.content_vertical_margin_mobile }} {{ block.settings.content_horizontal_margin_mobile }};
                    padding: {{ block.settings.content_vertical_padding_mobile }} {{ block.settings.content_horizontal_padding_mobile  }};
                  }
                }
              </style>
              <div class="cc_content cc_content-{{ section.id }}-{{ forloop.index }} relative overflow-hidden cc_text flex {{ block.settings.content_position }} {{ block.settings.content_mobile_position }} {{ block.settings.content_horizontal_align }}">
                <div 
                  {% if block.settings.background_entrance%} surface-direction="down" surface-delay="{{settings.entrance_animation_timing|remove:'ms'|remove:'s'}}"{%- endif-%} 
                  class="cc-block relative lh-copy {{block.settings.content_entrance}}" style="background-color: {{ block.settings.content_color }}">

                  {% if block.settings.link_element == 'content'%}
                    <a target="{{ block.settings.link_target }}" class="db no-underline" href="{{ block.settings.url }}">
                  {% endif %}

                  {% if block.settings.content_image != blank %}
                    <img src="{{ block.settings.content_image | img_url: '2400x' }}" class="db mb3" alt="{{ block.settings.title }}">
                  {% endif %}
                  
                  {%- if block.settings.pretitle != blank -%}
                    <style>
                      .block_pretitle-{{ section.id }}-{{ forloop.index }} {
                        font-size: {{block.settings.pretitle_size}};
                      }
                      @media (max-width: 960px) {
                        .block_pretitle-{{ section.id }}-{{ forloop.index }} {
                          font-size: {{block.settings.pretitle_size_mobile}};
                        }
                      }
                    </style>
                    <span class="block_pretitle-{{ section.id }}-{{ forloop.index }} mt0 mb1 normal {{ block.settings.title_font_family }}" style='{%- if block.settings.text_color != blank-%}color: {{ block.settings.text_color }};{%- endif-%}'>{{ block.settings.pretitle }}</span>
                  {%- endif-%}
   
                  {% if block.settings.title != blank %}
                    <style>
                      .block_title-{{ section.id }}-{{ forloop.index }} {
                        font-size: {{block.settings.title_size}};
                      }
                      @media (max-width: 960px) {
                        .block_title-{{ section.id }}-{{ forloop.index }} {
                          font-size: {{block.settings.title_size_mobile}};
                        }
                      }
                    </style>
                    <{{block.settings.title_element}} class="block_title-{{ section.id }}-{{ forloop.index }} normal mb1 {{ block.settings.title_font_family }}" style='{% if block.settings.text_color != blank%}color: {{ block.settings.text_color }};{% endif%}'>{{ block.settings.title }}</{{block.settings.title_element}}>
                  {% endif %}
                  
                  {%- if block.settings.subtitle != blank-%}
                    <style>
                      .block_subtitle-{{ section.id }}-{{ forloop.index }} {
                        font-size: {{block.settings.subtitle_size}};
                      }
                      @media (max-width: 960px) {
                        .block_subtitle-{{ section.id }}-{{ forloop.index }} {
                          font-size: {{block.settings.subtitle_size_mobile}};
                        }
                      }
                    </style>
                    <span class="block_subtitle-{{ section.id }}-{{ forloop.index }} mb1 normal {{ block.settings.title_font_family }}" style='{%- if block.settings.text_color != blank-%}color: {{ block.settings.text_color }};{%- endif-%}'>{{ block.settings.subtitle }}</span>
                  {%- endif-%}

                  {%- assign body_text = block.settings.text | strip | strip_html-%}
                  {%- unless body_text == blank-%}
                  
                  <div>
                    <div class="desc f3 {{ block.settings.copy_size }}" style='
                      display:inline-block; {% if block.settings.text_color != blank %}
                      color: {{ block.settings.text_color }};{% endif %}
                      font-size: {{block.settings.copy_size}};'>
                      {{ block.settings.text }}
                    </div>
                  </div>

                  {%- endunless-%}
                  
                  {%- if block.settings.link_title != blank and block.settings.text != blank%}
                  
                  <!-- <hr class="divider"> -->
                  {%- endif-%}

                  {%- if block.settings.link_element == 'content'%}
                    {%- if block.settings.link_title != blank-%}
                      <style>
                          .block_button-{{ section.id }}-{{ forloop.index }} {
                            padding: {{ block.settings.button_vertical_padding}} {{block.settings.button_horizontal_padding}};
                            font-size: {{ block.settings.button_font_size }};
                          }
                      </style>
                      <button class="block_button-{{ section.id }}-{{ forloop.index }} db dib-l mb3 mb0-l mh2 fw6 tracked-slight {%- if block.settings.button_border-%} ba {%- endif-%} tracked-slight f6" style="min-width: 130px;background-color: {{ block.settings.btn_color}}; border: 1px solid {% if block.settings.button_border == false %} {{ block.settings.btn_color}} {% else %} {{ block.settings.btn_text_color}} {% endif %}; color: {{ block.settings.btn_text_color}};">{{ block.settings.link_title }}</button>
                    {%- endif-%}

                    {%- if block.settings.link_title_two != blank-%}
                      <button class="db dib-l block_button-{{ section.id }}-{{ forloop.index }} mb3 mb0-l mh2 fw6 tracked-slight {%- if block.settings.button_border-%} ba {%- endif-%} tracked-slight f6" style="min-width: 130px;background-color: {{ block.settings.btn_color}}; border: 1px solid {% if block.settings.button_border == false %} {{ block.settings.btn_color}} {% else %} {{ block.settings.btn_text_color}} {% endif %}; color: {{ block.settings.btn_text_color}};">{{ block.settings.link_title_two }}</button>
                    {%- endif-%}

                  {%- else -%}
                    
                    {%- if block.settings.link_title != blank-%}
                      <style>
                          .block_button-{{ section.id }}-{{ forloop.index }} {
                            padding: {{ block.settings.button_vertical_padding}} {{block.settings.button_horizontal_padding}};
                            font-size: {{ block.settings.button_font_size }};
                          }
                      </style>
                      <a href="{{ block.settings.url }}" target="{{ block.settings.link_target }}" class="block_button-{{ section.id }}-{{ forloop.index }} db dib-l mb3 mb0-l mh2 fw6 tracked-slight {%- if block.settings.button_border-%} ba {%- endif-%} tracked-slight f6" style="min-width: 130px;background-color: {{ block.settings.btn_color}}; border: 1px solid {% if block.settings.button_border == false %} {{ block.settings.btn_color}} {% else %} {{ block.settings.btn_text_color}} {% endif %}; color: {{ block.settings.btn_text_color}};">{{ block.settings.link_title }}</a>
                    {%- endif-%}

                    {%- if block.settings.link_title_two != blank-%}
                      <a href="{{ block.settings.url_two }}" target="{{ block.settings.link_target_two }}" class="db dib-l block_button-{{ section.id }}-{{ forloop.index }} mb3 mb0-l mh2 fw6 tracked-slight {%- if block.settings.button_border-%} ba {%- endif-%} tracked-slight f6" style="min-width: 130px;background-color: {{ block.settings.btn_color}}; border: 1px solid {% if block.settings.button_border == false %} {{ block.settings.btn_color}} {% else %} {{ block.settings.btn_text_color}} {% endif %}; color: {{ block.settings.btn_text_color}};">{{ block.settings.link_title_two }}</a>
                    {%- endif-%}

                  {%- endif -%}

                  {%- if block.settings.link_element == 'content'%}
                  </a>
                  {%- endif -%}

                </div>
              </div>
              
            </div>
          </div>

          {%- if block.settings.link_element == 'block'%}
          </a>
          {%- endif-%}

          {{ inclusions }}
          {%- assign inclusions = ''-%}

          {{ hotspots }}
          {%- assign hotspots = ''-%}

        </div>

      {%- endif-%}

    {%- endfor-%}

  </div>

  {%- for block in section.blocks-%}
    {%- if block.type == 'floating-item' and block.settings.relative_to == 'section'-%}
      {%- include 'custom-content-floater'-%}
    {%- endif-%}
  {%- endfor-%} 

  {%- for block in section.blocks-%}
    {%- if block.type == 'hotspot' and block.settings.relative_to == 'section'-%}
      {%- include 'hotspot'-%}
    {%- endif-%}
  {%- endfor-%}  

</section>

<style>

  {%- if section.settings.height != 'auto'-%}
    .custom-content-{{ section.id }} .cc_text {
      position: absolute;
      z-index: 3;
      top: 0;
      left: 0;
    }
  {%- endif-%}

  .cc_text {
    width: 100%;
    height: 100%;
  }
  .container-fluid {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  
  .custom-content-{{ section.id }} .product-hotspot .hotspot-dot, .custom-content-{{ section.id }} .product-hotspot .hotspot-text {
    opacity: 1;
  }

  .custom-content-{{ section.id }} .block .container {
    position: static;
  }

  @media (min-width: 1040px) {
    {% if height != 'h-auto-img-l' %}
      
      .custom-content-{{ section.id }} .section-height {
        min-height: {{ height }};
      }

    {% else %}
      .custom-content-{{ section.id }} .section-height {
        height: 100%;
        position: absolute;
        top: 0;
        width: 100%;
      }

    {% endif %}

    .custom-content-{{ section.id }} {
      cursor: url('{{ section.settings.cursor }}') 0 0, auto;
    }
  }

  @media (max-width: 1039px) {
    .custom-content-{{ section.id }} {
      background-attachment:scroll!important;
    }

    {% if mobile_height != 'h-auto-img' %}

      .custom-content-{{ section.id }} .section-height {
        height: auto;
        min-height: {{ mobile_height }};
      }

    {% else %}
      
      .custom-content-{{ section.id }} .section-height {
        height: 100%;
        position: absolute;
        top: 0;
        width: 100%;
      }

    {% endif %}

    .custom-content-{{ section.id }} .section-height [data-content-position-mobile="content-below-image"] .h-100 {
      height: auto !important;
    }
    [data-content-position-mobile="content-below-image"] .cc_text, [data-content-position-mobile="content-below-image"] .block-bg { 
      position: static; 
      display:block;
    } 
    [data-content-position-mobile="content-below-image"] .block-bg, 
    [data-content-position-mobile="content-below-image"] video {
      min-height: {{ section.settings.mobile_height }};
    }
    [data-content-position-mobile="content-below-image"] .overlay {
      display: none;
    }
    .custom-content-{{ section.id }} .block-bg {
      background-attachment:scroll!important;
    }
    {%- if section.settings.mobile_height != 'auto'-%}
       .custom-content-{{ section.id }} .cc_text {
        position: absolute;
        z-index: 3;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    {%- endif-%}
  }


  .desc > p {font-size:inherit!important;font-family:inherit!important;}

</style>


{%- schema %}
  {
  "name": "Custom content",
  "max_blocks": 20,
  "settings": [
    {
      "type": "header",
      "content": "A/B Testing"
    },
    {
      "type": "checkbox",
      "id": "remove_section_condition",
      "label": "Remove Section",
      "default": false,
      "info": "This will hide the section in one of the experiment variations."
    },
    {
      "type": "header",
      "content": "Section Content Settings"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background Color"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Background Image"
    },
    {
      "type": "image_picker",
      "id": "mobile_image",
      "label": "Mobile Background Image"
    },
    {
      "type": "text",
      "id": "image_alt_tag",
      "label": "Backgroung Image Alt Tag"
    },
    {
      "type": "select",
      "id": "background_attachment",
      "label": "Background Attachment",
      "options": [
        {
          "value": "scroll",
          "label": "Scroll"
        },
        {
          "value": "fixed",
          "label": "Fixed"
        }
      ],
      "default": "scroll"
    },
    {
      "type": "select",
      "id": "background_size",
      "label": "Background Size",
      "options": [
        {
          "value": "cover",
          "label": "Cover"
        },
        {
          "value": "contain",
          "label": "Contain"
        },
        {
          "value": "auto 100%",
          "label": "Contain Height"
        },
        {
          "value": "100% auto",
          "label": "Contain Width"
        },
        {
          "value": "100% 100%",
          "label": "Squish"
        }
      ],
      "default": "cover"
    },
    {
      "type": "select",
      "id": "background_position",
      "label": "Background Position",
      "options": [
        {
          "value": "top left",
          "label": "Top Left"
        },
        {
          "value": "top center",
          "label": "Top Center"
        },
        {
          "value": "top right",
          "label": "Top Right"
        },
        {
          "value": "left center",
          "label": "Middle Left"
        },
        {
          "value": "center center",
          "label": "Center"
        },
        {
          "value": "right center",
          "label": "Middle Right"
        },
        {
          "value": "left bottom",
          "label": "Bottom Left"
        },
        {
          "value": "center bottom",
          "label": "Bottom Center"
        },
        {
          "value": "right bottom",
          "label": "Bottom Right"
        }
      ],
      "default": "center center"
    },
    {
      "type": "text",
      "id": "video_url",
      "label": "Background Video (Desktop)",
      "info": "To get mp4 file: 1) Go to vimeo.com, 2) Click on desired video, 3) click on settings, 4) Click on Video file"
    },
    {
      "type": "text",
      "id": "video_url_mobile",
      "label": "Background Video (Mobile)",
      "info": "To get mp4 file: 1) Go to vimeo.com, 2) Click on desired video, 3) click on settings, 4) Click on Video file"
    },
    {
      "type": "text",
      "id": "cursor",
      "label": "Custom Cursor",
      "info": "Choose an svg or png from your [files](/admin/settings/files) and paste the image url in the field below."
    },
    {
      "type": "header",
      "content": "Section Display Settings"
    },
    {
      "type": "text",
      "id": "section_vertical_padding",
      "label": "Section vertical padding",
      "default": "4px"
    },
    {
      "type": "select",
      "id": "container",
      "label": "Container",
      "options": [
        {
          "value": "container",
          "label": "Container"
        },
        {
          "value": "ph5-l",
          "label": "Padded"
        },
        {
          "value": "w-100",
          "label": "Full width"
        }
      ],
      "default": "w-100"
    },
    {
      "type": "select",
      "id": "block_justify",
      "label": "Justify Content",
      "options": [
        {
          "value": "justify-start",
          "label": "Start"
        },
        {
          "value": "justify-end",
          "label": "End"
        },
        {
          "value": "justify-center",
          "label": "Center"
        },
        {
          "value": "justify-between",
          "label": "Between"
        },
        {
          "value": "justify-around",
          "label": "Around"
        }
      ],
      "default": "justify-start"
    },
    {
      "type": "select",
      "id": "height",
      "label": "Height",
      "options": [
        {
          "value": "100vh",
          "label": "Full height"
        },
        {
          "value": "75vh",
          "label": "Three quarter height"
        },
        {
          "value": "50vh",
          "label": "Half height"
        },
        {
          "value": "25vh",
          "label": "Quarter height"
        },
        {
          "value": "auto",
          "label": "Auto height"
        },
        {
          "value": "h-auto-img-l",
          "label": "Image height"
        }
      ],
      "default": "100vh"
    },
    {
      "type": "select",
      "id": "mobile_height",
      "label": "Mobile height",
      "options": [
        {
          "value": "100vh",
          "label": "Full height"
        },
        {
          "value": "75vh",
          "label": "Three quarter height"
        },
        {
          "value": "50vh",
          "label": "Half height"
        },
        {
          "value": "25vh",
          "label": "Quarter height"
        },
        {
          "value": "auto",
          "label": "Auto height"
        },
        {
          "value": "h-auto-img",
          "label": "Image height"
        }
      ],
      "default": "50vh"
    }
  ],
  "blocks": [
    {
      "type": "floating-item",
      "name": "Floating Item",
      "settings": [
        {
          "type": "header",
          "content": "Floating Item Settings"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Floating Image"
        },
        {
          "type": "select",
          "id": "relative_to",
          "label": "Floats within",
          "info": "Important: When adding to a block, make sure that the floating item is ordered before the section you want it to show in.",
          "options": [
            {
              "value": "block",
              "label": "Block"
            },
            {
              "value": "section",
              "label": "Section"
            }
          ]
        },
        {
          "type": "html",
          "id": "html_settings",
          "label": "HTML for additional settings"
        },
        {
          "type": "header",
          "content": "Floating Layout Settings"
        },
        {
          "type": "checkbox",
          "id": "display_desktop",
          "label": "Display on desktop",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "display_mobile",
          "label": "Display on mobile",
          "default": true
        },
        {
          "type": "select",
          "id": "content_position",
          "label": "Desktop Content Position",
          "options": [
            {
              "value": "ap-tl-l",
              "label": "Top Left"
            },
            {
              "value": "ap-tc-l",
              "label": "Top Center"
            },
            {
              "value": "ap-tr-l",
              "label": "Top Right"
            },
            {
              "value": "ap-ml-l",
              "label": "Middle Left"
            },
            {
              "value": "ap-mc-l",
              "label": "Middle Center"
            },
            {
              "value": "ap-mr-l",
              "label": "Middle Right"
            },
            {
              "value": "ap-bl-l",
              "label": "Bottom Left"
            },
            {
              "value": "ap-bc-l",
              "label": "Bottom Center"
            },
            {
              "value": "ap-br-l",
              "label": "Bottom Right"
            }
          ],
          "default": "ap-mc-l"
        },
        {
          "type": "select",
          "id": "content_mobile_position",
          "label": "Mobile Content Position",
          "options": [
            {
              "value": "ap-tl",
              "label": "Top Left"
            },
            {
              "value": "ap-tc",
              "label": "Top Center"
            },
            {
              "value": "ap-tr",
              "label": "Top Right"
            },
            {
              "value": "ap-ml",
              "label": "Middle Left"
            },
            {
              "value": "ap-mc",
              "label": "Middle Center"
            },
            {
              "value": "ap-mr",
              "label": "Middle Right"
            },
            {
              "value": "ap-bl",
              "label": "Bottom Left"
            },
            {
              "value": "ap-bc",
              "label": "Bottom Center"
            },
            {
              "value": "ap-br",
              "label": "Bottom Right"
            }
          ],
          "default": "ap-mc"
        },
        {
          "type": "text",
          "id": "background_horizontal_padding",
          "label": "Desktop Block Horizontal Padding",
          "default": "4px"
        },
        {
          "type": "text",
          "id": "background_horizontal_padding_mobile",
          "label": "Mobile Block Horizontal Padding",
          "default": "4px"
        },
        {
          "type": "text",
          "id": "background_vertical_padding",
          "label": "Desktop Block Vertical Padding",
          "default": "4px"
        },
        {
          "type": "text",
          "id": "background_vertical_padding_mobile",
          "label": "Mobile Block Vertical Padding",
          "default": "4px"
        },
        {
          "type": "range",
          "id": "z-index",
          "label": "Z-index",
          "min": 1,
          "max": 9,
          "step": 1,
          "default": 4
        }
      ]
    },
    {
      "type": "Block",
      "name": "Block",
      "settings": [
        {
          "type": "header",
          "content": "Layout Settings"
        },
        {
          "type": "select",
          "id": "width",
          "label": "Width",
          "options": [
            {
              "value": "w-100-l",
              "label": "100%"
            },
            {
              "value": "w-10-l",
              "label": "10%"
            },
            {
              "value": "w-20-l",
              "label": "20%"
            },
            {
              "value": "w-25-l",
              "label": "25%"
            },
            {
              "value": "w-third-l",
              "label": "33%"
            },
            {
              "value": "w-40-l",
              "label": "40%"
            },
            {
              "value": "w-50-l",
              "label": "50%"
            },
            {
              "value": "w-60-l",
              "label": "60%"
            },
            {
              "value": "w-two-thirds-l",
              "label": "66%"
            },
            {
              "value": "w-70-l",
              "label": "70%"
            },
            {
              "value": "w-75-l",
              "label": "75%"
            },
            {
              "value": "w-80-l",
              "label": "80%"
            },
            {
              "value": "w-90-l",
              "label": "90%"
            }
          ]
        },
        {
          "type": "select",
          "id": "mobile_width",
          "label": "Mobile Width",
          "options": [
            {
              "value": "w-100",
              "label": "100%"
            },
            {
              "value": "w-25",
              "label": "25%"
            },
            {
              "value": "w-third",
              "label": "33%"
            },
            {
              "value": "w-50",
              "label": "50%"
            },
            {
              "value": "w-two-thirds",
              "label": "66%"
            },
            {
              "value": "w-75",
              "label": "75%"
            }
          ],
          "default": "w-100"
        },
        {
          "type": "header",
          "content": "Background Settings"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Background Image"
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "Mobile Background Image"
        },
        {
          "type": "text",
          "id": "background_horizontal_padding",
          "label": "Desktop Block Horizontal Padding",
          "default": "4px"
        },
        {
          "type": "text",
          "id": "background_horizontal_padding_mobile",
          "label": "Mobile Block Horizontal Padding",
          "default": "4px"
        },
        {
          "type": "select",
          "id": "background_attachment",
          "label": "Background Attachment",
          "options": [
            {
              "value": "scroll",
              "label": "Scroll"
            },
            {
              "value": "fixed",
              "label": "Fixed"
            }
          ],
          "default": "scroll"
        },
        {
          "type": "select",
          "id": "background_size",
          "label": "Background Size",
          "options": [
            {
              "value": "cover",
              "label": "Cover"
            },
            {
              "value": "contain",
              "label": "Contain"
            },
            {
              "value": "auto 100%",
              "label": "Contain Height"
            },
            {
              "value": "100% auto",
              "label": "Contain Width"
            },
            {
              "value": "100% 100%",
              "label": "Squish"
            }
          ],
          "default": "cover"
        },
        {
          "type": "select",
          "id": "mobile_background_size",
          "label": "Mobile Background Size",
          "options": [
            {
              "value": "cover",
              "label": "Cover"
            },
            {
              "value": "contain",
              "label": "Contain"
            },
            {
              "value": "auto 100%",
              "label": "Contain Height"
            },
            {
              "value": "100% auto",
              "label": "Contain Width"
            },
            {
              "value": "100% 100%",
              "label": "Squish"
            }
          ],
          "default": "cover"
        },
        {
          "type": "select",
          "id": "background_position",
          "label": "Background Position",
          "options": [
            {
              "value": "top left",
              "label": "Top Left"
            },
            {
              "value": "top center",
              "label": "Top Center"
            },
            {
              "value": "top right",
              "label": "Top Right"
            },
            {
              "value": "left center",
              "label": "Middle Left"
            },
            {
              "value": "center center",
              "label": "Center"
            },
            {
              "value": "right center",
              "label": "Middle Right"
            },
            {
              "value": "left bottom",
              "label": "Bottom Left"
            },
            {
              "value": "center bottom",
              "label": "Bottom Center"
            },
            {
              "value": "right bottom",
              "label": "Bottom Right"
            }
          ],
          "default": "center center"
        },
        {
          "type": "text",
          "id": "video_url",
          "label": "Video mp4 url (Desktop)",
          "info": "To get mp4 file: 1) Go to vimeo.com, 2) Click on desired video, 3) click on settings, 4) Click on Video file"
        },
        {
          "type": "text",
          "id": "video_url_mobile",
          "label": "Video mp4 url (Mobile)",
          "info": "To get mp4 file: 1) Go to vimeo.com, 2) Click on desired video, 3) click on settings, 4) Click on Video file"
        },
        {
          "type": "image_picker",
          "id": "video_image",
          "label": "Video background image",
          "info": "Preview image while video loads"
        },
        {
          "type": "color",
          "id": "bg_color",
          "label": "Background color"
        },
        {
          "type": "color",
          "id": "overlay",
          "label": "Overlay color"
        },
        {
          "type": "range",
          "id": "overlay_opacity",
          "label": "Overlay Opacity",
          "min": 10,
          "max": 90,
          "step": 10,
          "default": 40
        },
        {
          "type": "header",
          "content": "Content Layout Settings"
        },
        {
          "type": "color",
          "id": "content_color",
          "label": "Content Background Color"
        },
        {
          "type": "select",
          "id": "content_position",
          "label": "Desktop Content Position",
          "options": [
            {
              "value": "items-start-l justify-start-l",
              "label": "Top Left"
            },
            {
              "value": "items-start-l justify-center-l",
              "label": "Top Center"
            },
            {
              "value": "items-start-l justify-end-l",
              "label": "Top Right"
            },
            {
              "value": "items-center-l justify-start-l",
              "label": "Middle Left"
            },
            {
              "value": "items-center-l justify-center-l",
              "label": "Middle Center"
            },
            {
              "value": "items-center-l justify-end-l",
              "label": "Middle Right"
            },
            {
              "value": "items-end-l justify-start-l",
              "label": "Bottom Left"
            },
            {
              "value": "items-end-l justify-center-l",
              "label": "Bottom Center"
            },
            {
              "value": "items-end-l justify-end-l",
              "label": "Bottom Right"
            }
          ],
          "default": "items-center-l justify-center-l"
        },
        {
          "type": "select",
          "id": "content_mobile_position",
          "label": "Mobile Content Position",
          "options": [
            {
              "value": "items-start justify-start",
              "label": "Top Left"
            },
            {
              "value": "items-start justify-center",
              "label": "Top Center"
            },
            {
              "value": "items-start justify-end",
              "label": "Top Right"
            },
            {
              "value": "items-center justify-start",
              "label": "Middle Left"
            },
            {
              "value": "items-center justify-center",
              "label": "Middle Center"
            },
            {
              "value": "items-center justify-end",
              "label": "Middle Right"
            },
            {
              "value": "items-end justify-start",
              "label": "Bottom Left"
            },
            {
              "value": "items-end justify-center",
              "label": "Bottom Center"
            },
            {
              "value": "items-end justify-end",
              "label": "Bottom Right"
            },
            {
              "value": "content-below-image",
              "label": "Below Image"
            }
          ],
          "default": "items-center justify-center"
        },
        {
          "type": "text",
          "id": "content_vertical_margin",
          "label": "Desktop Content vertical margin",
          "default": "4px"
        },
        {
          "type": "text",
          "id": "content_vertical_margin_mobile",
          "label": "Mobile Content vertical margin",
          "default": "4px"
        },
        {
          "type": "text",
          "id": "content_horizontal_margin",
          "label": "Desktop Content Horizontal Margin",
          "default": "4px"
        },
        {
          "type": "text",
          "id": "content_horizontal_margin_mobile",
          "label": "Mobile Content Horizontal Margin",
          "default": "4px"
        },
        {
          "type": "text",
          "id": "content_vertical_padding",
          "label": "Desktop Content Vertical Padding",
          "default": "4px"
        },
        {
          "type": "text",
          "id": "content_vertical_padding_mobile",
          "label": "Content Vertical Padding Mobile",
          "default": "4px"
        },
        {
          "type": "text",
          "id": "content_horizontal_padding",
          "label": "Desktop Content Horizontal Padding",
          "default": "4px"
        },
        {
          "type": "text",
          "id": "content_horizontal_padding_mobile",
          "label": "Mobile Content Horizontal Padding",
          "default": "4px"
        },
        {
          "type": "header",
          "content": "Text Format Settings"
        },
        {
          "type": "select",
          "id": "text_align",
          "label": "Desktop Text Alignment",
          "options": [
            {
              "value": "tl-l",
              "label": "Left"
            },
            {
              "value": "tc-l",
              "label": "Center"
            },
            {
              "value": "tr-l",
              "label": "Right"
            }
          ],
          "default": "tc-l"
        },
        {
          "type": "select",
          "id": "text_align_mobile",
          "label": "Mobile Text Alignment",
          "options": [
            {
              "value": "tl",
              "label": "Left"
            },
            {
              "value": "tc",
              "label": "Center"
            },
            {
              "value": "tr",
              "label": "Right"
            }
          ],
          "default": "tc"
        },
        {
          "type": "select",
          "id": "title_element",
          "label": "Title Element",
          "options": [
            {
              "value": "h1",
              "label": "Heading 1"
            },
            {
              "value": "h2",
              "label": "Heading 2"
            },
            {
              "value": "h3",
              "label": "Heading 3"
            },
            {
              "value": "h4",
              "label": "Heading 4"
            },
            {
              "value": "h5",
              "label": "Heading 5"
            }
          ],
          "default": "h3"
        },
        {
          "type": "select",
          "id": "title_font_family",
          "label": "Title Font",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium ",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "MonumentGrotesk-Regular"
        },
        {
          "type": "text",
          "id": "title_size",
          "label": "Desktop Title Font Size",
          "default": "12px"
        },
        {
          "type": "text",
          "id": "title_size_mobile",
          "label": "Mobile Font Size 123",
          "default": "12px"
        },
        {
          "type": "text",
          "id": "subtitle_size",
          "label": "Desktop Subtitle Font Size",
          "default": "12px"
        },
        {
          "type": "text",
          "id": "subtitle_size_mobile",
          "label": "Mobile Subtitle Font Size",
          "default": "12px"
        },
        {
          "type": "text",
          "id": "pretitle_size",
          "label": "Desktop Pretitle Font Size",
          "default": "4px"
        },
        {
          "type": "text",
          "id": "pretitle_size_mobile",
          "label": "Mobile Pretitle Font Size",
          "default": "12px"
        },
        {
          "type": "text",
          "id": "copy_size",
          "label": "Copy Font Size",
          "default": "12px"
        },
        {
          "type": "select",
          "id": "copy_font_family",
          "label": "Copy Font",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium ",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "MonumentGrotesk-Regular"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text color"
        },
        {
          "type": "text",
          "id": "pretitle",
          "label": "Pretitle"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "text",
          "id": "subtitle",
          "label": "Subtitle"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Content"
        },
        {
          "type": "image_picker",
          "id": "content_image",
          "label": "Image"
        },
        {
          "type": "header",
          "content": "Button Settings"
        },
        {
          "type": "text",
          "id": "link_title",
          "label": "Link title"
        },
        {
          "type": "url",
          "id": "url",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "link_target",
          "label": "Link Target",
          "options": [
            {
              "value": "_blank",
              "label": "New Window"
            },
            {
              "value": "_self",
              "label": "Same Window"
            }
          ],
          "default": "_self"
        },
        {
          "type": "text",
          "id": "link_title_two",
          "label": "Link title two"
        },
        {
          "type": "url",
          "id": "url_two",
          "label": "Link two"
        },
        {
          "type": "select",
          "id": "link_target_two",
          "label": "Link Target",
          "options": [
            {
              "value": "_blank",
              "label": "New Window"
            },
            {
              "value": "self",
              "label": "Same Window"
            }
          ],
          "default": "self"
        },
        {
          "type": "text",
          "id": "button_horizontal_padding",
          "label": "Button Horizontal Padding",
          "default": "4px"
        },
        {
          "type": "text",
          "id": "button_vertical_padding",
          "label": "Button Vertical Padding",
          "default": "4px"
        },
        {
          "type": "text",
          "id": "button_font_size",
          "label": "Button Font Size",
          "default": "4px"
        },
        {
          "type": "color",
          "id": "btn_color",
          "label": "Button bg color"
        },
        {
          "type": "color",
          "id": "btn_text_color",
          "label": "Button text color"
        },
        {
          "type": "checkbox",
          "id": "button_border",
          "label": "Button Border",
          "default": false
        },
        {
          "type": "header",
          "content": "Interactivity"
        },
        {
          "type": "select",
          "id": "link_element",
          "label": "Link Element",
          "options": [
            {
              "value": "",
              "label": "Button Only"
            },
            {
              "value": "content",
              "label": "Content Area"
            },
            {
              "value": "block",
              "label": "Entire Block"
            }
          ],
          "default": ""
        },
        {
          "type": "select",
          "id": "background_hover",
          "label": "Background Hover Effect",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "push-hover",
              "label": "Push"
            },
            {
              "value": "pull-hover",
              "label": "Pull"
            },
            {
              "value": "dim",
              "label": "Dim"
            },
            {
              "value": "push-hover dim",
              "label": "Push Dim"
            }
          ],
          "default": "push-hover"
        },
        {
          "type": "select",
          "id": "background_entrance",
          "label": "Image Entrance Effect",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "surface fade-in",
              "label": "Fade In"
            },
            {
              "value": "surface fade-in-scale-up",
              "label": "Fade In Scale Up"
            },
            {
              "value": "surface fade-in-scale-down",
              "label": "Fade In Scale Down"
            },
            {
              "value": "surface fade-in-up",
              "label": "Fade In Rise"
            },
            {
              "value": "surface fade-in-left",
              "label": "Fade In Left"
            },
            {
              "value": "surface fade-in-right",
              "label": "Fade In Right"
            },
            {
              "value": "surface clip-up",
              "label": "Clip Up"
            },
            {
              "value": "surface clip-down",
              "label": "Clip Down"
            },
            {
              "value": "surface clip-left",
              "label": "Clip Left"
            },
            {
              "value": "surface clip-right",
              "label": "Clip Right"
            },
            {
              "value": "surface clip-split-v",
              "label": "Vertical Split Clip"
            },
            {
              "value": "surface clip-split-h",
              "label": "Horizontal Split Clip"
            }
          ],
          "default": ""
        },
        {
          "type": "select",
          "id": "content_entrance",
          "label": "Content Entrance Effect",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "surface fade-in",
              "label": "Fade In"
            },
            {
              "value": "surface fade-in-scale-up",
              "label": "Fade In Scale Up"
            },
            {
              "value": "surface fade-in-scale-down",
              "label": "Fade In Scale Down"
            },
            {
              "value": "surface fade-in-up",
              "label": "Fade In Rise"
            },
            {
              "value": "surface fade-in-left",
              "label": "Fade In Left"
            },
            {
              "value": "surface fade-in-right",
              "label": "Fade In Right"
            }
          ],
          "default": ""
        }
      ]
    },
    {
      "type": "hotspot",
      "name": "Hotspot",
      "settings": [
        {
          "type": "header",
          "content": "Hotspot Settings"
        },
        {
          "type": "product",
          "id": "product",
          "label": "Product"
        },
        {
          "type": "select",
          "id": "relative_to",
          "label": "Floats within",
          "info": "Important: When adding to a block, make sure that the floating item is ordered before the section you want it to show in.",
          "options": [
            {
              "value": "block",
              "label": "Block"
            },
            {
              "value": "section",
              "label": "Section"
            }
          ]
        },
        {
          "type": "color",
          "id": "spot_color",
          "label": "Spot Color"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text Color",
          "default": "#000"
        },
        {
          "type": "header",
          "content": "Floating Layout Settings"
        },
        {
          "type": "checkbox",
          "id": "display_desktop",
          "label": "Display on desktop",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "display_mobile",
          "label": "Display on mobile",
          "default": true
        },
        {
          "type": "select",
          "id": "content_position",
          "label": "Desktop Content Position",
          "options": [
            {
              "value": "ap-tl-l",
              "label": "Top Left"
            },
            {
              "value": "ap-tc-l",
              "label": "Top Center"
            },
            {
              "value": "ap-tr-l",
              "label": "Top Right"
            },
            {
              "value": "ap-ml-l",
              "label": "Middle Left"
            },
            {
              "value": "ap-mc-l",
              "label": "Middle Center"
            },
            {
              "value": "ap-mr-l",
              "label": "Middle Right"
            },
            {
              "value": "ap-bl-l",
              "label": "Bottom Left"
            },
            {
              "value": "ap-bc-l",
              "label": "Bottom Center"
            },
            {
              "value": "ap-br-l",
              "label": "Bottom Right"
            }
          ],
          "default": "ap-mc-l"
        },
        {
          "type": "select",
          "id": "content_mobile_position",
          "label": "Mobile Content Position",
          "options": [
            {
              "value": "ap-tl",
              "label": "Top Left"
            },
            {
              "value": "ap-tc",
              "label": "Top Center"
            },
            {
              "value": "ap-tr",
              "label": "Top Right"
            },
            {
              "value": "ap-ml",
              "label": "Middle Left"
            },
            {
              "value": "ap-mc",
              "label": "Middle Center"
            },
            {
              "value": "ap-mr",
              "label": "Middle Right"
            },
            {
              "value": "ap-bl",
              "label": "Bottom Left"
            },
            {
              "value": "ap-bc",
              "label": "Bottom Center"
            },
            {
              "value": "ap-br",
              "label": "Bottom Right"
            }
          ],
          "default": "ap-mc"
        },
        {
          "type": "text",
          "id": "background_horizontal_padding",
          "label": "Desktop Block Horizontal Padding",
          "default": "4px"
        },
        {
          "type": "text",
          "id": "background_horizontal_padding_mobile",
          "label": "Mobile Block Horizontal Padding",
          "default": "4px"
        },
        {
          "type": "text",
          "id": "background_vertical_padding",
          "label": "Desktop Block Vertical Padding",
          "default": "4px"
        },
        {
          "type": "text",
          "id": "background_vertical_padding_mobile",
          "label": "Mobile Block Vertical Padding",
          "default": "4px"
        },
        {
          "type": "range",
          "id": "z-index",
          "label": "Z-index",
          "min": 1,
          "max": 9,
          "step": 1,
          "default": 4
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Custom content",
      "category": "Advanced",
      "settings": {},
      "blocks": []
    }
  ]
}
{% endschema -%}