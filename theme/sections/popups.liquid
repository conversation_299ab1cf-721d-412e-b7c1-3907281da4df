<aside popups id="{{ section.id }}" class="fixed h-screen w-screen inset-0 left-0 right-0 top-0 bottom-0 pointer-none pointer-events-none flex items-center justify-center z-50" neptune-liquid="{topic:Popup}">
	<template>
		{% raw %}
		  {% if Popup.active %}
		  {% assign active = Popups[Popup.active] %}

		  {% if active.block %}
		    <div class="absolute bg-black opacity-50 o-50 inset-0 left-0 right-0 top-0 bottom-0 pointer-all pointer-events-auto popup-overlay" onclick="{% if active.close_outside %}Popup.close(){% else %}return false;{% endif %}"></div>
				<style>
					html:has(.popup-overlay) {
						height:100%;
						overflow:hidden;
					}
				</style>
		  {% endif %}

		  <article
        id="{{ Popup.active }}"
        class="
          pointer-events-auto pointer-all absolute ma4 m-4
          {{ active.horizontal_position_mobile }}
          {{ active.vertical_position_mobile }}
          {{ active.horizontal_position_desktop }}
          {{ active.vertical_position_desktop }}
          {{ active.width_mobile }}
          {{ active.width_desktop }}
          rounded-sm overflow-hidden
          popup
          popup--animation-{{ active.animation_type }}
          {% if active.animation_type == 'slide' %}
            {{ active.animation_slide_direction }}
            transition-transform
          {% else %}
            transition-opacity
          {% endif %}
        "
        style="
          background: {{ active.background }};
          {% if active.max_width != blank -%}
            max-width: {{ active.max_width }};
          {%- endif %}
          {% if active.animation_duration != blank -%}
            transition-duration: {{ active.animation_duration }}ms;
          {%- endif %}
          {% if active.animation_easing != blank -%}
            transition-timing-function: {{ active.animation_easing }};
          {%- endif %}
        "
        data-animation-type="{{ active.animation_type | escape }}"
      >

		    {% if active.link != blank %}
		    <a {{'h'}}ref="{{ active.link}}" class="block db relative">
		    {% endif %}

			    {% if active.video != blank %}
			    	<video class="w-full w-100 h-auto" preload="true" {{'s'}}rc="{{ active.video }}" autoplay playsinline muted mute loop {{'p'}}oster="{{ active.video_poster }}"><source {{'s'}}rc="{{ active.video }}"></video>
			    {% endif %}
			    {% if active.image != blank %}
			    	<img {{'s'}}rc="{{ active.image }}" class="w-full w-100 h-auto" alt="">
			    {% endif %}
			    {% if active.title != blank or active.content != blank or active.international != blank %}
				    <div class="p-6 {{ active.text_align }} {{ active.text_align_mobile }}">
					    {% if active.title != blank %}
					      <h2 class="font-pop mb-2 text-sm uppercase">
					        {{ active.title }}
					      </h2>
					    {% endif %}
					    {% if active.content != blank %}
					      <div class="">
					        {{ active.content }}
					      </div>
					    {% endif %}
					    {% if active.international != blank %}
					      <div class="mb-2 leading-[26px] text-sm">
					      	{% assign international = active.international | newline_split %}
					      	<ul class="popup__international">
					      		{% for item in international %}
							      	{% assign message = item | split: '|' %}
							      	<li data-international="{{message[0]|strip}}">{{message[1]}}</li>
						      	{% endfor %}
					      	</ul>
					      </div>
					    {% endif %}
					    {% if active.custom_liquid != blank %}
					      <div class="">
					        {{ active.custom_liquid }}
					      </div>
					    {% endif %}
				    </div>
			    {% endif %}

			    {% if active.title_svg != blank %}
			    	<span class="absolute w-full h-full w-100 h-100 top-0 left-0 flex flex-column flex-col items-center justify-center">
		        	<img {{'s'}}rc="{{ active.title_svg }}" class="dib inline-block" style="max-width: 100%; width: {{active.title_image_width }};" alt="">
		        </span>
		      {% endif %}
		    
		    {% if active.link != blank %}
		    </a>
		    {% endif %}

		    {% if active.close_inside %}
		    <button class="absolute top-1 right-1 w2 h2 w-6 h-6 flex items-center justify-center rounded-full br-100 bg-near-white black text-black p-0 m-0 bn" onclick="Popup.close(); return false;">
		    	{% endraw %}{% render 'icon' icon:'x' width:16 height:16 %}{% raw %}
		    </button>
		    {% endif %}

		  </article>
		  {% endif %}
	  {% endraw %}
	</template>
</aside>

<script>
	window.Popups = {
		{% for block in section.blocks %}
		{{ block.id | json }}: {{ block.settings | json }}
		{%- unless forloop.last %},{% endunless %}
		{% endfor %}
	};
</script>

{% schema %}
{
  "name": "Popups",
	"blocks": [
		{
			"type": "popup",
			"name": "Popup",
			"settings": [
				{
          "type": "header",
          "content": "Position Settings"
        },
				{
					"type": "select",
					"id": "vertical_position_mobile",
					"label": "Vertical Position (Mobile)",
					"options": [
						{
							"label":"Top",
							"value":"top-0"
						},
						{
							"label":"Bottom",
							"value":"bottom-0"
						},
						{
							"label":"Center",
							"value":"top-1/2"
						}
					]
				},
				{
					"type": "select",
					"id": "horizontal_position_mobile",
					"label": "Horizontal Position (Mobile)",
					"options": [
						{
							"label":"Left",
							"value":"left-0 right-auto"
						},
						{
							"label":"Right",
							"value":"right-0 left-auto"
						},
						{
							"label":"Center",
							"value":"left-auto right-auto"
						}
					]
				},
				{
					"type": "select",
					"id": "vertical_position_desktop",
					"label": "Vertical Position (Desktop)",
					"options": [
						{
							"label":"Top",
							"value":"lg:top-0 lg:bottom-auto"
						},
						{
							"label":"Bottom",
							"value":"lg:bottom-0 lg:top-auto"
						},
						{
							"label":"Center",
							"value":"lg:top-1/2"
						}
					]
				},
				{
					"type": "select",
					"id": "horizontal_position_desktop",
					"label": "Horizontal Position (Desktop)",
					"options": [
						{
							"label":"Left",
							"value":"lg:left-0 lg:right-auto"
						},
						{
							"label":"Right",
							"value":"lg:right-0 lg:left-auto"
						},
						{
							"label":"Center",
							"value":"lg:left-auto lg:right-auto"
						}
					]
				},
				{
          "type": "header",
          "content": "Size Settings"
        },
				{
					"type": "select",
					"id": "width_desktop",
					"label": "Width Desktop",
					"options": [
            {
              "value": "w-20-l lg:w-1/5",
              "label": "20%"
            },
            {
              "value": "w-25-l lg:w-1/4",
              "label": "25%"
            },
            {
              "value": "w-third-l lg:w-1/3",
              "label": "33%"
            },
            {
              "value": "w-50-l lg:w-1/2",
              "label": "50%"
            },
            {
              "value": "w-two-thirds-l lg:w-2/3",
              "label": "66%"
            },
            {
              "value": "w-75-l lg:w-3/4",
              "label": "75%"
            },
						{
              "value": "w-100-l lg:w-full",
              "label": "100%"
            }
					]
				},
				{
					"type": "select",
					"id": "width_mobile",
					"label": "Width Mobile",
					"options": [
            {
              "value": "w-third w-1/3",
              "label": "33%"
            },
            {
              "value": "w-50 w-1/2",
              "label": "50%"
            },
            {
              "value": "w-two-thirds w-2/3",
              "label": "66%"
            },
            {
              "value": "w-75 w-3/4",
              "label": "75%"
            },
						{
              "value": "w-100 w-full",
              "label": "100%"
            }
					]
				},
				{
					"type": "text",
					"id": "max_width",
					"label": "Max width"
				},
				{
          "type": "header",
          "content": "Content Settings"
        },
				{
					"type": "url",
					"id": "link",
					"label": "Link"
				},
				{
					"type": "color",
					"id": "background",
					"label": "Background Color"
				},
				{
					"type": "image_picker",
					"id": "image",
					"label": "Image Content"
				},
				{
					"type": "text",
					"id": "video",
					"label": "Video Content (url)"
				},
				{
					"type": "image_picker",
					"id": "video_poster",
					"label": "Video Poster"
				},
				{
          "type": "header",
          "content": "SVG Overlay"
        },
        {
          "type": "text",
          "label": "Overlay SVG",
          "id": "title_svg",
          "info": "Choose an SVG from your [files](/admin/settings/files) and paste the file url in the field above."
        },
        {
          "type": "text",
          "label": "Overlay Image/SVG Max width",
          "id": "title_image_width",
          "info": "Use a pixel or percentage value here.",
          "default": "100%"
        },
        {
          "type": "header",
          "content": "Text Settings"
        },
        {
          "type": "select",
          "id": "text_align",
          "label": "Text Alignment (Desktop)",
          "options": [
            {
              "value": "lg:text-left",
              "label": "Left"
            },
            {
              "value": "lg:text-center",
              "label": "Center"
            },
            {
              "value": "lg:text-right",
              "label": "Right"
            },
            {
              "value": "lg:text-justify lg:text-justify-last",
              "label": "Justify"
            }
          ],
          "default": "lg:text-left"
        },
        {
          "type": "select",
          "id": "text_align_mobile",
          "label": "Text Alignment (Mobile)",
          "options": [
            {
              "value": "text-left",
              "label": "Left"
            },
            {
              "value": "text-center",
              "label": "Center"
            },
            {
              "value": "text-right",
              "label": "Right"
            },
            {
              "value": "text-justify text-justify-last",
              "label": "Justify"
            }
          ],
          "default": "text-left"
        },
        {
					"type": "text",
					"id": "alt_text",
					"label": "Overlay Alt Text"
				},
        {
          "type": "header",
          "content": "Caption"
        },
        {
					"type": "text",
					"id": "title",
					"label": "Title"
				},
				{
					"type": "richtext",
					"id": "content",
					"label": "Content"
				},
				{
					"type": "textarea",
					"id": "international",
					"label": "International Content"
				},
				{
          "type": "header",
          "content": "Event Settings"
        },
				{
					"type": "checkbox",
					"id": "block",
					"label": "Interaction-Blocking Overlay over Page"
				},
				{
					"type": "checkbox",
					"id": "close_outside",
					"label": "Close popup when clicking outside of Popup, requires Overlay"
				},
				{
					"type": "checkbox",
					"id": "close_inside",
					"label": "Display close icon in popup"
				},
				{
					"type": "select",
					"id": "track",
					"label": "Track User by",
					"options": [
						{
							"label":"Session",
							"value":"session"
						},
						{
							"label":"Device",
							"value":"local"
						}
					]
				},
				{
					"type": "select",
					"id": "on",
					"label": "Fire on Event",
					"options": [
						{
							"label":"Page Load",
							"value":"DOMContentLoaded"
						},
						{
							"label":"Scroll",
							"value":"scroll"
						},
						{
							"label":"Exit Intent",
							"value":"mouseout"
						}
					]
				},
				{
					"type": "number",
					"id": "minimum_page_views",
					"label": "Minimum Page Views"
				},
				{
					"type": "text",
					"id": "including",
					"label": "Page inclusions (count only)",
					"info": "Comma-separated paths, example: collections/sale, with * for wildcard like collections*"
				},
				{
					"type": "text",
					"id": "excluding",
					"label": "Page exclusions (do not count)",
					"info": "Comma-separated paths, example: collections/sale, with * for wildcard like collections*"
				},
				{
					"type": "number",
					"id": "maximum_opens",
					"label": "Maximum Popup Opens"
				},
				{
					"type": "number",
					"id": "maximum_closes",
					"label": "Maximum Popup Closes"
				},
				{
					"type": "number",
					"id": "scrolled_past",
					"label": "Min Scroll Point",
					"info": "Only supported with scroll Event"
				},
				{
					"type": "number",
					"id": "delay",
					"label": "Show Popup after",
					"info": "Milliseconds"
				},
        {
          "type": "header",
          "content": "Animation Settings"
        },
        {
          "type": "select",
          "id": "animation_type",
          "label": "Animation Type",
          "options": [
            {
              "value": "none",
              "label": "None"
            },
            {
              "value": "fade",
              "label": "Fade In/Out"
            },
            {
              "value": "slide",
              "label": "Slide In/Out"
            }
          ],
          "default": "none"
        },
        {
          "type": "select",
          "id": "animation_slide_direction",
          "label": "Slide Direction",
          "info": "Direction popup will slide from",
          "options": [
          	{
              "value": "",
              "label": "None"
            },
            {
              "value": "from-top",
              "label": "From Top"
            },
            {
              "value": "from-right",
              "label": "From Right"
            },
            {
              "value": "from-bottom",
              "label": "From Bottom"
            },
            {
              "value": "from-left",
              "label": "From Left"
            }
          ],
          "default": "",
          "visible_if": "{{ block.settings.animation_type == 'slide' }}"
        },
        {
          "type": "range",
          "id": "animation_duration",
          "min": 100,
          "max": 2000,
          "step": 100,
          "unit": "ms",
          "label": "Animation Duration",
          "default": 300
        },
        {
          "type": "select",
          "id": "animation_easing",
          "label": "Animation Easing",
          "options": [
            {
              "value": "linear",
              "label": "Linear"
            },
            {
              "value": "ease-in",
              "label": "Ease In"
            },
            {
              "value": "ease-out",
              "label": "Ease Out"
            },
            {
              "value": "ease-in-out",
              "label": "Ease In Out"
            }
          ],
          "default": "ease-in"
        },
				{
          "type": "header",
          "content": "Advanced Settings"
        },
        {
					"type": "liquid",
					"id": "js_display_logic",
					"label": "Advanced: JavaScript Display Logic"
				},
        {
					"type": "liquid",
					"id": "liquid_display_logic",
					"label": "Advanced: Liquid Display Logic"
				},
        {
					"type": "liquid",
					"id": "custom_liquid",
					"label": "Advanced: Custom Liquid Output"
				}
			]
		}
	]
}
{% endschema %}