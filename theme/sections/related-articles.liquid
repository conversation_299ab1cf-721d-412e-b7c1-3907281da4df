<section class="related-articles border-t border-gray mt-16 px-8">
  <h3 class="text-center MonumentGrotesk-Bold uppercase mt-16 mb-10">Related Articles</h3>
  <ul class="list-none pl-0 container flex lg:flex-row flex-col justify-center gap-y-16 gap-x-8">
    {% assign count = 0 %}
    {% for related in blogs.blog.articles %}
      {% unless article.url == related.url %}
        {% assign count = count | plus: 1 %}
        <div class="lg:w-1/3 w-full text-left">
          {% render 'article-card', article: related, show_image: section.settings.show_image, show_date: section.settings.show_date, show_excerpt: section.settings.show_excerpt, show_category: section.settings.show_category %}
        </div> 
      {% endunless %}
      {% if count >= 3 %}
        {% break %}
      {% endif %}
    {% endfor %}
    </ul>
</section>
 

{% schema %}
{
  "name": "Related Articles",
  "tag": "section",
  "settings": [
    {
      "type": "header",
      "content": "Display Settings"
    },
    {
      "type": "checkbox",
      "id": "show_desktop",
      "label": "Show desktop",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_mobile",
      "label": "Show mobile",
      "default": true
    },
    {
      "type": "header",
      "content": "Interactivity"
    },
    {
      "type": "select",
      "id": "scroll_effect",
      "label": "Scroll Effect",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "fade-in in-view:fade-in",
          "label": "Fade In"
        },
        {
          "value": "fade-in-rise in-view:fade-in-rise",
          "label": "Fade In and Rise"
        }
      ],
      "default": ""
    },
    {
      "type": "checkbox",
      "id": "show_image",
      "default": true,
      "label": "Show Image"
    },
    {
      "type": "checkbox",
      "id": "show_category",
      "default": true,
      "label": "Show Category"
    },
    {
      "type": "checkbox",
      "id": "show_date",
      "default": true,
      "label": "Show Date"
    },
    {
      "type": "checkbox",
      "id": "show_excerpt",
      "default": true,
      "label": "Show Excerpt"
    },
    {
      "type": "number",
      "id": "truncate_length",
      "label": "Truncate Length",
      "default": 130,
      "info": "The number of characters to truncate the article excerpt to."
    }
  ]
}
{% endschema %}
