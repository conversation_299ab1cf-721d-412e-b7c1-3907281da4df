{% schema %}
{
  "name": "Ticker",
  "settings": [
    {
      "type": "header",
      "content": "A/B Testing"
    },
    {
      "type": "checkbox",
      "id": "remove_section_condition",
      "label": "Remove Section",
      "default": false,
      "info": "This will hide the section in one of the experiment variations."
    },
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "text",
      "id": "position",
      "label": "Position",
      "info": "For collection pages. Set Ticker position among products."
    },
    {
      "type": "text",
      "id": "page",
      "label": "Page",
      "info": "For collection pages. Set Ticker position among pages."
    },
    {
      "type": "color",
      "id": "ticker_background_color",
      "label": "Background Color"
    },
    {
      "type": "header",
      "content": "Ticker Item Spacing"
    },
    {
      "type": "range",
      "id": "atb_vertical_padding",
      "label": "Vertical padding (Desktop)",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 30
    },
    {
      "type": "range",
      "id": "atb_horizontal_padding",
      "label": "Horizontal padding (Desktop)",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 15
    },
    {
      "type": "range",
      "id": "atb_vertical_padding_mobile",
      "label": "Vertical padding (Mobile)",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 15
    },
    {
      "type": "range",
      "id": "atb_horizontal_padding_mobile",
      "label": "Horizontal padding (Mobile)",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 15
    },
    {
      "type": "text",
      "label": "Font Size Override (Desktop)",
      "id": "atb_font_override"
    },
    {
      "type": "text",
      "label": "Font Size Override (Mobile)",
      "id": "atb_font_override_mobile"
    },
    {
      "type": "header",
      "content": "Additional Settings"
    },
    {
      "id": "speed",
      "type": "range",
      "label": "Speed",
      "default": 4,
      "min": 1,
      "max": 10
    },
    {
      "id": "direction",
      "type": "select",
      "label": "Direction",
      "options": [
        { "value": "reverse", "label": "Left to right" },
        { "value": "normal", "label": "Right to left" }
      ],
      "default": "normal"
    }
  ],
  "blocks": [
    {
      "type": "ticker_item",
      "name": "Ticker Item",
      "settings": [
        {
          "type": "header",
          "content": "Item Count"
        },
        {
          "id": "block_ticker_count",
          "type": "number",
          "label": "Item Count",
          "default": 1
        },
        {
          "type": "header",
          "content": "Image Settings"
        },
        {
          "type": "image_picker",
          "id": "block_ticker_image",
          "label": "Image"
        },
        {
          "type": "text",
          "id": "block_ticker_image_width",
          "label": "Image Width",
          "default": "250px"
        },
        {
          "type": "text",
          "id": "block_ticker_image_size",
          "label": "Image Resolution",
          "info": "If left blank image will use original resolution",
          "default": "250x"
        },
        {
          "type": "header",
          "content": "Text Settings"
        },
        {
          "type": "textarea",
          "id": "block_ticker_misc",
          "label": "Text"
        },
        {
          "type": "select",
          "id": "text_type",
          "label": "Typeface",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "font-pop",
              "label": "ABC Monument Grotesk Black"
            },
            {
              "value": "font-highlight",
              "label": "ABC Monument Grotesk Light"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "MonumentGrotesk-Regular"
        },
        {
          "type": "text",
          "id": "text_leading",
          "label": "Text Line Height",
          "default": "1.38"
        },
        {
          "type": "text",
          "id": "text_letter_spacing",
          "label": "Letter Spacing"
        },
        {
          "type": "select",
          "id": "text_align",
          "label": "Text Alignment (Desktop)",
          "options": [
            {
              "value": "lg:text-left",
              "label": "Left"
            },
            {
              "value": "lg:text-center",
              "label": "Center"
            },
            {
              "value": "lg:text-right",
              "label": "Right"
            },
            {
              "value": "lg:text-justify lg:text-justify-last",
              "label": "Justify"
            }
          ],
          "default": "lg:text-left"
        },
        {
          "type": "select",
          "id": "text_align_mobile",
          "label": "Text Alignment (Mobile)",
          "options": [
            {
              "value": "text-left",
              "label": "Left"
            },
            {
              "value": "text-center",
              "label": "Center"
            },
            {
              "value": "text-right",
              "label": "Right"
            },
            {
              "value": "text-justify text-justify-last",
              "label": "Justify"
            }
          ],
          "default": "text-left"
        },
        {
          "type": "color",
          "id": "block_ticker_color",
          "label": "Color"
        },
        {
          "type": "url",
          "id": "block_link",
          "label": "Link"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Ticker",
      "category": "Advanced",
      "blocks": []
    }
  ]
}
{% endschema %}

{% if section.settings.position != blank %}
  <div
    id="collectionGridBanner-{{section.id}}"
    class="collection-injection-banner w-full"
    collection-injection="{page:{{section.settings.page}} ,position:{{section.settings.position}} }"
  >
{% endif %}

<section
  dir="ltr"
  id="shopify-section-{{section.id}}"
  class="shopify-section section__ticker ticker {% if section.settings.remove_section_condition %} vwo-hide{% endif %}"
  {% if section.settings.ticker_background_color != blank %}
    style="background-color: {{ section.settings.ticker_background_color }};"
  {% endif %}
  data-speed="{{ section.settings.speed }}"
  data-direction="{{ section.settings.direction }}"
>
  <ul>
    <style>
      #shopify-section-{{section.id}} .ticker__item {
        padding:{{ section.settings.atb_vertical_padding }}px {{ section.settings.atb_horizontal_padding }}px;
      }
      {% if section.settings.atb_font_override != blank %}
        #shopify-section-{{section.id}} .ticker__item span {
          font-size: {{ section.settings.atb_font_override }};
        }
      {% endif %}
      @media (max-width: 960px) {
        #shopify-section-{{section.id}} .ticker__item {
          padding:{{ section.settings.atb_vertical_padding_mobile }}px {{ section.settings.atb_horizontal_padding_mobile }}px;
        }
        {% if section.settings.atb_font_override_mobile != blank %}
          #shopify-section-{{section.id}} .ticker__item span {
            font-size: {{ section.settings.atb_font_override_mobile }};
          }
        {% endif %}
      }
    </style>

    {% assign count = 0 %}
    {% for block in section.blocks %}
      {% style %}
        #shopify-section-{{section.id}} .ticker__link {
          line-height: {{ block.settings.text_leading }};
          letter-spacing: {{ block.settings.text_letter_spacing }}px;
        }
      {% endstyle %}
      {% assign count = count | plus: 1 %}
      {% for i in (1..block.settings.block_ticker_count) %}
        <li class="ticker__item">
					{% if block.settings.block_link != blank %}
						<a
							href="{{ block.settings.block_link }}"
							class="ticker__link w-auto flex flex-col whitespace-nowrap gap-y-4 items-center {{ block.settings.block_text_align }}"
						>
					{% else %}
						<span>
					{% endif %}
            {% if block.settings.block_ticker_image != blank %}
              <img
                src="{{ block.settings.block_ticker_image | img_url: block.settings.block_ticker_image_size }}"
                class="ticker__image"
                alt="{{ block.settings.block_ticker_image.alt }}"
                {% if block.settings.block_ticker_image_width != blank %}
                  style="max-width:{{ block.settings.block_ticker_image_width }}; width:{{ block.settings.block_ticker_image_width }};"
                {% endif %}
              >
            {% endif %}
            {% if block.settings.block_ticker_misc != blank %}
              <span
                class="block text-2xl lg:text-7xl {{ block.settings.block_ticker_font }} {{ block.settings.text_type }}"
                style="color: {{ block.settings.block_ticker_color }};"
              >
                {{ block.settings.block_ticker_misc }}
              </span>
            {% endif %}
					{% if block.settings.block_link != blank %}
						</a>
					{% else %}
						</span>
					{% endif %}
        </li>
      {% endfor %}
    {% endfor %}
  </ul>
</section>

{% if section.settings.position != blank %}
  </div>
{% endif %}

{% style %}
  .ticker {
    overflow: hidden;
    position: relative;
    padding: 0;
    margin: 0;
  }
  
  .ticker ul {
    white-space: nowrap;
    display: inline-flex;
    align-items: center;
    overflow: hidden;
    margin: 0;
  }

  .ticker li {
    list-style: none;
    display: inline-block;
    vertical-align: center;
  }

  .ticker span {
    display: inline-flex;
    align-items: center;
  }

  .ticker-wrapper {
    z-index: 1 !important;
  }
{% endstyle %}
