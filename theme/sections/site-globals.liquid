{% schema %}
	{
	  "name": "Globals",
	  "settings": []
	}
{% endschema %}

<div class="w-full p-4 mx-auto lg:p-8 lg:w-1/2">
	<h1 class="mb-8">{{ page.titext-lefte }}</h1>

	 <h3>Colors</h3>
		<p class="my-4"></p>
		<div class="flex flex-wrap p-0 m-0 list">

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-green-50" href="javascript:void()">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-primary"></span>
				<span data-copy="true">Primary <br/>-primary</span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-green-50" href="javascript:void()">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-secondary"></span>
				<span data-copy="true">Secondary <br/>-secondary</span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-green-50" href="javascript:void()">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-tertiary"></span>
				<span data-copy="true">Tertiary <br/>-tertiary</span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-green-50" href="javascript:void()">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-light"></span>
				<span data-copy="true">Light <br/>-light</span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-green-50" href="javascript:void()">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-dark"></span>
				<span data-copy="true">Dark <br/>-dark</span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-green-50" href="javascript:void()">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-pop"></span>
				<span data-copy="true">Pop <br/>-pop</span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-green-50" href="javascript:void()">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-highlight"></span>
				<span data-copy="true">Highlight <br/>-highlight</span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-green-50" href="javascript:void()">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-body"></span>
				<span data-copy="true">Body <br/>-body</span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-green-50" href="javascript:void()">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-heading"></span>
				<span data-copy="true">Heading <br/>-header</span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-green-50" href="javascript:void()">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-custom-1"></span>
				<span data-copy="true">Custom 1 <br/>-custom-1</span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-green-50" href="javascript:void()">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-custom-2"></span>
				<span data-copy="true">Custom 2 <br/>-custom-2</span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-green-50" href="javascript:void()">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-custom-3"></span>
				<span data-copy="true">Custom 3 <br/>-custom-3</span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-green-50" href="javascript:void()">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-custom-4"></span>
				<span data-copy="true">Custom 4 <br/>-custom-4</span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-green-50" href="javascript:void()">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-custom-5"></span>
				<span data-copy="true">Custom 5 <br/>-custom-5</span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-green-50" href="javascript:void()">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-custom-6"></span>
				<span data-copy="true">Custom 6 <br/>-custom-6</span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-green-50" href="javascript:void()">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-grey-1"></span>
				<span data-copy="true">Grey 1 <br/>-grey-1</span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-green-50" href="javascript:void()">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-grey-2"></span>
				<span data-copy="true">Grey 2 <br/>-grey-2</span>
			</a>

		</div>

		<hr class="my-5">

		<h3>Font Types</h3>
		<p class="my-4"></p>

		<h3 class="my-4 text-base font-body">.font-body</h3>
		<h3 class="my-4 text-base font-heading">.font-heading</h3>
		<h3 class="my-4 text-base font-subheading">.font-subheading</h3>
		<h3 class="my-4 font-legal">.font-legal</h3>
		<h3 class="my-4 font-caption-caps">.font-caption-caps</h3>
		<h3 class="my-4 font-desktop-nav">.font-desktop-nav</h3>
		<h3 class="my-4 font-caption">.font-caption</h3>
		<h3 class="my-4 font-link">.font-link</h3>

		<hr class="my-5">

		<h3>Font Families</h3>
		<p class="my-4"></p>

		<h3 class="my-4 text-base font-body">MonumentGrotesk-Regular: .font-body</h3>
		<h3 class="my-4 text-base font-heading">MonumentGrotesk-Bold: .font-heading</h3>
		<h3 class="my-4 text-base font-subheading">Futura Condensed BQ: .font-subheading</h3>
		<h3 class="my-4 text-base font-alt">Libre Caslon Text: .font-alt</h3>
		<h3 class="my-4 text-base font-pop">ABC Monument Grotesk Black: .font-pop</h3>
		<h3 class="my-4 text-base font-highlight">ABC Monument Grotesk Light: .font-highlight</h3>

		<hr class="my-5">

		<h3>Forms & Buttons</h3>
		<p class="mt-4 mb-2"></p>

		<form>

			<p class="mt-4 mb-2 underline">Text Input</p>
			<div class="mt-4 field">
	      <label class="">
	        Label
	      </label>
	      <input type="text" placeholder="Text Input" class="max-w-lg">
	    </div>

	    <p class="mt-4 mb-2 underline">Text Area</p>

	    <div class="field">
		    <label for="comment" class="">Comments <span class="">(optional)</span></label>
		    <textarea id="comment" name="comment" class="max-w-lg" aria-describeblocky="comment-desc"></textarea>
		  </div>

		  <p class="mt-4 mb-2 underline">Select</p>

		  <div class="max-w-lg field field--select">
			  <select class="">
				  <option>Select with arrow </option>
				  <option>No external borderckground image</option>`
				  <option>No wrapper</option>
				</select>
			</div>

	    <p class="mt-4 mb-2 underline">Checkbox & Radios</p>

	   	<div class="field">
	   		<label for="checkbox1">
	        <input type="checkbox" id="checkbox1">
	        <span class=""></span> 
	        <span>
	          Checkbox Label
	        </span>
	      </label>
	   		<label for="checkbox2">
	        <input type="checkbox" id="checkbox2">
	        <span class=""></span> 
	        <span>
	          Checkbox Label
	        </span>
	      </label>
			</div>

	   	<div class="field">
	   		<label for="radio1">
	        <input type="radio" id="radio1" name="radio">
	        <span class=""></span> 
	        <span>
	          Radio Button Label
	        </span>
	      </label>

	   		<label for="radio2">
	        <input type="radio" id="radio2" name="radio">
	        <span class=""></span> 
	        <span>
	          Radio Button Label
	        </span>
	      </label>
			</div>

			<p class="mt-4 mb-2 underline">Quanity Input</p>

			<div class="flex items-center w-1/3 field field-plus-minus">
	      <button onclick="Neptune.cart.changeItem({{forloop.index0}},{{item.quantity | minus: 1}})">{% render 'icon' icon: 'minus' width:16 height:16 strokeWidth:1 %}</button>
	      <input type="text" oninput="Neptune.cart.changeItem({{forloop.index0}},this.value)" value="{{item.quantity}}"></input>
	      <button onclick="Neptune.cart.changeItem({{forloop.index0}},{{item.quantity | plus: 1}})">{% render 'icon' icon: 'plus' width:16 height:16 strokeWidth:1 %}</button>
	    </div>

	    <p class="mt-4 mb-2 underline">Tools</p>

	    <div class="flex mb-4">
	    	<button class="btn-control">
      		{% render 'icon' icon:'chevron-left' width:30 height:30 strokeWidth:1 %}
    		</button>

		    <button class="btn-control">
		      {% render 'icon' icon:'chevron-right' width:30 height:30 strokeWidth:1 %}
		    </button>

		    <button class="btn-control">
					{% render 'icon' icon:'x' width:30 height:30 strokeWidth:1 %}
				</button>
	    </div>

		</form>

		<div class="py-4 mb-4">
			<div class="max-w-xs pagination swiper-pagination-bullets swiper-pagination-horizontal">
				<span class="swiper-pagination-bullet pagination-bullet swiper-pagination-bullet-active active"></span>
				<span class="swiper-pagination-bullet pagination-bullet"></span>
				<span class="swiper-pagination-bullet pagination-bullet"></span>
			</div>
		</div>

		<ul class="mb-4 pagination--page" role="list">
			<li>
	      <a href="#" class="pp-control" aria-label="Previous page">
					{% render 'icon' icon:'chevron-left' width:20 height:20 strokeWidth:1 %}
	        <span class="mx-4 lg:hidden clamp-1">
	          Previous page
	        </span>
	      </a>
	    </li>
	    <li class="hidden lg:block">
	      <a href="#" class="" aria-label="Page 1">1</a>
	    </li>
	    <li class="hidden lg:block">
	      <span aria-current="page" aria-label="Page 2">2</span>
	    </li>
	    <li class="hidden lg:block">
	    	<a href="#" class="" aria-label="Page 3">3</a>
	    </li>
	    <li class="hidden lg:block">
	      <a href="#" class="" aria-label="Page 4">4</a>
	    </li>
	    <li class="self-end hidden lg:block">
	      <span class="block h-full p-4">…</span>
	    </li>
	    <li class="hidden lg:block">
	      <a href="#" class="" aria-label="Page 16">16</a>
	    </li>
	    <li>
	      <a href="#" class="pp-control" aria-label="Next page">
	        <span class="mx-4 lg:hidden clamp-1">
	          Next page
	        </span>
					{% render 'icon' icon:'chevron-right' width:20 height:20 strokeWidth:1 %}
	      </a>
	    </li>
	  </ul>

    <p class="mt-4 mb-2 underline">Buttons</p>

   	<div class="flex flex-wrap">
		
			<div class="w-full py-2 lg:w-1/2">
	    	<button class="button button--primary">Button Primary</button>
	    </div>
			<div class="w-full py-2 lg:w-1/2">
	    	<button class="button button--secondary">Button Secondary</button>
	    </div>
	    <div class="w-full py-2 lg:w-1/2">
	    	<button class="button button--tertiary">Button Tertiary</button>
	    </div>
	    <div class="w-full py-2 lg:w-1/2">
	    	<button class="button button--light">Button Light</button>
	    </div>
	    <div class="w-full py-2 lg:w-1/2">
		    <button class="button button--dark">Button Dark</button>
		  </div>	
		  <div class="w-full py-2 lg:w-1/2">
	    	<a class="inline-block button button--pop">Button Highlight (Example: A Tag)</a>
	    </div>
	    <div class="w-full py-2 lg:w-1/2">
	    	<input type="button" class="button button--primary" value="Button Input" />
	    </div>
	    <div class="w-full py-2 lg:w-1/2">
	    	<button class="button button--w-icon"><span>{% render 'icon' icon: 'user' width:20 height:20 %}</span><span>Button w/ Icon</span></button>
	    </div>
	    <div class="py-2 w-/1/4">
	    	<a class="inline-block button button--link">Button Text Only</a>
	    </div>

    </div>

    <hr class="my-5">

    <p class="mt-4 mb-2 underline">Accordion</p>

	  <div class="mb-4">
	  	<ul class="pl-0 list">
	  		<li class="accordion group">
				  <button 
				    id="AccordionTitle-1" 
				    class="accordion-title" 
				    aria-expanded="false" 
				    aria-disabled="false" 
				    aria-controls="AccordionSection-1" 
				    neptune-engage='targets:[
				      { 
				        selector:_parent,
				        classes:toggle:active,
				        siblings:classes:remove:active
				      },
				      {
				        selector:"_grandparent button",
				        attributes:[{att:"aria-disabled" set:"false"} {att:"aria-expanded" set:"false"}]
				      },
				      { 
				        selector:"_grandparent .active button",
				        attributes:[{att:"aria-disabled" set:"true"} {att:"aria-expanded" set:"true"}]
				      },
				      {
				        selector:"_grandparent .accordion-panel",
				        attributes:{att:"aria-hidden" set:"true"}
				      },
				      { 
				        selector:"_grandparent .active .accordion-panel",
				        attributes:{att:"aria-hidden" set:"false"}
				      }
				    ]'
				  >
				    <span>Accordion Title 1</span>

				    <span class="flex group-active:hidden accordion-control">
				      {% render 'icon', icon: 'chevron-down' width:24 height:24 %}
				    </span>
				    <span class="hidden group-active:flex accordion-control">
				      {% render 'icon', icon: 'chevron-up' width:24 height:24 %}
				    </span>
				  </button>
				  <div 
				    id="AccordionSection-1" 
				    aria-hidden="true" 
				    aria-labelledby="AccordionTitle-1" 
				    role="region" 
				    class="hidden accordion-panel group-active:block">
				    <div>
				      <p>Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.</p>
				      <p>Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.</p>
				      <input class="sr-only" type="text">
				    </div>
				  </div>
				</li>
				<li class="accordion">
				  <button 
				    id="AccordionTitle-2" 
				    class="accordion-title" 
				    aria-expanded="false" 
				    aria-disabled="false" 
				    aria-controls="AccordionSection-2" 
				    neptune-engage='targets:[
				      { 
				        selector:_parent,
				        classes:toggle:active,
				        siblings:classes:remove:active
				      },
				      {
				        selector:"_grandparent button",
				        attributes:[{att:"aria-disabled" set:"false"} {att:"aria-expanded" set:"false"}]
				      },
				      { 
				        selector:"_grandparent .active button",
				        attributes:[{att:"aria-disabled" set:"true"} {att:"aria-expanded" set:"true"}]
				      },
				      {
				        selector:"_grandparent .accordion-panel",
				        attributes:{att:"aria-hidden" set:"true"}
				      },
				      { 
				        selector:"_grandparent .active .accordion-panel",
				        attributes:{att:"aria-hidden" set:"false"}
				      }
				    ]'
				  >
				    <span>Accordion Title 2</span>

				    <span class="flex group-active:hidden accordion-control">
				      {% render 'icon', icon: 'chevron-down' width:24 height:24 %}
				    </span>
				    <span class="hidden group-active:flex accordion-control">
				      {% render 'icon', icon: 'chevron-up' width:24 height:24 %}
				    </span>
				  </button>
				  <div 
				    id="AccordionSection-2" 
				    aria-hidden="true" 
				    aria-labelledby="AccordionTitle-2" 
				    role="region" 
				    class="hidden accordion-panel group-active:block">
				    <div>
				      <p>Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.</p>
				      <p>Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.</p>
				      <input class="sr-only" type="text">
				    </div>
				  </div>
				</li>
	  	</ul>
	  </div>

	   <p class="mt-4 mb-2 underline">Tabs</p>

	  <div class="mb-4 tabs" neptune-liquid="{topic: Accordion1}" neptune-brig="{keys:[37,38,39,40],targets:.tab-title}">
      <ul class="" role='tablist'>
        {% for i in (1..3) %}
	        <li>
	          <button id="Tab-{{ i }}" class="tab-title {% if i == 1 %} active{% endif %}" aria-selected="{% if i == 1 %}true{% else %}false{% endif %}" aria-controls="TabSection-{{ i }}" neptune-engage='[
	            {on:keydown keys:[9 40] targets:[{selector:".tab-panel.active" focus:true}]},
	            {on:keyup keys:[13] targets:[{selector:".tab-panel.active" focus:true}]},
	            {on:click targets:[
	              {selector:"_grandparent button" classes:remove:active attributes:{att:aria-selected set:"false"}},
	              {selector:_self classes:add:active attributes:{att:aria-selected set:"true"}},
	              {selector:".tab-panel" classes:remove:active attributes:{att:aria-hidden set:"false"}},
	              {selector:"#TabSection-{{ i }}" classes:add:active attributes:{att:aria-hidden set:"true"}}
	            ]}
	          ]'>
	            <span>Tab Title {{ i }}</span>
	          </button>
	        </li>
        {% endfor %}
      </ul>
      <div>
        {% for i in (1..3) %}
        <div id="TabSection-{{ i }}" aria-hidden="{% if i == 1 %}true{% else %}false{% endif %}" aria-labelledby="Tab-{{ i }}" role="tabpanel" class="tab-panel active:block hidden {% if i == 1 %} active{% endif %}">
          <div>
            <p>Body {{ i }}</p>
            <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Obcaecati id similique beatae possimus. Rem accusantium harum necessitatibus voluptates consectetur saepe? Voluptate quisquam odio, officia temporibus cumque dolorem quidem nesciunt aperiam.</p>
            <input type="text">
          </div>
        </div>
      	{% endfor %}
      </div>
    </div>

    <hr class="my-5">

    <h3 class="underline mb4">Typeface Options</h3>

		<div class="">
			<p class="type--headline">Type Headline</p>
			<p class="type--subline">Type Subline</p>
			<p class="type--eyebrow">Type Eyebrow</p>
			<p class="type--primary">Type Primary</p>
      <p class="type--secondary">Type Secondary</p>
      <p class="type--page">Type Page</p>
      <p class="type--section">Type Section</p>
      <p class="type--article">Type Article</p>
      <p class="type--label">Type Label</p>
		</div>

    <hr class="my-5">

    <h3 class="underline mb4">Typeface Options</h3>

		<div class="">
			
			<h1 class="block mb-4">Headline Font</h1>

			<p class="type--subline"><strong>Pellentesque habitant morbi tristique</strong> senectus et netus et malesuada fames ac turpis egestas. Vestibulum tortor quam, feugiat vitae, ultricies eget, tempor sit amet, ante. Donec eu libero sit amet quam egestas semper. <em>Aenean ultricies mi vitae est.</em> Mauris placerat eleifend leo. Quisque sit amet est et sapien ullamcorper pharetra. Vestibulum erat wisi, condimentum sed, <code>commodo vitae</code>, ornare sit amet, wisi. Aenean fermentum, elit eget tincidunt condimentum, eros ipsum rutrum orci, sagittis tempus lacus enim ac dui. <a href="#">Donec non enim</a> in turpis pulvinar facilisis. Ut felis.</p>

			<h2 class="mt-8 mb-4 secondary-headline">Secondary Headline Font</h2>

			<ol>
			   <li>Lorem ipsum dolor sit amet, consectetuer adipiscing elit.</li>
			   <li>Aliquam tincidunt mauris eu risus.</li>
			</ol>

			<blockquote><p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus magna. Cras in mi at felis aliquet congue. Ut a est eget ligula molestie gravida. Curabitur massa. Donec eleifend, libero at sagittis mollis, tellus est malesuada tellus, at luctus turpis elit sit amet quam. Vivamus pretium ornare est.</p></blockquote>

			<h3>Header Level 3</h3>

			<ul>
			   <li>Lorem ipsum dolor sit amet, consectetuer adipiscing elit.</li>
			   <li>Aliquam tincidunt mauris eu risus.</li>
			</ul>

			<h4>Header Level 4</h4>
			<h5>Header Level 5</h5>
			<h6>Header Level 6</h6>

		</div>

		<hr class="my-5">

    <h3 class="underline mb4">Icons</h3>

		<div class="flex flex-wrap p-0 m-0 list">

			<div class="w-1/4 py-4 mb-2 text-center cursor-pointer">
				{% render 'icon' icon: 'menu' height:20 width:20 fill:'currentColor' %}
			</div>

			<div class="w-1/4 py-4 mb-2 text-center cursor-pointer">
				{% render 'icon' icon: 'search' height:20 width:20 %}
			</div>

			<div class="w-1/4 py-4 mb-2 text-center cursor-pointer">
				{% render 'icon' icon: 'shopping-circle' height:20 width:20 %}
			</div>

			<div class="w-1/4 py-4 mb-2 text-center cursor-pointer">
				{% render 'icon' icon: 'exit' height:20 width:20 %}
			</div>

			<div class="w-1/4 py-4 mb-2 text-center cursor-pointer">
				{% render 'icon' icon: 'exit-sm' height:20 width:20 %}
			</div>

			<div class="w-1/4 py-4 mb-2 text-center cursor-pointer">
				{% render 'icon' icon: 'chevron-left' height:20 width:20 %}
			</div>

			<div class="w-1/4 py-4 mb-2 text-center cursor-pointer">
				{% render 'icon' icon: 'chevron-right' height:20 width:20 %}
			</div>

			<div class="w-1/4 py-4 mb-2 text-center cursor-pointer">
				{% render 'icon' icon: 'location' height:20 width:20 %}
			</div>

			<div class="w-1/4 py-4 mb-2 text-center cursor-pointer">
				{% render 'icon' icon: 'user' height:20 width:20 %}
			</div>

			<div class="w-1/4 py-4 mb-2 text-center cursor-pointer">
				{% render 'icon' icon: 'plus' height:20 width:20 %}
			</div>

			<div class="w-1/4 py-4 mb-2 text-center cursor-pointer">
				{% render 'icon' icon: 'account-alt' height:20 width:20 %}
			</div>

			<div class="w-1/4 py-4 mb-2 text-center cursor-pointer">
				{% render 'icon' icon: 'facebook' height:20 width:20 %}
			</div>

			<div class="w-1/4 py-4 mb-2 text-center cursor-pointer">
				{% render 'icon' icon: 'instagram' height:20 width:20 %}
			</div>

			<div class="w-1/4 py-4 mb-2 text-center cursor-pointer">
				{% render 'icon' icon: 'twitter' height:20 width:20 %}
			</div>

			<div class="w-1/4 py-4 mb-2 text-center cursor-pointer">
				{% render 'icon' icon: 'tiktok' height:20 width:20 %}
			</div>

		</div>

</div>
