{% schema %}

{
  "name": "Slideshow Sequence",
  "settings": [
    {
      "type": "header",
      "content": "A/B Testing"
    },
    {
      "type": "checkbox",
      "id": "remove_section_condition",
      "label": "Remove Section",
      "default": false,
      "info": "This will hide the section in one of the experiment variations."
    },
    {
      "type": "header",
      "content": "Display Settings"
    },
    {
      "type": "checkbox",
      "id": "async",
      "label": "Asynchronously Load Section on Scroll",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_desktop",
      "label": "Show desktop",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_mobile",
      "label": "Show mobile",
      "default": true
    },
    {
      "type": "header",
      "content": "Section Display Settings"
    },
    {
      "type": "select",
      "id": "container",
      "label": "Container",
      "options": [
        {
          "value": "container",
          "label": "Container"
        },
        {
          "value": "w-full",
          "label": "Full width"
        }
      ],
      "default": "container"
    },
    {
      "type": "color",
      "label": "Background Color",
      "id": "background_color"
    },
    {
      "type": "select",
      "id": "height",
      "label": "Height (Desktop)",
      "options": [
        {
          "value": "lg:h-main",
          "label": "Full height"
        },
        {
          "value": "lg:h-75v",
          "label": "Three quarter height"
        },
        {
          "value": "lg:h-50v",
          "label": "Half height"
        },
        {
          "value": "lg:h-25v",
          "label": "Quarter height"
        },
        {
          "value": "lg:h-auto content-height",
          "label": "Text content height"
        },
        {
          "value": "lg:h-auto image-height",
          "label": "Image height"
        },
        {
          "value": "lg:h-auto content-below-height",
          "label": "Content outside image"
        }
      ],
      "default": "lg:h-main"
    },
    {
      "type": "select",
      "id": "height_mobile",
      "label": "Height (Mobile)",
      "options": [
        {
          "value": "h-main",
          "label": "Full height"
        },
        {
          "value": "h-75v",
          "label": "Three quarter height"
        },
        {
          "value": "h-50v",
          "label": "Half height"
        },
        {
          "value": "h-25v",
          "label": "Quarter height"
        },
        {
          "value": "h-auto content-height-mobile",
          "label": "Text content height"
        },
        {
          "value": "h-auto image-height-mobile",
          "label": "Image height"
        },
        {
          "value": "h-auto content-below-height",
          "label": "Content outside image"
        }
      ],
      "default": "h-main"
    },
    {
      "type": "header",
      "content": "Section Spacing Settings (Desktop)"
    },
    {
      "type": "select",
      "id": "section_top_margin",
      "label": "Margin top",
      "options": [
        {
          "value": "0",
          "label": "None"
        },
        {
          "value": "1",
          "label": ".25rem"
        },
        {
          "value": "2",
          "label": ".5rem"
        },
        {
          "value": "3",
          "label": "1rem"
        },
        {
          "value": "4",
          "label": "2rem"
        },
        {
          "value": "5",
          "label": "4rem"
        },
        {
          "value": "6",
          "label": "8rem"
        },
        {
          "value": "7",
          "label": "16rem"
        }
      ],
      "default": "2"
    },
    {
      "type": "select",
      "id": "section_bottom_margin",
      "label": "Margin bottom ",
      "options": [
        {
          "value": "0",
          "label": "None"
        },
        {
          "value": "1",
          "label": ".25rem"
        },
        {
          "value": "2",
          "label": ".5rem"
        },
        {
          "value": "3",
          "label": "1rem"
        },
        {
          "value": "4",
          "label": "2rem"
        },
        {
          "value": "5",
          "label": "4rem"
        },
        {
          "value": "6",
          "label": "8rem"
        },
        {
          "value": "7",
          "label": "16rem"
        }
      ],
      "default": "2"
    },
    {
      "type": "select",
      "id": "section_top_padding",
      "label": "Padding top",
      "options": [
        {
          "value": "0",
          "label": "None"
        },
        {
          "value": "1",
          "label": ".25rem"
        },
        {
          "value": "2",
          "label": ".5rem"
        },
        {
          "value": "3",
          "label": "1rem"
        },
        {
          "value": "4",
          "label": "2rem"
        },
        {
          "value": "5",
          "label": "4rem"
        },
        {
          "value": "6",
          "label": "8rem"
        },
        {
          "value": "7",
          "label": "16rem"
        }
      ],
      "default": "0"
    },
    {
      "type": "select",
      "id": "section_bottom_padding",
      "label": "Padding bottom ",
      "options": [
        {
          "value": "0",
          "label": "None"
        },
        {
          "value": "1",
          "label": ".25rem"
        },
        {
          "value": "2",
          "label": ".5rem"
        },
        {
          "value": "3",
          "label": "1rem"
        },
        {
          "value": "4",
          "label": "2rem"
        },
        {
          "value": "5",
          "label": "4rem"
        },
        {
          "value": "6",
          "label": "8rem"
        },
        {
          "value": "7",
          "label": "16rem"
        }
      ],
      "default": "0"
    },
    {
      "type": "header",
      "content": "Section Spacing Settings (Mobile)"
    },
    {
      "type": "select",
      "id": "section_top_margin_mobile",
      "label": "Margin top",
      "options": [
        {
          "value": "0",
          "label": "None"
        },
        {
          "value": "1",
          "label": ".25rem"
        },
        {
          "value": "2",
          "label": ".5rem"
        },
        {
          "value": "4",
          "label": "1rem"
        },
        {
          "value": "8",
          "label": "2rem"
        },
        {
          "value": "16",
          "label": "4rem"
        },
        {
          "value": "32",
          "label": "8rem"
        },
        {
          "value": "64",
          "label": "16rem"
        }
      ],
      "default": "2"
    },
    {
      "type": "select",
      "id": "section_bottom_margin_mobile",
      "label": "Margin bottom",
      "options": [
        {
          "value": "0",
          "label": "None"
        },
        {
          "value": "1",
          "label": ".25rem"
        },
        {
          "value": "2",
          "label": ".5rem"
        },
        {
          "value": "4",
          "label": "1rem"
        },
        {
          "value": "8",
          "label": "2rem"
        },
        {
          "value": "16",
          "label": "4rem"
        },
        {
          "value": "32",
          "label": "8rem"
        },
        {
          "value": "64",
          "label": "16rem"
        }
      ],
      "default": "2"
    },
    {
      "type": "select",
      "id": "section_top_padding_mobile",
      "label": "Padding top",
      "options": [
        {
          "value": "0",
          "label": "None"
        },
        {
          "value": "1",
          "label": ".25rem"
        },
        {
          "value": "2",
          "label": ".5rem"
        },
        {
          "value": "4",
          "label": "1rem"
        },
        {
          "value": "8",
          "label": "2rem"
        },
        {
          "value": "16",
          "label": "4rem"
        },
        {
          "value": "32",
          "label": "8rem"
        },
        {
          "value": "64",
          "label": "16rem"
        }
      ],
      "default": "0"
    },
    {
      "type": "select",
      "id": "section_bottom_padding_mobile",
      "label": "Padding bottom",
      "options": [
        {
          "value": "0",
          "label": "None"
        },
        {
          "value": "1",
          "label": ".25rem"
        },
        {
          "value": "2",
          "label": ".5rem"
        },
        {
          "value": "4",
          "label": "1rem"
        },
        {
          "value": "8",
          "label": "2rem"
        },
        {
          "value": "16",
          "label": "4rem"
        },
        {
          "value": "32",
          "label": "8rem"
        },
        {
          "value": "64",
          "label": "16rem"
        }
      ],
      "default": "0"
    },
    {
      "type": "header",
      "content": "Gap Settings"
    },
    {
      "type": "select",
      "id": "gap",
      "label": "Gap (Desktop)",
      "options": [
        {
          "value": "lg:gap-0",
          "label": "None"
        },
        {
          "value": "lg:gap-xs",
          "label": "XS"
        },
        {
          "value": "lg:gap-sm",
          "label": "SM"
        },
        {
          "value": "lg:gap-base",
          "label": "MD / Base"
        },
        {
          "value": "lg:gap-lg",
          "label": "LG"
        },
        {
          "value": "lg:gap-xl",
          "label": "XL"
        },
        {
          "value": "lg:gap-2xl",
          "label": "2XL"
        },
        {
          "value": "lg:gap-3xl",
          "label": "3XL"
        },
        {
          "value": "lg:gap-4xl",
          "label": "4XL"
        },
        {
          "value": "lg:gap-5xl",
          "label": "5XL"
        },
        {
          "value": "lg:gap-6xl",
          "label": "6XL"
        },
        {
          "value": "lg:gap-7xl",
          "label": "7XL"
        }
      ],
      "default": "lg:gap-base"
    },
    {
      "type": "select",
      "id": "gap_mobile",
      "label": "Gap (Mobile)",
      "options": [
        {
          "value": "gap-0",
          "label": "None"
        },
        {
          "value": "gap-xs",
          "label": "XS"
        },
        {
          "value": "gap-sm",
          "label": "SM"
        },
        {
          "value": "gap-base",
          "label": "MD / Base"
        },
        {
          "value": "gap-lg",
          "label": "LG"
        },
        {
          "value": "gap-xl",
          "label": "XL"
        },
        {
          "value": "gap-2xl",
          "label": "2XL"
        }
      ],
      "default": "gap-base"
		},
		{
			"type": "header",
			"content": "Interactivity"
		},
		{
			"type": "select",
			"id": "image_effect",
			"label": "Image Effect",
			"options": [
				{
					"value": "",
					"label": "None"
				},
				{
					"value": "animate zoom-in",
					"label": "Zoom In"
				}
			],
			"default": ""
    }
  ],
  "blocks": [
    {
      "type": "parent",
      "name": "Slideshow",
      "settings": [
        {
          "type": "header",
          "content": "Slideshow Settings"
        },
        {
          "type": "select",
          "id": "swiper_effect",
          "label": "Slider effect",
          "options": [
            {
              "value": "slide",
              "label": "Slide"
            },
            {
              "value": "fade",
              "label": "Fade"
            }
          ],
          "default": "slide"
        },
        {
          "type": "checkbox",
          "id": "autoplay",
          "label": "Autoplay"
        },
        {
          "type": "checkbox",
          "id": "loop",
          "label": "Loop"
        },
        {
          "type": "range",
          "id": "autoplay_slide_duration",
          "label": "Autoplay Slide Duration",
          "min": 3,
          "max": 8,
          "step": 1,
          "default": 8
        },
        {
          "type": "checkbox",
          "id": "show_play",
          "label": "Show Play/Pause Button",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_pagination",
          "label": "Show Pagination (Desktop)",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_pagination_mobile",
          "label": "Show Pagination (Mobile)",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "arrows",
          "label": "Show arrows (Desktop)"
        },
        {
          "type": "checkbox",
          "id": "arrows_mobile",
          "label": "Show arrows (Mobile)"
        },
        {
          "type": "select",
          "id": "arrow_type",
          "label": "Arrow Type",
          "options": [
            {
              "value": "chevron",
              "label": "Chevron"
            },
            {
              "value": "standard",
              "label": "Standard"
            }
          ],
          "default": "chevron"
        },
        {
          "type": "color",
          "label": "Arrow color",
          "id": "arrow_color"
        },
        {
          "type": "color",
          "label": "Play/Pause Color",
          "id": "play_pause_color"
        },
        {
          "type": "header",
          "content": "Width Settings"
        },
        {
          "type": "select",
          "id": "item_width",
          "label": "Grid Item Width (Desktop)",
          "options": [
            {
              "value": "lg:col-span-1",
              "label": "8.33%"
            },
            {
              "value": "lg:col-span-2",
              "label": "16.67%"
            },
            {
              "value": "lg:col-span-3",
              "label": "25%"
            },
            {
              "value": "lg:col-span-4",
              "label": "33.33%"
            },
            {
              "value": "lg:col-span-5",
              "label": "41.67%"
            },
            {
              "value": "lg:col-span-6",
              "label": "50%"
            },
            {
              "value": "lg:col-span-7",
              "label": "58.33%"
            },
            {
              "value": "lg:col-span-8",
              "label": "66.67%"
            },
            {
              "value": "lg:col-span-9",
              "label": "75%"
            },
            {
              "value": "lg:col-span-10",
              "label": "83.33%"
            },
            {
              "value": "lg:col-span-11",
              "label": "91.67%"
            },
            {
              "value": "lg:col-span-12",
              "label": "100%"
            }
          ],
          "default": "lg:col-span-6"
        },
        {
          "type": "select",
          "id": "item_width_mobile",
          "label": "Grid Item Width (Mobile)",
          "options": [
            {
              "value": "col-span-12",
              "label": "100%"
            },
            {
              "value": "col-span-3",
              "label": "25%"
            },
            {
              "value": "col-span-4",
              "label": "33.33%"
            },
            {
              "value": "col-span-6",
              "label": "50%"
            },
            {
              "value": "col-span-8",
              "label": "66.67%"
            },
            {
              "value": "col-span-9",
              "label": "75%"
            }
          ],
          "default": "col-span-12"
        }
      ] 
    },
    {
      "name": "Slide",
      "type": "slide",
      "settings": [
        {
          "type": "header",
          "content": "Image Banner Settings"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Banner Image (Desktop)"
        },
        {
          "type": "select",
          "id": "image_position",
          "label": "Image Position",
          "options": [
            {
              "value": "bg-top-left",
              "label": "Top Left"
            },
            {
              "value": "bg-top",
              "label": "Top Center"
            },
            {
              "value": "bg-top-right",
              "label": "Top Right"
            },
            {
              "value": "bg-left",
              "label": "Middle Left"
            },
            {
              "value": "bg-center",
              "label": "Center"
            },
            {
              "value": "bg-right",
              "label": "Middle Right"
            },
            {
              "value": "bg-bottom-left",
              "label": "Bottom Left"
            },
            {
              "value": "bg-bottom",
              "label": "Bottom Center"
            },
            {
              "value": "bg-bottom-right",
              "label": "Bottom Right"
            }
          ],
          "default": "bg-center"
        },
        {
          "type": "image_picker",
          "id": "image_mobile",
          "label": "Banner Image (Mobile)"
        }, 
        {
          "type": "select",
          "id": "image_position_mobile",
          "label": "Image Position",
          "options": [
            {
              "value": "bg-top-left",
              "label": "Top Left"
            },
            {
              "value": "bg-top",
              "label": "Top Center"
            },
            {
              "value": "bg-top-right",
              "label": "Top Right"
            },
            {
              "value": "bg-left",
              "label": "Middle Left"
            },
            {
              "value": "bg-center",
              "label": "Center"
            },
            {
              "value": "bg-right",
              "label": "Middle Right"
            },
            {
              "value": "bg-bottom-left",
              "label": "Bottom Left"
            },
            {
              "value": "bg-bottom",
              "label": "Bottom Center"
            },
            {
              "value": "bg-bottom-right",
              "label": "Bottom Right"
            }
          ],
          "default": "bg-center"
        },
        {
          "type": "header",
          "content": "Link Settings"
        },
        {
          "type": "url",
          "id": "content_url",
          "label": "Link URL"
        },
        {
          "type": "header",
          "content": "Video Banner Settings"
        },
        {
          "type": "text",
          "id": "video_url",
          "label": "Video mp4 url (Desktop)",
          "info": "For section height settings of: Full height, Three Quarter Height, Half Height, and Quarter Height. To get mp4 file: 1) Go to vimeo.com, 2) Click on desired video, 3) Click on settings, 4) Click on Video file"
        },
        {
          "type": "text",
          "id": "video_url_mobile",
          "label": "Video mp4 url (Mobile)",
          "info": "For section height settings of: Full height, Three Quarter Height, Half Height, and Quarter Height. To get mp4 file: 1) Go to vimeo.com, 2) Click on desired video, 3) Click on settings, 4) Click on Video file"
        },
        { 
          "type": "image_picker",
          "id": "poster_image",
          "label": "Video Poster Image"
        },
        {
          "type": "header",
          "content": "Content Item Settings"
        },
        {
          "type": "select",
          "id": "content_direction",
          "label": "Direction (Desktop)",
          "options": [
            {
              "value": "lg:flex-row",
              "label": "Row"
            },
            {
              "value": "lg:flex-row-reverse",
              "label": "Row Reverse"
            },
            {
              "value": "lg:flex-col",
              "label": "Column"
            },
            {
              "value": "lg:flex-col-reverse",
              "label": "Column Reverse"
            }
          ],
          "default": "lg:flex-col"
        },
        {
          "type": "select",
          "id": "content_direction_mobile",
          "label": "Direction (Mobile)",
          "options": [
            {
              "value": "flex-row",
              "label": "Row"
            },
            {
              "value": "flex-row-reverse",
              "label": "Row Reverse"
            },
            {
              "value": "flex-col",
              "label": "Column"
            },
            {
              "value": "flex-col-reverse",
              "label": "Column Reverse"
            }
          ],
          "default": "flex-col"
        },
        {
          "type": "select",
          "id": "content_gap",
          "label": "Gap (Desktop)",
          "options": [
            {
              "value": "lg:gap-0",
              "label": "None"
            },
            {
              "value": "lg:gap-xs",
              "label": "XS"
            },
            {
              "value": "lg:gap-sm",
              "label": "SM"
            },
            {
              "value": "lg:gap-base",
              "label": "MD / Base"
            },
            {
              "value": "lg:gap-lg",
              "label": "LG"
            },
            {
              "value": "lg:gap-xl",
              "label": "XL"
            },
            {
              "value": "lg:gap-2xl",
              "label": "2XL"
            },
            {
              "value": "lg:gap-3xl",
              "label": "3XL"
            },
            {
              "value": "lg:gap-4xl",
              "label": "4XL"
            },
            {
              "value": "lg:gap-5xl",
              "label": "5XL"
            },
            {
              "value": "lg:gap-6xl",
              "label": "6XL"
            },
            {
              "value": "lg:gap-7xl",
              "label": "7XL"
            }
          ],
          "default": "lg:gap-base"
        },
        {
          "type": "select",
          "id": "content_gap_mobile",
          "label": "Gap (Mobile)",
          "options": [
            {
              "value": "gap-0",
              "label": "None"
            },
            {
              "value": "gap-xs",
              "label": "XS"
            },
            {
              "value": "gap-sm",
              "label": "SM"
            },
            {
              "value": "gap-base",
              "label": "MD / Base"
            },
            {
              "value": "gap-lg",
              "label": "LG"
            },
            {
              "value": "gap-xl",
              "label": "XL"
            },
            {
              "value": "gap-2xl",
              "label": "2XL"
            }
          ],
          "default": "gap-base"
        },
        {
          "type": "header",
          "content": "Text Settings"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text color"
        },
        {
          "type": "select",
          "id": "text_align",
          "label": "Text Alignment (Desktop)",
          "options": [
            {
              "value": "lg:text-left",
              "label": "Left"
            },
            {
              "value": "lg:text-center",
              "label": "Center"
            },
            {
              "value": "lg:text-right",
              "label": "Right"
            },
            {
              "value": "lg:text-justify lg:text-justify-last",
              "label": "Justify"
            }
          ],
          "default": "lg:text-left"
        },
        {
          "type": "select",
          "id": "text_align_mobile",
          "label": "Text Alignment (Mobile)",
          "options": [
            {
              "value": "text-left",
              "label": "Left"
            },
            {
              "value": "text-center",
              "label": "Center"
            },
            {
              "value": "text-right",
              "label": "Right"
            },
            {
              "value": "text-justify text-justify-last",
              "label": "Justify"
            }
          ],
          "default": "text-left"
        },
        {
          "type": "header",
          "content": "Title Image Options"
        },
        {
          "type": "image_picker",
          "label": "Title Image",
          "id": "title_image"
        },
        {
          "type": "text",
          "label": "Title SVG",
          "id": "title_svg",
          "info": "Choose an SVG from your [files](/admin/settings/files) and paste the file name in the field above."
        },
        {
          "type": "text",
          "label": "Title Image/SVG Max width",
          "id": "title_image_width",
          "info": "Use a pixel or percentage value here.",
          "default": "100%"
        },
        {
          "type": "header",
          "content": "Pretitle Typeface Options"
        },
        {
          "type": "text",
          "id": "pretitle",
          "label": "Pretitle"
        },
        {
          "type": "select",
          "id": "pretitle_type",
          "label": "Typeface",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "font-pop",
              "label": "ABC Monument Grotesk Black"
            },
            {
              "value": "font-highlight",
              "label": "ABC Monument Grotesk Light"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "MonumentGrotesk-Regular"
        },
        {
          "type": "number",
          "id": "pretitle_size",
          "label": "Pretitle Font Size (Desktop)",
          "default": 11
        },
        {
          "type": "number",
          "id": "pretitle_size_mobile",
          "label": "Pretitle Font Size (Mobile)",
          "default": 11
        },
        {
          "type": "color",
          "id": "pretitle_color",
          "label": "Pretitle color",
          "default": "#504F4F"
        },
        {
          "type": "text",
          "id": "pretitle_leading",
          "label": "Preitle Line Height",
          "default": "1.38"
        },
        {
          "type": "text",
          "id": "pretitle_tracking",
          "label": "Pretitle Letter Spacing"
        },
        {
          "type": "header",
          "content": "Title Typeface Options"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "select",
          "id": "title_type",
          "label": "Typeface",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "font-pop",
              "label": "ABC Monument Grotesk Black"
            },
            {
              "value": "font-highlight",
              "label": "ABC Monument Grotesk Light"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "MonumentGrotesk-Bold"
        },
        {
          "type": "number",
          "id": "title_size",
          "label": "Title Font Size (Desktop)",
          "default": 60
        },
        {
          "type": "number",
          "id": "title_size_mobile",
          "label": "Title Font Size (Mobile)",
          "default": 50
        },
        {
          "type": "color",
          "id": "title_color",
          "label": "Title color",
          "default": "#000000"
        },
        {
          "type": "text",
          "id": "title_leading",
          "label": "Title Line Height",
          "default": "1.38"
        },
        {
          "type": "text",
          "id": "title_tracking",
          "label": "Title Letter Spacing"
        },
        {
          "type": "header",
          "content": "Subtitle Typeface Options"
        },
        {
          "type": "text",
          "id": "subtitle",
          "label": "Subtitle"
        },
        {
          "type": "select",
          "id": "subtitle_type",
          "label": "Typeface",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "font-pop",
              "label": "ABC Monument Grotesk Black"
            },
            {
              "value": "font-highlight",
              "label": "ABC Monument Grotesk Light"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "MonumentGrotesk-Regular"
        },
        {
          "type": "number",
          "id": "subtitle_size",
          "label": "Subtitle Font Size (Desktop)",
          "default": 20
        },
        {
          "type": "number",
          "id": "subtitle_size_mobile",
          "label": "Subtitle Font Size (Mobile)",
          "default": 20
        },
        {
          "type": "color",
          "id": "subtitle_color",
          "label": "Subtitle color",
          "default": "#000000"
        },
        {
          "type": "text",
          "id": "subtitle_leading",
          "label": "Subtitle Line Height",
          "default": "1.38"
        },
        {
          "type": "text",
          "id": "subtitle_tracking",
          "label": "Subtitle Letter Spacing"
        },
        {
          "type": "header",
          "content": "Featured Content Typeface Options"
        },
        {
          "type": "richtext",
          "id": "featured_content",
          "label": "Featured Content"
        },
        {
          "type": "select",
          "id": "featured_content_type",
          "label": "Typeface",
          "options": [
            {
              "value": "MonumentGrotesk-Regular",
              "label": "Monument Grotesk Regular"
            },
            {
              "value": "MonumentGrotesk-Medium",
              "label": "Monument Grotesk Medium "
            },
            {
              "value": "MonumentGrotesk-Medium-Italic",
              "label": "Monument Grotesk Medium Italic"
            },
            {
              "value": "MonumentGrotesk-Bold",
              "label": "Monument Grotesk Bold"
            },
            {
              "value": "ABC-Monument",
              "label": "ABC Monument"
            },
            {
              "value": "font-pop",
              "label": "ABC Monument Grotesk Black"
            },
            {
              "value": "font-highlight",
              "label": "ABC Monument Grotesk Light"
            },
            {
              "value": "Libre-Caslon",
              "label": "Libre Caslon"
            }
          ],
          "default": "MonumentGrotesk-Regular"
        },
        {
          "type": "number",
          "id": "featured_content_size",
          "label": "Featured Content Font Size (Desktop)",
          "default": 16
        },
        {
          "type": "number",
          "id": "featured_content_size_mobile",
          "label": "Featured Content Font Size (Mobile)",
          "default": 16
        },
        {
          "type": "color",
          "id": "featured_content_color",
          "label": "Featured Content color",
          "default": "#000000"
        },
        {
          "type": "text",
          "id": "featured_leading",
          "label": "Featured Line Height",
          "default": "1.38"
        },
        {
          "type": "text",
          "id": "featured_tracking",
          "label": "Featured Letter Spacing"
        },
        {
          "type": "header",
          "content": "Content Typeface Options"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Content"
        },
        {
          "type": "header",
          "content": "HTML Options"
        },
        {
          "type": "html",
          "id": "html",
          "label": "HTML"
        },
        {
          "type": "checkbox",
          "id": "grid_styling",
          "label": "Grid Styling",
          "default": false,
          "info": "Display HTML content in a 2-column layout."
        },
        {
          "type": "header",
          "content": "Link Settings"
        },
        {
          "type": "select",
          "id": "link_element",
          "label": "Link Element",
          "options": [
            {
              "value": "",
              "label": "Button Only"
            },
            {
              "value": "block",
              "label": "Entire Block"
            }
          ],
          "default": ""
        },
        {
          "type": "header",
          "content": "Button Alignment Settings"
        },
        {
          "type": "select",
          "id": "button_horizontal_align",
          "label": "Justify (Desktop)",
          "options": [
            {
              "value": "lg:justify-start",
              "label": "Start"
            },
            {
              "value": "lg:justify-center",
              "label": "Center"
            },
            {
              "value": "lg:justify-end",
              "label": "End"
            },
            {
              "value": "lg:justify-between",
              "label": "Between"
            }
          ],
          "default": "lg:justify-start"
        },
        {
          "type": "select",
          "id": "button_horizontal_align_mobile",
          "label": "Justify (Mobile)",
          "options": [
            {
              "value": "justify-start",
              "label": "Start"
            },
            {
              "value": "justify-center",
              "label": "Center"
            },
            {
              "value": "justify-end",
              "label": "End"
            },
            {
              "value": "justify-between",
              "label": "Between"
            }
          ],
          "default": "justify-start"
        },
        {
          "type": "select",
          "id": "button_vertical_align",
          "label": "Self Align (Desktop)",
          "options": [
            {
              "value": "lg:self-start",
              "label": "Start"
            },
            {
              "value": "lg:self-center",
              "label": "Center"
            },
            {
              "value": "lg:self-end",
              "label": "End"
            }
          ],
          "default": "lg:self-center"
        },
        {
          "type": "select",
          "id": "button_vertical_align_mobile",
          "label": "Self Align (Mobile)",
          "options": [
            {
              "value": "self-start",
              "label": "Start"
            },
            {
              "value": "self-center",
              "label": "Center"
            },
            {
              "value": "self-end",
              "label": "End"
            }
          ],
          "default": "self-center"
        },
        {
          "type": "header",
          "content": "Button 1 Layout Settings"
        },
        {
          "type": "text",
          "id": "link_title",
          "label": "Link title"
        },
        {
          "type": "url",
          "id": "url",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "link_target",
          "label": "Link Target",
          "options": [
            {
              "value": "_blank",
              "label": "New Window"
            },
            {
              "value": "_self",
              "label": "Same Window"
            }
          ],
          "default": "_self"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Button Style",
          "options": [
            {
              "value": "button button--primary",
              "label": "Button Primary"
            },
            {
              "value": "button button--secondary",
              "label": "Button Secondary"
            },
            {
              "value": "button button--tertiary",
              "label": "Button Tertiary"
            },
            {
              "value": "button button--light",
              "label": "Button Light"
            },
            {
              "value": "button button--higlight",
              "label": "Button Highlight"
            },
            {
              "value": "button button--link",
              "label": "Button Text"
            },
            {
              "value": "button button--text",
              "label": "Text link"
            }
          ],
          "default": "button button--primary"
        },
        {
          "type": "color",
          "id": "btn_color",
          "label": "Button bg color",
          "default": "#f7f7f7"
        },
        {
          "type": "color",
          "id": "btn_text_color",
          "label": "Button text color",
          "default": "#222222"
        },
        {
          "type": "color",
          "id": "btn_border_color",
          "label": "Button border color",
          "default": "#222222"
        },
        {
          "type": "color",
          "id": "btn_hover_color",
          "label": "Button bg color (hover)"
        },
        {
          "type": "color",
          "id": "btn_hover_text_color",
          "label": "Button text color (hover)"
        },
        {
          "type": "color",
          "id": "btn_hover_border_color",
          "label": "Button border color (hover)"
        },
        {
          "type": "number",
          "id": "button_size",
          "label": "Button Font Size",
          "info": "Optional"
        },
        {
          "type": "number",
          "id": "button_size_mobile",
          "label": "Button Font Size (Mobile)",
          "info": "Optional"
        },
        {
          "type": "header",
          "content": "Button 2 Layout Settings"
        },
        {
          "type": "text",
          "id": "link_title_two",
          "label": "Link title"
        },
        {
          "type": "url",
          "id": "url_two",
          "label": "Link"
        },
        {
          "type": "select",
          "id": "link_target_two",
          "label": "Link Target",
          "options": [
            {
              "value": "_blank",
              "label": "New Window"
            },
            {
              "value": "_self",
              "label": "Same Window"
            }
          ],
          "default": "_self"
        },
        {
          "type": "select",
          "id": "button_style_two",
          "label": "Button Style",
          "options": [
            {
              "value": "button button--primary",
              "label": "Button Primary"
            },
            {
              "value": "button button--secondary",
              "label": "Button Secondary"
            },
            {
              "value": "button button--tertiary",
              "label": "Button Tertiary"
            },
            {
              "value": "button button--light",
              "label": "Button Light"
            },
            {
              "value": "button button--higlight",
              "label": "Button Highlight"
            },
            {
              "value": "button button--link",
              "label": "Button Text"
            },
            {
              "value": "button button--text",
              "label": "Text link"
            }
          ],
          "default": "button button--primary"
        },
        {
          "type": "color",
          "id": "btn_color_two",
          "label": "Button bg color",
          "default": "#f7f7f7"
        },
        {
          "type": "color",
          "id": "btn_text_color_two",
          "label": "Button text color",
          "default": "#000000"
        },
        {
          "type": "color",
          "id": "btn_border_color_two",
          "label": "Button border color",
          "default": "#000000"
        },
        {
          "type": "color",
          "id": "btn_hover_color_two",
          "label": "Button bg color (hover)"
        },
        {
          "type": "color",
          "id": "btn_hover_text_color_two",
          "label": "Button text color (hover)"
        },
        {
          "type": "color",
          "id": "btn_hover_border_color_two",
          "label": "Button border color (hover)"
        },
        {
          "type": "number",
          "id": "button_size_two",
          "label": "Button Font Size",
          "info": "Optional"
        },
        {
          "type": "number",
          "id": "button_size_mobile_two",
          "label": "Button Font Size (Mobile)",
          "info": "Optional"
        },
        {
          "type": "header",
          "content": "Content Layout Settings"
        },
        {
          "type": "header",
          "content": "Desktop Content Layout Settings"
        },
        {
          "type": "select",
          "id": "content_width",
          "label": "Content Width",
          "options": [
            {
              "value": "lg:w-full",
              "label": "100%",
              "group": "Percentage"
            },
            {
              "value": "lg:w-3/4",
              "label": "75%",
              "group": "Percentage"
            },
            {
              "value": "lg:w-3/5",
              "label": "60%",
              "group": "Percentage"
            },
            {
              "value": "lg:w-1/2",
              "label": "50%",
              "group": "Percentage"
            },
            {
              "value": "lg:w-1/4",
              "label": "25%",
              "group": "Percentage"
            },
            {
              "value": "lg:max-w-xl",
              "label": "XS",
              "group": "Fixed"
            },
            {
              "value": "lg:max-w-2xl",
              "label": "SM",
              "group": "Fixed"
            },
            {
              "value": "lg:max-w-3xl",
              "label": "MD",
              "group": "Fixed"
            },
            {
              "value": "lg:max-w-4xl",
              "label": "LG",
              "group": "Fixed"
            },
            {
              "value": "lg:max-w-5xl",
              "label": "XL",
              "group": "Fixed"
            },
            {
              "value": "lg:max-w-6xl",
              "label": "2XL",
              "group": "Fixed"
            }
          ],
          "default": "lg:w-full"
        },
        {
          "type": "select",
          "id": "content_height",
          "label": "Content Height",
          "options": [
            {
              "value": "h-full",
              "label": "100%"
            }, 
            {
              "value": "h-3/4",
              "label": "75%"
            }, 
            {
              "value": "h-1/2",
              "label": "50%"
            }, 
            {
              "value": "h-1/4",
              "label": "25%"
            }, 
            {
              "value": "h-auto",
              "label": "Auto"
            }
          ],
          "default": "h-auto"
        },
        {
          "type": "select",
          "id": "content_position",
          "label": "Content Position",
          "options": [
            {
              "value": "lg:items-start lg:justify-start",
              "label": "Top Left"
            },
            {
              "value": "lg:items-center lg:justify-start",
              "label": "Top Center"
            },
            {
              "value": "lg:items-end lg:justify-start",
              "label": "Top Right"
            },
            {
              "value": "lg:items-start lg:justify-center",
              "label": "Middle Left"
            },
            {
              "value": "lg:items-center lg:justify-center",
              "label": "Middle Center"
            },
            {
              "value": "lg:items-end lg:justify-center",
              "label": "Middle Right"
            },
            {
              "value": "lg:items-start lg:justify-end",
              "label": "Bottom Left"
            },
            {
              "value": "lg:items-center lg:justify-end",
              "label": "Bottom Center"
            },
            {
              "value": "lg:items-end lg:justify-end",
              "label": "Bottom Right"
            }
          ],
          "default": "lg:items-center lg:justify-center"
        },
        {
          "type": "select",
          "id": "content_vertical_padding",
          "label": "Content Vertical Padding",
          "options": [
            {
              "value": "lg:py-0",
              "label": "None"
            },
            {
              "value": "lg:py-1",
              "label": ".25rem"
            },
            {
              "value": "lg:py-2",
              "label": ".5rem"
            },
            {
              "value": "lg:py-4",
              "label": "1rem"
            },
            {
              "value": "lg:py-8",
              "label": "2rem"
            },
            {
              "value": "lg:py-16",
              "label": "4rem"
            },
            {
              "value": "lg:py-32",
              "label": "8rem"
            },
            {
              "value": "lg:py-64",
              "label": "16rem"
            }
          ],
          "default": "lg:py-0"
        },
        {
          "type": "select",
          "id": "content_horizontal_padding",
          "label": "Content Horizontal Padding",
          "options": [
            {
              "value": "lg:px-0",
              "label": "None"
            },
            {
              "value": "lg:px-1",
              "label": ".25rem"
            },
            {
              "value": "lg:px-2",
              "label": ".5rem"
            },
            {
              "value": "lg:px-4",
              "label": "1rem"
            },
            {
              "value": "lg:px-8",
              "label": "2rem"
            },
            {
              "value": "lg:px-16",
              "label": "4rem"
            },
            {
              "value": "lg:px-32",
              "label": "8rem"
            },
            {
              "value": "lg:px-64",
              "label": "16rem"
            }
          ],
          "default": "lg:px-0"
        },
        {
          "type": "header",
          "content": "Mobile Content Layout Settings"
        },
        {
          "type": "select",
          "id": "content_position_mobile",
          "label": "Content Position",
          "options": [
            {
              "value": "h-full",
              "label": "None"
            },
            {
              "value": "items-center justify-start",
              "label": "Top"
            },
            {
              "value": "items-center justify-center",
              "label": "Middle"
            },
            {
              "value": "items-center justify-end",
              "label": "Bottom"
            }
          ],
          "default": "items-center justify-start"
        },
        {
          "type": "select",
          "id": "content_vertical_padding_mobile",
          "label": "Content Vertical Padding",
          "options": [
            {
              "value": "py-0",
              "label": "None"
            },
            {
              "value": "py-1",
              "label": ".25rem"
            },
            {
              "value": "py-2",
              "label": ".5rem"
            },
            {
              "value": "py-4",
              "label": "1rem"
            },
            {
              "value": "py-8",
              "label": "2rem"
            },
            {
              "value": "py-16",
              "label": "4rem"
            },
            {
              "value": "py-32",
              "label": "8rem"
            },
            {
              "value": "py-64",
              "label": "16rem"
            }
          ],
          "default": "py-0"
        },
        {
          "type": "select",
          "id": "content_horizontal_padding_mobile",
          "label": "Content Horizontal Padding",
          "options": [
            {
              "value": "px-0",
              "label": "None"
            },
            {
              "value": "px-1",
              "label": ".25rem"
            },
            {
              "value": "px-2",
              "label": ".5rem"
            },
            {
              "value": "px-4",
              "label": "1rem"
            },
            {
              "value": "px-8",
              "label": "2rem"
            },
            {
              "value": "px-16",
              "label": "4rem"
            },
            {
              "value": "px-32",
              "label": "8rem"
            },
            {
              "value": "px-64",
              "label": "16rem"
            }
          ],
          "default": "px-0"
        },
        {
          "type": "header",
          "content": "Desktop Text Format Settings"
        },
        {
          "type": "select",
          "id": "title_element",
          "label": "Heading Type",
          "options": [
            {
              "value": "h1",
              "label": "H1"
            },
            {
              "value": "h2",
              "label": "H2"
            },
            {
              "value": "h3",
              "label": "H3"
            },
            {
              "value": "h4",
              "label": "H4"
            },
            {
              "value": "h5",
              "label": "H5"
            }
          ],
          "default": "h2"
        },
				{
					"type": "header",
					"content": "Interactivity"
				},
				{
					"type": "select",
					"id": "content_scroll_effect",
					"label": "Content Scroll Effect",
					"info": "Sticky based animations requires a Content Height greater than Auto",
					"options": [
						{
							"value": "",
							"label": "None"
						},
						{
							"value": "animate sticky-text",
							"label": "Sticky Text"
						},
						{
							"value": "animate text-fade-rise",
							"label": "Fade In Rise"
						},
						{
							"value": "animate text-fade-rise sticky-text",
							"label": "Sticky Fade In Rise"
						},
						{
							"value": "animate sticky-text invert-color",
							"label": "Sticky Invert Color"
						},
            {
              "value": "animate text-fade-rise sticky-text invert-color",
              "label": "Sticky Fade In with Invert Color"
            }
					],
					"default": ""
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Slideshow Sequence",
      "category": "Advanced",
      "settings": {},
      "blocks": []
    }
  ]
}

{% endschema %}


{% include 'async-section' skeleton:'<div class="p-16 w-full h-50v bg-white"><div class="bg-gray-100 p-16 rounded-2xl h-full w-full drop-shadow-lg flex flex-col items-center justify-center"><span class="sr-only">Skeleton</span><span class="bg-gray-200 h-24 w-24"></span><span class="bg-gray-200 h-2 w-1/4 mt-4"></span><span class="bg-gray-200 h-2 w-1/4 mt-4"></span><span class="bg-gray-200 h-2 w-1/4 mt-4"></span><span class="bg-gray-200 h-2 w-1/4 mt-4"></span></div></div>' %}
{% unless defer_section %}

  <section class="section-{{section.id}} nav-transparent section-slideshow-sequence relative flex flex-col flex-wrap items-center justify-center {{ section.settings.section_top_margin }} {{ section.settings.section_bottom_margin }} {{ section.settings.section_top_margin_mobile }} {{ section.settings.section_bottom_margin_mobile }} {{ section.settings.height }} {{ section.settings.height_mobile }} {% if section.settings.show_desktop == true and section.settings.show_mobile == true %} block {% elsif section.settings.show_desktop == true and section.settings.show_mobile == false %} hidden dlg:font-bold {% elsif section.settings.show_desktop == false and section.settings.show_mobile == false %} hidden {% elsif section.settings.show_desktop == false and section.settings.show_mobile == true %} block lg:hidden {% endif %} {% if section.settings.remove_section_condition %} vwo-hide{% endif %}">

    <div class="grid grid-cols-12 {{ section.settings.gap }} {{ section.settings.gap_mobile }} {{ section.settings.height }} {{ section.settings.height_mobile }}">
    {% for block in section.blocks %}
      {% case block.type %}
        {% when 'parent' %}
          {% unless forloop.first %}
            <!-- Close the previous slideshow if this isn't the first block -->
                </div>
              </div> 
              {% render 'slideshow-controls', settings: slideshow.settings %}
            </section>
            <!-- /slideshow -->
          {% endunless %}
          <!-- Open a new slideshow -->
            {% assign slideshow = block %}
            <section class="slideshow-{{block.id}} relative {{ block.settings.item_width }} {{ block.settings.item_width_mobile }} {% unless section.settings.height contains 'lg:h-auto' %} lg:h-full{% endunless %}{% unless section.settings.height_mobile contains 'h-auto' %} h-full{% endunless %}">
              <div 
                class="swiper-container w-full h-full {% if block.settings.autoplay == true %}swiper-autoplay{% endif %}"
                neptune-swiper='{ 
                  "mousewheel": { "forceToAxis": true },
                  "updateOnImagesReady": true,
                  "effect": "{{ block.settings.swiper_effect }}",
                  "watchOverflow": true,
                  "preloadImages": true,
                  "observer": true,
                  "observeParents": true,
                  "observeSlideChildren": true,
                  "updateOnImagesReady": true,
                  {% if block.settings.autoplay == true %}
                  "autoplay": {
                    "delay": {{ block.settings.autoplay_slide_duration }}000,
                    "disableOnInteraction": false
                  },
                  {% endif %}
                  "pagination": {
                    "el": ".slideshow-{{block.id}} .pagination",
                    "bulletElement": "button",
                    "clickable": true
                  },
                  "loop": "{{ block.settings.loop | json }}",
                  "navigation": {
                    "nextEl": ".slideshow-{{block.id}} .swiper-button-next-unique",
                    "prevEl": ".slideshow-{{block.id}} .swiper-button-prev-unique"
                  }
                }'
              >
							<div class="swiper-wrapper h-full items-stretch" 
								neptune-surface="{
									'windowEdge': 'bottom',
									'elementEdge': 'top',
									'targets': [
										{
											'engage:action': {
												'classes': {
													'add': ['active']
												}
											}
										}
									]
								}"
							>
        {% when 'slide' %}
          <!-- Add a new slide to the current slideshow -->
            <article id="block-{{ block.id }}" class="swiper-slide h-full w-full {{block.settings.content_scroll_effect}}">
              {% if block.settings.link_element == 'block' %}
                <a class="block w-full h-full link black" href="{{ block.settings.url }}">
              {% endif %}
                {% render 'content-item', sectionType: 'slideshow', section: section.settings, block: block.settings, blockId: block.id %}
              {% if block.settings.link_element == 'block' %}
                </a>
              {% endif %}
            </article>
          <!-- /slide -->
      {% endcase %}
      {% if forloop.last %}
        <!-- Close the last slideshow -->
            </div>
          </div>
          {% render 'slideshow-controls', settings: slideshow.settings %}
        </section>
        <!-- /slideshow -->
      {% endif %}
    {% endfor %}
    </div>

  </section>

{% endunless %}
