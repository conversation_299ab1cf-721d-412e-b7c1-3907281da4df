<!DOCTYPE html>
<html lang="{{ locale }}" dir="{{ direction }}" class="{{ checkout_html_classes }}">
  <head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, height=device-height, minimum-scale=1.0, user-scalable=0">
    <meta name="referrer" content="origin">

    <title>{{ page_title }}</title>

    {{ content_for_header }}

    {{ checkout_stylesheets }}
    {{ checkout_scripts }}

    {% render 'vwo-smartcode' %}

    {% include "globale-checkout-css" %}

  </head>
  <body>

    {{ skip_to_content_link }}

    <div class="banner" data-header>
      <div class="wrap">
        {{ content_for_logo }}
      </div>
    </div>

    {{ order_summary_toggle }}

    <div class="content" data-content>
      <div class="wrap">
        <div class="main" role="main">
          <div class="main__header">
            {{ content_for_logo }}
            {{ breadcrumb }}
            {{ alternative_payment_methods }}
          </div>
          <div class="main__content">
            {{ content_for_layout }}
          </div>
          <div class="main__footer">
            {{ content_for_footer }}
          </div>
        </div>
        <div class="sidebar" role="complementary">
          <div class="sidebar__header">
            {{ content_for_logo }}
          </div>
          <div class="sidebar__content">
            {{ content_for_order_summary }}
          </div>
        </div>
      </div>
    </div>

    {{ tracking_code }}

    <script>
      setTimeout(function(){
        if(sessionStorage.getItem('sessionPromoCode')){        

          document.querySelector('[data-discount-field]').value = sessionStorage.getItem('sessionPromoCode')
          setTimeout(function(){
            var event = document.createEvent('Event');
            event.initEvent('input', true, true);
            document.querySelector('[data-discount-field]').dispatchEvent(event);
          
            setTimeout(function(){
              document.querySelector('[data-trekkie-id="apply_discount_button"]').click()
            },200)

          },200)
        }
      },2000)
    </script>

    {% assign persistentMessage = 'general.persistent_messaging.persistent_message' | t %}
    {% assign shippingMessage = 'checkout.domestic.shipping_message' | t %}

    {% if settings.international_logic %}
      {% render 'international-checkout' %}
    {% endif %}

    <style>
      {% if settings.hide_preview_bar %}
        #preview-bar-iframe {
          display: none !important;
        }
      {% endif %}

      .sidebar .order-summary__small-text.product__description__property {
        color: #FF0000;
      }
      .section__content.section__content__attentive {
        border-radius: 4px;
        background-color: #fafafa;
        border: solid 1px #e6e6e6;
        padding: 12px;
      }
      .section__content.section__content__attentive .input-checkbox {
        background: #fff;
      }
      .section__footer__attentive {
        font-size: 9px;
        font-weight: normal;
        font-style: normal;
        font-stretch: normal;
        line-height: 1.25;
        letter-spacing: normal;
        text-align: left;
        color: #000000;
        padding-top: 4px;
      }
      .section__footer__attentive a {
        color: #000000;
        text-decoration: underline;
      }
      .section__footer__attentive b {
        font-weight: bold;
      }
      .checkbox__label.checkbox__label__attentive {
        cursor: auto;
        margin-left: 0.75em;
      }
      .checkbox__label__attentive__header {
        font-size: 14px;
        font-weight: bold;
        font-style: normal;
        font-stretch: normal;
        line-height: 1;
        letter-spacing: normal;
        text-align: left;
        color: #333333;
      }
      .checkbox__label__attentive__subheader {
        font-size: 12px;
        font-weight: normal;
        font-style: normal;
        font-stretch: normal;
        line-height: 1.17;
        letter-spacing: normal;
        text-align: left;
        color: #737373;
        padding-top: 4px;
      }
      .checkbox-wrapper {
        display: -webkit-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
        align-items: baseline;
      }

      {% if persistentMessage != blank %}
        .step__sections:after {
          content: "{{ persistentMessage }}";
          display: block;
          color: {{ settings.persistent_messaging_color }};
          font-weight: {{ settings.persistent_messaging_weight }};
          margin: 0.75em 0;
        }
      {% endif %}

      {% if locale contains '-US' and shippingMessage != blank %}
        .section--shipping-method .section__title {
          display: flex;
          flex-wrap: wrap;
          column-gap: 20px;
          row-gap: 4px;
        }
        .section--shipping-method .section__title:after {
          content: "{{ shippingMessage }}";
          font-size: 14px;
          color: {{ settings.persistent_messaging_color }};
          font-weight: {{ settings.persistent_messaging_weight }};
        }
      {% endif %}

    </style>
  </body>
</html>