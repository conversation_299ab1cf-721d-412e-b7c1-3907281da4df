<!doctype html>
<html class="no-js" lang="{{ request.locale.iso_code }}" n:scroll-direction="Neptune.surface.direction" n:scroll-position="Neptune.surface.scrollPos" domestic>
  <head>

    {% render 'code' zone:'head' position:'start' %}

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="">
    <link rel="canonical" href="{{ canonical_url }}">
    <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>

    {%- if settings.favicon != blank -%}
      <link rel="icon" type="image/png" href="{{ settings.favicon | img_url: '32x32' }}">
    {%- endif -%}

    {%- unless settings.type_header_font.system? and settings.type_body_font.system? -%}
      <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
    {%- endunless -%}

    {% if settings.show_meta %}
    
      <title>{{ page_title }}{%- if current_tags %} | tagged "{{ current_tags | join: ', ' }}"{% endif -%}{%- if current_page != 1 %} | Page {{ current_page }}{% endif -%}{%- unless page_title contains shop.name %} | {{ shop.name }} DENIM{% endunless -%}</title>

      {% if page_description %}
        <meta name="description" content="{{ page_description | escape }}">
      {% endif %}

    {% endif %}

    {% render 'meta-tags' %}

    <script>
      window.cart = {{ cart | json }};  
      window.customer = {% render 'customer-data' %};
      window.requiredError = "{{ settings.required_error }}"; // set globally so this setting is accessible on all templates 
    </script> 
    
    {{ content_for_header }}

    {%- liquid
      assign body_font_bold = settings.type_body_font | font_modify: 'weight', 'bold'
      assign body_font_italic = settings.type_body_font | font_modify: 'style', 'italic'
      assign body_font_bold_italic = body_font_bold | font_modify: 'style', 'italic'
    %}

    {% include 'theme-styles' %}
    {{ 'main.css' | asset_url | stylesheet_tag }} 

    <script>
      window.addEventListener('load', function() {
        document.documentElement.className = document.documentElement.className.replace('no-js', 'js');
      });
      document.addEventListener('DOMContentLoaded', function() {
        if (Shopify.designMode) {
          document.querySelector('.template-search :is([data-section-type="custom-content"], .section-slideshow-collection)').style.setProperty('display', 'block', 'important');
        } 
      })
    </script> 

    <script src="{{ 'animations.js' | asset_url }}"></script>
    <script src="{{ 'vendors.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'runtime.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'main.js' | asset_url }}" defer="defer"></script>

    <script src="{{ 'customer.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'customer.segmentation.js' | asset_url }}" defer="defer"></script>

    {% render 'code' zone:'head' position:'end' %}

  </head>

  <body class="{% unless template %} proxy {%endunless%} {% if customer %}customer-logged-in {% endif %}template-{{ template | replace: '.', ' ' | truncatewords: 1, '' | handle }}{% if womens_header_theme != blank %} {{ womens_header_theme }}{% endif %}{% if mens_header_theme != blank %} {{ mens_header_theme }}{% endif %}{% if womens_logo_theme != blank %} {{ womens_logo_theme }}{% endif %}{% if mens_logo_theme != blank %} {{ mens_logo_theme }}{% endif %}{% if settings.show_promo_msg or settings.show_promo_msg_2 %} hdr--promo-on{% endif %}{% if settings.show_promo_msg_mens or settings.show_promo_msg_mens_2 %} hdr--promo-on-mens{% endif %}{% if template contains 'mens-home' %} template-index{% endif %}{% if template contains 'mens' or mode %} mens{% endif %}{% if template == 'index' or isWomen %} womens{% endif %}{% if isMen %} mens{% endif %}{% if settings.layout_toggle == 'on' %} template-index--alt{% endif %}{% if collection.handle contains 'v-excited' %} v-custom-cursor{% endif %} hide-localized-content" neptune-engage='{on:keyup,which:Tab,classes:add:accessibility-mode}'>

    {% render 'code' zone:'body' position:'start' %}

    <a class="skip-to-content-link button button--primary focus:text-white absolute top-0 left-0 m-2 z-50 inline-block transform translate-x-off focus:translate-x-0" href="#MainContent">
      {{ "accessibility.skip_to_text" | t }}
    </a>

    {% if template == 'index' %}
      <h1 class="sr-only m-0 p-0">{{ shop.name }}</h1>
    {% endif %}
    {% section 'header' %}
    {% section 'mega-menu' %}
    {% section 'panel-menu' %}
    {% section 'search' %}
    {% section 'cart-modal' %}
    {% section 'product-badges' %} 

    <main 
      id="MainContent" 
      class="content-for-layout focus-none" 
      role="main" tabindex="-1"
    >
      {{ content_for_layout }}
    </main>

    {% section 'recently-viewed' %}
    {% section 'footer' %}
    {% render 'global-e' %}
    {% section 'popups' %}

    <ul hidden>
      <li id="a11y-refresh-page-message">{{ 'accessibility.refresh_page' | t }}</li>
    </ul>

    <script>
      window.routes = {
        cart_add_url: '{{ routes.cart_add_url }}',
        cart_change_url: '{{ routes.cart_change_url }}',
        cart_update_url: '{{ routes.cart_update_url }}'
      };

      window.cartStrings = {
        error: `{{ 'sections.cart.cart_error' | t }}`,
        quantityError: `{{ 'sections.cart.cart_quantity_error_html' | t }}`
      }

      window.variantStrings = {
        addToCart: `{{ 'products.product.add_to_cart' | t }}`,
        soldOut: `{{ 'products.product.sold_out' | t }}`,
        unavailable: `{{ 'products.product.unavailable' | t }}`,
      }

      window.accessibilityStrings = {
        shareSuccess: `{{ 'general.share.success_message' | t }}`,
      }
    </script>

    {% render 'modal-overlay' %}
    {% render 'quick-add' %}
    {% render 'lightbox' %}
    {% render 'icons' %}
    {% render 'esp-tracking' product:product %}
    {% if template contains 'product' %}
      {% render 'payment-modal' %}
    {% endif %}
    
    {% comment %}{% render 'gorgias' %}{% endcomment %}

    {% if settings.ss_enable and settings.ss_site_id %}
      {% render 'ss-conversion-tracking' %}
      {% render 'searchspring-search-settings' %}
    {% endif %}

    {% render 'code' zone:'body' position:'end' %}

    {% if settings.hide_preview_bar == true %}
      <style>
        #preview-bar-iframe {display:none!important;}
      </style>
    {% endif %}

    {% render 'scripts' %} 

  </body>

  <!-- version 2.0 -->

</html>

