{% comment %}
    Renders an article card for a given blog with settings to either show the image or not.

    Accepts:
    - blog: {Object} Blog object
    - article: {Object} Article object
    - show_image: {String} The setting either show the article image or not. If it's not included it will show the image by default
    - show_date: {String} The setting either show the article date or not. If it's not included it will not show the image by default
    - show_author: {String} The setting either show the article author or not. If it's not included it will not show the author by default

    Usage:
    {% render 'article-card' blog: blog, article: article, show_image: section.settings.show_image %}
{% endcomment %}

<article 
  class="article-card fade-in-rise animate animate-slow {% if article.image == blank or show_image == false %} article-card--no-image{% endif %}" 
  aria-labelledby="Article-{{ article.id }}"
  neptune-surface="{
    'windowEdge': 'bottom',
    'elementEdge': 'top',
    'delay': '400',
    'targets': [
      {
        'engage:action': {
          'classes': {
            'add': ['active']
          }
        }
      }
    ]
  }"
>
  <a href="{{ article.url }}" class="article-content motion-reduce">
    {%- if show_image == true and article.image -%}
      <div class="article-card__image-wrapper mb-8">
        <div class="article-card__image relative aspect-w-4 aspect-h-3">
          <img
            srcset="{%- if article.image.src.width >= 165 -%}{{ article.image.src | img_url: '165x' }} 165w,{%- endif -%}
              {%- if article.image.src.width >= 360 -%}{{ article.image.src | img_url: '360x' }} 360w,{%- endif -%}
              {%- if article.image.src.width >= 533 -%}{{ article.image.src | img_url: '533x' }} 533w,{%- endif -%}
              {%- if article.image.src.width >= 720 -%}{{ article.image.src | img_url: '720x' }} 720w,{%- endif -%}
              {%- if article.image.src.width >= 1000 -%}{{ article.image.src | img_url: '1000x' }} 1000w,{%- endif -%}
              {%- if article.image.src.width >= 1500 -%}{{ article.image.src | img_url: '1500x' }} 1500w,{%- endif -%}"
            src="{{ article.image.src | img_url: '533x' }}"
            sizes="(min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 }}px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
            alt="{{ article.image.src.alt | escape }}"
            width="{{ article.image.width }}"
            height="{{ article.image.height }}"
            loading="lazy"
          
            class="motion-reduce absolute object-cover"
          >
        </div>
      </div>
    {%- endif -%}

    <div class="article-card__info w-full">
      <header class="article-card__header">
        <h2 class="article-card__title text-base mb-3" id="Article-{{ article.id }}">
          {{ article.title | escape }}
        </h2>
        
        <div class="article-card__meta flex gap-x-3 mb-6">  
          {%- if show_category -%}
          <p class="article-card__category">
            {% for tag in article.tags %}
              {% if tag contains 'Category:' %}
                {{ tag | split: ':' | last }}
              {% endif %}
            {% endfor %}
          </p>
          {%- endif -%}

          {% if show_category and show_date %}
            <span class="article-card__divider">&#124;</span>
          {% endif %}

          {%- if show_date -%}
            <span class="article-card__date">
              {{- article.published_at | time_tag: "%m.%d.%y" -}}
            </span>
          {%- endif -%}
        </div>
      </header>
      {%- if show_excerpt and article.excerpt.size > 0 or article.content.size > 0 -%}
        <p class="article-card__excerpt rte-width text-base mb-0">
          {{ article.excerpt | strip_html | truncate: section.settings.truncate_length }}
        </p>
      {%- endif -%}
    </div>
  </a>
</article>