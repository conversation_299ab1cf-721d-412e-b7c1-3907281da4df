{%- liquid
  assign size_guide_countries =  settings.countries_list
%}

{% if product %}
    {% assign guides_data = guides_data %}
{% else %}
    {% assign guides_data = section.blocks %}    
{% endif %}

<script>
  const size_guide = {
    active_country: '',
    page_type: "{% if product %}modal{% else %}section{% endif %}",
    countries: [
        {% if product %}  
            {% assign countries_modal = guides_data.countries_values | newline_to_br | split: '<br />' %}
            {% for group in countries_modal %}
                {% assign key = group | split: '_' | first %}
                {% assign value = group | split: '_' | last %}
                {% assign values = value | split: ',' %}
                { key: "{{ key | strip }}", values: [{% for n in values %}'{{ n }}'{% unless forloop.last %},{% endunless %}{% endfor %}] }{% unless forloop.last %},{% endunless %}
            {% endfor %}
        {% else %}
            {% assign countries = size_guide_countries | newline_to_br | split: '<br />' %}
            {% for country in countries %}
                "{{ country | strip }}"{% unless forloop.last %},{% endunless %}
            {% endfor %}
        {% endif %}
    ],
    charts: [
        {% if product %}
        {
            title: "{{ guides_data.title }}",
            note_text: "{{ settings.note_text }}",
            mother: {
            {% assign mother_sizing = guides_data.mother_sizing_values | newline_to_br | split: '<br />' %}
            {% for group in mother_sizing %}
                {% assign key = group | split: '_' | first %}
                {% assign value = group | split: '_' | last %}
                {% assign values = value | split: ',' %}
                "{{ key | strip }}": [{% for n in values %}'{{ n }}'{% unless forloop.last %},{% endunless %}{% endfor %}]{% unless forloop.last %},{% endunless %}
            {% endfor %}
            },
            measurements: {
            chest: [{{ guides_data.chest_values }}],
            waist: [{{ guides_data.waist_values }}],
            hip: [{{ guides_data.hip_values }}]
            },
            countries_sizes: {
            {% assign countries_sizes = guides_data.countries_values | newline_to_br | split: '<br />' %}
            {% for group in countries_sizes %}
                {% assign key = group | split: '_' | first %}
                {% assign value = group | split: '_' | last %}
                {% assign values = value | split: ',' %}
                "{{ key | strip }}": [{% for n in values %}'{{ n }}'{% unless forloop.last %},{% endunless %}{% endfor %}]{% unless forloop.last %},{% endunless %}
            {% endfor %}
            }
        }
        {% else %}
            {% for block in guides_data %}
            {
                title: "{{ block.settings.title }}",
                note_text: "{{ block.settings.note_text }}",
                mother: {
                {% assign mother_sizing = block.settings.mother_sizing_values | newline_to_br | split: '<br />' %}
                {% for group in mother_sizing %}
                    {% assign key = group | split: '_' | first %}
                    {% assign value = group | split: '_' | last %}
                    {% assign values = value | split: ',' %}
                    "{{ key | strip }}": [{% for n in values %}'{{ n }}'{% unless forloop.last %},{% endunless %}{% endfor %}]{% unless forloop.last %},{% endunless %}
                {% endfor %}
                },
                measurements: {
                chest: [{{ block.settings.chest_values }}],
                waist: [{{ block.settings.waist_values }}],
                hip: [{{ block.settings.hip_values }}]
                },
                countries_sizes: {
                {% assign countries_sizes = block.settings.countries_values | newline_to_br | split: '<br />' %}
                {% for group in countries_sizes %}
                    {% assign key = group | split: '_' | first %}
                    {% assign value = group | split: '_' | last %}
                    {% assign values = value | split: ',' %}
                    "{{ key | strip }}": [{% for n in values %}'{{ n }}'{% unless forloop.last %},{% endunless %}{% endfor %}]{% unless forloop.last %},{% endunless %}
                {% endfor %}
                }
            }{% unless forloop.last %},{% endunless %}
            {% endfor %}
        {% endif %}
    ]
}
  window.size_guide = size_guide
</script>
<script src="{{ 'size-guide.js' | asset_url }}" defer="defer"></script>

<div class="size-guide__country-selector mt-4 lg:text-center text-left max-w-full mx-auto">
    <label for="size-guide-country" class="text-base">{{ section.settings.select_country_title }}</label>
    <div class="field field--select mt-3" neptune-liquid="{topic:SizeGuide, source:size_guide}">
      <template>
        {% raw %}
          <select id="size-guide-country" onchange="SizeGuide.changeCountry(this.value)">
            {% if page_type == 'modal'%}
              {% for country in countries %}
                {% if active_country == country.key %}
                  <option selected>{{ country.key | strip }}</option>
                {% else %}
                  <option>{{ country.key | strip }}</option>
                {% endif %}
              {% endfor %}
            {% else %}     
              {% for country in countries %}
                {% if active_country == country %}
                  <option selected>{{ country | strip }}</option>
                {% else %}
                  <option>{{ country | strip }}</option>
                {% endif %}
              {% endfor %}
            {% endif %}
          </select>
        {% endraw %}
      </template>
    </div>
</div>

<div class="size-guide__block-content" neptune-liquid="{topic:SizeGuide, source:size_guide}">
    <template>
      {% raw %}
        {% for chart in charts %}
          <div class="size-guide__unit-switcher flex lg:justify-center justify-between items-start w-full gap-x-8 container mx-auto relative">        
            {% if chart.title != blank %}
              <h4 class="font-body text-base my-0 text-left">{{ chart.title }}</h4>
            {% endif %}     
        
            <fieldset class="flex items-center border-none gap-6">
              <legend class="sr-only">Switch unit</legend>

              {% if chart.unit != nil %}
                {% if chart.unit == 'in' %}
                  <label for="size-guide-unit-inches-{{ forloop.index }}" class="flex items-center gap-1">
                    <input type="radio" class="accent-black" name="size-guide-unit-{{ forloop.index }}" id="size-guide-unit-inches-{{ forloop.index }}" value="in" onchange="SizeGuide.changeUnit(this.value, {{ forloop.index0 }})"  checked />
                    <span>inches</span>
                  </label>

                  <label for="size-guide-unit-cm-{{ forloop.index }}" class="flex items-center gap-1">
                    <input type="radio" class="accent-black" name="size-guide-unit-{{ forloop.index }}" id="size-guide-unit-cm-{{ forloop.index }}" value="cm" onchange="SizeGuide.changeUnit(this.value, {{ forloop.index0 }})"/>
                    <span>cm</span>
                  </label>
                {% else %}
                  <label for="size-guide-unit-inches-{{ forloop.index }}" class="flex items-center gap-1">
                    <input type="radio" class="accent-black" name="size-guide-unit-{{ forloop.index }}" id="size-guide-unit-inches-{{ forloop.index }}" value="in" onchange="SizeGuide.changeUnit(this.value, {{ forloop.index0 }})"  />
                    <span>inches</span>
                  </label>

                  <label for="size-guide-unit-cm-{{ forloop.index }}" class="flex items-center gap-1">
                    <input type="radio" class="accent-black" name="size-guide-unit-{{ forloop.index }}" id="size-guide-unit-cm-{{ forloop.index }}" value="cm" onchange="SizeGuide.changeUnit(this.value, {{ forloop.index0 }})" checked/>
                    <span>cm</span>
                  </label>             
                {% endif %}       
              {% else %}                      
                <label for="size-guide-unit-inches-{{ forloop.index }}" class="flex items-center gap-1">
                  <input type="radio" class="accent-black" name="size-guide-unit-{{ forloop.index }}" id="size-guide-unit-inches-{{ forloop.index }}" value="in" onchange="SizeGuide.changeUnit(this.value, {{ forloop.index0 }})"  checked />
                  <span>inches</span>
                </label>

                <label for="size-guide-unit-cm-{{ forloop.index }}" class="flex items-center gap-1">
                  <input type="radio" class="accent-black" name="size-guide-unit-{{ forloop.index }}" id="size-guide-unit-cm-{{ forloop.index }}" value="cm" onchange="SizeGuide.changeUnit(this.value, {{ forloop.index0 }})"/>
                  <span>cm</span>
                </label>
              {% endif %}
            </fieldset>
          </div>
        
          <div class="size-guide__table mt-3 container mx-auto">
            <div class="size-guide--wrapper scrollbar-show flex text-center overflow-y-hidden overflow-x-scroll">

        
                  {% assign grid_size = 0 %}
                  {% for group in chart.mother %}
                    {% assign grid_size = grid_size | plus: group[1].size %}
                  {% endfor %}

        
                  <div class="flex-shrink-0 z-10 sticky left-0 bg-near-white">
                    <div class="size-guide__header inline-flex flex-col h-full gap-px bg-black border-black border rounded-l-[10px] overflow-hidden">
                      <div class="table__heading flex-grow">Mother Sizing</div>
                      {% assign active_country_present = false %}
                      {% for aux in chart.countries_sizes %}
                        {% if aux[0] == active_country %}
                          {% assign active_country_present = true %}
                        {% endif %}
                      {% endfor %}
                      {% if active_country_present %}
                        <div class="table__heading">{{ active_country }}</div>
                      {% endif %}
                      <div class="table__heading">Chest</div>
                      <div class="table__heading">Waist</div>
                      <div class="table__heading">Hip</div>
                    </div>
                  </div>
                  <div class="table__grid" style="--grid-size: {{ grid_size }}">
                  
                    {% for group in chart.mother %}
                      <div class="table__heading col-span-{{ group[1].size }}">{{ group[0] }}</div>
                    {% endfor %}
                  
                    {% for group in chart.mother %}
                      {% for value in group[1] %}
                        <div class="table__item">{{ value }}</div>
                      {% endfor %}
                    {% endfor %}
                  
                    {% for group in chart.countries_sizes %}
                      {% unless group[0] == active_country %}{% continue %}{% endunless %}
                      {% for value in group[1] %}
                        <div class="table__item">{{ value }}</div>
                      {% endfor %}
                    {% endfor %}
                  
                    {% for measurement in chart.measurements %}
                        {% for value in measurement[1] %}
                          {% case chart.unit %}
                            {% when 'cm' %}
                              <div class="table__item">{{ value | times: 2.54 | round: 1 }} cm</div>
                            {% else %}
                              <div class="table__item">{{ value }} in</div>
                          {% endcase %}
                        {% endfor %}
                    {% endfor %}
                    
                  </div>
            </div>
        
            <p class="size-guide__note text-center text-xs lg:px-0 lg:px-8 leading-normal mt-0 lg:max-w-full max-w-[251px] mx-auto">{{ chart.note_text }}</p>
          </div>
        {% endfor %}
      {% endraw %}
    </template>
</div> 

<div class="size-guide__images lg:pl-16 lg:pr-8 px-[22px] mt-9">
    <h4 class="font-body text-base my-0">{{ section.settings.guide_title }}</h4>
    <div class="size-guide__images-content block w-full relative mt-1">

      {% if section.settings.guide_image_mobile != blank %}
        {% assign image_classes = 'guide-image-desktop lg:block hidden' %}
      {% else %}  
        {% assign image_classes = 'guide-image-desktop single_image' %}
      {% endif %}
      {{ section.settings.guide_image |
        image_url: width: 552 |
        image_tag:
        widths: '500, 600, 700',
        class: image_classes,
        sizes: '100vw',
        style: 'width: 276px; margin: auto' 
      }}

      {% if section.settings.guide_image_mobile != blank %}      
        {{ section.settings.guide_image_mobile |
          image_url: width: 270 |
          image_tag:
          widths: '300, 400, 500',
          class: 'guide-image-mobile lg:hidden block',
          sizes: '100vw',
          style: 'width: 135px; margin: auto' 
        }}
      {% endif %}

      {% if section.settings.chest_title != blanh and section.settings.chest_text != blank  %}
        <div class="text-{{ section.settings.chest_title  | handleize }} w-full lg:max-w-[163px] max-w-[80px] leading-3 absolute lg:top-1/4 top-[12%] lg:left-3.5 left-1.5">
          <p class="text-sm mb-0">{{ section.settings.chest_title }}</p>
          <span class="text-xs">{{ section.settings.chest_text }}</span>
        </div>
      {% endif %}

      {% if section.settings.hip_title != blanh and section.settings.hip_text != blank  %}
        <div class="text-{{ section.settings.hip_title | handleize }} w-full lg:max-w-[163px] max-w-[80px] leading-3 absolute lg:bottom-[20%] bottom-[-6%] lg:left-3.5 left-1.5">
          <p class="text-sm mb-0">{{ section.settings.hip_title }}</p>
          <span class="text-xs">{{ section.settings.hip_text }}</span>
        </div>
      {% endif %}

      {% if section.settings.waist_title != blanh and section.settings.waist_text != blank  %}
        <div class="text-{{ section.settings.waist_title | handleize }} w-full lg:max-w-[163px] max-w-[80px] leading-3 absolute right-0 lg:bottom-1/4 bottom-[15%]">
          <p class="text-sm mb-0">{{ section.settings.waist_title }}</p>
          <span class="text-xs">{{ section.settings.waist_text }}</span>
        </div>
      {% endif %}

    </div>
  </div>