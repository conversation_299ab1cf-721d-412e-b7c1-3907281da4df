  {% render 'price-removal' %}
  {% raw %}
  {% if product.subtotal %}
    <span class="product-essentials__price">
      {{ product.subtotal | money: 'local' | remove: removal }}
    </span>
  {% else %} 
    {% assign discount_amount = variant.price | divided_by: variant.compare_at_price | times: -100 | plus: 100 | round %}
    {% assign discount_threshold = 10 %}

    {% if variant.available %}
      <p class="product-essentials__price type--product m-0 flex justify-center">
        {% if variant.price != variant.compare_at_price and variant.compare_at_price > 0 %}
          <span class="mr-2 text-gray-dark line-through">{{variant.compare_at_price | money: 'local' | remove: removal }}</span>
        {% endif %}
        <span class="{% if variant.price != variant.compare_at_price and variant.compare_at_price > 0 %}text-black{% endif %}">
          <span>{{ variant.price | money: 'local' | remove: removal }}</span>
        </span>
      </p>                       
    {% else %}
      <p class="product-essentials__price type--product m-0 flex justify-center">
        {% if variant.price != variant.compare_at_price and variant.compare_at_price > 0 %}<span class="mr-2 text-gray-dark line-through">{{variant.compare_at_price | money: 'local' | remove: removal }}</span>{% endif %}
        <span class="{% if variant.price != variant.compare_at_price and variant.compare_at_price > 0 %}text-black{% endif %}">
          {{ variant.price | money: 'local' | remove: removal }}
        </span>
      </p>                       
    {% endif %}

  {% endif %}

{% endraw %}  
