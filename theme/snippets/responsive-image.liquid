{% comment %} 


This is generated by webpack so don't edit this unless you want to be sad when your work gets overwritten in production.
You can edit this file in ../../components/** 


{% endcomment %}






{%- comment -%}
    It creates a style tag and it restricts an image from growing larger than its max resolution.

    Dependencies:
    - Lazysizes plugin (https://github.com/aFarkas/lazysizes) which enable responsive image with faster load time and better performance.
    - Lazysizes Responsive Images as a Service (https://github.com/aFarkas/lazysizes/tree/gh-pages/plugins/rias) To load the correct image size with our CDN
    - Lazysizes Bgset (https://github.com/aFarkas/lazysizes/tree/gh-pages/plugins/bgset) To use responsive images on background-image (CSS)

    Accepts:
    - max_width: {Number} Max width of the image container
    - max_height: {Number} Max height of the image container
    - image: {Object} Image object
    - image_class: {String} class name of the <img />
    - image_attributes: {String}  additional HTML attributes of the <img />
    - wrapper_class: {String} class name of the <div> wrapper
    - wrapper_attributes: {String} additional HTML attributes of the <div> wrapper

    Usage:
    In your liquid template file, copy the following line
    - {% include 'responsive-image' with image: featured_image, image_class: "css-class", wrapper_class: "wrapper-css-class", max_width: 700, max_height: 800 %}
{%- endcomment -%}

{%- comment -%} Added incremental number to avoid conflict styling code when the same images are using this snippet {%- endcomment -%}
{%- capture responsive_image_counter %}{% increment responsive_image_counter %}{% endcapture -%}

{% comment %}<style>
  {%- if image.aspect_ratio <= 1 -%}
    {%- assign max_height_image = image.height | at_most: max_height -%}
    {%- assign max_width_image = max_height_image | times: image.aspect_ratio -%}
  {%- else -%}
    {%- assign max_width_image = image.width | at_most: max_width -%}
    {%- assign max_height_image = max_width_image | divided_by: image.aspect_ratio -%}
  {%- endif -%}

  #Image-{{ image.id }}-{{ responsive_image_counter }} {
    max-width: {{ max_width_image }}px;
    max-height: {{ max_height_image }}px;
  }
  #ImageWrapper-{{ image.id }}-{{ responsive_image_counter }} {
    max-width: {{ max_width_image }}px;
  }

  #ImageWrapper-{{ image.id }}-{{ responsive_image_counter }}::before {
    padding-top:{{ max_height_image | divided_by: max_width_image | times: 100}}%;
  }
</style>
{% endcomment %}

{%- assign img_url = image | img_url: '1x1' | replace: '_1x1.', '_{width}x.' -%}

{%- comment -%} Limit image widths to valid options based on image.width {%- endcomment -%}
{%- assign image_widths = '180,360,540,720,900,1080,1296,1512,1728,1944,2160,2376,2592,2808,3024' | split: ',' -%}
{%- capture image_widths -%}
  {%- for width in image_widths -%}
    {%- comment -%} Check if image width is less or equal to width {%- endcomment -%}
    {%- assign width_num = width | plus: 0 | round -%}
    {%- if image.width >= width_num -%}{{ width_num }},{%- endif -%}
  {%- endfor -%}
  {{ image.width }}
{%- endcapture -%}
{%- assign image_widths = image_widths | strip -%}

<div id="ImageWrapper-{{ image.id }}-{{ responsive_image_counter }}" data-image-id="{{ image.id }}" class="responsive-image__wrapper {{ wrapper_class }}" {{ wrapper_attributes }}>

  {% if image.alt contains '.mp4' %}

  <video src="{{image.alt}}" playsinline mute class="responsive-image__image responsive-image__image--video w-full h-full object-cover" {{ video_attributes }}>
    <source src="{{image.alt}}">
  </video>

  {% else %}
  <img id="Image-{{ image.id }}-{{ responsive_image_counter }}"
    class="responsive-image__image w-full m-0 lazyload {{ image_class }}"
    src="{{ image | img_url: '300x' }}"
    data-src="{{ img_url }}"
    data-widths="[{{ image_widths }}]"
    data-aspectratio="{{ image.aspect_ratio }}"
    data-sizes="auto"
    itemprop="image"
    alt="{{ image.alt | escape }}"
    {{ image_attributes }}
  >

  {% endif %}
</div>

<noscript>
  <img class="{{ image_class }} w-100 ma0" src="{{ image | img_url: '2048x2048' }}" alt="{{ image.alt | escape }}">
</noscript>
