
{% comment %}
 SEO Manager - v6.1.2
 Copyright (c) venntov
 https://venntov.com / http://SEOManager.com
 Josh Highland
 NOTICE: All information contained herein is property of venntov.
 The intellectual and technical concepts contained herein are proprietary
 to venntov and are protected by trade secret and copyright law.
 Reproduction of this code is strictly forbidden unless prior written
 permission is obtained from venntov. If violated, legal action
 will be taken. Just don't do it.
{% endcomment %}
{% capture DAKYCLFQTU %} {% endcapture %} {%- assign TAAKQUBPIQ = shop.metafields.SEOMetaManager.globalConfig -%} {%- assign AGBXXDEBLZ = TAAKQUBPIQ | remove: "{"| remove: "}" -%} {%- assign CLYKCLQPAS = AGBXXDEBLZ | split: ',"' -%} {%- for JPIYAMILEL in CLYKCLQPAS -%} {%- assign POGRNDKRAT = JPIYAMILEL | remove: '"' -%} {%- assign ZKUEALXMJM = POGRNDKRAT | split: ":" -%} {%- case ZKUEALXMJM[0] -%} {%- when 'product_title_template' -%} {%- assign ZSWFOUEXUL = ZKUEALXMJM[1] -%} {%- when 'product_title_template_force' -%} {%- assign KAYNGEYGQM = ZKUEALXMJM[1] -%} {%- when 'product_noindex_follow' -%} {%- assign JIATSMXOUJ = ZKUEALXMJM[1] -%} {%- when 'collection_title_template' -%} {%- assign MICGTVUZQQ = ZKUEALXMJM[1] -%} {%- when 'collection_title_template_force' -%} {%- assign PDSXSFJVNU = ZKUEALXMJM[1] -%} {%- when 'collection_noindex_follow' -%} {%- assign DTJKDDUZEB = ZKUEALXMJM[1] -%} {%- when 'page_title_template' -%} {%- assign PGVMEOPPJQ = ZKUEALXMJM[1] -%} {%- when 'page_title_template_force' -%} {%- assign ITPQMQVWPJ = ZKUEALXMJM[1] -%} {%- when 'page_noindex_follow' -%} {%- assign OOHXWLPJOE = ZKUEALXMJM[1] -%} {%- when 'blog_title_template' -%} {%- assign GYQYBCCDXD = ZKUEALXMJM[1] -%} {%- when 'blog_title_template_force' -%} {%- assign UNCPNOZCGZ = ZKUEALXMJM[1] -%} {%- when 'blog_noindex_follow' -%} {%- assign UDRFQQLRJJ = ZKUEALXMJM[1] -%} {%- when 'hidePageNumber' -%} {%- assign XOAQUVBFEG = ZKUEALXMJM[1] -%} {%- when 'google_notranslate' -%} {%- assign NIRZIYCLRK = ZKUEALXMJM[1] -%} {%- when 'google_unavailable_after_globalDisabled' -%} {%- assign BQEEKZVZNH = ZKUEALXMJM[1] -%} {%- when 'google_nositelinkssearchbox' -%} {%- assign ZGIQFFASNZ = ZKUEALXMJM[1] -%} {%- when 'show_keywordsMeta' -%} {%- assign CTOQJIJTEW = ZKUEALXMJM[1] -%} {%- when 'metaRedirect_globalDisabled' -%} {%- assign JQRIIZLDRE = ZKUEALXMJM[1] -%} {%- endcase -%} {%- endfor -%} {%- if template contains 'index' -%} {%- assign HJZJMTDFKI = 'shop' -%} {%- assign SUYRUDMEZJ = shop.metafields.SEOMetaManager -%} {%- assign KOKGAKSHFC = 'yes' -%} {%- assign BLMKGYWZXV = 'homepage' -%} {%- assign UYVGTSBASG = shop.metafields.SEOMetaManager.config_homepage -%} {%- elsif template contains 'product' -%} {%- assign HJZJMTDFKI = 'product' -%} {%- assign SUYRUDMEZJ = product.metafields.SEOMetaManager -%} {%- assign YWQSAMAJWR = ZSWFOUEXUL -%} {%- assign MPXUUEZDJU = KAYNGEYGQM -%} {%- if JIATSMXOUJ == 'yes' -%} {%- assign YVUHMWIJEI = 'yes' -%} {%- endif -%} {%- elsif template contains 'page' -%} {%- assign HJZJMTDFKI = 'page' -%} {%- assign SUYRUDMEZJ = page.metafields.SEOMetaManager -%} {%- assign YWQSAMAJWR = PGVMEOPPJQ -%} {%- assign MPXUUEZDJU = ITPQMQVWPJ -%} {%- if OOHXWLPJOE == 'yes' -%} {%- assign YVUHMWIJEI = 'yes' -%} {%- endif -%} {%- elsif template contains 'list-collections' -%} {%- assign HJZJMTDFKI = 'shop' -%} {%- assign SUYRUDMEZJ = shop.metafields.SEOMetaManager -%} {%- assign KOKGAKSHFC = 'yes' -%} {%- assign BLMKGYWZXV = 'collectionsALL' -%} {%- assign UYVGTSBASG = shop.metafields.SEOMetaManager.config_collectionsAll -%} {%- assign TEKKFGSUBE = 'yes' -%} {%- elsif template contains 'collection' -%} {%- assign HJZJMTDFKI = 'collection' -%} {%- assign SUYRUDMEZJ = collection.metafields.SEOMetaManager -%} {%- if collection.title == 'Products' -%} {%- assign SUYRUDMEZJ = shop.metafields.SEOMetaManager -%} {%- assign TEKKFGSUBE = 'yes' -%} {%- endif -%} {%- if collection.handle == 'all' -%} {%- assign SUYRUDMEZJ = shop.metafields.SEOMetaManager -%} {%- assign TEKKFGSUBE = 'yes' -%} {%- endif -%} {%- assign YWQSAMAJWR = MICGTVUZQQ -%} {%- assign MPXUUEZDJU = PDSXSFJVNU -%} {%- if DTJKDDUZEB == 'yes' -%} {%- assign YVUHMWIJEI = 'yes' -%} {%- endif -%} {%- elsif template contains 'article' -%} {%- assign HJZJMTDFKI = 'article' -%} {%- assign SUYRUDMEZJ = article.metafields.SEOMetaManager -%} {%- assign YWQSAMAJWR = GYQYBCCDXD -%} {%- assign MPXUUEZDJU = UNCPNOZCGZ -%} {%- if UDRFQQLRJJ == 'yes' -%} {%- assign YVUHMWIJEI = 'yes' -%} {%- endif -%} {%- elsif template contains 'blog' -%} {%- assign HJZJMTDFKI = 'blog' -%} {%- assign SUYRUDMEZJ = blog.metafields.SEOMetaManager -%} {%- elsif template contains 'search' -%} {%- assign HJZJMTDFKI = 'shop' -%} {%- assign SUYRUDMEZJ = shop.metafields.SEOMetaManager -%} {%- assign KOKGAKSHFC = 'yes' -%} {%- assign BLMKGYWZXV = 'search' -%} {%- assign UYVGTSBASG = shop.metafields.SEOMetaManager.config_search -%} {%- elsif template contains '404' -%} {%- assign HJZJMTDFKI = 'shop' -%} {%- assign SUYRUDMEZJ = shop.metafields.SEOMetaManager -%} {%- assign KOKGAKSHFC = 'yes' -%} {%- assign BLMKGYWZXV = 'page404' -%} {%- assign UYVGTSBASG = shop.metafields.SEOMetaManager.config_404 -%} {%- else -%} <!-- NO OBJECT - {{ template }} --> {%- endif -%} {%- assign NLTGCYKFZA = 'NewCondition' -%} {%- if KOKGAKSHFC == 'yes' -%} {%- assign XMHVVOLFPV = UYVGTSBASG -%} {%- else -%} {%- if TEKKFGSUBE == 'yes' -%} {%- assign XMHVVOLFPV = shop.metafields.SEOMetaManager.config_collectionsAll -%} {%- else -%} {% assign XMHVVOLFPV = SUYRUDMEZJ.config -%} {%- endif -%} {%- endif -%} {%- assign UMEWSWPPZG = XMHVVOLFPV | remove: "{"| remove: "}" -%} {%- assign CLYKCLQPAS = UMEWSWPPZG | split: ',"' -%} {%- for JPIYAMILEL in CLYKCLQPAS -%} {%- assign POGRNDKRAT = JPIYAMILEL | remove: '"' -%} {%- assign ZKUEALXMJM = POGRNDKRAT | split: ":" -%} {%- assign SBOECZNTSB = ZKUEALXMJM[0] -%} {%- assign KJQFZDLSJJ = ZKUEALXMJM[1] | replace: '%3A', ':' | replace: '\/', '/' -%} {%- case SBOECZNTSB -%} {%- when 'title_template_force' -%} {%- assign QGROIYEOLB = KJQFZDLSJJ -%} {%- when 'robots_index' -%} {%- assign LNCMZYKFNP = KJQFZDLSJJ -%} {%- when 'robots_follow' -%} {%- assign TYCUBNGBVR = KJQFZDLSJJ -%} {%- when 'robots_noimageindex' -%} {%- assign QIWDKBEHZD = KJQFZDLSJJ -%} {%- when 'robots_noarchive' -%} {%- assign PFYPJANTNI = KJQFZDLSJJ -%} {%- when 'robots_nosnippet' -%} {%- assign ZFPDGWTQZL = KJQFZDLSJJ -%} {%- when 'google_notranslate' -%} {%- assign NIRZIYCLRK = KJQFZDLSJJ -%} {%- when 'google_unavailable_after' -%} {%- assign EPREMUEQEU = KJQFZDLSJJ -%} {%- when 'google_unavailable_after_date' -%} {%- assign QWVITYVJCI = KJQFZDLSJJ -%} {%- when 'metaRedirect_enabled' -%} {%- assign MFLERIKXED = KJQFZDLSJJ -%} {%- when 'metaRedirect_url' -%} {%- assign HKTMBPVYTJ = KJQFZDLSJJ -%} {%- when 'itemCondition' -%} {%- assign NLTGCYKFZA = KJQFZDLSJJ -%} {%- when 'pinterest_nopin' -%} {%- assign QOHCYYPAVA = KJQFZDLSJJ -%} {%- endcase -%} {%- endfor -%} {%- if HJZJMTDFKI == 'product' -%} {%- if JQRIIZLDRE == 'yes' -%} {%- else -%} {%- if MFLERIKXED == 'yes' -%} {%- if product.available != true -%} <meta HTTP-EQUIV="refresh" CONTENT="0;URL={{ HKTMBPVYTJ }}"> {%- endif -%} {%- endif -%} {%- endif -%} {%- endif -%} {%- assign BDXHENRLLG = page_title -%} {%- if TEKKFGSUBE == 'yes' -%} {%- if SUYRUDMEZJ.title_collectionsAll == empty or SUYRUDMEZJ.title_collectionsAll == nil -%} {%- assign BDXHENRLLG = page_title -%} {%- else -%} {%- assign BDXHENRLLG = SUYRUDMEZJ.title_collectionsAll -%} {%- endif -%} {%- endif -%} {%- assign KHCQJUECXX = page_description -%} {%- if TEKKFGSUBE == 'yes' -%} {%- if SUYRUDMEZJ.description_collectionsAll == empty or SUYRUDMEZJ.description_collectionsAll == nil -%} {%- assign KHCQJUECXX = page_description -%} {%- else -%} {%- assign KHCQJUECXX = SUYRUDMEZJ.description_collectionsAll -%} {%- endif -%} {%- endif -%} {%- if CTOQJIJTEW == 'yes' -%} {%- assign RXMOAVGOMC = SUYRUDMEZJ.keywords -%} {%- endif -%} {%- capture BTYMAQZAHW -%}{{ current_tags }}{%- endcapture -%} {%- for QXEQMYPPLN in collection.all_tags -%} {%- if BTYMAQZAHW == QXEQMYPPLN -%} {%- capture BDXHENRLLG -%}{{ BDXHENRLLG }} - {{ QXEQMYPPLN }}{%- endcapture -%} {%- capture NXUUPCEUQK -%}title_{{ QXEQMYPPLN | slice: 0, 24 }}{%- endcapture -%} {%- capture CYZYBWTCVD -%}description_{{ QXEQMYPPLN | slice: 0, 18 }}{%- endcapture -%} {%- capture AZDJXGUHEB -%}noindex_{{ QXEQMYPPLN | slice: 0, 22 }}{%- endcapture -%} {%- assign CFJYRNMFXB = "false" -%} {%- for JPIYAMILEL in SUYRUDMEZJ -%} {%- capture SBOECZNTSB -%}{{ JPIYAMILEL | first }}{%- endcapture -%} {%- capture OVOKBAPKMA -%}{{ JPIYAMILEL | last }}{%- endcapture -%} {%- if SBOECZNTSB == NXUUPCEUQK -%} {%- assign BDXHENRLLG = OVOKBAPKMA -%} {%- assign CFJYRNMFXB = "true" -%} {%- elsif SBOECZNTSB == CYZYBWTCVD -%} {%- assign KHCQJUECXX = OVOKBAPKMA -%} {%- assign CFJYRNMFXB = "true" -%} {%- elsif SBOECZNTSB == AZDJXGUHEB -%} {%- assign YVUHMWIJEI = OVOKBAPKMA -%} {%- endif -%} {%- endfor -%} {%- if CFJYRNMFXB == "false" -%} {%- capture NXUUPCEUQK -%}title_{{ QXEQMYPPLN | slice: 0, 24 | downcase | handleize }}{%- endcapture -%} {%- capture CYZYBWTCVD -%}description_{{ QXEQMYPPLN | slice: 0, 18 | downcase | handleize }}{%- endcapture -%} {%- capture AZDJXGUHEB -%}noindex_{{ QXEQMYPPLN | slice: 0, 22 | downcase | handleize }}{%- endcapture -%} {%- for JPIYAMILEL in SUYRUDMEZJ -%} {%- capture SBOECZNTSB -%}{{ JPIYAMILEL | first }}{%-endcapture-%} {%- capture OVOKBAPKMA -%}{{ JPIYAMILEL | last }}{%- endcapture -%} {%- if SBOECZNTSB == NXUUPCEUQK -%} {%- assign BDXHENRLLG = OVOKBAPKMA -%} {%- elsif SBOECZNTSB == CYZYBWTCVD -%} {%- assign KHCQJUECXX = OVOKBAPKMA -%} {%- elsif SBOECZNTSB == AZDJXGUHEB -%} {%- assign YVUHMWIJEI = OVOKBAPKMA -%} {%- endif -%} {%- endfor -%} {%- endif -%} {%- assign MPXUUEZDJU = 'no' -%} {%- assign QGROIYEOLB = 'no' -%} {%- break -%} {%- endif -%} {%- endfor -%} {%- if KOKGAKSHFC == 'yes' -%} {%- capture UBXHPXHIAC -%}title_{{ BLMKGYWZXV }}{%- endcapture -%} {%- capture HQBKQLYVVQ -%}description_{{ BLMKGYWZXV }}{%- endcapture -%} {%- for JPIYAMILEL in SUYRUDMEZJ -%} {%- capture SBOECZNTSB -%}{{ JPIYAMILEL | first }}{%-endcapture-%} {%- capture OVOKBAPKMA -%}{{ JPIYAMILEL | last }}{%- endcapture -%} {%- if SBOECZNTSB == UBXHPXHIAC -%} {%- assign BDXHENRLLG = OVOKBAPKMA -%} {%- elsif SBOECZNTSB == HQBKQLYVVQ -%} {%- assign KHCQJUECXX = OVOKBAPKMA -%} {% endif %} {%- endfor -%} {%- endif -%} {%- if MPXUUEZDJU == 'yes' or QGROIYEOLB == 'yes' -%} {%- assign RUTZXMOSQY = [HJZJMTDFKI].price | money | strip_html -%} {%- assign XHJYPVQGBR = [HJZJMTDFKI].price_max | money | strip_html -%} {%- assign CKNYBSWMQD = [HJZJMTDFKI].price_min | money | strip_html -%} {%- assign IUBACHCHKN = YWQSAMAJWR | replace: '##title##', [HJZJMTDFKI].title -%} {%- assign IUBACHCHKN = IUBACHCHKN | replace: '##seotitle##', page_title -%} {%- assign IUBACHCHKN = IUBACHCHKN | replace: '##price##', RUTZXMOSQY -%} {%- assign IUBACHCHKN = IUBACHCHKN | replace: '##price_max##', XHJYPVQGBR -%} {%- assign IUBACHCHKN = IUBACHCHKN | replace: '##price_min##', CKNYBSWMQD -%} {%- assign IUBACHCHKN = IUBACHCHKN | replace: '##type##', [HJZJMTDFKI].type -%} {%- assign IUBACHCHKN = IUBACHCHKN | replace: '##vendor##', [HJZJMTDFKI].vendor -%} {%- assign IUBACHCHKN = IUBACHCHKN | replace: '##author##', [HJZJMTDFKI].author -%} {%- assign IUBACHCHKN = IUBACHCHKN | replace: '##shopname##', shop.name -%} {%- assign BDXHENRLLG = IUBACHCHKN -%} {%- endif -%} {%- unless XOAQUVBFEG == "yes" -%} {%- if current_page > 1 -%} {% capture BDXHENRLLG %}{{ BDXHENRLLG }} | {{ current_page }}{% endcapture %} {%- endif -%} {%- endunless -%}
<!-- SEO Manager 6.1.2 -->
<meta name='seomanager' content='6.1' />
<title>{{ BDXHENRLLG | escape_once }}</title>
<meta name='description' content='{{ KHCQJUECXX | escape_once }}' />
{%- if CTOQJIJTEW == 'yes' -%}
{{ DAKYCLFQTU }}
<meta name='keywords' content='{{ RXMOAVGOMC | escape_once }}' />
{%- endif -%}
{%- if YVUHMWIJEI == 'yes' -%}
{{ DAKYCLFQTU }}
<meta name="robots" content="noindex">
<meta name="robots" content="follow">
{%- else -%}
{%- if LNCMZYKFNP == 'no' -%}
{{ DAKYCLFQTU }}
<meta name="robots" content="noindex">
{%- else -%}
{{ DAKYCLFQTU }}
<meta name="robots" content="index">
{%- endif -%}
{%- if TYCUBNGBVR == 'no' -%}
{{ DAKYCLFQTU }}
<meta name="robots" content="nofollow">
{%- else -%}
{{ DAKYCLFQTU }}
<meta name="robots" content="follow">
{%- endif -%}
{%- endif -%}
{%- if QIWDKBEHZD == 'yes' -%}
{{ DAKYCLFQTU }}
<meta name="robots" content="noimageindex">
{%- endif -%}
{%- if PFYPJANTNI == 'yes' -%}
{{ DAKYCLFQTU }}
<meta name="robots" content="noarchive">
{%- endif -%}
{%- if ZFPDGWTQZL == 'yes' -%}
{{ DAKYCLFQTU }}
<meta name="robots" content="nosnippet">
{%- endif -%}
{%- if NIRZIYCLRK == 'yes' -%}
{{ DAKYCLFQTU }}
<meta name="google" content="notranslate" />
{%- endif -%}
{%- if BQEEKZVZNH == 'yes' -%}
{%- elsif EPREMUEQEU == 'yes' -%}
{{ DAKYCLFQTU }}
<meta name="googlebot" content="unavailable_after: {{ QWVITYVJCI }} UTC" />
{%- endif -%}
{%- if QOHCYYPAVA == 'yes' -%}
{{ DAKYCLFQTU }}
<meta name="pinterest" content="nopin" />
{%- endif -%}
{%- assign VDMQQEDCTN = 'yes' -%} {%- assign FEGHDKFMHS = 'yes' -%} {%- assign AJHUMUKLLC = 'Store' -%} {%- assign BHZXWBJFJO = 'no' -%} {%- assign ADDBWWEIGK = '' -%} {%- assign JJDVVGBBIN = 'yes' -%} {%- assign TBDQAKTCZC = 'yes' -%} {%- assign VPBOYGLCVM = 'automatic' -%} {%- assign QWCFUDZFHF = 'yes' -%} {%- assign WCQBKSNHSY = "" -%} {%- assign TNAMWXFTFD = shop.metafields.SEOMetaManager.knowledgeGraphConfig -%} {%- assign EFMZCHEJIY = TNAMWXFTFD | remove: "{"| remove: "}" -%} {%- assign CLYKCLQPAS = EFMZCHEJIY | split: ',"' -%} {%- for JPIYAMILEL in CLYKCLQPAS -%} {%- assign POGRNDKRAT = JPIYAMILEL | remove: '"' -%} {%- assign ZKUEALXMJM = POGRNDKRAT | split: ":" -%} {%- assign SBOECZNTSB = ZKUEALXMJM[0] -%} {%- assign KJQFZDLSJJ = ZKUEALXMJM[1] | replace: '%3A', ':' | replace: '\/', '/' | replace: '%2F', '/' | replace: '%2B', '+' -%} {%- case SBOECZNTSB -%} {%- when 'facebook' -%} {%- assign WCQBKSNHSY = WCQBKSNHSY | append: ', "' | append: KJQFZDLSJJ | append: '"' -%} {%- when 'twitter' -%} {%- assign WCQBKSNHSY = WCQBKSNHSY | append: ', "' | append: KJQFZDLSJJ | append: '"' -%} {%- when 'instagram' -%} {%- assign WCQBKSNHSY = WCQBKSNHSY | append: ', "' | append: KJQFZDLSJJ | append: '"' -%} {%- when 'pinterest' -%} {%- assign WCQBKSNHSY = WCQBKSNHSY | append: ', "' | append: KJQFZDLSJJ | append: '"' -%} {%- when 'youtube' -%} {%- assign WCQBKSNHSY = WCQBKSNHSY | append: ', "' | append: KJQFZDLSJJ | append: '"' -%} {%- when 'googleplus' -%} {%- assign WCQBKSNHSY = WCQBKSNHSY | append: ', "' | append: KJQFZDLSJJ | append: '"' -%} {%- when 'linkedin' -%} {%- assign WCQBKSNHSY = WCQBKSNHSY | append: ', "' | append: KJQFZDLSJJ | append: '"' -%} {%- when 'JSONLD_enable' -%} {%- assign VDMQQEDCTN = KJQFZDLSJJ -%} {%- when 'JSONLD_showLocalBusiness' -%} {%- assign FEGHDKFMHS = KJQFZDLSJJ -%} {%- when 'JSONLD_customerServicePhone' -%} {%- assign DASZOPBREN = KJQFZDLSJJ -%} {%- when 'JSONLD_techSupportPhone' -%} {%- assign MMLKSDVRWM = KJQFZDLSJJ -%} {%- when 'JSONLD_salesPhone' -%} {%- assign RYBJGEVDJM = KJQFZDLSJJ -%} {%- when 'JSONLD_businessType' -%} {%- assign AJHUMUKLLC = KJQFZDLSJJ -%} {%- when 'JSONLD_brand' -%} {%- assign UAOZOCRIJD = KJQFZDLSJJ -%} {%- when 'JSONLD_openingHours' -%} {%- assign LGHBTYPVUU = KJQFZDLSJJ -%} {%- when 'JSONLD_map' -%} {%- assign PIUCRQXIPH = KJQFZDLSJJ -%} {%- when 'JSONLD_priceRange' -%} {%- assign RMUUCIDASA = KJQFZDLSJJ -%} {%- when 'JSONLD_phone' -%} {%- assign CEXKIMIVZN = KJQFZDLSJJ -%} {%- when 'JSONLD_address' -%} {%- assign ZQMACEWPLW = KJQFZDLSJJ -%} {%- when 'JSONLD_city' -%} {%- assign BFCRWPPAZX = KJQFZDLSJJ -%} {%- when 'JSONLD_province' -%} {%- assign ZNEPACPFTS = KJQFZDLSJJ -%} {%- when 'JSONLD_zip' -%} {%- assign BHQSXNXATU = KJQFZDLSJJ -%} {%- when 'JSONLD_country' -%} {%- assign ADDBWWEIGK = KJQFZDLSJJ -%} {%- when 'JSONLD_showProducts' -%} {%- assign JJDVVGBBIN = KJQFZDLSJJ -%} {%- when 'JSONLD_showReviews' -%} {%- assign TBDQAKTCZC = KJQFZDLSJJ -%} {%- when 'JSONLD_reviewProvider' -%} {%- assign VPBOYGLCVM = KJQFZDLSJJ -%} {%- when 'JSONLD_showArticles' -%} {%- assign QWCFUDZFHF = KJQFZDLSJJ -%} {%- when 'JSONLD_alternativeName' -%} {%- assign WMEIKLJHWZ = KJQFZDLSJJ -%} {%- endcase -%} {%- endfor -%} {%- assign WCQBKSNHSY = WCQBKSNHSY | remove_first: ', ' -%}
{%- assign IERIXYLUAD = shop.metafields.SEOMetaManager.googleVerify -%}
{% unless IERIXYLUAD == blank %}
{{ IERIXYLUAD }}
{% endunless %}
{%- unless VDMQQEDCTN == "no" -%}
 {%- if HJZJMTDFKI == 'shop' -%}
 <!-- JSON-LD support -->
 <script type="application/ld+json">
 {
 "@context": "https://schema.org",
 "@type": "WebSite",
 "name": {{ shop.name | json }},
 {%- unless WMEIKLJHWZ == blank -%}
 "alternateName": "{{ WMEIKLJHWZ }}",
 {%- endunless -%}
 "url": "https://{{ shop.domain }}",
 "potentialAction": {
 "@type": "SearchAction",
 "target": "https://{{ shop.domain }}/search?q={query}",
 "query-input": "required name=query"
 }
 }
 </script>
 <script type="application/ld+json">
 {
 "@context": "http://schema.org",
 "@type": "Organization",
 "name": {{ shop.name | json }},
 "url": "https://{{ shop.domain }}"
 {%- unless shop.metafields.SEOMetaManager.knowledgeGraphLogo == blank -%}
 ,"logo": "https://{{ shop.metafields.SEOMetaManager.knowledgeGraphLogo | remove: "\" }}"
 ,"image": "https://{{ shop.metafields.SEOMetaManager.knowledgeGraphLogo | remove: "\" }}"
 {%- endunless -%}
 ,"contactPoint": [
 {%- unless DASZOPBREN == blank -%}
 { "@type": "ContactPoint",
 "telephone": {{ DASZOPBREN | json }},
 "contactType": "customer service"
 }
 {%- endunless -%}
 {%- unless MMLKSDVRWM == blank -%}
 ,{ "@type": "ContactPoint",
 "telephone": {{ MMLKSDVRWM | json }},
 "contactType": "technical support"
 }
 {%- endunless -%}
 {%- unless RYBJGEVDJM == blank -%}
 ,{ "@type": "ContactPoint",
 "telephone": {{ RYBJGEVDJM | json }},
 "contactType": "sales"
 }
 {%- endunless -%}
 ]
 {%- unless WCQBKSNHSY == blank -%}
 ,"sameAs" : [
 {{ WCQBKSNHSY }}
 ]
 {%- endunless -%}
 }
 </script>
 {%- unless FEGHDKFMHS == "no" -%}
 <script type="application/ld+json">
 {
 "@context": "https://schema.org",
 "@type": "{{ AJHUMUKLLC }}",
 "name": {{ shop.name | json }},
 "url": "https://{{ shop.domain }}",
 "description": {{ shop.description | json }}
 {%- unless shop.metafields.SEOMetaManager.knowledgeGraphLogo == blank -%}
 ,"image": "https://{{ shop.metafields.SEOMetaManager.knowledgeGraphLogo | remove: "\" }}"
 {%- endunless -%}
 {%- unless CEXKIMIVZN == blank -%}
 ,"telephone": "{{ CEXKIMIVZN }}"
 {%- endunless -%}
 {%- unless UAOZOCRIJD == blank -%}
 ,"brand": {
 "@type": "Brand",
 "name": "{{ UAOZOCRIJD }}"
 }
 {%- endunless -%}
 {%- unless BHZXWBJFJO == 'no' -%}
 ,"openingHoursSpecification": [
 {%- unless JSONLD_hours_mo_open == "closed" -%}
 {
 "@type": "OpeningHoursSpecification",
 "dayOfWeek": "http://schema.org/Monday",
 "opens":  "{{ JSONLD_hours_mo_open }}",
 "closes": "{{ JSONLD_hours_mo_close }}"
 },
 {%- endunless -%}
 {%- unless JSONLD_hours_tu_open == "closed" -%}
 {
 "@type": "OpeningHoursSpecification",
 "dayOfWeek": "http://schema.org/Tuesday",
 "opens":  "{{ JSONLD_hours_tu_open }}",
 "closes": "{{ JSONLD_hours_tu_close }}"
 },
 {%- endunless -%}
 {%- unless JSONLD_hours_we_open == "closed" -%}
 {
 "@type": "OpeningHoursSpecification",
 "dayOfWeek": "http://schema.org/Wednesday",
 "opens":  "{{ JSONLD_hours_we_open }}",
 "closes": "{{ JSONLD_hours_we_close }}"
 },
 {%- endunless -%}
 {%- unless JSONLD_hours_th_open == "closed" -%}
 {
 "@type": "OpeningHoursSpecification",
 "dayOfWeek": "http://schema.org/Thursday",
 "opens":  "{{ JSONLD_hours_th_open }}",
 "closes": "{{ JSONLD_hours_th_close }}"
 },
 {%- endunless -%}
 {%- unless JSONLD_hours_fr_open == "closed" -%}
 {
 "@type": "OpeningHoursSpecification",
 "dayOfWeek": "http://schema.org/Friday",
 "opens":  "{{ JSONLD_hours_fr_open }}",
 "closes": "{{ JSONLD_hours_fr_close }}"
 },
 {%- endunless -%}
 {%- unless JSONLD_hours_sa_open == "closed" -%}
 {
 "@type": "OpeningHoursSpecification",
 "dayOfWeek": "http://schema.org/Saturday",
 "opens":  "{{ JSONLD_hours_sa_open }}",
 "closes": "{{ JSONLD_hours_sa_close }}"
 },
 {%- endunless -%}
 {%- unless JSONLD_hours_su_open == "closed" -%}
 {
 "@type": "OpeningHoursSpecification",
 "dayOfWeek": "http://schema.org/Sunday",
 "opens":  "{{ JSONLD_hours_su_open }}",
 "closes": "{{ JSONLD_hours_su_close }}"
 }
 {%- endunless -%}
 ]
 {%- endunless -%}
 {%- unless PIUCRQXIPH == blank -%}
 ,"hasMap": "{{ PIUCRQXIPH }}"
 {%- endunless -%}
 {%- unless RMUUCIDASA == blank -%}
 ,"priceRange": "{{ RMUUCIDASA }}"
 {%- endunless -%}
 ,"geo": {
 "@type": "GeoCoordinates",
 "latitude": "{{ JSONLD_lat | default: shop.latitude }}",
 "longitude": "{{ JSONLD_long | default: shop.longitude }}"
 }
 ,"address": {
 "@type": "PostalAddress",
 "streetAddress": "{{ ZQMACEWPLW | default: shop.address.street }}",
 "addressLocality": "{{ BFCRWPPAZX | default: shop.address.city }}",
 "addressRegion": "{{ ZNEPACPFTS | default: shop.address.province }}",
 "postalCode": "{{ BHQSXNXATU | default: shop.address.zip }}",
 "addressCountry": "{{ ADDBWWEIGK | default: shop.address.country }}"
 }
 }
 </script>
 {%- endunless -%}
 {%- endif -%}
 {%- if HJZJMTDFKI == 'product' -%}
 {%- unless JJDVVGBBIN == "no" -%}
 <script type="application/ld+json">
 {
 "@context": "https://schema.org",
 "@id": {{ canonical_url | json }},
 "@type": "Product",
 {%- if product.first_available_variant.sku != blank -%}
 "sku": {{ product.first_available_variant.sku | json}},
 {%- else -%}
 "sku": {{ product.first_available_variant.id | json }},
 {%- endif -%}
 {%- if product.first_available_variant.sku != blank -%}
 "mpn": {{ product.first_available_variant.sku | json }},
 {%- else -%}
 "mpn": {{ product.first_available_variant.id | json }},
 {%- endif -%}
 "brand": {
 "@type": "Brand",
 "name": {{ product.vendor | json }}
 },
 "description": {{ product.description | strip_html | json }},
 "url": {{ canonical_url | json }},
 "name": {{ product.title | json }},
 {%- if product.featured_image -%}
 "image": "https:{{ product.featured_image | product_img_url: 'master' }}",
 {%- endif -%}
 "offers": [
 {%- for UJPBNBNFAS in product.variants -%}
 {
 "@type": "Offer",
 {%- if UJPBNBNFAS.available == true -%}
 {%- assign VCOLDJBHZY = "InStock" -%}
 {%- else -%}
 {%- assign VCOLDJBHZY = "OutOfStock" -%}
 {%- endif -%}
 "availability": "https://schema.org/{{ VCOLDJBHZY }}",
 "priceCurrency": "{{ shop.currency }}",
 "price": "{{ UJPBNBNFAS.price | money_without_currency | remove: "," }}",
 "priceValidUntil": {{ "today" | date: '%s' | plus: 31536000 | date: "%Y-%m-%d" | json }},
 "itemCondition": "https://schema.org/{{ NLTGCYKFZA }}",
 "url": {{ canonical_url | append: UJPBNBNFAS.url | json }},
 "image": "https:{{ UJPBNBNFAS.image | default: product.featured_image | img_url: 'master' }}",
 "mpn": {{ UJPBNBNFAS.sku | default: UJPBNBNFAS.id | json }},
 {%- if UJPBNBNFAS.sku != blank -%}
 "sku": {{ UJPBNBNFAS.sku | json }},
 {%- else -%}
 "sku": "{{ UJPBNBNFAS.id }}",
 {%- endif -%}
 {%- if UJPBNBNFAS.barcode.size == 12 -%}
 "gtin12": {{ UJPBNBNFAS.barcode | json }},
 {% endif %}
 {%- if UJPBNBNFAS.barcode.size == 13 -%}
 "gtin13": {{ UJPBNBNFAS.barcode | json }},
 {%- endif -%}
 {%- if UJPBNBNFAS.barcode.size == 14 -%}
 "gtin14": {{ UJPBNBNFAS.barcode | json }},
 {%- endif -%}
 "seller": {
 "@type": "Organization",
 "name": {{ shop.name | json }}
 }
 }
 {%- unless forloop.last -%},{% endunless -%}
 {%- endfor -%}]
 {%- unless JSONLD_hideReviews == "yes" -%}
 {%- case VPBOYGLCVM -%}
 {%- when 'eggviews' -%}
 {%- assign COKREAALEB = product.metafields.eggviews.reviews_average -%}
 {%- assign LHICAJTKQQ = product.metafields.eggviews.reviews_count -%}
 {%- when 'judgeme' -%}
 {%- when 'kudobuzz' -%}
 {%- assign COKREAALEB = product.metafields.kudobuzz.review_rating -%}
 {%- assign LHICAJTKQQ = product.metafields.kudobuzz.reviews_count -%}
 {%- when 'loox' -%}
 {%- assign COKREAALEB = product.metafields.loox.avg_rating -%}
 {%- assign LHICAJTKQQ = product.metafields.loox.num_reviews -%}
 {%- when 'okendo' -%}
 {%- assign COKREAALEB = product.metafields.okendo.RatingAndReviewAverageValue -%}
 {%- assign LHICAJTKQQ = product.metafields.okendo.RatingAndReviewCount -%}
 {%- when 'opinew' -%}
 {%- assign COKREAALEB = product.metafields.opinew.reviews_rich_snippet | split: 'ratingValue" content="' | last | split: '"' |first | plus: 0 -%}
 {%- assign LHICAJTKQQ = product.metafields.opinew.reviews_rich_snippet | split: 'ratingCount" content="' | last | split: '"' |first | plus: 0 -%}
 {%- when 'orankl' -%}
 {%- assign COKREAALEB = product.metafields.orankl.rating -%}
 {%- assign LHICAJTKQQ = product.metafields.orankl.review_count -%}
 {%- when 'reviewsio' -%}
 {%- assign COKREAALEB = product.metafields.reviewscouk.rating -%}
 {%- assign LHICAJTKQQ = product.metafields.reviewscouk.total -%}
 {%- when 'rivio' -%}
 {%- assign COKREAALEB = product.metafields.reevio.reviews_average -%}
 {%- assign LHICAJTKQQ = product.metafields.reevio.reviews_count -%}
 {%- when 'ryviu' -%}
 {%- assign COKREAALEB = product.metafields.ryviu.reviews | split: 'ratingValue" content="' | last | split: '"' |first | plus: 0 -%}
 {%- assign LHICAJTKQQ = product.metafields.ryviu.reviews | split: 'reviewCount" content="' | last | split: '"' |first | plus: 0 -%}
 {%- when 'shopifyreviews' -%}
 {%- assign COKREAALEB = product.metafields.spr.reviews | split: 'ratingValue" content="' | last | split: '"' |first | plus: 0 -%}
 {%- assign LHICAJTKQQ = product.metafields.spr.reviews | split: 'reviewCount" content="' | last | split: '"' |first | plus: 0 -%}
 {%- when 'socialshopwave' -%}
 {%- assign COKREAALEB = product.metafields.ssw.avg_rate -%}
 {%- assign LHICAJTKQQ = product.metafields.ssw.count_rate -%}
 {%- when 'stampedio' -%}
 {%- assign COKREAALEB = product.metafields.stamped.reviews_average -%}
 {%- assign LHICAJTKQQ = product.metafields.stamped.reviews_count -%}
 {%- when 'superreviews' -%}
 {%- assign COKREAALEB = product.metafields.reviewapp.reviews | split: '"average" itemprop="ratingValue">' | last | split: '</span>' |first | plus: 0 -%}
 {%- assign LHICAJTKQQ = product.metafields.reviewapp.reviews | split: '"votes" itemprop="ratingCount">' | last | split: "</span>" | first | plus: 0 -%}
 {%- when 'trust' -%}
 {%- assign COKREAALEB = product.metafields.vnreviews.ratingValue -%}
 {%- assign LHICAJTKQQ = product.metafields.vnreviews.reviewCount -%}
 {%- when 'yotpo' -%}
 {%- assign COKREAALEB = product.metafields.yotpo.reviews_average -%}
 {%- assign LHICAJTKQQ = product.metafields.yotpo.reviews_count -%}
 {%- when 'yotpo_free' -%}
 {%- else -%}
 {%- if product.metafields.kudobuzz.review_rating and product.metafields.kudobuzz.reviews_count != "0" -%}
 {%- assign COKREAALEB = product.metafields.kudobuzz.review_rating -%}
 {%- assign LHICAJTKQQ = product.metafields.kudobuzz.reviews_count -%}
 {%- endif -%}
 {%- if product.metafields.loox.num_reviews and product.metafields.loox.num_reviews != "0" -%}
 {%- assign COKREAALEB = product.metafields.loox.avg_rating -%}
 {%- assign LHICAJTKQQ = product.metafields.loox.num_reviews -%}
 {%- endif -%}
 {%- if product.metafields.okendo.RatingAndReviewAverageValue and product.metafields.okendo.RatingAndReviewAverageValue != "0" -%}
 {%- assign COKREAALEB = product.metafields.okendo.RatingAndReviewAverageValue -%}
 {%- assign LHICAJTKQQ = product.metafields.okendo.RatingAndReviewCount -%}
 {%- endif -%}
 {%- when 'orankl' -%}
 {%- assign COKREAALEB = product.metafields.orankl.rating -%}
 {%- assign LHICAJTKQQ = product.metafields.orankl.review_count -%}
 {%- if product.metafields.reviewscouk.rating and product.metafields.reviewscouk.rating != "0" -%}
 {%- assign COKREAALEB = product.metafields.reviewscouk.rating -%}
 {%- assign LHICAJTKQQ = product.metafields.reviewscouk.total -%}
 {%- endif -%}
 {%- if product.metafields.reevio.reviews_average and product.metafields.reevio.reviews_count != "0" -%}
 {%- assign COKREAALEB = product.metafields.reevio.reviews_average -%}
 {%- assign LHICAJTKQQ = product.metafields.reevio.reviews_count -%}
 {%- endif -%}
 {%- if product.metafields.ssw.count_rate and product.metafields.ssw.count_rate != "0" -%}
 {%- assign COKREAALEB = product.metafields.ssw.avg_rate -%}
 {%- assign LHICAJTKQQ = product.metafields.ssw.count_rate -%}
 {%- endif -%}
 {%- if product.metafields.stamped.reviews_average and product.metafields.stamped.reviews_count != "0" -%}
 {%- assign COKREAALEB = product.metafields.stamped.reviews_average -%}
 {%- assign LHICAJTKQQ = product.metafields.stamped.reviews_count -%}
 {%- endif -%}
 {%- if product.metafields.vnreviews.ratingValue and product.metafields.vnreviews.reviewCount != "0" -%}
 {%- assign COKREAALEB = product.metafields.vnreviews.ratingValue -%}
 {%- assign LHICAJTKQQ = product.metafields.vnreviews.reviewCount -%}
 {% endif %}
 {%- if product.metafields.yotpo.reviews_count and product.metafields.yotpo.reviews_count != "0" -%}
 {%- assign COKREAALEB = product.metafields.yotpo.reviews_average -%}
 {%- assign LHICAJTKQQ = product.metafields.yotpo.reviews_count -%}
 {% endif %}
 {%- endcase -%}
     {%- if COKREAALEB != nil and LHICAJTKQQ != nil -%}
 ,"aggregateRating": {
 "@type": "AggregateRating",
 "ratingValue": {{ COKREAALEB }},
 "ratingCount": {{ LHICAJTKQQ }}
 }
 {%- endif -%}
 {%- endunless -%}
 }
 </script>
 {%- endunless -%}
 {%- endif -%}
 {%- if HJZJMTDFKI == 'article' -%}
 {%- unless QWCFUDZFHF == "no" -%}
 <script type="application/ld+json">
 {
 "@context": "https://schema.org",
 "@type": "Article",
 "url": {{ canonical_url | json }},
 "mainEntityOfPage": {{ canonical_url | json }},
 "name": {{ article.title | json }},
 "headline": {{ article.title | json }},
 "author": {
 "@type": "Person",
 "name": {{ article.author | json }}
 },
 "publisher": {
 "@type": "Organization",
 "name": {{ shop.name | json }}
 {%- unless shop.metafields.SEOMetaManager.knowledgeGraphLogo == blank -%}
 ,"logo":
 {
 "@type": "ImageObject",
 "url": "https://{{ shop.metafields.SEOMetaManager.knowledgeGraphLogo | remove: "\" }}"
 }
 {%- endunless -%}
 },
 "image": {
 "@type": "ImageObject",
 {%- if article.image -%}
 "url": "https:{{ article | img_url: '1024x1024' }}",
{%- else -%} {%- assign VZEQMDHQLZ = article.content | escape -%} {%- if VZEQMDHQLZ contains '&lt;img' -%} {%- assign VZEQMDHQLZ = VZEQMDHQLZ | split: 'src=&quot;' -%} {%- assign VZEQMDHQLZ = VZEQMDHQLZ[1] | split: '&quot;' -%} {%- assign VZEQMDHQLZ = VZEQMDHQLZ[0] | remove: '//cdn' -%} {%- assign VZEQMDHQLZ = VZEQMDHQLZ | remove: 'http:http://' -%} {%- assign VZEQMDHQLZ = VZEQMDHQLZ | remove: 'https:' -%} {%- assign VZEQMDHQLZ = VZEQMDHQLZ | prepend: 'https://cdn' -%} {%- capture PDYMDREIWK -%}_1024x1024.{%- endcapture -%} {%- assign XZQUGMRFUG = 'pico icon thumb small compact medium large grande 1024x1024 2048x2048' | split:' ' -%} {%- for MYJNZMYMWH in XZQUGMRFUG -%} {%- capture IMAGTKWHJS -%}_{{ MYJNZMYMWH }}.{%- endcapture -%} {%- assign VZEQMDHQLZ = VZEQMDHQLZ | replace:search,replacement -%} {%- endfor -%}
 "url": "{{ VZEQMDHQLZ }}",
 {%- else -%}
 "url": "https://cdn.shopify.com/s/images/admin/no-image-grande.gif",
 {%- endif -%}
 {%- endif -%}
 "height": "1024",
 "width": "1024"
 },
 "datePublished": "{{ article.created_at }}",
 "dateModified": "{{ article.published_at }}",
 "description": {{ article.excerpt_or_content | strip_html | json }},
 "articleBody": {{ article.content | strip_html | json }}
 }
 </script>
 {%- endunless -%}
 {%- endif -%}
{%- endunless -%}
{% comment %} ROM 8:31 {% endcomment %}
<!-- end: SEO Manager 6.1.2 -->