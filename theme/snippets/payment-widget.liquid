<div class="paymentsBanner_container" neptune-permanent="paymentsBanner--{{ block.id }}">
  <style>
    .payments-banner .money {
      margin: 0 4px;
    }
  </style>

  {% liquid
    assign country_inclusion = block.settings.country_inclusion | split: ","
    assign data_country = ""

    for country in country_inclusion
      assign country_code =  country | strip

      if country_code == "US" 
        assign data_country = "domestic"
      else
        assign data_country = "international"
      endif  
    endfor    
  %}

  <p class="{% if block.settings.country_inclusion == blank %} hidden {% endif %} flex items-center justify-start text-xs  ml-0 pt-2 mb-4 mr-auto px-5 lg:px-10 payments-banner" data-country-inclusion="{{ block.settings.country_inclusion }}" data-{{ data_country }}>
  {%- liquid
    assign payment_price = product.price | divided_by: block.settings.payment_count | money 
    assign parts = block.settings.text | replace: '[ count ]', block.settings.payment_count | replace: '[ payment ]', payment_price | split: '(('
    assign icon_number = 0 
  -%}
  {%- for part in parts -%}
  {%- if part contains '))' -%}
    {%- liquid 
      assign icon_number = icon_number | plus: 1
      assign link_key = icon_number | prepend: 'link_'
      assign image_key = icon_number | prepend: 'image_'
      assign icon = part | remove: '))' | split: ' ' | first
      assign dims = part | split: '))' | first | remove: icon | strip | split: 'x' 
    -%}
    {%- capture icon -%}
      <span>{% render 'icon' icon:icon, width:dims[0] height:dims[1] fill:'currentColor' strokeWidth:0 %}</span> <span class="ml-1 hidden">{% render 'icon' icon:'info', height:12 width:12 %}</span>
    {%- endcapture -%}
    {% if block.settings[image_key] != blank %}
      <button class="inline-flex items-end px-1" onclick="Neptune.liquid.load('PaymentModal',{ image:'{{ block.settings[image_key] | img_url: 'master' }}', link: '{{ block.settings[link_key] }}'}); modal.open('PaymentModal')">
        {{ icon }}
      </button>
    {% else %}
      <span class="inline-flex items-end px-1">
      {{ icon }}
      </span>
    {% endif %}
    {%- assign bits = part | split: '))' -%}
    {%- if bits.size > 1 -%}
      {{ part | split: '))' | last }}
    {%- endif -%}
  {%- else -%}
    {{ part }}
  {%- endif -%}
  {%- endfor -%}
  </p>
</div>