{% comment %} 


This is generated by webpack so don't edit this unless you want to be sad when your work gets overwritten in production.
You can edit this file in ../../components/** 


{% endcomment %}






<aside data-modal="quickshop" class="fixed bg-white z-50 modal modal-center left-1/2 top-1/2 max-w-screen-xl max-h-screen w-full focus:outline-none focus:shadow-none overflow-y-scroll" tabindex="0" neptune-engage="{
  on:keyup,
  which: 27,
  targets:[{
    selector:body,
    attributes:[{
      att:data-active-modal,
      set:_remove
    }]
  },
  { 
    selector:'[data-return-focus]',
    attributes:[{
      att:data-return-focus,
      set:_remove
    }],
    focus:true
  }]
}">
	<article quickshop></article>

	<button close class="absolute top-3 right-3 bg-gray-800 bg-opacity-50 hover:bg-white hover:text-black p-5 rounded-full text-white transform z-10 transition-all user-select-none" onclick="modal.close()">
    {% render 'icon' icon:'close' %}        
	</button>

  <button back class="absolute top-3 left-5 bg-gray-800 bg-opacity-50 hover:bg-white hover:text-black p-5 rounded-full text-white transform z-10 transition-all user-select-none" onclick="modal.hide('quickshop')">
    {% render 'icon' icon:'arrow-left' %}        
  </button>

</aside>