{% liquid 
  assign trainer_handle = name | handle | prepend: 'trainers/' 
  assign trainer = articles[trainer_handle]
%}
{% if trainer.title != blank %}
{% case format %}

  {% when 'full' %}
    <address rel="author" class="flex not-italic mb-0" itemprop="author" itemscope itemtype="http://schema.org/Person">
      <span class="rounded-full w-14 h-14 bg-gray-200 flex-shrink-0 mr-3">
        <img src="{{ trainer.image.src | img_url }}" alt=" {{ trainer.image.alt }}" class="rounded-full object-cover w-full h-full border border-gray-400">    
      </span>
      <span>
        <p class="m-0 text-sm" rel="author" itemprop="name">{{ trainer.title | split: ',' | first }}</p>
        <p class="m-0 text-xs mb-1">{{ trainer.title | split: ',' | last }}</p>
        <p class="m-0 text-xs">{{ trainer.tags | join: ', ' }}</p>
      </span>
    </address>


  {% when 'mini' %}
    <address rel="author" class="flex items-center not-italic mb-2">
      <span class="rounded-full w-6 h-6 bg-gray-200 flex-shrink-0 mr-3">
        <img src="{{ trainer.image.src | img_url }}" alt=" {{ info[0] }}" class="rounded-full object-cover w-full h-full border border-gray-400">    
      </span>
      <span>
        <p class="m-0 text-sm" rel="author">{{ trainer.title }}</p>
      </span>
    </address>

{% endcase %}
{% endif %}

