<script>
window.search = {
  settings:{},
};

{%- capture remote_url -%}
https://{{ settings.ss_site_id }}.a.searchspring.io/api/search/autocomplete.json
?siteId={{ settings.ss_site_id }}&resultsFormat=json
&resultsPerPage=12
&q=${term}
{%- endcapture -%}

search.settings.remote_url = "{{ remote_url | strip_newlines }}";
if (Shopify.country === 'US') search.settings.remote_url += '&bgfilter.ss_exclude_flow=0'

search.settings.remote = {
  method: 'GET', 
  headers: {
    accept: 'application/json'
  }
};

search.settings.map = {
  products: {
    from: 'results',
    each: {
      id:'uid',
      title:'name',
      handle:'handle',
      price:'price|*100',
      type:'product_type',
      compare_at_price:'msrp|*100',
      price_min:'price_min|*100',
      price_max:'price_max|*100',
      featured_image:'image',
      hover_image: 'ss_image_alt',
      hover_video: 'mfield_product_gallery_video',
      alt_image: 'ss_image_alt',
      alt_images:'gender_alternative',
      images: 'images',
      tags: 'tags|.split(", ")',
      variants:'variants',
      metafields:'metafields',
      siblings:'siblings'
    }
  },
  total_products: 'pagination.totalResults',
  redirect: 'merchandising.redirect'
};

window.addEventListener('Search:loaded', (e) => {

  const remote_url = "https://{{ settings.ss_site_id }}.a.searchspring.io/api/suggest/query?siteId={{ settings.ss_site_id }}&q=${term}&suggestionCount=20"
  const map = { keywords: { from: 'alternatives', each: { keyword: 'text' }}}

  let fetch_config = {}
  Object.keys(search.settings.remote).forEach(key=>{
    if (key.includes('config_') && !!search.settings.remote[key])
      fetch_config[key.replace('config_','')] = _n.literal(search.settings.remote[key], {term:term})
  })
  if(!!fetch_config.headers && typeof fetch_config.headers == 'string')
  fetch_config.headers = JSON.parse(fetch_config.headers)

  fetch(_n.literal(remote_url,{term:search.query}),fetch_config)
    .then(response=>response.json())
    .then(data=>_n.map(data,map))
    .then(data=>{  
      search.results.keywords = data.keywords;
      if (window.search.fromInput == true) {
        Neptune.liquid.load('SearchSuggestions');
      }
    })

})
</script>