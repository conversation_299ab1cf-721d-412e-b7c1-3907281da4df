<li id="panel-{{ id }}" class="{{ settings.mobile_width | default: 'col-span-4'}} {{ settings.desktop_width | default: 'lg:col-span-4' }} panel__item panel__item--{{ type | handle }} panel__item--index-{{ index }} group {% if settings.linklist_columns != blank %}grid grid-cols-2{% endif %} {% if settings.show_desktop == false %}lg:hidden{% endif %} {% if settings.show_mobile == false %}hidden{% endif %} {% if settings.show_mobile == false and settings.show_desktop == true %}hidden lg:grid{% endif %}">
  {% if settings.show_buttons %}<p class="sr-only">Menu: {{ linklists[settings.linklist_menu].title }}</p>{% endif %}
  
  {% assign item_count = 0 %}

  {% if settings.linklist_columns != blank %}
  
    <ul class="list-none m-0 px-0 {% if settings.show_buttons %}grid gap-x-2 gap-y-4 grid-cols-4 pt-3 pb-4{% else %}text-xl font-highlight{% endif %}">
      
      {% assign item_count = linklists[settings.linklist_menu].links.size | divided_by: 2 | round %}
    	
      {% for link in linklists[settings.linklist_menu].links limit: item_count %}
      {% if link.links != blank %}
    		<li id="mainMenu-{{ link.title | handleize }}" class="link-list__parent w-full whitespace-nowrap">
          {%- liquid
            assign external_link = false
            if link.type == "http_link" 
              unless link.url contains '/vendors?' or link.url contains '/types?'
                assign external_link = true
              endunless
            endif
          -%}
    	    <a class="block pt-4 font-caption-caps" href="{{ link.url }}"{% if external_link %} target="_blank"{% endif %}>
    	      <span class="block">{{ link.title }}</span>
    	    </a>
    	  </li>
        <li id="mainMenu-{{ link.title | handleize }}-child" class="link-list__child">
      	  <ul class="list-none m-0 p-0 child">
      	    {% for child_link in link.links %} 
      	    	<li class="w-full whitespace-nowrap">
                {%- liquid
                  assign external_link = false
                  if child_link.type == "http_link" 
                    unless child_link.url contains '/vendors?' or child_link.url contains '/types?'
                      assign external_link = true
                    endunless
                  endif
                -%}
      	    		<a class="block py-1" href= "{{ child_link.url }}"{% if external_link %} target="_blank"{% endif %}>{{ child_link.title }}</a>
      	    	</li>
      	   	{% endfor %}
      	  </ul>
        </li>
      {% else %}
    	  <li id="mainMenu-{{ link.title | handleize }}" class="w-full whitespace-nowrap">
          {%- liquid
            assign external_link = false
            if link.type == "http_link" 
              unless link.url contains '/vendors?' or link.url contains '/types?'
                assign external_link = true
              endunless
            endif
          -%}
    	    <a class="block {% if settings.show_buttons %}button button--light px-0{% else %}py-1{% endif %}" href="{{ link.url }}"{% if external_link %} target="_blank"{% endif %}>
    	      <span class="block">{{ link.title }}</span>
    	    </a>
    	  </li>
      {% endif %}
    {% endfor %}
    </ul>
  {% endif %}

  <ul class="list-none m-0 px-0 {% if settings.show_buttons %}grid gap-x-2 gap-y-4 grid-cols-4 pt-3 pb-4{% else %}text-xl font-highlight{% endif %}">
    {% for link in linklists[settings.linklist_menu].links  offset: item_count %}
    {% if link.links != blank %}
      <li id="mainMenu-{{ link.title | handleize }}" class="link-list__parent w-full whitespace-nowrap">
        <a class="block pt-4 font-caption-caps" href="{{ link.url }}">
          <span class="block">{{ link.title }}</span>
        </a>
      </li>
      <li id="mainMenu-{{ link.title | handleize }}-child" class="link-list__child">
        <ul class="list-none m-0 p-0 child">
          {% for child_link in link.links %} 
            <li class="w-full whitespace-nowrap">
              <a class="block py-1" href= "{{ child_link.url }}">{{ child_link.title }}</a>
            </li>
          {% endfor %}
        </ul>
      </li>
    {% else %}
      <li id="mainMenu-{{ link.title | handleize }}" class="w-full whitespace-nowrap">
        <a class="block {% if settings.show_buttons %}button button--light px-0{% else %}py-1{% endif %}" href="{{ link.url }}">
          <span class="block">{{ link.title }}</span>
        </a>
      </li>
    {% endif %}
  {% endfor %}
  </ul>


</li>
{% if settings.show_divder != blank %}
  {% render 'panel-item-divider' %}
{% endif %}
{% if settings.subpane_end == true %}{% break %}{% endif %}

{% if settings.linklist_column %}{% endif %}
