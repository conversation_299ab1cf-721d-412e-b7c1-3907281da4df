<div class="ss-top-facets ss-targeted ng-scope">
	<div class="ss-top-facets-top">
		<div class="ss-facets-row ng-scope">
			<a class="ss-top-value ng-binding" href="https://motherdenim.myshopify.com/collections/denim?oseid=d1S1C53XLmQ7WxLFnDJA1KD7#/filter:ss_tags_reveal_leg_opening:straight">straight</a>
		</div>
	</div>
	<div class="ss-top-facets-bottom">
		<div class="ss-facets-row ng-scope">
			<a class="ss-top-value ng-binding" href="https://motherdenim.myshopify.com/collections/denim?oseid=d1S1C53XLmQ7WxLFnDJA1KD7#/filter:ss_tags_reveal_fit:ankle">ankle</a>
		</div>
	</div>
</div>

<div class="shopify-section" id="shopify-section-template--14303492898904__product-grid">
	<script defer src="/assets/searchspring.js?v=13935048171099310528">
	</script>
	<h1 class="sr-only">Skinny</h1>
	<div class="tooling lg:flex items-center relative leading-loose sticky w-full left-0 bottom-0 z-30">
		<div class="breadcrumbs hidden lg:block w-1/2">
			<div class="px-8 py-4">
				<nav aria-label="breadcrumbs" class="breadcrumbs__links py-0 tracking-wide">
					<a class="hover:underline" href="/">Home</a> <span class="mx-4">|</span> <span><a class="breadcrumbs__link hover:underline" href="/collections/denim-by-fit">Denim By Fit</a></span> <span class="mx-4">|</span> <span><a class="hover:underline" href="/collections/denim-skinny">Skinny</a></span>
				</nav>
			</div>
		</div>
		<div class="tools lg:w-1/2 w-full text-right ss-targeted">
			<div class="collection-tools ph4-l pv0 lg:px-8 py-0 lg:border-0 border-t border-b border-gray-dark ng-scope">
				<ul class="list db pr0 pl0 pv0 mv0 mla-l black tracked-slight w-25-l w-100 z-4 pr0-l tailwinds flex flex-row items-center relative block px-0 py-0 my-0 lg:ml-auto lg:w-1/4 w-full tailwind z-40 tracking-wide list-none">
					<li class="w-50 w-auto-l tc tr-l dim-l ml4-l bb b--border-gray bn-l radioactive parent-group toggleable active-black active-underline tracked-slight us-none tailwinds w-1/2 lg:w-auto lg:text-right text-center lg:ml-8 tracking-wide lg:border-0 border-bottom border-gray-dark"><label aria-controls="ss-collection-filters" aria-expanded="false" class="ma0 db dib-l pa0-l pa3 pointer activate input-hybrid-label ttu tailwinds group-active:underline group-active:font-semibold uppercase m-0 block lg:inline-block p-4 lg:p-0 cursor-pointer" data-target="_parent" for="collectionFilters" tabindex="0">Filter</label></li>
					<li class="w-50 w-auto-l tc tr-l bl bb b--border-gray bn-l pointer dim-l ml4-l radioactive toggleable active-black active-underline tracked-slight us-none tailwinds w-1/2 lg:w-auto border-l border-bottom border-gray-dark lg:border-0 cursor-pointer lg:ml-8 tracking-wide parent-group"><label aria-controls="ss-sort" aria-expanded="false" class="ma0 db dib-l pa0-l pa3 pointer activate input-hybrid-label ttu tailwinds uppercase m-0 block lg:inline-block p-4 lg:p-0 cursor-pointer group-active:underline group-active:font-semibold text-center" data-target="_parent" for="collectionSort" tabindex="0">Sort</label></li>
					<li class="dn db-l dim-l ml3 ml4-l lg:block ml-4 lg:ml-8"><label class="ma0 pa0 pointer active m-0 p-0 cursor-pointer flex flex-col justify-center" data-views="4" style="margin-top: 1px;margin-right: 1px;"><span aria-controls="ss-collection-grid" class="show-4 active-show" data-views="4" style="margin-top: 1px;margin-right: 1px;" tabindex="0"><svg data-name="Layer 1" height="20" id="a936b6f2-8cc3-4866-871f-36e46dd01c2c" viewbox="0 0 20 20" width="20" xmlns="http://www.w3.org/2000/svg">
					<title>view_4</title>
					<path d="M14.26,21.48a4.26,4.26,0,1,0,4.26,4.26A4.26,4.26,0,0,0,14.26,21.48Zm11.48-3a4.26,4.26,0,1,0-4.26-4.26A4.26,4.26,0,0,0,25.74,18.52ZM14.26,10a4.26,4.26,0,1,0,4.26,4.26A4.26,4.26,0,0,0,14.26,10ZM25.74,21.48A4.26,4.26,0,1,0,30,25.74,4.25,4.25,0,0,0,25.74,21.48Z" transform="translate(-10 -10)"></path></svg></span> <span aria-controls="ss-collection-grid" class="show-2 active-hide" data-views="4" style="margin-top: 1px;margin-right: 1px;" tabindex="0"><svg data-name="Layer 1" height="9.2" id="aa857e73-c78c-40c5-8dfa-5874c73dfd7e" viewbox="0 0 20 9.2" width="20" xmlns="http://www.w3.org/2000/svg">
					<title>View 2</title>
					<path d="M14.44,15.38A4.6,4.6,0,1,0,19,20,4.6,4.6,0,0,0,14.44,15.38Zm10.8,0a4.6,4.6,0,1,0,4.6,4.6A4.6,4.6,0,0,0,25.24,15.38Z" transform="translate(-9.84 -15.38)"></path></svg></span></label></li>
				</ul>
			</div>
			<div class="ng-scope">
				<input class="hidden" id="collectionSort" name="collection-tools" type="radio">
				<div class="active-show checkbox-controlled-display h-100-header tl bt b--border-gray bl-l pa5-l pa4 w-100 list ttu z-3 collection-sort tailwinds list-none absolute right-0 bg-near-white h-main text-left border-t border-gray-dark lg:border-l lg:p-16 p-8 w-full uppercase z-30 lg:max-w-md" id="ss-sort">
					<button class="drawer__close bn pa3 dn db-l tailwinds border-0 bg-near-white absolute top-0 left-0 p-4 lg:block" type="button"><span class="screenreader sr-only">Close</span> <svg class="feather feather-x" height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
					<line x1="18" x2="6" y1="6" y2="18"></line>
					<line x1="6" x2="18" y1="6" y2="18"></line></svg></button>
					<ul class="list ma0 pa0 m-0 p-0 list-none">
						<!-- ngRepeat: option in sorting.options track by $index -->
						<li class="ng-scope"><span class="db pa4 ph0-l pv3-l pointer tailwinds block p-8 lg:px-0 lg:py-4 cursor-pointer ng-binding active" tabindex="0">Newest</span></li><!-- end ngRepeat: option in sorting.options track by $index -->
						<li class="ng-scope"><span class="db pa4 ph0-l pv3-l pointer tailwinds block p-8 lg:px-0 lg:py-4 cursor-pointer ng-binding" tabindex="0">Price: Low to High</span></li><!-- end ngRepeat: option in sorting.options track by $index -->
						<li class="ng-scope"><span class="db pa4 ph0-l pv3-l pointer tailwinds block p-8 lg:px-0 lg:py-4 cursor-pointer ng-binding" tabindex="0">Price: High to Low</span></li><!-- end ngRepeat: option in sorting.options track by $index -->
					</ul>
					<div class="w-100 flex-column-l pt5 tailwinds bottom-0 left-0 w-full flex flex-row flex-wrap lg:flex-col pt-16">
						<button class="w-100-l w-50 pa3 ba b--black black ttu fw6 tracked-slight mb3-l bg-near-white ss-apply tailwinds lg:w-full w-1/2 p-4 border border-black uppercase font-semibold lg:mb-4" type="submit"><span class="db block">APPLY</span></button> <button class="w-100-l w-50 pa3 ba b--black black ttu fw6 tracked-slight bg-near-white ss-clear tailwinds lg:w-full w-1/2 p-4 border border-black uppercase font-semibold" type="submit"><span class="db block">Clear All</span></button>
					</div>
				</div>
			</div><input class="dn hidden ng-scope" id="collectionFilters" name="collection-tools" type="radio">
			<div class="collection-filters h-100-header bt bl-l b--border-gray checkbox-controlled-display mt0-l tl w-100 z-3 ng-scope tailwinds absolute h-main overflow-y-scroll bg-near-white border-t lg:border-l border-gray-dark right-0 lg:mt-0 text-left w-full z-30 top-0 lg:max-w-md" id="ss-collection-filters">
				<button class="drawer__close bn pa3 dn db-l tailwinds border-0 bg-near-white absolute top-0 left-0 p-4 lg:block" type="button"><span class="screenreader sr-only">Close</span> <svg class="feather feather-x" height="24" viewbox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">
				<line x1="18" x2="6" y1="6" y2="18"></line>
				<line x1="6" x2="18" y1="6" y2="18"></line></svg></button>
				<div class="w-100 pa5-l pa4 tailwinds w-full lg:p-16 p-4">
					<div class="w-100 ss-facets w-full ss-targeted">
						<div class="filter-set group relative ss-facet-container ss-facet-container-list ss-collapsed" id="ss_inseam">
							<h5 class="ss-title ma0 ph0-l pa4 pv3-l ttu tailwinds m-0 lg:px-0 p-8 lg:py-4 uppercase text-base group-active:font-semibold" tabindex="0"><label class="db ma0 pa0 pointer input-hybrid-label us-none ttu filter-set-label pointer tailwinds block m-0 p-0 cursor-poinIter uppercase ng-binding" for="filter-set-3">Inseam<span class="fr b db dn-l font-bold block float-right ng-binding plus">+</span></label></h5><input class="dn hidden ss-collapsed" id="filter-set-3" name="filtersets" type="radio"> <!-- ngSwitchWhen: palette --> <!-- ngSwitchDefault:  -->
							<ul class="checkbox-controlled-height list ma0 pa0-l ph4 ph0-l fw3 flex flex-wrap flex-row animate list-none m-0 lg:p-0 px-8 font-light ng-scope">
								<!-- ngRepeat: value in facet.values | limitTo:facet.overflow.limit track by $index -->
								<li class="ma0 ph0 pointer activate w-50 tailwinds m-0 px-0 cursor-pointer w-1/2 ss_inseam">
									<a class="dib v-mid pv2 tailwinds inline-block align-middle py-2"><!-- ngIf: value.active --> <!-- ngIf: !value.active --><span class="visually-hidden sr-only ng-scope">Unselected</span><!-- end ngIf: !value.active --> <span class="dib v-mid active-underline active-black lh-title tailwinds inline-block align-middle leading-tight no-underline ng-binding" tabindex="-1">Cropped</span> <span class="dib h1-5 v-mid tailwinds inline-block align-middle h-6">&nbsp;</span></a>
								</li><!-- end ngRepeat: value in facet.values | limitTo:facet.overflow.limit track by $index -->
								<li class="ma0 ph0 pointer activate w-50 tailwinds m-0 px-0 cursor-pointer w-1/2 ss_inseam">
									<a class="dib v-mid pv2 tailwinds inline-block align-middle py-2"><!-- ngIf: value.active --> <!-- ngIf: !value.active --><span class="visually-hidden sr-only ng-scope">Unselected</span><!-- end ngIf: !value.active --> <span class="dib v-mid active-underline active-black lh-title tailwinds inline-block align-middle leading-tight no-underline ng-binding" tabindex="-1">Ankle</span> <span class="dib h1-5 v-mid tailwinds inline-block align-middle h-6">&nbsp;</span></a>
								</li><!-- end ngRepeat: value in facet.values | limitTo:facet.overflow.limit track by $index -->
								<li class="ma0 ph0 pointer activate w-50 tailwinds m-0 px-0 cursor-pointer w-1/2 ss_inseam">
									<a class="dib v-mid pv2 tailwinds inline-block align-middle py-2"><!-- ngIf: value.active --> <!-- ngIf: !value.active --><span class="visually-hidden sr-only ng-scope">Unselected</span><!-- end ngIf: !value.active --> <span class="dib v-mid active-underline active-black lh-title tailwinds inline-block align-middle leading-tight no-underline ng-binding" tabindex="-1">Regular</span> <span class="dib h1-5 v-mid tailwinds inline-block align-middle h-6">&nbsp;</span></a>
								</li><!-- end ngRepeat: value in facet.values | limitTo:facet.overflow.limit track by $index -->
								<li class="ma0 ph0 pointer activate w-50 tailwinds m-0 px-0 cursor-pointer w-1/2 ss_inseam">
									<a class="dib v-mid pv2 tailwinds inline-block align-middle py-2"><!-- ngIf: value.active --> <!-- ngIf: !value.active --><span class="visually-hidden sr-only ng-scope">Unselected</span><!-- end ngIf: !value.active --> <span class="dib v-mid active-underline active-black lh-title tailwinds inline-block align-middle leading-tight no-underline ng-binding" tabindex="-1">Long</span> <span class="dib h1-5 v-mid tailwinds inline-block align-middle h-6">&nbsp;</span></a>
								</li><!-- end ngRepeat: value in facet.values | limitTo:facet.overflow.limit track by $index -->
								<li class="ma0 ph0 pointer activate w-50 tailwinds m-0 px-0 cursor-pointer w-1/2 ss_inseam">
									<a class="dib v-mid pv2 tailwinds inline-block align-middle py-2"><!-- ngIf: value.active --> <!-- ngIf: !value.active --><span class="visually-hidden sr-only ng-scope">Unselected</span><!-- end ngIf: !value.active --> <span class="dib v-mid active-underline active-black lh-title tailwinds inline-block align-middle leading-tight no-underline ng-binding" tabindex="-1">cropped</span> <span class="dib h1-5 v-mid tailwinds inline-block align-middle h-6">&nbsp;</span></a>
								</li><!-- end ngRepeat: value in facet.values | limitTo:facet.overflow.limit track by $index -->
							</ul>
						</div><!-- end ngIf: (facet.type == 'hierarchy' ? facet.values.length : true) --><!-- end ngRepeat: facet in facets track by $index --><!-- ngIf: (facet.type == 'hierarchy' ? facet.values.length : true) -->
					</div>
					<div class="w-100 flex-column-l pt5 tailwinds bottom-0 left-0 flex lg:flex-col flex-row flex-wrap pt-16">
						<button class="w-100-l w-50 pa3 ba b--black black ttu fw6 tracked-slight mb3-l bg-near-white hidden ss-apply" type="submit"><span class="db">APPLY</span></button> <button class="w-100-l w-50 pa3 ba b--black black ttu fw6 tracked-slight bg-near-white tailwinds lg:w-full w-1/2 p-4 border border-black uppercase font-semibold ss-clear" type="submit"><span class="db">Clear All</span></button>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="ss-targeted" id="searchspring-content">
		<!-- ngIf: merchandising.content.header.length > 0 --><!-- ngIf: pagination.totalResults -->
		<div class="ss-results ng-scope">
			<!-- ngIf: merchandising.content.banner.length > 0 --><!-- ngIf: ss_show_top_facets -->
			<ul class="collection-grid pure-g pl1 pl4-l pr0 mv0 tailwinds flex content-start flex-row flex-wrap pl-0 lg:pl-8 pr-0 m-0 ss-targeted" id="ss-collection-grid">
				<!-- ngRepeat: result in results track by result.uid -->
				<li class="product-grid-item pure-u-1-2 pure-u-lg-1-4 tailwinds relative lg:w-1/4 w-1/2 inline-block tracking-wide align-top overflow-hidden group ng-scope" data-tags="Apparel, Class: Bottoms, Color: Medium_Blue, construction: Woven, Division: Womens, Fabric: Denim, fit: Skinny, Fly Closure: Zipper, hem: Clean, Holiday 2021 Del 3, Holiday 21 Del 3 Bottoms, inseam: Regular, model_5_10_26_small, New Arrivals Holiday 3, reveal_leg_opening: skinny, reveal_rise: mid_rise, rise: Mid_Rise, stretch_2, style: Looker, Sub Class: Jeans, Treatment: Distressed, type: womens, Women's">
					<!-- ngIf: result.isInlineBanner --><!-- ngIf: !result.isInlineBanner -->
					<div class="pr1 pb5 pr4-l pb4-l tailwinds pr-1 pb-16 lg:pb-8 ng-scope">
						<div class="w-100 product-item tailwinds overflow-hidden w-full relative" data-index="0" itemscope itemtype="http://schema.org/Product">
							<a class="w-100 h-100 tailwinds absolute top-0 w-full h-full group block" href="/collections/denim-skinny/products/the-looker-skimp-play-like-a-pirate" title="The Looker Skimp - Play Like A Pirate - Womens - APPAREL"><span class="visually-hidden sr-only ng-binding">Jeans</span>
							<div class="product-item--badges tailwinds absolute top-4 left-0 z-10 uppercase MonumentGrotesk-Bold">
								<!-- ngRepeat: badge in result.tags_ss_badge track by $index -->
							</div>
							<meta content="The Looker Skimp - Play Like A Pirate" itemprop="name">
							<meta content="/collections/denim-skinny/products/the-looker-skimp-play-like-a-pirate" itemprop="url">
							<div itemprop="offers" itemscope itemtype="http://schema.org/Offer">
								<meta content="USD" itemprop="priceCurrency">
								<meta content="$248.00" itemprop="price">
								<meta content="Out of Stock" itemprop="availability">
								<meta content="http://schema.org/NewCondition" itemprop="itemCondition" itemtype="http://schema.org/OfferItemCondition">
							</div>
							<div class="pi__img aspect-ratio aspect-ratio--3x4 tailwinds relative">
								<div class="pi__link ir ir--product tailwinds block aspect-w-3 aspect-h-4 relative overflow-hidden">
									<img alt="" class="aspect-ratio--object tailwinds absolute top-0 left-0 z-0 block w-full h-full ng-isolate-scope" data-src="//cdn.shopify.com/s/files/1/2094/5935/products/PLAYLIKEAPIRATE_THETRIPPERANKLEFRAY_10326-1008-PLP_02885_1300x.jpg?v=1635457793" data-srcset="//cdn.shopify.com/s/files/1/2094/5935/products/PLAYLIKEAPIRATE_THETRIPPERANKLEFRAY_10326-1008-PLP_02885_620x.jpg?v=1635457793 1x, //cdn.shopify.com/s/files/1/2094/5935/products/PLAYLIKEAPIRATE_THETRIPPERANKLEFRAY_10326-1008-PLP_02885_1240x.jpg?v=1635457793 2x" height="1653" id="Image--" onerror="this.src='//cdn.searchspring.net/ajax_search/img/default_image.png';" sizes="100vw" src="//cdn.shopify.com/s/files/1/2094/5935/products/PLAYLIKEAPIRATE_THETRIPPERANKLEFRAY_10326-1008-PLP_02885.jpg?v=1635457793" srcset="{{ result.srcSet }}" style="min-height: 1px;" width="1240">
									<div class="pi__photo-hover tailwinds opacity-0 group-hover:opacity-100 absolute left-0 top-0 h-full w-full delay-200 transition-opacity ease-in-out">
										<!-- ngIf: result.videoHTML -->
										<div class="product-grid-video w-full h-full ng-binding ng-scope">
											<!--<video ng-if="//player.vimeo.com/external/644037429.sd.mp4?s=958980cef1751fdf8d78701e1f929751baa74f67&profile_id=165" class="product-video plyr==setup" preload="true" src="//player.vimeo.com/external/644037429.sd.mp4?s=958980cef1751fdf8d78701e1f929751baa74f67&profile_id=165" muted mute autoplay loop="" style="width: calc(100% + 20px); max-width: calc(100% + 20px); margin-left: -10px; margin-right: -10px; height: 100%; object-fit: contain;" poster=""><source src="{{ result.mfield_product_gallery_video }}"></video>-->
										</div><!-- end ngIf: result.videoHTML --><!-- ngIf: !result.videoHTML && result.ss_image_alt --><!-- ngIf: !result.videoHTML && result.ss_image_alt -->
									</div>
								</div>
							</div>
							<div class="lh-title product-item-info mt3 mh0 tailwinds mx-0 mt-4 leading-tight tracking-wide text-center">
								<div class="db block">
									<h2 class="fw5 f5 mb0 tailwinds MonumentGrotesk-Bold uppercase text-base m-0 leading-tight tracking-wide ng-binding">The Looker Skimp</h2>
									<div class="fw4 font-normal text-lg leading-tight ng-binding">
										Play Like A Pirate
									</div><span class="db fw3 block font-light text-lg leading-tight"><span class="visually-hidden sr-only">Original Price:</span> <!-- ngIf: result.msrp != 0 && result.msrp != result.price --> <span class="visually-hidden sr-only">Current Price:</span> <span class="global-price ng-binding">$248</span></span>
								</div>
							</div>
							<div class="quick-shop outline w-100 h-100 pointer-none tailwinds absolute top-0 outline-none w-full h-full top-0 pointer-events-none overflow-hidden">
								<!-- ngIf: result.isSelected && (result.ss_in_stock * 1) == 1 --><!-- ngIf: !result.isSelected && (result.ss_in_stock * 1) == 1 --><!-- ngIf: (result.ss_in_stock * 1) == 0 -->
								<form class="db absolute bottom-0 w-100 pointer-all ss-add-to-cart tailwinds bg-near-white overflow-x-scroll absolute bottom-0 pointer-events-auto w-full block ng-scope ng-pristine ng-valid">
									<button class="w-100 bn pa3 bg-black white ttc fw6 tracked-slight tailwinds hidden" disabled="disabled"><span class="dib mv1">Out of Stock</span></button>
								</form><!-- end ngIf: (result.ss_in_stock * 1) == 0 -->
							</div></a>
						</div>
					</div><!-- end ngIf: !result.isInlineBanner -->
				</li><!-- end ngRepeat: result in results track by result.uid -->
			</ul><!-- ngIf: merchandising.content.footer.length > 0 -->
		</div><!-- end ngIf: pagination.totalResults --><!-- ngIf: pagination.totalPages > 0 -->
		<div class="ss-pagination block text-center w-full ss-targeted ng-scope">
			<!-- ngIf: pagination.previous -->
			<ol class="mx-0 my-4 p-0 list-none">
				<!-- ngRepeat: page in pagination.getPages(5) track by $index -->
				<li class="inline-block text-center relative w-8 before:absolute before:bg-black before:text-white before:w-8 before:h-8 ng-scope current">
					<nav aria-label="page">
						<!-- ngIf: !page.active -->
					</nav><!-- ngIf: page.active --><span class="ng-binding ng-scope">1</span><!-- end ngIf: page.active -->
				</li><!-- end ngRepeat: page in pagination.getPages(5) track by $index -->
				<li class="inline-block text-center relative w-8 before:absolute before:bg-black before:text-white before:w-8 before:h-8 ng-scope">
					<nav aria-label="page">
						<!-- ngIf: !page.active --><a class="ng-binding ng-scope" href="http://127.0.0.1:9292/collections/denim-skinny/?page=2">2</a><!-- end ngIf: !page.active -->
					</nav><!-- ngIf: page.active -->
				</li><!-- end ngRepeat: page in pagination.getPages(5) track by $index -->
			</ol><!-- ngIf: pagination.next -->
			<div class="pages-next ng-scope">
				<span><a class="next i-next" href="http://127.0.0.1:9292/collections/denim-skinny/?page=2" title="Next"></a></span>
			</div><!-- end ngIf: pagination.next -->
		</div><!-- end ngIf: pagination.totalPages > 0 --><!-- ngIf: pagination.totalResults === 0 -->
	</div><!-- <div collection-injection="{page:1,position:4}">Inject me at page 1 position 4</div> -->
	<style>
	[collection-injection]:not([injected]){display: none;}.pi__photo-hover {margin-top: 1px;}
	</style>
</div>

<ul>
	<li class="product-grid-item pure-u-1-2 pure-u-lg-1-4 tailwinds relative lg:w-1/4 w-1/2 inline-block tracking-wide align-top overflow-hidden group ng-scope" data-tags="30 OFF, Apparel, BF 2021 NEW, Class: Bottoms, Color: Medium_Blue, construction: Woven, Division: Womens, Fabric: Denim, Fall 2021 Del 2, Fall 21 Del 2 Bottoms, fit: Skinny, hem: Clean, model_5_10_26_small, reveal_leg_opening: skinny, reveal_rise: high_rise, rise: High_Rise, sale, sale2021, stretch_3, style: Swooner, Sub Class: Jeans, type: womens"><!-- ngIf: result.isInlineBanner --><!-- ngIf: !result.isInlineBanner --></li>
</ul>
<div class="pr1 pb5 pr4-l pb4-l tailwinds pr-1 pb-16 lg:pb-8 ng-scope">
	<div class="w-100 product-item tailwinds overflow-hidden w-full relative" data-index="3" itemscope itemtype="http://schema.org/Product">
		<a class="w-100 h-100 tailwinds absolute top-0 w-full h-full group block" href="/collections/denim-skinny/products/the-double-swooner-hover-love-bombs" title="The Double Swooner Hover - Love Bombs - Womens - APPAREL"><span class="visually-hidden sr-only ng-binding">Jeans</span>
		<div class="product-item--badges tailwinds absolute top-4 left-0 z-10 uppercase MonumentGrotesk-Bold">
			<!-- ngRepeat: badge in result.tags_ss_badge track by $index -->
		</div>
		<meta content="The Double Swooner Hover - Love Bombs" itemprop="name">
		<meta content="/collections/denim-skinny/products/the-double-swooner-hover-love-bombs" itemprop="url">
		<div itemprop="offers" itemscope itemtype="http://schema.org/Offer">
			<meta content="USD" itemprop="priceCurrency">
			<meta content="$159.60" itemprop="price">
			<meta content="In Stock" itemprop="availability">
			<meta content="http://schema.org/NewCondition" itemprop="itemCondition" itemtype="http://schema.org/OfferItemCondition">
		</div>
		<div class="pi__img aspect-ratio aspect-ratio--3x4 tailwinds relative">
			<div class="pi__link ir ir--product tailwinds block aspect-w-3 aspect-h-4 relative overflow-hidden">
				<img alt="" class="aspect-ratio--object tailwinds absolute top-0 left-0 z-0 block w-full h-full ng-isolate-scope" data-src="//cdn.shopify.com/s/files/1/2094/5935/products/LOVEBOMBS_DOUBLESWOONERHOVER_10571-624-LBM_02767_1300x.jpg?v=1631831488" data-srcset="//cdn.shopify.com/s/files/1/2094/5935/products/LOVEBOMBS_DOUBLESWOONERHOVER_10571-624-LBM_02767_620x.jpg?v=1631831488 1x, //cdn.shopify.com/s/files/1/2094/5935/products/LOVEBOMBS_DOUBLESWOONERHOVER_10571-624-LBM_02767_1240x.jpg?v=1631831488 2x" height="1653" id="Image--" onerror="this.src='//cdn.searchspring.net/ajax_search/img/default_image.png';" sizes="100vw" src="//cdn.shopify.com/s/files/1/2094/5935/products/LOVEBOMBS_DOUBLESWOONERHOVER_10571-624-LBM_02767.jpg?v=1631831488" srcset="{{ result.srcSet }}" style="min-height: 1px;" width="1240">
				<div class="pi__photo-hover tailwinds opacity-0 group-hover:opacity-100 absolute left-0 top-0 h-full w-full delay-200 transition-opacity ease-in-out">
					<!-- ngIf: result.videoHTML -->
					<div class="product-grid-video w-full h-full ng-binding ng-scope">
						<video autoplay="" class="product-video plyr--setup" loop="" poster="" preload="true" src="//player.vimeo.com/external/525846123.sd.mp4?s=8e4efcc37e5461d6795c89e4f20a3f93b54ba2db&amp;profile_id=165" style="width: calc(100% + 20px); max-width: calc(100% + 20px); margin-left: -10px; margin-right: -10px; height: 100%; object-fit: contain;"><source src="{{%20result.mfield_product_gallery_video%20}}"></video>
					</div><!-- end ngIf: result.videoHTML --><!-- ngIf: !result.videoHTML && result.ss_image_alt --><!-- ngIf: !result.videoHTML && result.ss_image_alt -->
				</div>
			</div>
		</div>
		<div class="lh-title product-item-info mt3 mh0 tailwinds mx-0 mt-4 leading-tight tracking-wide text-center">
			<div class="db block">
				<h2 class="fw5 f5 mb0 tailwinds MonumentGrotesk-Bold uppercase text-base m-0 leading-tight tracking-wide ng-binding">The Double Swooner Hover</h2>
				<div class="fw4 font-normal text-lg leading-tight ng-binding">
					Love Bombs
				</div><span class="db fw3 block font-light text-lg leading-tight"><span class="visually-hidden sr-only">Original Price:</span> <!-- ngIf: result.msrp != 0 && result.msrp != result.price --> <span class="global-price ng-binding ng-scope">$228</span> <!-- end ngIf: result.msrp != 0 && result.msrp != result.price --> <span class="visually-hidden sr-only">Current Price:</span> <span class="flow-price ng-binding">$159.60</span></span>
			</div>
		</div>
		<div class="quick-shop outline w-100 h-100 pointer-none tailwinds absolute top-0 outline-none w-full h-full top-0 pointer-events-none overflow-hidden">
			<!-- ngIf: result.isSelected && (result.ss_in_stock * 1) == 1 --><!-- ngIf: !result.isSelected && (result.ss_in_stock * 1) == 1 -->
			<form class="db absolute bottom-0 w-100 pointer-all ss-add-to-cart tailwinds bg-near-white overflow-x-scroll absolute bottom-0 pointer-events-auto w-full block ng-scope ng-pristine ng-valid">
				<!-- ngIf: result.ss_variants && result.ss_variants.length > 1 -->
				<div class="bg-near-white bt option-selection tailwinds mx-auto flex justify-center h-auto ss-targeted ng-scope">
					<!-- ngRepeat: variant in result.ss_variants track by $index --> 
					<label class="pv3 ph2 ma0 lh-2 sans-serif pointer lh-solid tailwinds cursor-pointer py-2 px-15 m-0 leading-none MonumentGrotesk-Regular inline-block ng-scope" title="23">
						<input checked="checked" class="dn hidden" data-available="" data-variants="33614052819032" id="the-double-swooner-hover-love-bombssize23" name="option2" type="radio" value="23">
					 	<span class="checkbox-controlled-bold text-center h-5 flex align-center justify-center items-center min-w-5 text-base ng-binding">23</span>
					 </label> <!-- end ngRepeat: variant in result.ss_variants track by $index --> 
					 <label class="pv3 ph2 ma0 lh-2 sans-serif pointer lh-solid tailwinds cursor-pointer py-2 px-15 m-0 leading-none MonumentGrotesk-Regular inline-block ng-scope" title="24"><input class="dn hidden" data-available="33614052851800" data-variants="33614052851800" id="the-double-swooner-hover-love-bombssize24" name="option2" type="radio" value="24"> <span class="checkbox-controlled-bold text-center h-5 flex align-center justify-center items-center min-w-5 text-base ng-binding">24</span></label> <!-- end ngRepeat: variant in result.ss_variants track by $index --> <label class="pv3 ph2 ma0 lh-2 sans-serif pointer lh-solid tailwinds cursor-pointer py-2 px-15 m-0 leading-none MonumentGrotesk-Regular inline-block ng-scope" title="25"><input class="dn hidden" data-available="33614052884568" data-variants="33614052884568" id="the-double-swooner-hover-love-bombssize25" name="option2" type="radio" value="25"> <span class="checkbox-controlled-bold text-center h-5 flex align-center justify-center items-center min-w-5 text-base ng-binding">25</span></label> <!-- end ngRepeat: variant in result.ss_variants track by $index --> <label class="pv3 ph2 ma0 lh-2 sans-serif pointer lh-solid tailwinds cursor-pointer py-2 px-15 m-0 leading-none MonumentGrotesk-Regular inline-block ng-scope" title="26"><input class="dn hidden" data-available="" data-variants="33614052917336" id="the-double-swooner-hover-love-bombssize26" name="option2" type="radio" value="26"> <span class="checkbox-controlled-bold text-center h-5 flex align-center justify-center items-center min-w-5 text-base ng-binding">26</span></label> <!-- end ngRepeat: variant in result.ss_variants track by $index --> <label class="pv3 ph2 ma0 lh-2 sans-serif pointer lh-solid tailwinds cursor-pointer py-2 px-15 m-0 leading-none MonumentGrotesk-Regular inline-block ng-scope" title="27"><input class="dn hidden" data-available="" data-variants="33614052950104" id="the-double-swooner-hover-love-bombssize27" name="option2" type="radio" value="27"> <span class="checkbox-controlled-bold text-center h-5 flex align-center justify-center items-center min-w-5 text-base ng-binding">27</span></label> <!-- end ngRepeat: variant in result.ss_variants track by $index --> <label class="pv3 ph2 ma0 lh-2 sans-serif pointer lh-solid tailwinds cursor-pointer py-2 px-15 m-0 leading-none MonumentGrotesk-Regular inline-block ng-scope" title="28"><input class="dn hidden" data-available="" data-variants="33614052982872" id="the-double-swooner-hover-love-bombssize28" name="option2" type="radio" value="28"> <span class="checkbox-controlled-bold text-center h-5 flex align-center justify-center items-center min-w-5 text-base ng-binding">28</span></label> <!-- end ngRepeat: variant in result.ss_variants track by $index --> <label class="pv3 ph2 ma0 lh-2 sans-serif pointer lh-solid tailwinds cursor-pointer py-2 px-15 m-0 leading-none MonumentGrotesk-Regular inline-block ng-scope" title="29"><input class="dn hidden" data-available="" data-variants="33614053015640" id="the-double-swooner-hover-love-bombssize29" name="option2" type="radio" value="29"> <span class="checkbox-controlled-bold text-center h-5 flex align-center justify-center items-center min-w-5 text-base ng-binding">29</span></label> <!-- end ngRepeat: variant in result.ss_variants track by $index --> <label class="pv3 ph2 ma0 lh-2 sans-serif pointer lh-solid tailwinds cursor-pointer py-2 px-15 m-0 leading-none MonumentGrotesk-Regular inline-block ng-scope" title="30"><input class="dn hidden" data-available="" data-variants="33614053048408" id="the-double-swooner-hover-love-bombssize30" name="option2" type="radio" value="30"> <span class="checkbox-controlled-bold text-center h-5 flex align-center justify-center items-center min-w-5 text-base ng-binding">30</span></label> <!-- end ngRepeat: variant in result.ss_variants track by $index --> <label class="pv3 ph2 ma0 lh-2 sans-serif pointer lh-solid tailwinds cursor-pointer py-2 px-15 m-0 leading-none MonumentGrotesk-Regular inline-block ng-scope" title="31"><input class="dn hidden" data-available="" data-variants="33614053081176" id="the-double-swooner-hover-love-bombssize31" name="option2" type="radio" value="31"> <span class="checkbox-controlled-bold text-center h-5 flex align-center justify-center items-center min-w-5 text-base ng-binding">31</span></label> <!-- end ngRepeat: variant in result.ss_variants track by $index --> <label class="pv3 ph2 ma0 lh-2 sans-serif pointer lh-solid tailwinds cursor-pointer py-2 px-15 m-0 leading-none MonumentGrotesk-Regular inline-block ng-scope" title="32"><input class="dn hidden" data-available="33614053113944" data-variants="33614053113944" id="the-double-swooner-hover-love-bombssize32" name="option2" type="radio" value="32"> <span class="checkbox-controlled-bold text-center h-5 flex align-center justify-center items-center min-w-5 text-base ng-binding">32</span></label> <!-- end ngRepeat: variant in result.ss_variants track by $index --> <label class="pv3 ph2 ma0 lh-2 sans-serif pointer lh-solid tailwinds cursor-pointer py-2 px-15 m-0 leading-none MonumentGrotesk-Regular inline-block ng-scope" title="33"><input class="dn hidden" data-available="33614053146712" data-variants="33614053146712" id="the-double-swooner-hover-love-bombssize33" name="option2" type="radio" value="33"> <span class="checkbox-controlled-bold text-center h-5 flex align-center justify-center items-center min-w-5 text-base ng-binding">33</span></label> <!-- end ngRepeat: variant in result.ss_variants track by $index --> <label class="pv3 ph2 ma0 lh-2 sans-serif pointer lh-solid tailwinds cursor-pointer py-2 px-15 m-0 leading-none MonumentGrotesk-Regular inline-block ng-scope" title="34"><input class="dn hidden" data-available="" data-variants="33614053179480" id="the-double-swooner-hover-love-bombssize34" name="option2" type="radio" value="34"> <span class="checkbox-controlled-bold text-center h-5 flex align-center justify-center items-center min-w-5 text-base ng-binding">34</span></label> <!-- end ngRepeat: variant in result.ss_variants track by $index -->
				</div><!-- end ngIf: result.ss_variants && result.ss_variants.length > 1 --> <button class="w-100 bn pa3 bg-black white ttc fw6 tracked-slight tailwinds hidden"><span class="dib mv1 ng-binding">Choose Your Size</span></button>
			</form><!-- end ngIf: !result.isSelected && (result.ss_in_stock * 1) == 1 --><!-- ngIf: (result.ss_in_stock * 1) == 0 -->
		</div></a>
	</div>
</div><!-- end ngIf: !result.isInlineBanner -->
<ul></ul>

