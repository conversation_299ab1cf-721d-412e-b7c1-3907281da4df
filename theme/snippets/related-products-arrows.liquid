{%- assign num_related_products = 0 -%}
{%- for r_product in collections[block.settings.collection].products limit: 12%}
  {%- if r_product.id != product.id -%}
    {%- assign num_related_products = num_related_products | plus: 1 -%}
  {%- endif -%}
{%- endfor -%}
{%- if num_related_products > 1 -%}
<div class="related-products-prev-next-container z-50">
  <button class="swiper-prev swiper-button-prev-unique px-4 pt-0 border-0 bg-transparent cursor-pointer"><svg class="h-32 w-32" viewBox="0 0 100 100"><path d="M 10,50 L 60,100 L 65,95 L 20,50  L 65,5 L 60,0 Z" class="arrow"></path></svg>
    <span class="screenreader">previous slide</span>
  </button>
  <button class="swiper-next swiper-button-next-unique px-4 pt-0 border-0 bg-transparent cursor-pointer"><svg class="h-32 w-32" viewBox="0 0 100 100"><path d="M 10,50 L 60,100 L 65,95 L 20,50  L 65,5 L 60,0 Z" class="arrow" transform="translate(100, 100) rotate(180) "></path></svg>
    <span class="screenreader">next slide</span>
  </button>
</div>
{%- endif -%}

<style>
  .related-products-prev-next-container {
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    position: absolute;
    left: 0;
    height: 26px;
  }
  .related-products-prev-next-container .swiper-prev, .related-products-prev-next-container .swiper-next {
    padding: 0;
    position: absolute;
  }

  .related-products-prev-next-container .swiper-prev {
    left: 2rem;
  }

  .related-products-prev-next-container .swiper-next {
    right: 2rem;
  }
</style>
