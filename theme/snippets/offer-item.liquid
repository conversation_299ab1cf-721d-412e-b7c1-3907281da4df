{% raw %}
{% assign product_tags = product.tags | json |  replace: '"', "`" %}

<div class="offer-item" data-product-id="{{ product.id }}">
  <a {{'h'}}ref="/products/{{ product.handle }}" class="offer-item__product-link" onclick="Neptune.later.store(event, 'product_item_click')"></a>
  <div class="offer-item__image-outer">
    <div class="offer-item__image-wrapper product-item__image-wrapper">
      <img {{'s'}}rc="{{ product.featured_image  | image_url: width: 200 }}" alt="{{ product.title }}" class="offer-item__image product-item__image">
    </div>
    <div class="offer-item__check">
      {% endraw %}{% render 'icon' icon:'check' strokeWidth:2 width:20 height:20%}{% raw %}
    </div>
  </div>
  <div class="offer-item__details w-full">

    {% assign offerTitle = product.title %}
    <div class="offer-item__meta product-item__meta">
     
      <h5 class="offer-item__title product-item__title">{{ product.title | split: "-" | first }}</h5>
      <p class="offer-item__type product-item__type">{{ product.title | split: "-" | last }}</p>

      <span class="offer-item__price product-item__price">
        {% assign discount_amount = 0 %}
        {% if offer.discount %}
          {% assign discount_amount = offer.discount | times: 0.01 | times: product.price %}
        {% endif %}
        {% endraw %}
        {% render 'price-removals' %}
        {% raw %}
        {{ product.price | minus: discount_amount | money: 'local' | remove: removal }}
      </span>

    </div>

    <div class="w-full offer-item__action flex flex-col items-start">

      {% if product.variants.size > 1 %}
        <button 
         data-handle="{{ product.handle }}"
         onclick="this.classList.toggle('active');this.classList.add('gtmQuickAddClicked');" 
         class="quick-add__button quick-add--toggle button button--light product-item__quick-add-toggle hidden lg:flex items-center quick-2 order-1">
          <span>{% endraw %}{{ 'products.product.quick_add.add_to_cart' | t }}{% raw %}</span> 
          <span class="quick-add__button--plus">{% endraw %}{% render 'icon', icon:'plus', width:12, height:12 %}{% raw %}</span>
          <span class="quick-add__button--minus">{% endraw %}{% render 'icon', icon:'minus', width:12, height:12 %}{% raw %}</span>
        </button>

        <div class="product-item__container order-3 w-full">
          <div class="product-item__variants">
            <div class="option-selection quick-add__form--wrapper">
              
              {% for variant in product.variants %}
                <div class="quick-add__form--option-container relative"> 
                  {% if variant.inventory_quantity > 0 or variant.available %}
                    <input  id="{{ product.handle }}-variant-{{ variant.title | handle }}" data-variants="" data-available="{{ variant.inventory_quantity | default: '1' }}" type="radio" name="option2" value="{{ variant.id }}" class="absolute inset-0 w-full h-full opacity-0 cursor-pointer" onclick="this.classList.add('gtmVariantClicked'); Neptune.later.store(event, 'quick_add_click');" 
                    onchange="
                    if(this.checked){
                      const itemToAdd = {
                        id: {{ variant.id }},
                        quantity: 1,
                        properties: {
                          _tags: {{ product_tags }},
                          _compare_at_price: {{ product.compare_at_price | remove: '.' }}
                        }
                      };
                      Neptune.cart.add(itemToAdd);
                    }">
                  {% else %}
                    <input disabled id="{{ product.handle }}-variant-{{ variant.title | handle }}" data-variants="" data-available="{{ variant.inventory_quantity }}" type="radio" name="option2" value="{{ variant.id }}" class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"> 
                  {% endif %}
                  <label class="{% endraw %}{{ action | handle }}{% raw %} quick-add__form--option cursor-pointer swatch--nested" for="{{ product.handle }}-variant-{{ variant.title | handle }}" title="{{ variant.option2 }}" data-available="{{ variant.inventory_quantity }}">  
                    <span class="checkbox-controlled-bold">{{ variant.option2 }}</span> 
                  </label>
                </div>
              {% endfor %}

            </div>
          </div>
        </div>

      {% else %}

        <button 
         onclick="
          const itemToAdd = {
            id: {{ product.variants[0].id }},
            quantity: 1,
            properties: {
              _tags: {{ product_tags }},
              _compare_at_price: {{ product.compare_at_price | remove: '.' }}
            }
          };
         Neptune.cart.add(itemToAdd); Neptune.later.store(event, 'quick_add_click');" 
         class="quick-add__button b-3 button button--light product-item__add-to-cart lg:flex items-center order-1">
          <span>{% endraw %}{{ 'products.product.add_to_cart' | t }}{% raw %}</span> 
        </button>

      {% endif %}

    </div>
 
  </div>
</div>
{% endraw %}
