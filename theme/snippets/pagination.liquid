<div class="block w-full text-center mother-pagination">
  {% if paginate.previous.is_link %}
  <div class="pages-prev"> 
    <span> 
      <a class="previous i-previous" title="Previous" href="{{ paginate.previous.url }}"></a> 
    </span> 
  </div>
  {% endif %}
  <ol class="p-0 mx-0 my-4 list-none">
    {% for page in (1..paginate.pages) %}
      <li class="relative inline-block w-8 text-center before:absolute before:bg-black before:text-white before:w-8 before:h-8{% if page == paginate.current_page %} current{% endif %}">
        {% if page != paginate.current_page %}
        <nav aria-label="page">
          <a href="{{ collection.url }}?page={{ page }}">{{ page }}</a>
        </nav> 
        {% else %}
        <span>{{ page }}</span>
        {% endif %}
      </li>
    {% endfor %}
  </ol>
  <div class="pages-next"> 
    <span> 
      <a class="next i-next" title="Next" href="{{ paginate.next.url }}"></a> 
    </span> 
  </div>
</div>
