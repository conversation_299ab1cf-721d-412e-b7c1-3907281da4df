{% capture lightboxContent %}
<div neptune-liquid="topic:lightbox" class="relative w-full h-full lightbox" style="user-select:none;">
  {% raw %}
  <div class="relative w-full h-full overflow-y-scroll">
    <div class="fixed z-10 lg:w-40 w-1/6 space-y-4 overflow-hidden overflow-y-scroll lg:inset-4 inset-2 z-5">
      {% assign count = -1 %}
      {% for image in images %}
        {% unless image.alt contains 'https' %}
          {% assign count = count | plus: 1 %}
        	<div class="pv__photo" onclick="_n.qs('.lightbox [neptune-swiper]').swiper.slideTo('{{ count }}')">
            <img {{'s'}}rc="{{ image.src }}" alt="{{ image.alt }}" class="relative" />
          </div>
        {% endunless %}
      {% endfor %}
    </div>
    <div class="product-gallery-slider swiper-container max-w-100vw h-full" neptune-swiper>
      <div class="swiper-wrapper">
      {% assign count = -1 %}
      {%- for image in images -%}
        {% unless image.alt contains 'https' %}
          {% assign count = count | plus: 1 %}
          <div class="swiper-slide pv__photo h-full overflow-y-scroll product-gallery-{{ count }}">
            <div class="h-full">
              <img {{'s'}}rc="{{ image.src }}" alt="{{ image.alt }}" class="w-full">
            </div>
          </div>
        {% endunless %}
      {% endfor %}
      </div>
    </div>
    {% endraw %}
    <div class="fixed z-10 top-2 right-2 lg:top-4 lg:right-4">
      <button class="cursor-pointer" onclick="modal.close()">
        {% render 'icon' icon:'close' width:24 height:24 %}
      </button>
    </div>
  </div>
</div>
{% endcapture %}
{% render 'modal' position:'full' topic:'lightbox' content:lightboxContent %}