{% comment %} 


This is generated by webpack so don't edit this unless you want to be sad when your work gets overwritten in production.
You can edit this file in ../../components/** 


{% endcomment %}






<button class="absolute transform -translate-x-2/4 -translate-y-2/4 border-none w-8 h-8 rounded-full p-1 drop-shadow cursor-pointer z-20 group" style="background:linear-gradient(#e66465, #9198e5); top:{{ y }}%; left:{{ x }}%;" hotspot data-story="{{ product }}" data-product="{{ product }}" {% if hover %}onmouseenter="{{hover}}"{% endif %}>
  <div class="w-full h-full rounded-full bg-white p-1 ">
    <div class="w-full h-full rounded-full bg-opacity-50 group-hover:bg-opacity-100 group-active:bg-opacity-100 bg-white p-1"></div>
  </div>
  <span class="-bottom-2 -left-2 -right-2 -top-2 absolute border-4 bg-white border-white group-hover:opacity-0 group-active:opacity-0 group-hover:scale-100 group-active:scale-100 rounded-full scale-50 transform transition-all duration-1000"></span>
</button>