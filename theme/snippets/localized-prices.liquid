{%- unless localization.country.iso_code == 'US' -%}
  <script>
    {%- capture symbol -%}{%- render 'currency-symbol' -%}{%- endcapture -%}
    window.localizedPrices = {
      {%- if template contains 'search' -%}
        {%- paginate search.results by 10000 -%}
          {%- for product in search.results -%}
          "{{ product.handle }}": {
              price: {{ product.price }},
              compare_at_price: {{ product.compare_at_price | default: 0 }}
              {%#- render 'localized-price' product: product, symbol: symbol -%}
            }{%- unless forloop.last %},{% endunless %}
          {%- endfor %}
        {%- endpaginate -%}
      {%- else -%}
        {%- paginate collection.products by 10000 -%}
          {%- for product in collection.products -%}
            "{{ product.handle }}": {
              price: {{ product.price }},
              compare_at_price: {{ product.compare_at_price | default: 0 }}
              {%#- render 'localized-price' product: product, symbol: symbol -%}
            }{%- unless forloop.last %},{% endunless %}
          {%- endfor -%}
        {%- endpaginate -%}
      {%- endif -%}
    };
  </script>
  <script>
    window.addEventListener('Collection:productData', e=>{
      collection.products.map(p=>{

        p.price = localizedPrices[p.handle].price
        p.compare_at_price = localizedPrices[p.handle].compare_at_price
        return p
        
      })
      console.log('Collection:productData',collection.products)
    })
  </script>
{%- endunless -%}
