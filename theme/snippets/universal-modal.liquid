{% comment %}
  Universal Modal Content
  - Content is sourced from metaobjects
  - Used with existing modal system
  - Supports image, text+image, and video content types
  - Supports separate mobile/desktop images using lazy-image snippet
{% endcomment %}

{%- liquid

  assign modal_id = id | append: '-modal'

  assign modal = _settings.modal_content

  assign position = modal.position.value | handleize

  assign content_type = modal.content_type.value | handleize

  assign image_class = 'object-cover w-full h-auto'

-%}

{% capture modal_content %}
  <div class="universal-modal contents">

    <button type="button"
      class="absolute top-0 right-0 text-white rounded-full"
      data-modal-close
      aria-label="Close"
      onclick="window.modal.close()"
    >
      {% render 'icon' icon:'x' width:24 height:24 %}
    </button>

    <div class="universal-modal__content flex flex-col no-scrollbar">
      {% case content_type %}
        {% when 'image' %}
          <div class="universal-modal__image-container">
            {% if modal.image_mobile != blank and modal.image_desktop != blank %}
              <div class="hidden md:block">
                {% render 'lazy-image' with image: modal.image_desktop.value, image_class: image_class %}
              </div>
              <div class="block md:hidden">
                {% render 'lazy-image' with image: modal.image_mobile.value, image_class: image_class %}
              </div>
            {% elsif modal.image_desktop != blank %}
              {% render 'lazy-image' with image: modal.image_desktop.value, image_class: image_class %}
            {% elsif modal.image_mobile != blank %}
              {% render 'lazy-image' with image: modal.image_mobile.value, image_class: image_class %}
            {% elsif image != blank %}
              {# Fallback to original image field if new fields aren't used #}
              {% render 'lazy-image' with image: image, image_class: image_class %}
            {% endif %}
          </div>

        {% when 'image-with-text' %}
          <div class="universal-modal__image-container">
            {% if modal.image_mobile != blank and modal.image_desktop != blank %}
              <div class="hidden md:block">
                {% render 'lazy-image' with image: modal.image_desktop.value, image_class: image_class %}
              </div>
              <div class="block md:hidden">
                {% render 'lazy-image' with image: modal.image_mobile.value, image_class: image_class %}
              </div>
            {% elsif modal.image_desktop != blank %}
              {% render 'lazy-image' with image: modal.image_desktop.value, image_class: image_class %}
            {% elsif modal.image_mobile != blank %}
              {% render 'lazy-image' with image: modal.image_mobile.value, image_class: image_class %}
            {% elsif image != blank %}
              {# Fallback to original image field if new fields aren't used #}
              {% render 'lazy-image' with image: image, image_class: image_class %}
            {% endif %}
          </div>

          <div class="universal-modal__text-container flex flex-col">
            {% if modal.title != blank %}
              <h2 class="text-2xl font-bold my-0">{{ modal.title }}</h2>
            {% endif %}

            {% if modal.text != blank %}
              <div class="universal-modal-text my-0">
                {{ modal.text }}
              </div>
            {% endif %}
          </div>

        {% when 'video' %}
          {% if modal.vimeo_id != blank %}
            <div class="universal-modal__video-container">
              <iframe
                src="https://player.vimeo.com/video/{{ modal.vimeo_id }}?autoplay=0&loop=0&title=0&byline=0&portrait=0"
                allow="autoplay; fullscreen; picture-in-picture"
                allowfullscreen
                loading="lazy"
                class="w-full h-full"
              ></iframe>
            </div>
          {% endif %}
      {% endcase %}
    </div>

    {% style %}
      [data-modal="{{ modal_id }}"] {
        background: {{ modal.bg_color | default: 'transparent' }};
        transition-duration: {{ modal.animation_duration }}ms;
        {% if modal.max_width != blank -%}
          {%- if position != 'top' and position != 'bottom' and position != 'full' -%}
          max-width: min({{ modal.max_width }}px, 95vw);
          {%- endif -%}
        {%- endif %}
      }

      [data-modal="{{ modal_id }}"] .universal-modal__content {
        gap: {{ modal.gap_mobile }}px;
        padding: {{ modal.padding_mobile }}px;
      }

      @media (min-width: 1024px) {
        [data-modal="{{ modal_id }}"] .universal-modal__content {
          gap: {{ modal.gap_desktop }}px;
          padding: {{ modal.padding_desktop }}px;
        }
      }

      [data-modal="{{ modal_id }}"] .universal-modal__text-container {
        text-align: {{ modal.text_alignment | default: 'left'| handleize }};
        gap: {{ modal.gap_mobile }}px;
      }

      @media (min-width: 1024px) {
        [data-modal="{{ modal_id }}"]  .universal-modal__text-container {
          gap: {{ modal.gap_desktop }}px;
        }
      }

      /* Media */
      [data-modal="{{ modal_id }}"] :is(img, iframe) {
        aspect-ratio: {{ modal.media_aspect_ratio | default: 'auto' | handleize | replace: '-', '/' }};
        object-position: {{ modal.image_position | handleize }};
      }
    {% endstyle %}

  </div>

{% endcapture %}

{% render 'modal',
  topic: modal_id,
  position: position,
  content: modal_content
%}