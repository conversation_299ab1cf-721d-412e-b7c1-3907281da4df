{% comment %} 


This is generated by webpack so don't edit this unless you want to be sad when your work gets overwritten in production.
You can edit this file in ../../components/** 


{% endcomment %}






<form action="/search" method="get" role="search" class="flex lg:hidden relative container bg-light-gray pt-4 px-8 pb-0 items-center justify-center w-full group modal-search-form">
  <label for="headerSearchMobile" class="invisible hidden">
    Search
  </label>
  <input 
  type="search" 
  name="q" 
  id="headerSearchMobile" 
  value="" 
  placeholder="Search..." 
  class="search__input focus:border-0 focus:outline-none mt-0 shadow-none ui-autocomplete-input w-full px-4 py-2 rounded-none leading-tight text-lg border border-black" 
  style="font-size: 16px;"
  autocomplete="off" 
  autocorrect="off" 
  autocapitalize="off" 
  aria-label="Search" 
  aria-autocomplete="list" 
  onkeydown="this.typingTimer = this.typingTimer || {}; clearTimeout(this.typingTimer);" 
  onkeyup="clearTimeout(this.typingTimer); this.typingTimer = setTimeout(()=>{Neptune.liquid.load('SearchResults','url:/search/suggest.json?resources[type]=product,article,collection,page&q='+this.value+'&resources[options][fields]=title,product_type,variants.sku');_n.qs('.search-results').classList.remove('hidden')},1000)"
  >
  <input type="submit" value="go" class="sr-only">

  <button type="button" class="btn-none search__mobile_clear absolute activate btn-icon m-0 right-16 bottom-0 block h-11 w-16 flex flex-col items-center justify-center py-2 px-4" onclick="
    let searchMobileForm = document.querySelector('.search-mobile__wrap .search__form');
    document.getElementById('searchForm').reset();
    searchMobileForm.focus();
    searchMobileForm.click();"
    neptune-engage="{
      on:click,
      targets:[{
        selector:body
        attributes:[{
          att:data-active-modal-mobile
          set:_remove
        }]
      },
      {
        selector:.header,
        classes:add:z-5
      },
      {
        selector:.header__nav,
        classes:remove:active
      },
      {
        selector:.search-results,
        classes:add:hidden
      },
      {
        selector:.nav__item,
        classes:remove:active
      },
      {
        selector:'[data-return-focus]'
        attributes:[{
          att:data-return-focus 
          set:_remove
        }]
        focus:true
      }]
    }"
    >
    <span class="sr-only">Close</span>
    {% render 'icon' icon:'close' fill="currentColor" height:16 width:16 %}
  </button>

  <button class="border-none cursor-pointer text-center absolute animate right-8 bottom-0 w-16 h-11 flex flex-col items-center justify-center py-2 px-4 m-0">
    <span class="text-black flex flex-col justify-center items-center">
      {% render 'icon' icon:'search', height:16, width:16 %}
    </span>
    <span class="icon-fallback-text sr-only">Search</span>
  </button>          
</form>