{% raw %}
  {% for badge in productBadges | where: 'type', 'badge' %}
    {% unless badge.cart_badge %}{% continue %}{% endunless %}
    {% assign tagToCheck = badge.tag %}

    {% for tag in item.properties._tags %}
      {% if tag == tagToCheck %}
        <div style="color:{{ badge.color }}; ">
            <p class="mb-0 text-xs leading-4 text-right">{{ badge.cart_badge | newline_to_br }}</p>
        </div>      
        {% break %}
      {% endif %}
    {% endfor %} 
  {% endfor %}
{% endraw %}