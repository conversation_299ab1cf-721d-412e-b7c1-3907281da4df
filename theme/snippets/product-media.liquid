{% comment %}
    Renders product media

    Accepts:
    - media: {Object} Product Media object
    - loop: {Boolean} Enable video looping (optional)
    - variant_image: {Boolean} The media associated with a variant

    Usage:
    {% render 'product-media',
      media: media,
      loop: section.settings.enable_video_looping,
      variant_image: true
    %}
{% endcomment %}

{%- if media.media_type == 'image' -%}
  <img
    srcset="{%- if media.preview_image.width >= 550 -%}{{ media.preview_image | img_url: '550x' }} 550w,{%- endif -%}
            {%- if media.preview_image.width >= 1100 -%}{{ media.preview_image | img_url: '1100x' }} 1100w,{%- endif -%}
            {%- if media.preview_image.width >= 1680 -%}{{ media.preview_image | img_url: '1680x' }} 1680w,{%- endif -%}
            {%- if media.preview_image.width >= 2048 -%}{{ media.preview_image | img_url: '2048x' }} 2048w,{%- endif -%}
            {%- if media.preview_image.width >= 4096 -%}{{ media.preview_image | img_url: '4096x' }} 4096w{%- endif -%}"
    sizes="(min-width: 750px) calc(100vw - 12rem), 100vw"
    src="{{ media.preview_image | img_url: '750x' }}"
    alt="{{ media.alt | escape }}"
    loading="lazy"
    width="1100"
    height="{{ 1100 | divided_by: media.preview_image.aspect_ratio | ceil }}"
    data-media-id="{{ media.id }}"
    {% if variant_image %}class="product__media-item--variant"{% endif %}
  >
{%- else -%}
  {%- if media.media_type == 'model' -%}
    <div class="product-media-modal__model" data-media-id="{{ media.id }}">
      <product-model class="deferred-media media media--transparent" style="padding-top: min(calc(100vh - 12rem), 100%)">
  {%- else -%}
    <deferred-media class="deferred-media media" style="padding-top: min(calc(100vh - 12rem), {{ 1 | divided_by: media.aspect_ratio | times: 100 }}%)" data-media-id="{{ media.id }}">
  {%- endif -%}

    <button id="Deferred-Poster-Modal-{{ media.id }}" class="deferred-media__poster" type="button">
      <span class="deferred-media__poster-button motion-reduce">
        {%- if media.media_type == 'model' -%}
          {%- render 'icon-3d-model' -%}
        {%- else -%}
          {%- render 'icon-play' -%}
        {%- endif -%}
      </span>
      <img
        srcset="{% if media.preview_image.width >= 288 %}{{ media.preview_image | img_url: '288x' }} 288w,{% endif %}
                {% if media.preview_image.width >= 576 %}{{ media.preview_image | img_url: '576x' }} 576w,{% endif %}
                {% if media.preview_image.width >= 550 %}{{ media.preview_image | img_url: '550x' }} 550w,{% endif %}
                {% if media.preview_image.width >= 1100 %}{{ media.preview_image | img_url: '1100x' }} 1100w{% endif %}"
        src="{{ media | img_url: '550x550' }}"
        sizes="(min-width: 1200px) calc((1200px - 10rem) / 2), (min-width: 750px) calc((100vw - 11.5rem) / 2), calc(100vw - 4rem)"
        loading="lazy"
        width="576"
        height="{{ 576 | divided_by: media.preview_image.aspect_ratio }}"
        alt="{{ media.preview_image.alt | escape }}"
      >
    </button>
    <template>
      {%- case media.media_type -%}
      {%- when 'external_video' -%}
        {%- assign video_class = 'js-' | append: media.host -%}
        {%- if media.host == 'youtube' -%}
          {{ media | external_video_url: autoplay: true, loop: loop, playlist: media.external_id | external_video_tag: class: video_class, loading: "lazy" }}
        {%- else -%}
          {{ media | external_video_url: autoplay: true, loop: loop | external_video_tag: class: video_class, loading: "lazy" }}
        {%- endif -%}
      {%- when 'video' -%}
        {{ media | media_tag: image_size: "2048x", autoplay: true, loop: enable_video_looping, controls: true, preload: "none", autoplay: true }}
      {%- when 'model' -%}
        {{ media | media_tag: image_size: "2048x", toggleable: true }}
      {%- endcase -%}
    </template>

  {%- if media.media_type == 'model' -%}
      </product-model>
      <button
        class="button button--full-width product__xr-button"
        type="button"
        aria-label="{{ 'products.product.xr_button_label' | t }}"
        data-shopify-xr
        data-shopify-model3d-id="{{ media.id }}"
        data-shopify-title="title"
        data-shopify-xr-hidden
        >
        {% render 'icon-3d-model' %}
        {{ 'products.product.xr_button' | t }}
      </button>
    </div>
  {%- else -%}
    </deferred-media>
  {%- endif -%}
{%- endif -%}
