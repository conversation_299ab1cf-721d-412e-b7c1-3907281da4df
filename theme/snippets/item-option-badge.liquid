{% raw %}
  {% assign badges = productBadges | where: 'type', 'option' %}
  {% for badge in badges %}
    {% assign show_badge = false %}
    {% if product.tags contains badge.tag %}
      {% assign show_badge = true %}
    {% endif %}
    {% for sibling in product.siblings %}
      {% if sibling.tags contains badge.tag %}
        {% assign show_badge = true %}
      {% endif %}
    {% endfor %}
    {% if show_badge %}
    <span class="product-item__option-badge">{{ badge.label }}</span>
    {% endif %}
  {% endfor %}
{% endraw %}