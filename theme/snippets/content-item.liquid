{% liquid 

  if section.sticky == true and block.image == blank
    assign BlockTextPositionLg = 'lg:relative'
  elsif section.height contains 'content-height' or block.image == blank
    assign BlockImagePositionLg = 'lg:absolute lg:overflow-hidden'
    assign BlockTextPositionLg = 'lg:relative'
  elsif section.height contains 'content-below-height'
    assign BlockImagePositionLg = 'lg:relative lg:overflow-visible'
    assign BlockTextPositionLg = 'lg:relative'
  else
    assign BlockImagePositionLg = 'lg:relative lg:overflow-hidden'
    assign BlockTextPositionLg = 'lg:absolute'
  endif

  if section.sticky == true and block.image == blank
    assign BlockTextPosition = 'relative'
  elsif block.image_mobile == blank and block.image == blank and block.video_url_mobile == blank and block.video_url == blank
    assign BlockTextPosition = 'relative'
  elsif section.height_mobile contains 'content-height-mobile' or block.image == blank
    assign BlockImagePosition = 'absolute overflow-hidden'
    assign BlockTextPosition = 'relative'
  elsif section.height_mobile contains 'content-below-height'
    assign BlockImagePosition = 'relative'
    assign BlockTextPosition = 'relative'
  else
    assign BlockImagePosition = 'relative overflow-hidden'
    assign BlockTextPosition = 'absolute'
  endif

%}

{% if block.video_url != blank or block.image_mobile != blank or block.image != blank %}
  <div class="block__image_wrap h-full w-full {% if section.reverse_content %} order-{{ loop.rindex }}{% endif %} {{ block.corners }} {{ block.corners_mobile }} {{ BlockImagePosition }} {{ BlockImagePositionLg }} {{ section.item_height }} {{ section.item_height_mobile }}">

    {%- if block.video_url != blank and block.video_url_mobile != blank -%}
      <video preload="metadata" id="video-{{ blockId }}" autoplay playsinline muted loop {% if block.poster_image != blank %}poster="{{ block.poster_image | img_url: 'master' }}"{% endif %} class="absolute z-0 top-0 left-0 w-full h-full lg:block hidden" style="object-fit: cover;" neptune-uncomment="visible">
        <!-- <source src="{{ block.video_url }}" type="video/mp4"> -->
      </video>
      <video preload="metadata" id="video-{{ blockId }}__mobile" autoplay playsinline muted loop {% if block.poster_image != blank %}poster="{{ block.poster_image | img_url: 'master' }}"{% endif %} class="absolute z-0 top-0 left-0 w-full h-full lg:hidden block" style="object-fit: cover;" neptune-uncomment="visible">
        <!-- <source src="{{ block.video_url_mobile }}" type="video/mp4"> -->
      </video>
    {%- elsif block.video_url != blank and block.video_url_mobile == blank  -%}
      <video preload="metadata" id="video-{{ blockId }}" autoplay playsinline muted loop {% if block.poster_image != blank %}poster="{{ block.poster_image | img_url: 'master' }}"{% endif %} class="absolute z-0 top-0 left-0 w-full h-full" style="object-fit: cover;" neptune-uncomment="visible">
        <!-- <source src="{{ block.video_url }}" type="video/mp4"> -->
      </video>
    {%- endif-%}

    {%- if block.video_url != blank and block.video_url_mobile != blank -%}
      {% render 'video-controls' data:block %}       
    {%- endif-%}    

    {% capture imageMobileLg %}{{ BlockImagePositionLg }} top-0 left-0 max-w-full w-full h-full object-cover object-center lg:block hidden{% endcapture %}
    {% capture imageMobileSm %}{{ BlockImagePosition }} top-0 left-0 max-w-full w-full h-full object-cover object-center lg:hidden block{% endcapture %}
    {% capture imageMobile %}block {{ BlockImagePositionLg }} {{ BlockImagePosition }} top-0 left-0 max-w-full w-full h-full object-cover object-center{% endcapture %}

    {% liquid

      if section.height contains 'image-height' and section.height_mobile contains 'image-height-mobile' 
        if block.image_mobile != blank
          render 'lazy-image' with image:block.image, image_class:"max-w-full w-full lg:block hidden"
          render 'lazy-image' with image:block.image_mobile, image_class:"max-w-full w-full lg:hidden block"
        elsif block.image != blank
          render 'lazy-image' with image:block.image, image_class:"block max-w-full w-full"
        endif
      else
        if block.image_mobile != blank
          render 'lazy-image' with image:block.image, image_class:imageMobileLg
          render 'lazy-image' with image:block.image_mobile, image_class:imageMobileSm
        elsif block.image != blank 
          render 'lazy-image' with image:block.image, image_class:imageMobile
        endif
      endif
    %}

    {% if block.overlay_img != blank %}
      {%  render 'lazy-image' with image:block.overlay_img, image_class:"max-w-full w-full h-full absolute z-10 top-0 left-0" %}
    {% endif %}

    {% if block.overlay != blank %}
      <div class="absolute w-full h-full z-10 top-0 left-0" style="background-color: {{ block.overlay }}; opacity: {{ block.overlay_opacity }}%;"></div>
    {% endif %}
  </div>
{% endif %} 

{% if block.title_image != blank or block.title_svg != blank or block.pretitle != blank or block.title != blank or block.subtitle != blank or block.featured_content != blank or block.featured_content != blank or block.html != blank or block.link_title != blank %}
  <div id="contentItemText-{{ blockId }}" 
  class="block__text w-full z-20 left-0 top-0 flex flex-wrap flex-col 
    {{ block.content_position }} {{ block.content_position_mobile }} {{ block.text_align }} {{ block.text_align_mobile }} 
    {% if section.height contains 'content-below-height' %}lg:h-auto{% else %}lg:h-full{% endif %} 
    {% if section.height_mobile contains 'content-below-height' %}h-auto{% else %}h-full{% endif %} 
    {{ BlockTextPosition }} {{ BlockTextPositionLg }} {% if section.reverse_content %} order-{{ loop.index }}{% endif %}
    {% if BlockTextPositionLg == 'lg:absolute' and BlockTextPosition == 'absolute' %} {{ section.block_gutters }} {{ section.block_gutters_mobile }} {% elsif BlockTextPositionLg == 'lg:absolute' and BlockTextPosition != 'absolute' %} {{ section.block_gutters }} {% elsif BlockTextPositionLg != 'lg:absolute' and BlockTextPosition == 'absolute' %} lg:py-0 lg:ph-0 {{ section.block_gutters_mobile }}{% endif %}
  ">
    {% if block.content_height != 'h-auto' or section.content_height != 'h-auto' %}
      <div class="block__content-container relative flex flex-wrap flex-col w-full {{block.content_height}} {{ section.content_height }} {{ BlockTextPositionLg }} {{ BlockTextPosition }} {{ block.content_position }} {{ block.content_position_mobile }} {{ block.text_align }} {{ block.text_align_mobile }}">
    {% endif %}

      {% if block.content_scroll_effect contains 'sticky-text' %}
        <div class="content-item__text-container">
      {% endif %}
        <div class="content-item__text flex {% unless block.content_scroll_effect contains 'sticky-text invert-color' %} {{ block.content_width }} {{ block.content_direction }} {{ block.content_direction_mobile }} {{ block.content_vertical_padding }} {{ block.content_horizontal_padding }} {{ block.content_vertical_padding_mobile }} {{ block.content_horizontal_padding_mobile }} {{ block.content_gap }} {{ block.content_gap_mobile }}{% endunless %}" data-content-item-text-height
          neptune-surface="{
            'windowEdge': 'bottom',
            'elementEdge': 'top',
            'offset': '-50',
            'targets': [
              {
                'selector': '_self .block__title',
                'engage:action': {
                  'classes': {
                    'add': ['active']
                  }
                }
              }
            ]
          }"
          >
            {% if block.content_scroll_effect contains 'sticky-text invert-color' %}
              <div class="content-item__text-inner flex {{ block.content_width }} {{ block.content_direction }} {{ block.content_direction_mobile }} {{ block.content_vertical_padding }} {{ block.content_horizontal_padding }} {{ block.content_vertical_padding_mobile }} {{ block.content_horizontal_padding_mobile }} {{ block.content_gap }} {{ block.content_gap_mobile }}">
            {% endif %}

              <div class="block__text_content w-full flex flex-col {{ block.content_gap }} {{ block.content_gap_mobile }}">

                {% if block.title_image != blank %}
                  <div class="inline-block w-full" {% if block.title_image_width != blank %}style="max-width: {{ block.title_image_width }};"{% endif %} alt="{{ block.title }}">
                    {% render 'lazy-image' with image: block.title_image, image_class: 'block w-full m-0' %}
                  </div> 
                {% endif %}

                {%- if block.title_svg_code != blank -%}
                  {{- block.title_svg_code -}}
                {%- endif -%}

                {% if block.title_svg != blank %}
                  <img src="{{ block.title_svg | file_url }}" class="inline-block m-0" {% if block.title_image_width != blank %}style="max-width: 100%; width: {{block.title_image_width }};"{% endif %} alt="{{ block.title }}">
                {% endif %}

                {% if block.pretitle != blank %}
                  <p class="block__pretitle {{ block.pretitle_type }} my-0">{{ block.pretitle }}</p>
                {% endif %}

                {% if block.title != blank %}
                  <{{ block.title_element }} class="block__title {{ block.title_type }} my-0">
                      {{ block.title }}
                  </{{ block.title_element }}>
                {% endif %}

                {% if block.subtitle != blank %}
                  <h3 class="block__subtitle {{ block.subtitle_type }} my-0" >{{ block.subtitle }}</h3>
                {% endif %}

                {% if block.featured_content != blank %}
                  <div class="block__featured_content {{ block.featured_content_type }} {% if block.text_margins %}lg:my-8{% else %}lg:my-0{% endif %}">{{ block.featured_content }}</div>
                {% endif %}

                {% if block.html != blank %}
                  <div class="block__html">{{ block.html }}</div>
                {% endif %}

                {% style %}
                  #contentItemText-{{ blockId }} .block__pretitle {
                    font-size: {{ block.pretitle_size }}px;
                    line-height: {{ block.pretitle_leading }};
                    {% if block.pretitle_tracking != blank %}letter-spacing: {{ block.pretitle_tracking }};{% endif %}
                    {% if block.pretitle_color != blank %}color: {{ block.pretitle_color }};{% endif %}
                    {% if block.pretitle_font_override != blank %}font-family: {{block.pretitle_font_override}};{% endif %}
                  }
                  #contentItemText-{{ blockId }} .block__title {
                    font-size: {{ block.title_size }}px;
                    line-height: {{ block.title_leading }};
                    {% if block.title_tracking != blank %}letter-spacing: {{ block.title_tracking }};{% endif %}
                    {% if block.title_color != blank %}color: {{ block.title_color }};{% endif %}
                    {% if block.title_font_override != blank %}font-family: {{block.title_font_override}};{% endif %}
                  }
                  #contentItemText-{{ blockId }} .block__subtitle {
                    font-size: {{ block.subtitle_size }}px;
                    line-height: {{ block.subtitle_leading }};
                    {% if block.subtitle_tracking != blank %}letter-spacing: {{ block.subtitle_tracking }};{% endif %}
                    {% if block.subtitle_color != blank %}color: {{ block.subtitle_color }};{% endif %}
                    {% if block.subtitle_font_override != blank %}font-family: {{block.subtitle_font_override}};{% endif %}
                  }
                  #contentItemText-{{ blockId }} .block__featured_content , #contentItemText-{{ blockId }} .block__featured_content p {
                    font-size: {{ block.featured_content_size }}px;
                    line-height: {{ block.featured_leading }};
                    {% if block.featured_tracking != blank %}letter-spacing: {{ block.featured_tracking }};{% endif %}
                    {% if block.featured_content_color != blank %}color: {{ block.featured_content_color }};{% endif %}
                    {% if block.featured_content_font_override != blank %}font-family: {{block.featured_content_font_override}};{% endif %}
                  }
                  #contentItemText-{{ blockId }} .block__featured_content p {
                    margin-bottom: 1em;
                  }
                  #contentItemText-{{ blockId }} .block__featured_content p:last-child {
                    margin-bottom: 0;
                  }
                  #contentItemText-{{ blockId }} .block__featured_content p a {
                    text-decoration: underline;
                  }
                  @media (max-width: 767px) {
                    #contentItemText-{{ blockId }} .block__pretitle {
                      font-size: {{ block.pretitle_size_mobile }}px;
                    }
                    #contentItemText-{{ blockId }} .block__title {
                      font-size: {{ block.title_size_mobile }}px;
                    }
                    #contentItemText-{{ blockId }} .block__subtitle {
                      font-size: {{ block.subtitle_size_mobile }}px;
                    }
                    #contentItemText-{{ blockId }} .block__featured_content , #contentItemText-{{ blockId }} .block__featured_content p {
                      font-size: {{ block.featured_content_size_mobile }}px;
                    }
                  }
                  {% if block.html != blank and block.grid_styling == true %}
                    #contentItemText-{{ blockId }} .block__html {
                      display: grid; 
                      grid-template-columns: 1fr 1fr; 
                      grid-template-rows: 1fr; 
                      gap: 4rem; 
                    }
                    #contentItemText-{{ blockId }} .block__html iframe {
                      width: 100%;
                      height: 300px;
                    }
                    @media (max-width: 767px) {
                      #contentItemText-{{ blockId }} .block__html {
                        gap: 2rem; 
                        grid-template-columns: 1fr;
                        grid-template-rows: 1fr; 
                      }
                    }
                  {% endif %}
                  {% if section.height_mobile contains 'content-below-height' %}
                    @media (max-width: 1023px) {
                      .content-below-height #contentItemText-{{ blockId }} .block__title, .content-below-height #contentItemText-{{ blockId }} .block__pretitle, #contentItemText-{{ blockId }} .block__subtitle, .content-below-height #contentItemText-{{ blockId }} .block__featured_content, .content-below-height #contentItemText-{{ blockId }} .block__featured_content p {
                          color: #000!important;
                      }
                      .section-{{ sectionId }} .content-below-height .block__text_content .button {
                        background-color: #000 !important;
                        border-color: #000 !important;
                        color: #fff !important;
                      }
                    }
                  {% endif %}
                {% endstyle %}
              </div>

              <!-- Buttons -->

              {% if block.link_title != blank or block.link_title_two != blank %}
                <div class="content-item__buttons w-full flex {{ block.button_horizontal_align }} {{ block.button_horizontal_align_mobile }} {{ block.button_vertical_align }} {{ block.button_vertical_align_mobile }}">
                  <div class="flex flex-row flex-wrap gap-2">
                    {% if block.link_title != blank %}
                      <{% if block.link_element == 'block' %}button onclick="window.location.href='{{ block.url }}'"{% else %}a href="{{ block.url }}"{% endif %} class="button--one inline-flex gap-2 items-center {{ block.button_style }}">
                        {% render 'inline-icon-text' source: block.link_title %}
                      </{% if block.link_element == 'block' %}button{% else %}a{% endif %}>
                    {% endif %}
          
                    {% if block.link_title_two != blank %}
                      <{% if block.link_element == 'block' %}button onclick="window.location.href='{{ block.url_two }}'"{% else %}a href="{{ block.url_two }}"{% endif %} class="button--two inline-flex gap-2 items-center {{ block.button_style_two }}">
                        {% render 'inline-icon-text' source: block.link_title_two %}
                      </{% if block.link_element == 'block' %}button{% else %}a{% endif %}>
                    {% endif %}
                  </div>
          
                  <style>
                    {% if block.link_title != blank %}
                      #block-{{ blockId }} .button--one,
                      .content-{{ blockId }} .button--one {
                        background-color: {{ block.btn_color }}; 
                        border-color: {{ block.btn_border_color }}; 
                        color: {{ block.btn_text_color }};
                        {% if block.button_size %}
                          font-size: {{ block.button_size}}px;
                        {% endif %}
                      }
                      {% if block.button_size_mobile %}
                        @media(max-width: 1024px){
                          #block-{{ blockId }} .button--one,
                          .content-{{ blockId }} .button--one {
                            font-size: {{ block.button_size_mobile }}px;
                          }
                        }
                      {% endif %}
                      #block-{{ blockId }} .button--one:hover, #block-{{ blockId }} .button--one:focus,
                      .content-{{ blockId }} .button--one:hover, .content-{{ blockId }} .button--one:focus {
                        {% unless block.button_style contains 'button--text' or block.button_style contains 'button--link' %}
                          {% if block.btn_hover_color != blank %}background-color: {{ block.btn_hover_color }};{% endif %}
                          {% if block.btn_hover_border_color != blank %}border-color: {{ block.btn_hover_border_color }};{% endif %}
                        {% endunless %}
                        {% if block.btn_hover_text_color != blank %}color: {{ block.btn_hover_text_color }};{% endif %}
                      }
                    {% endif %}
                    
                    {% if block.link_title_two != blank %}
                      #block-{{ blockId }} .button--two,
                      .content-{{ blockId }} .button--two {
                        background-color: {{ block.btn_color_two }}; 
                        border-color: {{ block.btn_border_color_two }}; 
                        color: {{ block.btn_text_color_two }};
                        {% if block.button_size_two %}
                          font-size: {{ block.button_size_two }}px;
                        {% endif %}
                      }
                      {% if block.button_size_mobile_two %}
                        @media(max-width: 1024px){
                          #block-{{ blockId }} .button--two,
                          .content-{{ blockId }} .button--two {
                            font-size: {{ block.button_size_mobile_two }}px;
                          }
                        }
                      {% endif %}
                      #block-{{ blockId }} .button--two:hover, #block-{{blockId}} .button--two:focus,
                      .content-{{ blockId }} .button--two:hover, .content-{{ blockId }} .button--two:focus {
                        {% unless block.button_style_two contains 'button--text' or block.button_style_two contains 'button--link' %}
                          {% if block.btn_hover_color_two != blank %}background-color: {{ block.btn_hover_color_two }};{% endif %}
                          {% if block.btn_hover_border_color_two != blank %}border-color: {{ block.btn_hover_border_color_two }};{% endif %}
                        {% endunless %}
                        {% if block.btn_hover_text_color_two != blank %}color: {{ block.btn_hover_text_color_two }};{% endif %}
                      }
                    {% endif %}
                  </style>
                </div>
              {% endif %}

            {% if block.content_scroll_effect contains 'sticky-text invert-color' %}
              </div>
            {% endif %}
          </div>
      {% if block.content_scroll_effect contains 'sticky-text' %}
        </div>
      {% endif %}

    {% if block.content_height != 'h-auto' or section.content_height != 'h-auto' %}
      </div>
    {% endif %}
  </div>
{% endif %}

<script>
  {% if block.content_scroll_effect contains 'sticky-text' %}

    {% assign id = blockId | replace: '-', '' %} // removing hyphens to prevent js variable name errors

    let contentItemText_{{ id }} = document.querySelector("#contentItemText-{{ blockId }} .content-item__text"); 
    let contentItemImage_{{ id }} = document.querySelector("#block-{{ blockId }}.invert-color .block__image_wrap"); 
    
    let contentItemTextHeight_{{ id }} = contentItemText_{{ id }}.clientHeight + 'px';
    document.documentElement.style.setProperty('--content-item-text-height-{{ id }}', contentItemTextHeight_{{ id }});

    let imageWrapperHeight_{{ id }}  = `calc(100% - ${contentItemTextHeight_{{ id }}})`; 
    if(!!contentItemImage_{{ id }}) contentItemImage_{{ id }}.style.height = imageWrapperHeight_{{ id }};

    {% if block.content_scroll_effect contains 'invert-color' %}
      document.addEventListener("duplicate:text", function (e) {
        const triggerElement_{{ id }} = document.querySelector('.sticky-text.invert-color #contentItemText-{{ blockId }} .content-item__text');
        const viewportHeight = window.innerHeight;

        let top;
        if (window.innerWidth <= 1023) {
          top = (100 - ((triggerElement_{{ id }}.offsetHeight / viewportHeight) * 100)); // mobile requires a different calculation so the inversion effect triggers at the bottom of the image
        } else {
          top = 65; // this needs to match the top value in the css for the sticky-text effect for desktop
        }
        
        const elHeight_{{ id }} = triggerElement_{{ id }}.offsetHeight;
        const elHeightPercentage_{{ id }} = (elHeight_{{ id }} / viewportHeight) * 100;
        const posPercent_{{ id }} = top + elHeightPercentage_{{ id }};

        Animations.gsap.registerPlugin(Animations.ScrollTrigger, Animations.Draggable, Animations.InertiaPlugin);

        // color inversion effect
        Animations.gsap.fromTo(
          "#block-{{ blockId }} .content-item__text-inner--duplicate",
          { clipPath: "polygon(0% 100%, 100% 100%, 100% 100%, 0% 100%)" },
          {
            scrollTrigger: {
              trigger: "#block-{{ blockId }} .block__image_wrap", 
              start: () => "bottom top+=" + posPercent_{{ id }} + "%",
              end: () => '+=' + elHeight_{{ id }},
              scrub: true,
              id: "color-inversion-{{ id }}",
              onEnter() {
                document.querySelector('#block-{{ blockId }}').classList.remove('text-fade-rise'); // important to remove so the invert color effect works with the text rise effect
              },
              invalidateOnRefresh: true
            },
            clipPath: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
            ease: 'none',
            duration: 1
          }
        );
      });
    {% endif %}

  {% endif %}
</script>
