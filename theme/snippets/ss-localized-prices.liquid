{% unless localization.country.iso_code == 'US' %}
<style>
  #ss_all_prices-detail-parent {
        display: none!important;
      }
  </style>
<script>
  {%- capture symbol -%}{%- render 'currency-symbol' -%}{%- endcapture -%}
  {%- liquid 
    assign resource = collection.products
  -%}
  window.localizedPrices = {
      {%- if template contains 'search' -%}
        {%- paginate search.results by 10000 -%}
        {%- for product in search.results -%}
          "{{ product.handle }}": {
          {%- render 'localized-price' product: product, symbol: symbol -%}
          }{%- unless forloop.last %},{% endunless %}
        {%- endfor -%}
        {%- endpaginate -%}
      {%- else -%}
        {%- paginate collection.products by 10000 -%}
        {%- for product in collection.products -%}
          "{{ product.handle }}": {
          {%- render 'localized-price' product: product, symbol: symbol -%}
          }{%- unless forloop.last %},{% endunless %}
        {%- endfor -%}
        {%- endpaginate -%}
      {%- endif -%}
    };
  </script>
{% endunless %}
