{% liquid 

  assign defer_section = false
  if section.settings.async
    unless content_for_header contains section.id
      assign defer_section = true 
    endunless
  endif

%}

{% if defer_section %}

<div class="ansync-section-renderer" neptune-surface="{
      'windowEdge': 'bottom',
      'elementEdge': 'top',
      'offset':{{ settings.async_offset }},
      'once':true,
      'targets': [
        {
          'periscope:load': {
            'url': '?sections={{section.id}}',
            'path': '{{section.id}}',
            'skeleton': true
          }
        }
      ]
    }" >
  <div class="">
    {{ skeleton }}
  </div>
</div>

{% endif %}