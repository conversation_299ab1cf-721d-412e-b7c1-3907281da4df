<nav class="collection-pagination" aria-label="{{ 'general.pagination.label' | t }}" neptune-liquid="{topic:Pagination,source:Collection.pagination}">
  {% raw %}
    {% unless total_pages == 1  or total_pages == 0 %}

      <ul class="w-full list flex justify-center my-8" role="list">

        {% if current_page > 1 %}
        <button class="{{active}} pagination__arrow--prev w-6 h-6 text-center rounded-full flex justify-center items-center" onclick="Collection.page({{current_page | minus: 1}},1)">
          {% endraw %}
          {% render 'icon' icon:'arrow-left' width: 16 %}
          {% raw %}
        </button>
        {% endif %}
        
        {% for italic in pages %}
          {{ italic.number }}
        {% assign pageoffset = current_page | minus: italic | abs %}
        {% if pageoffset != 0 and pageoffset != 1 and pageoffset != 2 and pageoffset != 3 %}{% continue %}{% endif %}
        {% assign active = '' %}
        {% if current_page == italic %}
          {% assign active = 'bg-black text-white' %}
        {% endif %}
          <button class="{{active}} w-6 h-6 text-center rounded-full" onclick="Collection.page({{italic}},1)">{{italic}}</button>
          {% assign last_page_shown = italic %}
        {% endfor %}
        {% assign active = 'dim' %}
        {% if current_page == total_pages %}
          {% assign active = 'o-50 pointer-none' %}
        {% endif %}
        {% if last_page_shown != total_pages %} 
        <div class="w-0-75 h-0-75 border-none rounded-full flex items-center justify-center text-xs mx-0-5 font-bold">...</div>
        {% endif %}

        <button class="{{active}} pagination__arrow--next w-6 h-6 text-center rounded-full flex justify-center items-center" onclick="Collection.page({{current_page | plus: 1}},1)">
          {% endraw %}
          {% render 'icon' icon:'arrow-right' width: 16 %}
          {% raw %}
        </button>
      
      </ul>
    {% endunless %}
  {% endraw %}
</nav> 