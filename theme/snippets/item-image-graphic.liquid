{% raw %}
  {% assign badges = productBadges | where: 'type', 'graphic' %}
  {% for badge in badges %}
    {% if product.tags contains badge.tag %}
      {% if badge.icon contains '.' %}
        <img class="product-item-image-graphic w-full h-full absolute inset-0" src="{% endraw %}{{ '' | file_url | split: '?' | first }}{% raw %}{{ badge.icon }}" alt="{{ tag }}">
      {% else %}
        <svg class="product-item-image-graphic w-full h-full inset-0" fill="none" stroke="currentColor"><use xlink:href="#icon-def-{{ badge.icon }}"/></svg>
      {% endif %}
    {% break %}
    {% endif %}
  {% endfor %}
{% endraw %}