"id":{{ product.id}},
"handle":{{ product.handle | json }},
"title":{{ product.title | json }},
"available":{{ product.available | json }},
{%- if include_description %}
"description":{{ product.description | json }},
{%- endif %}
"siblings":[
{%- for sibling in product.metafields.family.siblings.products -%}
{
  "title": {{ sibling.title | json }},
  "featured_image": {{ sibling.featured_image | json }}
}{%- unless forloop.last  -%},{%- endunless -%}
{%- endfor -%}
],
"tags":{{ product.tags | json }},
"type":{{ product.type | json }},
"price":{{ product.price}},
"price_min":{{ product.price_min}},
"price_max":{{ product.price_max}},
"compare_at_price":{{ product.compare_at_price | json }},
{%- if include_variants -%}
  {%- if include_inventory -%}
  "yes":"do",
  "variants": [
    {%- for variant in product.variants -%}
      {%- assign inv_string = variant.inventory_quantity | prepend: '"inventory_quantity":' | append: ',"inventory_management"' -%}
      {{ variant | json | replace: '"inventory_management"', inv_string }}{%- unless forloop.last  -%},{%- endunless -%}
    {%- endfor -%}
  ],
  {%- else -%}
  "variants": {{ product.variants | json }},
  {%- endif -%}
{%- endif -%}
"featured_image": {{ product.featured_image.src | json }},
"featured_image_aspect_ratio": {{ product.images[0].aspect_ratio | json }},
"featured_image_width": {{ product.images[0].width | json }},
"featured_image_height": {{ product.images[0].height | json }},
"featured_image_id": {{ product.featured_image.id | json }},
"featured_image_alt": {{ product.featured_image.alt | json }},
"hover_image": {{ product.images[1].src | json }},
"hover_image_alt": {{ product.images[1].alt | json }},
"hover_video":{% if product.images[1].alt contains 'https' %}"{{ product.images[1].alt }}"{% else %}false{% endif %},
"alt_images":{{ product.metafields.image_indexes.gender_alternative | default: '[]' }},
"images":{{ product.images | map: 'src' | json }}
