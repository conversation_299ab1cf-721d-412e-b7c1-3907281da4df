{% if localization.available_countries.size > 1 %}
  <localization-form class="ml-auto">
    {% form 'localization' %}
      <div class="disclosure">
        <button type="button" class="disclosure__button uppercase" aria-expanded="false" aria-controls="CountryList">
          {{ localization.country.name }} ({{ localization.country.currency.iso_code }} {{ localization.country.currency.symbol }})
        </button>

        <section hidden>
          <div class="country-picker__overlay bg-black fixed inset-0 z-40 opacity-30"></div>
          <div class="country-picker-modal">
            <div class="country-picker-modal-header">
              <div class="country-picker-modal-title-container">
                <span class="country-picker-modal-title">Country</span>
                <svg class="country-picker-modal-close" viewbox="0 0 10 10" version="1.1">
                  <g stroke-width="2px">
                    <line x1="0" y1="0" x2="10" y2="10"></line>
                    <line x1="10" y1="0" x2="0" y2="10"></line>
                  </g>
                </svg>
              </div>
            </div>
            <ul id="CountryList" role="list" class="disclosure__list p-5 mt-0 overflow-y-scroll h-full">
              {% for country in localization.available_countries %}
                <li class="disclosure__item list-none block font-body text-xl leading-8 h-8" tabindex="-1">
                  <a href="#"{% if country.iso_code == localization.country.iso_code %} aria-current="true"{% endif %} data-value="{{ country.iso_code }}">
                    {{ country.name }} ({{ country.currency.symbol }})
                  </a>
                </li>
              {% endfor %}
            </ul>
          </div>
        </section>
        <input type="hidden" name="country_code" value="{{ localization.country.iso_code }}">
      </div>
    {% endform %}
  </localization-form>
{% endif %}
