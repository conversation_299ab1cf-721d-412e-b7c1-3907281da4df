
{% if search.performed %}
collection.settings.remote_url = 'search?view=feed&q={{ search.terms }}&include_variants=true&include_swatches=true&sort_by=${collection.sort || "featured"}&${ (() => { let obj = {}; Object.entries(_n.urlparams.get()).forEach(entry => { if (!entry[0].includes("filter")) return false; obj[entry[0]] = entry[1]; }); return _n.urlparams.build(obj);})() }';
{% else %}
collection.settings.remote_url = '/collections/{{ collection.handle }}?view=feed&include_variants=true&include_swatches=true&sort_by=${collection.sort || "featured"}&${ (() => { let obj = {}; Object.entries(_n.urlparams.get()).forEach(entry => { if (!entry[0].includes("filter")) return false; obj[entry[0]] = entry[1]; }); return _n.urlparams.build(obj);})() }';
{% endif %}

collection.settings.remote = {
  config_method: 'GET'
}

collection.settings.paging = 'paginated'

collection.settings.map = {
  products:{
    from:'products'
  },
  total_products:'pagination.collection.items',
  filters: []
}

collection.settings.filters = [
{% for block in section.blocks %}
{% comment %} grid, checkboxes, swatch {% endcomment %}
{% if block.type != 'set' %}{% continue %}{% endif %}
{% liquid 
  if block.settings.collection_exclusion contains 'search' and search.performed
    continue
  endif

  if block.settings.collection_exclusion contains collection.handle
    continue
  endif

  if block.settings.collection_inclusion != blank
    unless block.settings.collection_inclusion  contains collection.handle
      assign render_set = false
      continue
    endunless
  endif
%}
  {
    label: '{{ block.settings.title }}',
    key: '{{ block.settings.title | handle }}',
    format: '{{ block.settings.format }}',
    options: [
      {%- if block.settings.options != blank -%}
        {%- liquid 
          assign options = block.settings.options | newline_to_br | split: '<br />'
        -%}
        {%- for option in options -%}
          {%- liquid 
            assign label = option | split: '>>>' | first | strip
            assign value = option | split: '>>>' | last | strip
          -%}
          {
            label:'{{ label | escape }}',
            value:'{{ value | escape }}',
            handle:'{{ value | handle }}',
            count:''
          },
        {%- endfor -%}
      {% else %}
        {%- for tag in collection.all_tags -%}
          {%- liquid
            assign matchtag = tag | downcase
            assign matchinc = block.settings.inclusion | downcase
          -%}
          {%- if matchtag contains matchinc -%}
            {%- liquid 
              assign optionCount = optionCount | plus: 1

              if tag contains block.settings.delimiter
                assign value = tag | split: block.settings.delimiter | last
              else
                assign value = tag
              endif

              assign label = tag | split: block.settings.delimiter | last

              assign tag_handle = tag | handle


              assign find_replace = block.settings.find_replace | newline_to_br | split: '<br />'

              for fr in find_replace
                assign find = fr | strip_newlines | split: ':'
                assign label = label | replace: find[0], find[1]
              endfor

            -%}
            {
              label:'{{ label | escape }}',
              value:'{{ tag | escape }}',
              handle:'{{ tag_handle }}',
              delimiter:'{{ block.settings.delimiter }}',
              count:''
            },
          {%- endif -%}
        {%- endfor -%}
      {%- endif -%}
      false
    ].filter(function(v) { if (!!v) return v })
  },
{% endfor %}
  false
].filter(entry => !!entry)

window.addEventListener('Collection:productData', function () {
  
  collection.products = collection.products.filter(p=>!!p);
  
  collection.total_products = collection.products.length;

  collection.products = collection.products.splice((collection.page - 1) * collection.settings.limit, collection.settings.limit) 

  Collection.paginate(collection.page)

})