{% comment %} 


This is generated by webpack so don't edit this unless you want to be sad when your work gets overwritten in production.
You can edit this file in ../../components/** 


{% endcomment %}






<article neptune-liquid="{topic:storyViewer}" data-modal="storyViewer" id="storyViewer" class="hidden active:flex flex-col fixed bg-white w-96 lg:rounded overflow-hidden p-0 z-50 lg:transform lg:-translate-x-2/4 lg:-translate-y-2/4 flex-col h-75v inset-1/2">
  {% raw %}

  {% assign product = active_story.product %}
  {% assign prev_product = prev_story.product %}
  {% assign next_product = next_story.product %}

  <header class="w-full p-2 pointer-none animate border-b">

    <div class="flex justify-between items-center px-2">
    
      <h2 class="text-sm truncate w-11/12">
        <a href="/products/{{product.handle}}" class="hover:underline">
          {{ product.title }}
        </a>
      </h2>
      
      <button class="absolute top-1 right-1 bg-white bg-opacity-50 hover:bg-gray-800 hover:text-white p-3 rounded-full text-gray-800 transform z-10 transition-all user-select-none" onclick="modal.hide('storyViewer')">
        {% endraw %}{% render 'icon' icon:'close' width:20 height:20%}{% raw %}        
      </button>

    </div>
    
  </header>


  <main class="relative flex-grow h-1 bg-gray-200">
    
    {% if prev_product %}
    <div class="prev-story h-full absolute top-0 left-0 w-full h-full">
      <img loading="lazy" src="{{prev_product.images[0].src}}" onload="this.classList.remove('opacity-0')" class="opacity-0 animate w-full h-full object-cover inline-img">
      <div class="absolute top-0 left-0 righ-0 bottom-0 h-full w-full bg-gray-500"></div>
    </div>
    {% endif %}

    {% if next_product %}
    <div class="next-story h-full absolute top-0 left-0 w-full h-full dn">
      <img loading="lazy" src="{{next_product.images[0].src}}" onload="this.classList.remove('opacity-0')" class="opacity-0 animate w-full h-full object-contain bg-white inline-img">
      <div class="absolute top-0 left-0 righ-0 bottom-0 h-full w-full bg-gray-500"></div>
    </div>
    {% endif %}

    <div class="swiper h-full group" neptune-swiper="{
      zoom:true,
      autoplay:{
        delay:6000,
        disableOnInteraction:true,
        stopOnLastSlide:true
      },
      pagination: {
        el:'.story-pagination'
      }
    }">
      <div class="swiper-wrapper h-full">

        {% for image in product.images %}
        
          <div class="swiper-slide h-full bg-black" data-image="{{image.src}}">
            <img loading="lazy" src="{{image.src}}" onload="this.classList.remove('opacity-0')" class="opacity-0 animate w-full h-full object-cover opacity-90">
          </div>

        {% endfor %}

        <div class="swiper-slide h-full bg-white text-black overflow-y-scroll p-8 pt-16">
          {{ product.body_html }}
        </div>

      </div>

      <button class="-translate-y-1/2 absolute bg-gray-800 bg-opacity-50 group-hover:opacity-100 hover:bg-white hover:text-black opacity-0 p-3 right-1 rounded-full text-white top-1/2 transform transition-all user-select-none z-10" onclick="this.parentNode.swiper.slideNext();">
        {% endraw %}{% render 'icon' icon:'chevron-right' width:20 height:20 %}{% raw %}
      </button>

      <button class="-translate-y-1/2 absolute bg-gray-800 bg-opacity-50 group-hover:opacity-100 hover:bg-white hover:text-black opacity-0 p-3 left-1 rounded-full text-white top-1/2 transform transition-all user-select-none z-10" onclick="this.parentNode.swiper.slidePrev();">
        {% endraw %}{% render 'icon' icon:'chevron-left' width:20 height:20 %}{% raw %}
      </button>

    </div>
    <script>
      window.addEventListener('DOMContentLoaded', function(){
        _n.qs('#storyViewer .swiper').swiper.on('activeIndexChange', StoryViewer.slideChange);
        _n.qs('#storyViewer .swiper').swiper.on('afterInit', StoryViewer.slideChange);
        _n.qs('#storyViewer .swiper').swiper.on('beforeTransitionStart', swiper=>{
          // console.log(swiper.previousTranslate, swiper.translate)
          if(swiper.isEnd && (swiper.previousTranslate) < (swiper.translate - (_n.qs('#storyViewer').scrollWidth / 4) )) {
            StoryViewer.nextStory()
          }
          if(swiper.isBeginning && (swiper.previousTranslate) > (swiper.translate + (_n.qs('#storyViewer').scrollWidth / 4) )) {
            StoryViewer.prevStory()
          }
        });
      });
    </script>
    
  </main>

  <div class="story-pagination flex"></div>

  <footer class="bg-gray-50 p-4 flex items-center">
    <span class="font-medium">
      {{ product.variants[0].price | times: 100 | money }}
    </span>
    <span class="relative">
    </span>
    <span class="flex items-center ml-auto">
      <!-- <a class="block b--gray bb uppercase tracking-wide text-sm ">More...</a>  -->
      <select class="bg-white border-none rounded truncate text-base px-3 py-2 uppercase w-full {% if product.variants.size == 1 %}hidden{% endif %}" style="max-width:180px;padding-right:2rem;">
        {% for variant in product.variants %}
        <option value="{{variant.id}}">
          {% if product.options[0].values.size > 1 %}
            {{ variant.title }}
          {% else %}
            {{ variant.option2 }}
          {% endif %}
        </option>
        {% endfor %}
      </select>
      <button class="btn btn--small" onclick="Neptune.cart.addItem(this.previousElementSibling.value, 1, {Benefits:'{% for project in projects %}{% if product.tags contains project.tag %}{{project.tag}}{% endif %}{% endfor %}'})">
        Buy
      </button>
      
    </span>
  </footer>

  {% endraw %}

</article>
<script src="https://cdnjs.cloudflare.com/ajax/libs/color-thief/2.3.2/color-thief.min.js" integrity="sha512-mMe7BAZPOkGbq+zhRBMNV3Q+5ZDzcUEOJoUYXbHpEcODkDBYbttaW7P108jX66AQgwgsAjvlP4Ayb/XLJZfmsg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
