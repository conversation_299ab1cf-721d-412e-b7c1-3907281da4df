{%- if collection.id -%}
    {% capture ss_collection_attr %} collection="{{ collection.id }}" collection-name="{{ collection.title }}" collection-handle="{{ collection.handle }}"{% endcapture %}
{%- endif -%}

{%- if current_tags -%}
    {% capture ss_tags_attr %} tags="{{ current_tags | join: '|' }}"{% endcapture %}
{%- endif -%}

{%- if collection.id == nil and template != 'search' -%}
    {% assign ss_defer_attr = ' defer' %}
{%- endif -%}

{%- if collection.metafields.collection.revealed_filters == 'show' -%}
    {% assign ss_revealed_filters = ' revealed-filters="true"' %}
{%- endif -%}

{% comment %}SearchSpring Script{% endcomment %}
{% if request.page_type == 'collection' or request.page_type == 'search' %}
<script src="//cdn.searchspring.net/search/v3/js/searchspring.catalog.js?{{ settings.ss_site_id }}" hide-content="#searchspring-content"{{ ss_collection_attr }}{{ ss_tags_attr }}{{ ss_template_attr }}{{ ss_defer_attr }}{{ ss_revealed_filters }}></script>
<script>
    window.ss_script_loaded = true
</script>
{% else %}
<script>
    window.loadSSS = () => {
        document.head.appendChild( Object.assign(
            document.createElement('script'),
            {
                src: '//cdn.searchspring.net/search/v3/js/searchspring.catalog.js?{{ settings.ss_site_id }}',             
            }
        ));
        console.log('load ss script')
        window.ss_script_loaded = true
    }
    window.addEventListener('keydown',e=>{
        if (e.target.matches('[name="q"]') && !window.ss_script_loaded) window.loadSSS();
    })
    
</script>
{% endif %}