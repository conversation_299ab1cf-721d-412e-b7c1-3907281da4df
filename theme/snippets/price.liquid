{% comment %}
    Renders a list of product's price (regular, sale)

    Accepts:
    - product: {Object} Product Liquid object (optional)
    - use_variant: {Boolean} Renders selected or first variant price instead of overall product pricing (optional)
    - show_badges: {<PERSON>olean} Renders 'Sale' and 'Sold Out' tags if the product matches the condition (optional)
    - price_class: {String} Adds a price class to the price element (optional)

    Usage:
    {% render 'price', product: product %}
{% endcomment %}
{%- liquid
  if use_variant
    assign target = product.selected_or_first_available_variant
  else
    assign target = product
  endif

  assign compare_at_price = target.compare_at_price
  assign price = target.price | default: 1999
  assign available = target.available | default: false
  assign money_price = price | money
  if settings.currency_code_enabled
    assign money_price = price | money_with_currency
  endif

  if target == product and product.price_varies
    assign money_price = 'products.product.price.from_price_html' | t: price: money_price
  endif
-%}

<div class="price
  {%- if price_class %} {{ price_class }}{% endif -%}
  {%- if available == false %} price--sold-out {% endif -%}
  {%- if compare_at_price > price %} price--on-sale {% endif -%}
  {%- if product.price_varies == false and product.compare_at_price_varies %} price--no-compare{% endif -%}">
  <dl>
    {%- comment -%}
      Explanation of description list:
        - div.price__regular: Displayed when there are no variants on sale
        - div.price__sale: Displayed when a variant is a sale
        - div.price__availability: Displayed when the product is sold out
    {%- endcomment -%}
    <div class="price__regular">
      <dt>
        <span class="sr-only sr-only--inline">{{ 'products.product.price.regular_price' | t }}</span>
      </dt>
      <dd {% if show_badges == false %}class="price__last"{% endif %}>
        <span class="price-item price-item--regular">
          {{ money_price }}
        </span>
      </dd>
    </div>
    <div class="price__sale">
      <dt class="price__compare">
        <span class="sr-only sr-only--inline">{{ 'products.product.price.regular_price' | t }}</span>
      </dt>
      <dd class="price__compare">
        <s class="price-item price-item--regular">
          {% if settings.currency_code_enabled %}
            {{ compare_at_price | money_with_currency }}
          {% else %}
            {{ compare_at_price | money }}
          {% endif %}
        </s>
      </dd>
      <dt>
        <span class="sr-only sr-only--inline">{{ 'products.product.price.sale_price' | t }}</span>
      </dt>
      <dd {% if show_badges == false %}class="price__last"{% endif %}>
        <span class="price-item price-item--sale">
          {{ money_price }}
        </span>
      </dd>
    </div>
    <small class="unit-price caption{% if product.selected_or_first_available_variant.unit_price_measurement == nil %} hidden{% endif %}">
      <dt class="sr-only">{{ 'products.product.price.unit_price' | t }}</dt>
      <dd {% if show_badges == false %}class="price__last"{% endif %}>
        <span>{{- product.selected_or_first_available_variant.unit_price | money -}}</span>
        <span aria-hidden="true">/</span>
        <span class="sr-only">&nbsp;{{ 'accessibility.unit_price_separator' | t }}&nbsp;</span>
        <span>
          {%- if product.selected_or_first_available_variant.unit_price_measurement.reference_value != 1 -%}
            {{- product.selected_or_first_available_variant.unit_price_measurement.reference_value -}}
          {%- endif -%}
          {{ product.selected_or_first_available_variant.unit_price_measurement.reference_unit }}
        </span>
      </dd>
    </small>
  </dl>
  {%- if show_badges -%}
    <span class="badge price__badge-sale color-{{ settings.sale_badge_color_scheme }}" aria-hidden="true">
      {{ 'products.product.on_sale' | t }}
    </span>

    <span class="badge price__badge-sold-out color-{{ settings.sold_out_badge_color_scheme }}" aria-hidden="true">
      {{ 'products.product.sold_out' | t }}
    </span>
  {%- endif -%}
</div>
