{% if settings.onetrust_enable %}
	{{ settings.onetrust_script }}
{% endif %}

{%- comment %}
	Get current giveaway id
{% endcomment -%}
{% assign productHandle = settings.giveaway_product | replace: '/products/','' %}
{% assign product_item = all_products[productHandle] %}
{% if settings.enable_giveaway %}
	<script type="text/javascript">
		var giveawayItemId = {{ product_item.id }};
		var giveawayEnabled = {{ settings.enable_giveaway }};
	</script>
{% endif %}

{% if settings.accessibe %}
	<script defer>(function(){ var s = document.createElement('script'), e = ! document.body ? document.querySelector('head') : document.body; s.src = 'https://acsbapp.com/apps/app/dist/js/app.js'; s.async = true; s.onload = function(){ acsbJS.init({ statementLink : '', footerHtml : '', hideMobile : false, hideTrigger : true, language : 'en', position : 'left', leadColor : '#000000', triggerColor : '#000000', triggerRadius : '50%', triggerPositionX : 'right', triggerPositionY : 'bottom', triggerIcon : 'display', triggerSize : 'medium', triggerOffsetX : 20, triggerOffsetY : 20, mobile : { triggerSize : 'small', triggerPositionX : 'right', triggerPositionY : 'center', triggerOffsetX : 10, triggerOffsetY : 0, triggerRadius : '50%' } }); }; e.appendChild(s);}());</script>
{% endif %}

{% comment %}
	Save Storefront API access token
{% endcomment %}
{% if settings.storefront_api_access_token != blank %}
<script>
  window.SSAT46 = "{{ settings.storefront_api_access_token | base64_encode }}";
</script>
{% endif %}