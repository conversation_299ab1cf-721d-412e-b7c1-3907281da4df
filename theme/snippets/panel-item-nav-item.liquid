<li id="panel-{{ id }}" class="{{ settings.mobile_width | default: 'col-span-4'}} {{ settings.desktop_width | default: 'lg:col-span-4' }} panel__item panel__item--{{ type | handle }} panel__item--index-{{ index }} group {% if settings.show_desktop == false %}lg:hidden{% endif %} {% if settings.show_mobile == false %}hidden{% endif %} {% if settings.show_mobile == false and settings.show_desktop == true %}hidden lg:grid{% endif %}">
  {%- assign next = index | plus: offset -%}
  {% if blocks[next].settings.subpane_start == true %}
  	<button 
  		class="flex w-full items-center py-1 font-highlight group-active:font-body flex text-xl" 
  		neptune-engage="
  			{
          on:click,
          targets:[{
            selector:'#Pane_{{ blocks[next].id }}',
            classes:{
              add:active
            },
            siblings:{
              classes:{
                remove:active
              }
            }
          },
          {
            selector:'.subpanes',
            classes:{
              add:active
            }
          },
          {
            selector:.panel__item--nav-item.active,
            classes: {
              remove:active
            }
          },
          {
            selector:_parent,
            classes: {
              add:active
            }
          }]
        }
  	 ">
      <span>{{ settings.title }}</span>
  		<span class="ml-auto flex-grow flex justify-end items-center group-hover:opacity-100 group-active:opacity-100 group-focus:opacity-100 lg:opacity-0 lg:ease-in lg:duration-100 lg:transition-opacity">{% render 'icon' icon:'chevron-right' %}</span>
  	</button>

  {% else %}

    <a href="{{ settings.url }}" class="flex w-full py-1 font-highlight group-active:font-body text-xl">{{ settings.title }}</a>

	{% endif %}
</li>
{% if settings.show_divder != blank %}
  {% render 'panel-item-divider' %}
{% endif %}
{% if settings.subpane_end == true %}{% break %}{% endif %}
