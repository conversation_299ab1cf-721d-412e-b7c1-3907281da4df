{% comment %} 


This is generated by webpack so don't edit this unless you want to be sad when your work gets overwritten in production.
You can edit this file in ../../components/** 


{% endcomment %}






{% if display == blank %}{% assign display = value %}{% endif %}

{% if format == 'links' %}
  
	<li class="flex filter-option mb2">
    {% if strip != blank %}
    {% assign value = value | handle | remove: strip %}
    {% endif %}
    {{ display | default: value | link_to_tag: value | replace: 'title=', 'class="ml-2 my-2" title=' }}
  </li>


{% elsif format == 'templated_link' %}

  <li class="flex filter-option mb2">
    <a href="{{value}}" class="ml-2 my-2">{{display}}</a>
  </li>

{% else %}

	<li class="flex filter-option my-2">
	  <div class="checkbox-wrap relative">
      <input name="{{key | handle}}" data-field="{{key}}~{{field}}" type="{{format}}" value="{{value}}" data-constraint="false" id="{{value | append: forloop.index | handle}}" class="hidden" data-strip="{{strip}}">
      <span class="checkbox">
        {% render 'icon' icon: 'check' %}
      </span>
	    <label for="{{value | append: forloop.index | handle}}" class="CanelaText-Light lh-solid f15px-l{% if format == 'pills' %} ba br1 pa2 ma0 mr1 mb1{% endif %}"> {{display | default: value }} {{units}}</label>
	  </div>
	</li>

{% endif %}
