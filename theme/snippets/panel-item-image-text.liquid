<li id="panel-{{ id }}" class="h-inner-panel {{ settings.mobile_width | default: 'col-span-4'}} {{ settings.desktop_width | default: 'lg:col-span-4' }} panel__item panel__item--{{ block.type | handle }} group">
  {% if settings.image != blank %}
  	<a class="panel--image-text flex flex-col justify-center h-full" href="{{ settings.link }}">
  		<div class="panel__image w-full flex-grow relative bg-white overflow-hidden {{ settings.corners }} {{ settings.corners_mobile }}">
  			{% capture image_classes %}object-cover w-full h-full absolute inset-0 {{ settings.image_position }} {{ settings.image_position_mobile }}{% endcapture %}
  			{% render 'lazy-image' with image: settings.image, image_class: image_classes %}
  		</div>
  		{% if settings.title != blank %}<p class="mt-4 mb-2 font-pop uppercase text-sm text-center">{{ settings.title }}</p>{% endif %}
  		{% if settings.text != blank %}<p class="text-center mb-6">{{ settings.text }}</p>{% endif %}
  		{% if settings.button_text != blank %}<button class="button button--light">{{ settings.button_text }}</button>{% endif %}
  	</a>
  {% endif	%}
</li>
{% if settings.show_divder != blank %}
  {% render 'panel-item-divider' %}
{% endif %}
{% if settings.subpane_end == true %}{% break %}{% endif %}