{% raw %}
<article class="cart__item pr-8 pl-4 lg:px-11 py-6 group border-b-2 border-white relative" data-flow-cart-item-number="{{ item.variant_id }}" role="group" aria-label="product" data-cart-item-index="{{ forloop.index0 }}">

  <div class="w-full flex group">

    <div class="lg:w-1/4 w-[30%]">
      <a {{'h'}}ref="{{ item.url }}" class="block">
        {% if item.properties._image %}
          <img {{'s'}}rc="{{ item.properties._image }}" alt="{{ item.product_title }}" class="object-cover w-full">
        {% else %}
          <img {{'s'}}rc="{{ item.image }}" alt="{{ item.product_title }}" class="object-cover w-full">
        {% endif %}
      </a>
    </div>

    <div class="cart-item__details flex flex-col lg:w-3/4 w-[70%] lg:pl-8 pl-4 cart__item_details">

      <a {{'h'}}ref="{{ item.url }}" class="flex flex-col w-full no-underline black link">
        {% assign product_title = item.product_title | split: " - " %}
          
        <h2 class="product-item__title cart-item__title m-0">
          {{ product_title | first }}
        </h2>
        <h3 class="font-caption leading-snug m-0">{{ product_title[1] }}</h3>

        {% for option in item.options_with_values %}
          {% unless option.name == "Title" or option.name == "Color" %}
            <p class="m-0 font-light leading-snug text-xs">{{ option.name }} {{ option.value }}</p>
          {% endunless %}
        {% endfor %}

      </a>

      <div class="mt-2 flex justify-between">

        <div data-cart-item-tools class="">

          <div class="flex items-center w-full mb-2 border-0 gap-x-2.5">
            <button class="h-full text-xs cursor-pointer bg-white flex-fill border border-black p-1 rounded-full" 
              onclick="Neptune.cart.changeItem({{ forloop.index0 }},{{ item.quantity | minus: 1 }})">
              <span aria-hidden="true">
                <svg width="7" height="7" viewBox="0 0 7 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <line y1="-0.5" x2="6.92308" y2="-0.5" transform="matrix(1 0 -0.0165013 0.999864 0.0384521 3.81566)" stroke="black"/>
                </svg>
              </span>
              <span class="sr-only">Reduce item quantity</span>
            </button>
            <input name="item-quantity" class="w-3 h-full text-base font-light text-center border-none flex-fill bg-near-white" type="text" 
              oninput="if(this.value){Neptune.cart.changeItem({{forloop.index0}},this.value)}" value="{{item.quantity}}" aria-label="item quantity">
            </input>
            <button class="h-full text-xs cursor-pointer bg-white flex-fill border border-black p-1 rounded-full {% if item.quantity == item.properties._inventory_quantity %}pointer-events-none opacity-30{% endif %}" 
              onclick="Neptune.cart.changeItem({{forloop.index0}},{{item.quantity | plus: 1}})">
              <span aria-hidden="true">
                <svg width="7" height="7" viewBox="0 0 7 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <line y1="-0.5" x2="6.92308" y2="-0.5" transform="matrix(1 0 -0.0165013 0.999864 0.0384521 3.81566)" stroke="black"/>
                    <line y1="-0.5" x2="6.92308" y2="-0.5" transform="matrix(0 -1 0.999864 -0.0165013 3.90625 6.96155)" stroke="black"/>
                </svg>
              </span>
              <span class="sr-only">Increase item quantity</span>
            </button>
          </div>

          <button class="button button--link absolute top-0 right-0 p-4 lg:px-8 border-0" onclick="Neptune.cart.changeItem({{forloop.index0}},0)">
            {% endraw %}{% render 'icon' icon:'x' width:16 height:16 sr:'Remove product from cart' %}{% raw %}
          </button>

        </div>

        <div class="flex gap-[5px] leading-none font-light flex-col text-right">
          {% if item.properties._compare_at_price > price %}
            <s class="text-[#999]">{{ item.properties._compare_at_price | money: 'local' | remove: removal }}</s>
          {% endif %}
          {% endraw %}
          {% render 'price-removal' %}
          {% raw %}
          <span>{{ price | money: 'local' | remove: removal }}</span>
        </div>

      </div>

      <div class="flex justify-between mt-3.5">
      
        {% endraw %}{% if show_scarcity_msg %}{% raw %}
          
          <span class="text-xs leading-4 w-1/3 scarcity-message">
            {% if item.properties._inventory_quantity < {% endraw %}{{settings.variant_lowstock_threshold}}{% raw %} %}
              <span>
                    {% capture lsm %}{% endraw %}{{settings.variant_lowstock_message}}{% raw %}{% endcapture %}
                    {{ lsm | replace:'**n**', item.properties._inventory_quantity, | replace: '**size**', item.options_with_values[1].value }}
              </span> 
            {% endif %}
          </span>
        
        {% endraw %}{% endif %}{% raw %}
        
        <div class="{% if show_scarcity_msg %}w-2/3{% else %}w-full{% endif %}">
        {% endraw %}
          {% render 'cart-item-badge' %}
        {% raw %}
        </div>
      </div>

    </div>

  </div>

</article>
{% endraw %}
