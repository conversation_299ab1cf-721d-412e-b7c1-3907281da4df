<script>
  {% assign cart_limits = limits | newline_to_br | split: '<br />' %}
  Cart.limits = {};
  {% for limit in cart_limits %}
  Cart.limits[{{ limit | split: ':' | first | strip | json }}] = {{ limit | split: ':' | last }};
  {% endfor %}
  Cart.limit = () => {
    try {
      const cartFooter = document.querySelector('.cart__footer');
      const checkoutButton = cartFooter?.querySelector('[name="checkout"]');

      // If no limits are set or no checkout button, ensure button is visible and exit
      if (!Cart.limits[Shopify.currency.active] || !checkoutButton) {
        if (checkoutButton) checkoutButton.style.display = 'block';
        return;
      }
      const usdLimit = Cart.limits[Shopify.currency.active];
      const cents = usdLimit * 100;
      const rate = Shopify.currency.rate;
      const finalLimit = cents * rate;

      window.cart.limit = finalLimit;

      // Get cart total
      const cartTotal = cart.items_subtotal_price;

      // Always show button by default if cart total is 0 or less than limit
      if (cartTotal === 0 || cartTotal <= finalLimit) {
        checkoutButton.style.display = 'block';
        const messageEl = document.querySelector('.cart-limit-message');
        if (messageEl) messageEl.style.display = 'none';
        return;
      }
      // Only hide button and show message if cart total exceeds limit
      if (cartTotal > finalLimit) {
        checkoutButton.style.display = 'none';

        let messageEl = document.querySelector('.cart-limit-message');
        if (!messageEl) {
          messageEl = document.createElement('div');
          messageEl.className = 'cart-limit-message mt-3 text-center text-red-500';
          const formattedLimit = new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: Shopify.currency.active
          }).format(finalLimit/100);
          messageEl.innerHTML = `Cart value has exceeded the maximum allowed amount of ${formattedLimit}`;
          checkoutButton.parentNode.insertBefore(messageEl, checkoutButton);
        }
        messageEl.style.display = 'block';
      }
    } catch(err) {
      console.error('Cart limit error:', err);
      // If there's an error, ensure checkout button is visible
      const checkoutButton = document.querySelector('[name="checkout"]');
      if (checkoutButton) checkoutButton.style.display = 'block';
    }
  }
  document.addEventListener('templates:render', e=>{
    if (e.detail.info.topic=='cart') {
      Cart.limit()
    }
  })
  // Also check limit when cart updates
  document.addEventListener('cart:updated', () => {
    setTimeout(() => {
      Cart.limit();
    }, 100);
  });
</script>