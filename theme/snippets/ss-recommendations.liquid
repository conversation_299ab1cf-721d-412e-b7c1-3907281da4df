{%- liquid
  assign url = 'https://' | append: settings.ss_site_id | append: '.a.searchspring.io/boost/' | append: settings.ss_site_id | append: '/recommend?tags=' | append: data.settings.tags | append: '&product=' | append: data.settings.product_id | append: '&limits=' | append: section.settings.limit

  if data.settings.collection != blank
    assign url = url | append: '&categories=' | append: data.settings.collection | replace: ' ', '' 
  endif

  if data.settings.customer == true and customer != blank
    assign url = url | append: '&shopper=' | append: customer.id 
  endif

  if data.settings.cart == true and cart.items.size > 0
    assign cart_skus = ''
    for item in cart.items
      assign cart_skus = cart_skus | append: item.sku | append: ','
    endfor
    assign cart_skus = cart_skus | remove_last: ','
    assign url = url | append: '&cart=' | append: cart_skus 
  endif
-%}

<script>
  window['{{ source }}'] = {
    products: [],
    searchspring_tags: '{{ data.settings.tags }}'
  };

  window.addEventListener('DOMContentLoaded', () => {
    fetch('{{ url }}',{
      method: 'GET', 
      headers: {
        accept: 'application/json'
      }
    })
    .then(response=>response.json())
    .then(data => {
      console.log('\n\n SS Recs Data', data, '\n\n')
      
      let ssData = _n.map(data, {
        products:{
          from:'results',
          each:{
            id:'mappings.core.uid',
            title:'mappings.core.name',
            handle:'mappings.core.url|.split(`products/`).reverse()[0]',
            price:'mappings.core.price|*100',
            compare_at_price:'mappings.core.msrp|*100',
            featured_image:'mappings.core.imageUrl',
            alt_image:'attributes.ss_image_alt',
            thumbnail_image:'mappings.core.thumbnailImageUrl',
            alt_images:'attributes.gender_alternative',
            hover_image:'attributes.ss_image_alt',
            hover_video:'attributes.mfield_product_gallery_video',
            images:'attributes.images',
            variants:'attributes.variants',
            siblings:'attributes.mfield_family_siblings',
            metafields: 'attributes.metafields'
          }
        }
      })

      ssData = ssData[0].products.map(r => {
        try {
          r.siblings = JSON.parse(r.siblings.replace(/&quot;/g, `"`)).products;
        } catch (err) {}

        try {
          r.variants = JSON.parse(r.variants.replace(/&quot;/g, `"`));
        } catch (err) {}

        try {
          r.metafields = JSON.parse(r.metafields.replace(/&quot;/g, `"`));
        } catch (err) {}

        try {
          r.alt_images = JSON.parse(r.metafields.find(obj => obj['key'] === 'gender_alternative').value.replace(/&quot;/g, `"`))
        } catch (err) {}

        try {
          r.images = r.images.split(`|`)
          r.hover_image = r.attributes.images[1]
        } catch (err) {}

        return r
      })
      
      _n.hooks.process('Products:enhance', ssData)
        .then(processedData => {

          window['{{ source }}'].products = processedData

          Neptune.liquid.load('{{ topic }}', window['{{ source }}'])
        })
    })

  })
</script>