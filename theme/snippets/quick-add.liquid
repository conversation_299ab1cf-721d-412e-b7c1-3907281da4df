<div data-modal="quick-add" class="modal modal-bottom fixed bottom-0 bg-white z-40 w-screen duration-300 pb-16 user-select-none" tabindex="0" neptune-engage="{
  on:keyup,
  which: 27,
  targets:[{
    selector:html,
    attributes:[{
      att:data-active-modal,
      set:_remove
    }]
  },
  { 
    selector:'[data-return-focus]',
    attributes:[{
      att:data-return-focus,
      set:_remove
    }],
    focus:true
  }]
}">
    <div class="p-3 flex items-center justify-center" modal-drag-handle={threshold:30} style="cursor:grab">
      <div class="bg-gray-100 rounded-full w-20 h-1"></div>
    </div>
    <div class="max-h-40v overflow-y-scroll">
      <div neptune-liquid={topic:QuickAdd} class="flex items-center flex-col justify-center">
        <template>
          <div class="py-3 sticky top-0 z-10 w-full text-center" style="background: rgb(255,255,255);
background: linear-gradient(0deg, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 15%);">{{ 'products.product.quick_add.select_size' | t }}</div>
          {% raw %}
          {% for variant in product.variants %}
            {% if variant.available %}
              <button class="p-3 w-full" onclick="Neptune.cart.add({{ variant.id }}); modal.close(); return false;">{{ variant.option2 }}</button>
            {% else %}
              <button class="p-3 w-full" disabled>{{ variant.option2 }}</button>
            {% endif %}
          {% endfor %}
          {% endraw %}
        </template>
      </div>
    </div>
    <div class="absolute bottom-16 h-6 w-full left-0" style="background: rgb(255,255,255);
background: linear-gradient(0deg, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);"></div>
</div>