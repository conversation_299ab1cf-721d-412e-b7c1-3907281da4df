{% raw %}

<article class="bg-white cart-offers cart-offers--{{ offerIndex }} cart-offers__carousel accordion group active">

	{% if offer.prompt != blank %}
    <button 
      class="cart-offers__prompt accordion-title "
      neptune-engage='targets:[
        { 
          selector:_parent,
          classes:toggle:active,
          siblings:classes:remove:active
        },
        {
          selector:"_grandparent button",
          attributes:[{att:"aria-disabled" set:"false"} {att:"aria-expanded" set:"false"}]
        },
        { 
          selector:"_grandparent .active button",
          attributes:[{att:"aria-disabled" set:"true"} {att:"aria-expanded" set:"true"}]
        },
        {
          selector:"_grandparent .accordion-panel",
          attributes:{att:"aria-hidden" set:"true"}
        },
        { 
          selector:"_grandparent .active .accordion-panel",
          attributes:{att:"aria-hidden" set:"false"}
        }
      ]'
    >
    <span>
      {{ offer.prompt | newline_to_br }}
    </span>
    {% endraw %}
    <span class="group-active:block hidden">
      {% render 'icon' icon: 'chevron-down' %}
    </span>
    <span class="block group-active:hidden">
      {% render 'icon' icon: 'chevron-up' %}
    </span>
    {% raw %}
  </button>
	{% endif %}

  <div class="accordion-panel hidden group-active:block">
    <div neptune-swiper="
        {
          centeredSlides: false,
          slidesPerView:1.5, 
          spaceBetween:5,
          navigation: {
            prevEl: '.cart-offers--{{ offerIndex }} .swiper-button-prev-unique',
            nextEl: '.cart-offers--{{ offerIndex }} .swiper-button-next-unique',
            disabledClass: 'swiper-button-disabled'
          }
        }" class="swiper-container" data-offer-index="{{ offerIndex }}">
      <div class="swiper-wrapper">

        {% for product in offer.products %}

        {% assign productIndex = forloop.index0 %}

        {% assign line_item_handles = items | map: 'handle' %}

        {% if product.price <= 0 %}{% continue %}{% endif %}
        {% if line_item_handles contains product.handle %}{% continue %}{% endif %}

        <div for="{{ offer.title | default: offer.prompt | handle }}-{{ product.handle }}"
          class="cart-offers__{{ offer.product_selection | default: 'many' }} cart-offers__slide swiper-slide group active:border-pop"
        >

          {% endraw %}{% render 'offer-item' %}{% raw %}

        </div>

        {% endfor %}
      </div>
    </div>

    {% if offer.products.size > 1 %}
      <div class="cart-offers__prev-next-container z-50">
        <button class="swiper-prev swiper-button-prev-unique px-4 pt-0 border-0 bg-transparent cursor-pointer"><svg class="h-full w-full" viewBox="0 0 100 100"><path d="M 10,50 L 60,100 L 65,95 L 20,50  L 65,5 L 60,0 Z" class="arrow"></path></svg>
          <span class="screenreader sr-only">previous slide</span>
        </button>
        <button class="swiper-next swiper-button-next-unique px-4 pt-0 border-0 bg-transparent cursor-pointer"><svg class="h-full w-full" viewBox="0 0 100 100"><path d="M 10,50 L 60,100 L 65,95 L 20,50  L 65,5 L 60,0 Z" class="arrow" transform="translate(100, 100) rotate(180) "></path></svg>
          <span class="screenreader sr-only">next slide</span>
        </button>
      </div>
    {% endif %}
	</div>

</article>

{% endraw %}
