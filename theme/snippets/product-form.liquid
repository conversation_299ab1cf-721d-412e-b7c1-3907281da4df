{% comment %}PRODUCT FORM{% endcomment %}
{% assign form_classes = 'w-full px-5 lg:px-10 product-essentials--form' %}
{% if product.available == false %}
  {% assign form_classes = form_classes | append: ' unavailable' %}
{% endif %}

{% form 'product', product, data-product-form: '', data-product-handle: product.handle, data-enable-history-state: 'true', class: form_classes  %}

  <div class="flex flex-wrap items-center justify-between">

    <div class="product__options w-full">

      <noscript>
      
        <select name="id">
          {% for variant in product.variants %}
            <option
              {% if variant == current_variant %}selected="selected"{% endif %}
              {% unless variant.available %}disabled="disabled"{% endunless %}
              value="{{ variant.id }}">
                {{ variant.title }} {{ variant.price | money }}
            </option>
          {% endfor %}
        </select>
        
        <button type="submit" class="button">{{ 'products.product.add_to_cart' | t }}</button>

      </noscript>

      <div neptune-liquid="{topic:'ProductOptions', append:size_guide}">
        <template>
        {% raw %}

          {% for option in product.options_with_values %}

            {% if option.name contains 'Size' %}
              <div class="flex align-center justify-between gap-4">
                {% endraw %}
                {% render 'SizzleFit' %}

                {% raw %}
                {% if size_guide.charts != blank %}
                {% endraw %}
                  {% render 'size-guide-trigger' %}
                {% raw %}
                {% endif %}
              </div>
            {% endif %}

            {% assign optionIndex = forloop.index0 %}
            {% assign position = forloop.index %}

            <fieldset class="option option--{{ option.name | handle }} {% if option.values.size == 1 %}hidden{% endif %} p-0 mb-3 mr-auto">
              
              <div>
                <legend class="sr-only">{{option.name}}</legend>
              </div>

              <div class="flex flex-wrap relative option-selection font-highlight font-light leading-relaxed" {% endraw %}{% if section.settings.variant_sort_order != blank %}neptune-sort='{on:innerText.trim(),as:alpha,reverse:false,order:{{section.settings.variant_sort_order|split:","|json}} }'{% endif %}{% raw %}>
                
                {% assign sortedvalues = option.values %}
                {% 
                  if option.name contains 'Length' 
                  or option.name contains 'Width' 
                  or option.name contains 'Height'
                  or option.name contains 'Depth' 
                  or option.name contains 'Thickness' 
                  or option.name contains 'Weight' 
                %}
                  {% assign sortedvalues = option.values | sort %}
                {% endif %}

                {% for value in sortedvalues %}

                  {% assign inventory_quantity = 0 %}
                  {% assign variantId = '' %}
                  {% assign available = false %}
                  {% assign optionVariant = false %}
                  
                  {% if product.options_with_values.size == 3 and optionIndex == 2%}
                    {% for v in product.variants %}
                      {% unless v.bogus %}
                      {% if v.option1 == variant.option1 and v.option2 == variant.option2 and v.option3 == value %}
                        {% assign optionVariant = v %}
                        {% assign inventory_quantity = inventory_quantity | plus: v.inventory_quantity %}
                        {% assign variantId = v.id %}
                        {% if v.available %}{% assign available = true %}{% endif %}
                      {% endif %}
                      {% endunless %}
                    {% endfor %}
                  {% elsif product.options_with_values.size == 2 and optionIndex == 1%}
                    {% for v in product.variants %}
                      {% unless v.bogus %}
                        {% if v.option1 == variant.option1 and v.option2 == value %}
                          {% assign optionVariant = v %}
                        {% assign inventory_quantity = inventory_quantity | plus: v.inventory_quantity %}
                        {% assign variantId = v.id %}
                        {% if v.available %}{% assign available = true %}{% endif %}
                        {% endif %}
                      {% endunless %}
                    {% endfor %}
                  {% else %}
                    {% for v in product.variants %}
                      {% unless v.bogus %}
                        {% if v.option1 == value %}
                          {% assign optionVariant = v %}
                        {% assign inventory_quantity = inventory_quantity | plus: v.inventory_quantity %}
                        {% assign variantId = v.id %}
                        {% if v.available %}{% assign available = true %}{% endif %}
                        {% endif %}
                      {% endunless %}
                    {% endfor %}
                  {% endif %}

                  {% assign checked = "" %}

                  {% if option.name contains 'Size' %}
                    {% if product.current_size == value %}{% assign checked = "checked" %}{% endif %}
                  {% else %}
                    {% if variant.options[optionIndex] == value %}{% assign checked = "checked" %}{% endif %}
                  {% endif %}

                  <span class="flex mr-1 mb-1">
    

                    <input 
                    type="radio" 
                    id="option-{{ position }}-{{ value | handle }}" 
                    name="options[{{ option.name }}]" 
                    value="{{ value }}" 
                    data-variant="{{ variantId }}" 
                    class="sr-only" 
                    {{checked}}
                    neptune-option="{{ option.name }}"
                    data-inventory="{{inventory_quantity}}"
                    data-availability="{{available}}"
                    onchange="updateCurrentSize('{{ value }}'); BackInStock.show(this); Neptune.product.option(this)">

                    {% assign option_image = false%}
                    {% if option.name contains 'color' or option.name contains 'Color' %}
                      {% for v in product.variants %}
                        {% if v.option1 == value %}
                          {% assign option_image = v.images[0].src %}
                          {% break %}
                        {% endif %}
                      {% endfor %}
                    {% endif %}

                    <label
                      for="option-{{ position }}-{{ value | handle }}" 
                      class="active:border active:bg-black active:text-white option__item--label"
                      style="{% if option_image %}background: url('{{ option_image }}') #FFFFFF center/auto 84% no-repeat;{% endif %};"
                    > 
                      {% if option.name contains 'color' or option.name contains 'colour' or option.name contains 'Colour' or option.name contains 'Color' %} 
                        {% unless option_image %}
                          <i class="db w2 h2 pa1 ba br-100 overflow-hidden">
                            <img {{'s'}}rc="" alt="{{ value }}" onerror="this.parentNode.remove()" class="db w-full h-100 br-100 object-cover">
                          </i>
                        {% endunless %}

                      {% else %}
                        <span>{{value}}</span>

                      {% endif %}

                    </label>

                  </span>
                {% endfor %}

              </div> 
              {% endraw %}
              {% for block in section.blocks %}
                {% if block.type == 'option_description' %}
                  <div class="f6 mid-gray mt1">
                    {%- raw -%}
                    {%- capture block_option_method -%}{%- endraw -%}{{ block.settings.option_select_method }}{%- raw -%}{%- endcapture -%}
                    {%- capture block_option_value -%}{%- endraw -%}{{ block.settings.option_select_value }}{%- raw -%}{%- endcapture -%}
                    {%- capture block_option_description -%}{%- endraw -%}{{- block.settings.option_description -}}{%- raw -%}{%- endcapture -%}
                    {%- if block_option_method == 'name' -%}
                      {%- if block_option_value == option.name -%}
                        {{- block_option_description -}}
                      {%- endif -%}
                    {%- else -%}
                      {%- assign productTag = false -%}
                      {%- for tag in product.tags -%}
                        {%- if tag contains block_option_value -%}
                          {%- assign productTag = true -%}
                        {%- endif -%}
                      {%- endfor -%}
                      {%- if productTag == true -%}
                        {{- block_option_description -}}
                      {%- endif -%}
                    {%- endif -%}
                    {%- endraw -%}
                  </div>
                {% endif %}
              {% endfor %}
              {% raw %}
            </fieldset> 

          {% endfor %}

          {% if product.variants.size == 1 %}  

            <input type="hidden" id="inputVariantAvailability" data-inventory="{{ variant.inventory_quantity }}" data-variant="{{ variant.id }}">
            <script>BackInStock.show(_n.qs('#inputVariantAvailability'))</script>

          {%endif%}

        {% endraw %}
        </template>
      </div>
    </div>
  </div>

  <div class="" neptune-liquid="{topic:'ProductOptions',source:'eval:_n.parents(_self,`[neptune-product]`)'}">
    <template>
    {% raw %}
      {% if variant.available  %}
        {% if product.current_size != "" %}

          {% if variant.inventory_quantity < {% endraw %}{{settings.variant_lowstock_threshold}}{% raw %} %}
            <p class="scarcity-message text-[11px] uppercase mb-[11px] relative pl-4 mt-[-0.5rem] tracking-wider">
              {% capture lsm %}{% endraw %}{{ settings.variant_lowstock_message }}{% raw %}{% endcapture %}
              {{ lsm | replace:'**n**', variant.inventory_quantity, | replace: '**size**', variant.options[1] }}
            </p>
          {% endif %}

          <button id="AddToCart" class="button button--primary w-full" onclick="
            const el = this.closest('[neptune-product]');
            el.properties = el.properties || {};
            el.properties._inventory_quantity = {{ variant.inventory_quantity }};
            
            try {
              const mensImages = el.product.images.filter(i=>i.alt && i.alt.match(/\b(MEN)\b/i));
              const womensImages = el.product.images.filter(i=>i.alt && (i.alt.match(/\b(WOMAN|WOMEN)\b/i)));
              
              let selectedImage;
              if (window.location.pathname.includes('/mens')) {
                selectedImage = mensImages.length > 0 ? mensImages[0].src : el.product.images[0].src;
              } else {
                selectedImage = womensImages.length > 0 ? womensImages[0].src : el.product.images[0].src;
              }
              
              el.properties._image = selectedImage;
            } catch(e) {
              console.error('Error in image selection:', e);
              // Fallback to first image if anything fails
              el.properties._image = el.product.images[0].src;
            }
            
            Neptune.product.addToCart(this);
            return false">
            <span id="AddToCartText" class="block my-1">
              {% endraw %}{{ 'products.product.add_to_cart' | t }}{% raw %}
            </span>
          </button>
        {% else %}
          {% if product.variants.size > 1 %}
            <button id="AddToCart" class="button button--light w-full pointer-events-none bg-[#8E8F97] text-white text-xs border-[#8E8F97]" disabled>
              <span id="AddToCartText" class="block my-1">
                {% endraw %}{{ 'products.product.select_size' | t }}{% raw %}
              </span>
            </button>
          {% else %}

            {% if variant.inventory_quantity < {% endraw %}{{settings.variant_lowstock_threshold}}{% raw %} %}
              <p class="scarcity-message text-[11px] uppercase mb-[11px] relative pl-4 tracking-wider mt-[-0.3rem]">
                {% capture lsm %}{% endraw %}{{ settings.variant_lowstock_message }}{% raw %}{% endcapture %}
                {{ lsm | replace:'**n**', variant.inventory_quantity, | replace: '**size**', variant.options[1] }}
              </p>
            {% endif %}

            <button id="AddToCart" class="button button--primary w-full" onclick="
            const el = this.closest('[neptune-product]');
            el.properties = el.properties || {};
            el.properties._inventory_quantity = {{ variant.inventory_quantity }};
            
            try {
              const mensImages = el.product.images.filter(i=>i.alt && i.alt.match(/\b(MEN)\b/i));
              const womensImages = el.product.images.filter(i=>i.alt && (i.alt.match(/\b(WOMAN|WOMEN)\b/i)));
              
              let selectedImage;
              if (window.location.pathname.includes('/mens')) {
                selectedImage = mensImages.length > 0 ? mensImages[0].src : el.product.images[0].src;
              } else {
                selectedImage = womensImages.length > 0 ? womensImages[0].src : el.product.images[0].src;
              }
              
              el.properties._image = selectedImage;
            } catch(e) {
              console.error('Error in image selection:', e);
              // Fallback to first image if anything fails
              el.properties._image = el.product.images[0].src;
            }
            
            Neptune.product.addToCart(this);
            return false">
              <span id="AddToCartText" class="block my-1">
                {% endraw %}{{ 'products.product.add_to_cart' | t }}{% raw %}
              </span>
            </button>
          {% endif %}
        {% endif %}
        <script defer>
          setTimeout(function(){
            document.querySelector('#BIS') && document.querySelector('#BIS').classList.add('hidden');
          },50)
        </script>
      {% else %}
        <button class="button bg-transparent border-none text-black w-full no-underline p-0 text-left opacity-100 leading-normal pointer-events-none text-base normal-case font-light">
          <span>{% endraw %}{{ 'products.product.sold_out' | t }}{% raw %}</span>
        </button>
      {% endif %}
    {% endraw %}
    </template>
  </div>

  {% unless product.tags contains 'exclude_BIS' %}
    {% render 'back-in-stock' product:product %}
  {% endunless %}

  <div neptune-liquid="{topic:cart, append:Neptune}">
    <template>
    {% raw %}

      {% if error != blank %}
        <div id="quantityMessages" class="pv__qty-messages messages">
          <div class="message message--error">
            <p class="text-red-main m-0 pt-2">
              {% if error.description contains 'already sold out' %}
                Sorry, {{ Neptune.products[0].el.variant.inventory_quantity }} is the max quantity available in this size.
              {% else %}
                {{ error.description }}
              {% endif %}
            </p>
          </div>
        </div>
      {% endif %}

    {% endraw %}
    </template>
  </div>

  <div neptune-liquid="{topic:'Labels'}">
    <template>
    {% raw %}

      {% for label in productLabels %}
        {% capture tags %}{% endraw %}{{ product.tags }}{% raw %}{% endcapture %}
        {% if tags contains label.tag %}
          <p class="MonumentGrotesk-Bold mb0 mt3 lh-copy" style="background-color:{{ label.label_bg_color }}; color:{{ label.label_text_color }};">
            {{ label.product_label }}
          </p>
        {% endif %}  
      {% endfor %}

    {% endraw %}
    </template>
  </div>
{% endform %}

<script>

const updateCurrentSize = function (value) {
  const handlesArray = Array.from(document.querySelectorAll('[data-product-handle]'))
    .map(element => element.getAttribute('data-product-handle'));

  if (Neptune.products?.length) {
    Neptune.products[Neptune.products.length - 1].product.current_size = value;
  }
  sessionStorage.setItem('currentSize', value);
  sessionStorage.setItem('productHandles', JSON.stringify(handlesArray));
};

const initializeProductSize = function () {
  const storedSize = sessionStorage.getItem('currentSize');
  const storedHandles = JSON.parse(sessionStorage.getItem('productHandles'));

  if (storedHandles && storedSize) {
    const domHandles = Array.from(document.querySelectorAll('[data-product-handle]'))
      .map(element => element.getAttribute('data-product-handle'));

    const matchingHandles = storedHandles.filter(handle => domHandles.includes(handle));

    if (matchingHandles.length > 0) {
      document.querySelectorAll('.option--size input[type="radio"][value="' + storedSize + '"]').forEach(input => {
        input.checked = true;
        setTimeout(() => input.dispatchEvent(new Event('change', { bubbles: true, cancelable: true })), 10);
      });
    } else {
      sessionStorage.removeItem('productHandles');
      sessionStorage.removeItem('currentSize');
    }
  }
};

document.addEventListener("product:init", () => {
  setTimeout(initializeProductSize, 100);
});

window.addEventListener('pageshow', function () {
  const optionSize = document.querySelectorAll('.option--size [neptune-option="Size"]:checked');
  optionSize.forEach(size => {
    const sizeValue = String(size.value);
    console.log('hasExecuted');
    updateCurrentSize(sizeValue);
    initializeProductSize();
  });
});

</script>

