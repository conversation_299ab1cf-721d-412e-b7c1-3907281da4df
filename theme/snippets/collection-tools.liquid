<div class="collection-tools lg:px-4 py-0">
  <ul class="list block flex flex-row-reverse pr-0 pl-0 py-0 my-0 lg:ml-auto black tracked-slight lg:w-40 w-full relative z-40 hidden">
    <li class="w-1/2 lg:w-auto text-center lg:text-right bl bb border-gray-300 lg:border-none pointer dim-l lg:ml-8 radioactive toggleable active-black active-underline tracked-slight us-none">
      <label for="collectionSort" class="m-0 block lg:inline-block lg:p-0 p-4 pointer activate input-hybrid-label uppercase" data-target="_parent">
        {{'collections.sorting.title' | t}} 
      </label>
      
    </li>
    <li class="w-1/2 lg:w-auto text-center lg:text-right dim-l lg:ml-8 bb border-gray-300 lg:border-none radioactive toggleable active-black active-underline tracked-slight us-none">
      <label for="collectionFilters" class="m-0 block lg:inline-block lg:p-0 p-4 pointer activate input-hybrid-label uppercase" data-target="_parent">
        {{'collections.filters.title' | t}}
      </label>
    </li>
  </ul>

  <div>
    <input type="radio" name="collection-tools" id="collectionSort" class="hidden">

    <div class="active-show absolute checkbox-controlled-display h-100-header text-left right-0 bg-near-white border-t relative border-gray-300 lg:border-l lg:p-16 p-4 w-full list uppercase z-30 collection-sort">
      <button type="button" class="drawer__close bn bg-near-white absolute top-0 left-0 p-4 hidden lg:block">
        <span class="screenreader">Close</span>
        <i class="icon icon--close"></i>
      </button>
      <ul class="list m-0 p-0">
        <li><a class="block p-4 lg:px-0 lg:py-3">Newest</a></li>
        <li><a class="block p-4 lg:px-0 lg:py-3">Price: high to low</a></li>
        <li><a class="block p-4 lg:px-0 lg:py-3">Price: low to high</a></li>
      </ul>
      <div class="fixed lg:absolute bottom-0 left-0 flex w-full lg:flex-col flex-row flex-wrap lg:p-5 pt-5">
         <button type="submit" class="lg:w-full w-1/2 p-4 ba b--black black uppercase fw6 tracked-slight lg:mb-4 bg-near-white"><span class="block">APPLY</span></button>
         <button type="submit" class="lg:w-full w-1/2 p-4 ba b--black black uppercase fw6 tracked-slight bg-near-white"><span class="block">Clear All</span></button>
      </div>
    </div>
  </div>

  <input type="radio" name="collection-tools" id="collectionFilters" class="hidden">

  <div class="collection-filters absolute h-100-header overflow-y-scroll bg-near-white border-t lg:border-l border-gray-300 checkbox-controlled-display animate modal-right right-0 lg:mt-0 text-left w-full z-30">
    <button type="button" class="drawer__close bn bg-near-white absolute top-0 left-0 p-4 hidden lg:block">
      <span class="screenreader">Close</span>
      <i class="icon icon--close"></i>
    </button>
    <div class="w-full lg:p-16 p-8">
      <div class="w-full">

        <div class="filter-set">
          <h5 class="m-0 lg:px-0 p-4 lg:py-3 uppercase">
            <label for="filter-set-1" class="block m-0 p-0 pointer input-hybrid-label us-none uppercase filter-set-label" 
            neptune-engage='{ 
              on:click,
              targets: [
                { 
                  selector: "_grandparent",
                  classes:toggle:active,
                  siblings:classes:remove:active
                }
              ]
            }'
            >Waist Size 
            <span class="fr border block plus">+</span><span class="fr border block minus">-</span></label>
          </h5>
          <input type="radio" name="filtersets" id="filter-set-1" class="dn">
          <ul class="checkbox-controlled-height list m-0 lg:p-0 px-4 lg:px-0 fw3 flex flex-wrap flex-row animate">

            <li class="m-0 px-0 pointer activate w-1/2 lg:w-1/3">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">23</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2 lg:w-1/3">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">24</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2 lg:w-1/3">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">25</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2 lg:w-1/3">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">26</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2 lg:w-1/3">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">27</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2 lg:w-1/3">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">28</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2 lg:w-1/3">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">29</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2 lg:w-1/3">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">30</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2 lg:w-1/3">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">31</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2 lg:w-1/3">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">32</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>

          </ul>
        </div>

        <div class="filter-set">
          <h5 class="m-0 lg:px-0 p-4 lg:py-4 uppercase">
            <label for="filter-set-2" class="block m-0 pa0 pointer input-hybrid-label us-none uppercase filter-set-label" 
            neptune-engage='{ 
              on:click,
              targets: [
                { 
                  selector: "_grandparent",
                  classes:toggle:active,
                  siblings:classes:remove:active
                }
              ]
            }'
            >Size 
            <span class="fr border block plus">+</span><span class="fr border block minus">-</span></label>
          </h5>
          <input type="radio" name="filtersets" id="filter-set-2" class="dn">
          <ul class="checkbox-controlled-height list m-0 pa0-l px-4 lg:px-0 fw3 flex flex-wrap flex-row animate">

            <li class="m-0 px-0 pointer activate w-1/2 lg:w-1/3">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">XS</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2 lg:w-1/3">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">S</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2 lg:w-1/3">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">M</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2 lg:w-1/3">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">L</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2 lg:w-1/3">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">XL</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>

          </ul>
        </div>

        <div class="filter-set">
          <h5 class="m-0 lg:px-0 p-4 lg:py-4 uppercase">
            <label for="filter-set-3" class="block m-0 pa0 pointer input-hybrid-label us-none uppercase filter-set-label" 
            neptune-engage='{ 
              on:click,
              targets: [
                { 
                  selector: "_grandparent",
                  classes:toggle:active,
                  siblings:classes:remove:active
                }
              ]
            }'
            >Rise 
            <span class="fr border block plus">+</span><span class="fr border block minus">-</span></label>
          </h5>
          <input type="radio" name="filtersets" id="filter-set-3" class="dn">
          <ul class="checkbox-controlled-height list m-0 pa0-l px-4 lg:px-0 fw3 flex flex-wrap flex-row animate">

            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">Mid Rise</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">High Rise</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">Super High Rise</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>

          </ul>
        </div>

        <div class="filter-set">
          <h5 class="m-0 lg:px-0 p-4 lg:py-4 uppercase">
            <label for="filter-set-{{i}}" class="block m-0 pa0 pointer input-hybrid-label us-none uppercase filter-set-label" 
            neptune-engage='{ 
              on:click,
              targets: [
                { 
                  selector: "_grandparent",
                  classes:toggle:active,
                  siblings:classes:remove:active
                }
              ]
            }'
            >Inseam 
            <span class="fr border block plus">+</span><span class="fr border block minus">-</span></label>
          </h5>
          <input type="radio" name="filtersets" id="filter-set-{{i}}" class="dn">
          <ul class="checkbox-controlled-height list m-0 pa0-l px-4 lg:px-0 fw3 flex flex-wrap flex-row animate">

            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">Cropped</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">Ankle</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">Regular</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>

          </ul>
        </div>

        <div class="filter-set">
          <h5 class="m-0 lg:px-0 p-4 lg:py-3 uppercase">
            <label for="filter-set-4" class="block m-0 pa0 pointer input-hybrid-label us-none uppercase filter-set-label" 
            neptune-engage='{ 
              on:click,
              targets: [
                { 
                  selector: "_grandparent",
                  classes:toggle:active,
                  siblings:classes:remove:active
                }
              ]
            }'
            >Hem 
            <span class="fr border block plus">+</span><span class="fr border block minus">-</span></label>
          </h5>
          <input type="radio" name="filtersets" id="filter-set-4" class="dn">
          <ul class="checkbox-controlled-height list m-0 lg:p-0 px-4 lg:px-0 fw3 flex flex-wrap flex-row animate">

            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">Chewed</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">Clean</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">Cuffed</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">Frayed</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">Step Fray</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>

          </ul>
        </div>

        <div class="filter-set">
          <h5 class="m-0 lg:px-0 p-4 lg:py-3 uppercase">
            <label for="filter-set-5" class="block m-0 pa0 pointer input-hybrid-label us-none uppercase filter-set-label" 
            neptune-engage='{ 
              on:click,
              targets: [
                { 
                  selector: "_grandparent",
                  classes:toggle:active,
                  siblings:classes:remove:active
                }
              ]
            }'
            >Color 
            <span class="fr border block plus">+</span><span class="fr border block minus">-</span></label>
          </h5>
          <input type="radio" name="filtersets" id="filter-set-5" class="dn">
          <ul class="checkbox-controlled-height list m-0 lg:p-0 px-4 lg:px-0 fw3 flex flex-wrap flex-row animate">

            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="br-100 inline-block v-mid  w1-5 h1-5 ba b--gray bg-light-blue lh-solid border-box"></span>
                <span class="inline-block v-mid active-underline active-black lh-title">Light Blue</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>

            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="br-100 inline-block v-mid  w1-5 h1-5 ba b--gray bg-blue lh-solid border-box"></span>
                <span class="inline-block v-mid active-underline active-black lh-title">Medium Blue</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>

            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="br-100 inline-block v-mid  w1-5 h1-5 ba b--gray bg-navy lh-solid border-box"></span>
                <span class="inline-block v-mid active-underline active-black lh-title">Dark Blue</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>

            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="br-100 inline-block v-mid  w1-5 h1-5 ba b--gray bg-black lh-solid border-box"></span>
                <span class="inline-block v-mid active-underline active-black lh-title">Black</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>

            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="br-100 inline-block v-mid  w1-5 h1-5 ba b--gray bg-near-white lh-solid border-box"></span>
                <span class="inline-block v-mid active-underline active-black lh-title">White</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>

            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="br-100 inline-block v-mid  w1-5 h1-5 ba b--gray bg-pink lh-solid border-box"></span>
                <span class="inline-block v-mid active-underline active-black lh-title">Pink</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>

            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="br-100 inline-block v-mid  w1-5 h1-5 ba b--gray bg-green lh-solid border-box"></span>
                <span class="inline-block v-mid active-underline active-black lh-title">Green</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            

          </ul>
        </div>

        <div class="filter-set">
          <h5 class="m-0 lg:px-0 p-4 lg:py-3 uppercase">
            <label for="filter-set-6" class="block m-0 pa0 pointer input-hybrid-label us-none uppercase filter-set-label" 
            neptune-engage='{ 
              on:click,
              targets: [
                { 
                  selector: "_grandparent",
                  classes:toggle:active,
                  siblings:classes:remove:active
                }
              ]
            }'
            >Fit 
            <span class="fr border block plus">+</span><span class="fr border block minus">-</span></label>
          </h5>
          <input type="radio" name="filtersets" id="filter-set-6" class="dn">
          <ul class="checkbox-controlled-height list m-0 lg:p-0 px-4 lg:px-0 fw3 flex flex-wrap flex-row animate">

            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">Bootcut</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">Flare</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">Skinny</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">Straight</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">Tomboy</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>
            <li class="m-0 px-0 pointer activate w-1/2">
              <a class="inline-block v-mid pointer-none py-2 dim">
                <span class="inline-block v-mid active-underline active-black lh-title">Wide Leg</span>
                <span class="inline-block h1-5 v-mid">&nbsp;</span>
              </a>
            </li>

          </ul>
        </div>

      </div>

      <div class="fixed lg:absolute bottom-0 left-0 flex w-full lg:flex-col flex-row flex-wrap lg:p-5 pt-16">
         <button type="submit" class="lg:w-full w-1/2 p-4 ba b--black black uppercase fw6 tracked-slight lg:mb-4 bg-near-white"><span class="block">APPLY</span></button>
         <button type="submit" class="lg:w-full w-1/2 p-4 ba b--black black uppercase fw6 tracked-slight bg-near-white"><span class="block">Clear All</span></button>
      </div>
    </div>

  </div>

</div>