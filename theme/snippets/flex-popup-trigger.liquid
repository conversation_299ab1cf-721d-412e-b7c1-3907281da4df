<div data-trigger-container
  class="flex-popup-trigger pointer-events-auto fixed z-40 {% render '@classes' _settings:_settings prefix:'trigger_class' %}"
  style="
    {% render '@styles' _settings:_settings prefix:'trigger_style' %}
     width: calc(var(--width) * 1px);
  "
>
  {% if _settings.interaction_type == 'url' %}
    <a href="{{ _settings.destination_url }}" class="block w-full h-full">
  {% else %}
    <button
      type="button"
      class="w-full bg-transparent border-0 p-0 cursor-pointer"
      onclick="window.modal.open('{{ section_id | append: '-modal' }}', {returnFocus: this, aria: true})"
    >
  {% endif %}

    {% if _settings.trigger_image %}
      <img
        src="{{ _settings.trigger_image | img_url: 'master' }}"
        alt="{{ _settings.trigger_image.alt | escape }}"
        class="w-full h-auto"
        width="{{ _settings.trigger_width }}"
        height="{{ _settings.trigger_width | divided_by: _settings.trigger_image.aspect_ratio }}"
        loading="lazy"
      >
    {% endif %}
    {% if _settings.trigger_text != blank %}
      <span class="block text-xs font-light leading-sloose underline">{{ _settings.trigger_text }}</span>
    {% endif %}

  {% if _settings.interaction_type == 'url' %}
    </a>
  {% else %}
    </button>
  {% endif %}

  <button
    type="button"
    class="color-current bg-transparent absolute top-2 right-2 p-0.5 z-10"
    onclick="event.preventDefault(); event.stopPropagation(); this.closest('.flex-popup-container').remove();"
  >
    {% render 'icon' icon:'x' width:15 height:15 %}
  </button>
</div>