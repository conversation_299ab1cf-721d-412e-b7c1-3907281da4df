{% if background == blank %}
  {% assign background = 'bg-gray-100'%}
{% endif %}
{% raw %}
{% capture bg-color %}{% endraw %}{{background}}{% raw %}{% endcapture %}
{% assign productTitle = product.title | remove: product.type | split: ' | ' %}
<article
  class="product-item__wrapper flex flex-col font-body relative text-center {% endraw %}{{classes}}{% raw %} {% if product.available == false %}opacity-50{% endif %} {% endraw %}{{ classes }}{% raw %}"
  data-product-item
  data-position="{{ product.page }},{{forloop.index0}}"
  data-part-number="{{ product.title }}"
  onclick="window.history.replaceState(
    {
      page:{{ product.page }},
      position:{{ forloop.index0 }} 
    },
    document.title
    )"
  >

  <div class="relative flex flex-col h-full product-item" data-product-item-content>
  
    <div 
      class="flex flex-col h-full overflow-hidden product-item__outer group" 
      tabindex="0" 
      hover
      neptune-engage='{
        on:keyup
        targets: [
          {
            selector:.product-item__outer,
            classes:remove:active
          },
          {
            selector:_self,
            classes:add:active
          }
        ]
      }'
      >
      <div class="product-item__contents relative flex-grow rounded-sm {% if forloop.length == 1%}unique-product{% endif %}">
        
        <div class="relative {{bg-color}} product-item__image-bg">
          <a 
            id="product_link_{{ product.id }}"
            class="product-item__link"
            aria-label="{{ productTitle[1] }}, {{ product.price | money | remove: ' ' | remove: '.00' }}, go to product page"
            {{'h'}}ref="{% if handle != blank and handle != 'search' %}/collections/{{ handle }}{% endif %}/products/{{ product.handle }}"
          >
            <figure class="product-item__image-wrapper" data-product-item-image>

              {% assign productImage = product.featured_image | img_url: '600x' %}
              {% assign hoverImage = product.hover_image %}

              <img 
                class="product-item__image product-item__image--main {% if hoverImage == blank %}transition-transform transform group-hover:scale-105{% endif %}"
                aria-label="" 
                loading="lazy"
                {{'s'}}rc="{{ productImage }}"
                x-srcset="{{productImageRepsonsive}}"
                id="img_{{ product.id }}"
              />

              {% capture hoverImageRepsonsive %}
                {%- if hoverImage != blank -%}
                  {%- if product.featured_image_width > 180 -%}{{ hoverImage | img_url: '180x' }} 180w {{ 180 | divided_by: product.featured_image_aspect_ratio | round }}h,{%- endif -%}
                  {%- if product.featured_image_width > 360 -%}{{ hoverImage | img_url: '360x' }} 360w {{ 360 | divided_by: product.featured_image_aspect_ratio | round }}h,{%- endif -%}
                  {%- if product.featured_image_width > 540 -%}{{ hoverImage | img_url: '540x' }} 540w {{ 540 | divided_by: product.featured_image_aspect_ratio | round }}h,{%- endif -%}
                  {%- if product.featured_image_width > 720 -%}{{ hoverImage | img_url: '720x' }} 720w {{ 720 | divided_by: product.featured_image_aspect_ratio | round }}h,{%- endif -%}
                  {%- if product.featured_image_width > 900 -%}{{ hoverImage | img_url: '900x' }} 900w {{ 900 | divided_by: product.featured_image_aspect_ratio | round }}h,{%- endif -%}
                  {%- if product.featured_image_width > 1080 -%}{{ hoverImage | img_url: '1080x' }} 1080w {{ 1080 | divided_by: product.featured_image_aspect_ratio | round }}h,{%- endif -%}
                  {%- if product.featured_image_width > 1296 -%}{{ hoverImage | img_url: '1296x' }} 1296w {{ 1296 | divided_by: product.featured_image_aspect_ratio | round }}h,{%- endif -%}
                  {%- if product.featured_image_width > 1512 -%}{{ hoverImage | img_url: '1512x' }} 1512w {{ 1512 | divided_by: product.featured_image_aspect_ratio | round }}h,{%- endif -%}
                  {%- if product.featured_image_width > 1728 -%}{{ hoverImage | img_url: '1728x' }} 1728w {{ 1728 | divided_by: product.featured_image_aspect_ratio | round }}h,{%- endif -%}
                  {%- if product.featured_image_width > 1944 -%}{{ hoverImage | img_url: '1944x' }} 1944w {{ 1944 | divided_by: product.featured_image_aspect_ratio | round }}h,{%- endif -%}
                  {%- if product.featured_image_width > 2160 -%}{{ hoverImage | img_url: '2160x' }} 2160w {{ 2160 | divided_by: product.featured_image_aspect_ratio | round }}h,{%- endif -%}
                  {%- if product.featured_image_width > 2376 -%}{{ hoverImage | img_url: '2376x' }} 2376w {{ 2376 | divided_by: product.featured_image_aspect_ratio | round }}h,{%- endif -%}
                  {%- if product.featured_image_width > 2592 -%}{{ hoverImage | img_url: '2592x' }} 2592w {{ 2592 | divided_by: product.featured_image_aspect_ratio | round }}h,{%- endif -%}
                  {%- if product.featured_image_width > 2808 -%}{{ hoverImage | img_url: '2808x' }} 2808w {{ 2808 | divided_by: product.featured_image_aspect_ratio | round }}h,{%- endif -%}
                  {%- if product.featured_image_width > 3024 -%}{{ hoverImage | img_url: '3024x' }} 3024w {{ 3024 | divided_by: product.featured_image_aspect_ratio | round }}h,{%- endif -%}
                  {%- assign image_size = product.featured_image_width | append: 'x' -%}
                  {{ hoverImage | img_url: image_size }} {{ product.featured_image_width }}w {{ product.featured_image_height }}h
                {%- endif -%}
              {% endcapture %}

              {% if hoverImage != blank %}
                {% assign hover_alt = product.hover_image_alt %}
                <div class="product-image__hover {{ bg-color }} product-item__image-bg flex absolute transition-opacity opacity-0 group-hover:opacity-100  {% if hover_alt contains 'https' %} bg-{{bg}} {% else %} inset-0 {% endif %}">
                  
                  {% if hover_alt contains 'https' %}
                  <!--<video class="hidden {{ bg-color }} product-item__image-bg product-video" preload="true" src="{{ hover_alt }}" muted mute autoplay loop="" style="" poster=""><source src="{{ hover_alt }}"></video>-->
                  {% else %} 
                  <img 
                      class="object-contain w-full h-full product-item__image--hover" 
                      {{'s'}}rc="{{hoverImageRepsonsive}}"
                      loading="lazy"
                  />
                  {% endif %}
                </div>
              {% endif %}

            </figure>

            <div class="px-4 py-1 bg-secondary product-item__meta">
              <div class="text-left">
                {% for promo in promoMessaging %}
                  {% if product.tags contains promo.tag %}
                    <div 
                      tooltip="{% if promo.info_text != blank %}{{ promo.info_text }}{% endif %}"
                      class="relative flex items-center" 
                      style="color:{{ promo.badge_text_color }};"
                    >
                      <span class="product-item__promo text-left" style="color:{{ promo.badge_text_color }};">{{ promo.collection_badge }}</span>
                      {% if promo.info_text != blank %}
                        {% endraw %}
                        {% render 'icon' icon: 'info', strokeWidth: 2, width: 15, height: 15, class: 'ml-1' %}
                        {% raw %}
                      {% endif %}
                    </div>
                  {% endif %}  
                {% endfor %}
                <h3 class="my-1 leading-snug product-title product-item__title">
                  {{ productTitle[1] }}
                </h3>
              </div>

              <div class="leading-snug product-item__price flex space-x-2">{% assign discount_amount = product.price | divided_by: product.compare_at_price | times: -100 | plus: 100 | round %}  
                
                {% if product.compare_at_price > product.price %}
                
                  <span class="block text-red-600">{{ product.price | money | remove: " " | remove: ".00" }}</span>
                  <s class="block">{{ product.compare_at_price | money | remove: " " | remove: ".00" }}</s>
                
                {% else %}

                  {% if product.price_min != blank and product.price_min == product.price_max %}
                    {{ product.price_min | money | remove: " " | remove: ".00" }}
                  {% elsif product.price_min != blank %}
                    from {{ product.price | money | remove: " " | remove: ".00" }}
                  {% else %}
                    {{ product.price | money | remove: " " | remove: ".00" }}
                  {% endif %}

                {% endif %}
              </div>
            </div>
          </a>

          <div class="product-item__tools absolute flex opacity-100 lg:top-2 top-1 group-hover:opacity-100 group-active:opacity-100 lg:right-2 right-1 lg:opacity-0">
            {%- comment -%}
            <button class="product-item__quickshop p-1 lg:p-3 hover:bg-dark hover:text-white" onclick="_n.qs('[quickshop]').innerHTML='';Neptune.periscope.view(_n.qs('[quickshop]'),'/products/{{product.handle}}?view=quickshop'); modal.open('quickshop'); return false;">{% endraw %}{% render 'icon' icon:'eye' strokeWidth:2 width:20 height:20 %}{% raw %}</button>
            {%- endcomment -%}
            {% endraw %}{% render 'wishlist-toggle-js' %}{% raw %}
          </div>
        </div>
      


        <div class="product-item__info flex flex-col px-4 pt-1 pb-4 text-left">
          <div class="product-item__badges absolute top-0 left-0 flex flex-col items-start mt-3 ml-3 space-y-1 text-sm brand-semibold mb-0-5">
            {% for badge in badges %}
              
              {% if product.tags contains badge.tag and badge.tag != 'more-colors' %}
                <span class="px-2 product-item__badge" style="background-color:{{ badge.badge_bg_color }}; border-color:{{ badge.badge_text_color }}; color:{{ badge.badge_text_color }};">{{ badge.collection_badge }}</span>
              {% endif %}  
            {% endfor %}

            {% if product.available == false %}
            <span class="product-item__badge product-item__badge--soldout">{% endraw %}{{ 'products.product.sold_out' | t }}{% raw %}</span>
            {% endif %}

          </div>

          <div class="product-item__footer -mx-4 relative">
            
            <div class="product-item__siblings overflow-x-hidden">
              <p class="mb-0 mt-4 text-xs product-item__color" >Color: <span class="product-item__color-name">{{ productTitle[2] }}</span></p>
              {% capture family %}{{ product.title | split: '-' | first }}-{{ product.title | split: ' | ' | first | split: '-' | last | strip }}{% endcapture %}
              {% if siblings[family] %}
              <div class="overflow-x-scroll flex items-center">
                  <a class="relative h-20 w-20 overflow-hidden p-2 swatch hover:opacity-100 cursor-pointer" neptune-engage="{targets: [
                    { classes:{remove:opacity-50},siblings:{classes:add:opacity-50}}
                  ]}" style="width: 22% ;min-width:22%" onclick="
                  _n.cousin(this,'.product-item','.product-item__color-name').textContent = '{{ product.title | split: ' | ' | last }}';
                  _n.cousin(this,'.product-item','.product-item__image--main').src = '{{ product.featured_image }}';
                  _n.cousin(this,'.product-item','.product-item__image--hover').src = '{{ product.hover_image }}';
                  _n.cousin(this,'.product-item','.product-item__link').href = '{% if Collection.handle %}/{{ Collection.handle }}{% endif %}/products/{{ product.handle }}';
                  Neptune.periscope.view(_n.qs('.product-item__variants', _n.parents(this,'.product-item')),'/products/{{ product.handle }}?view=quick-add');
                  ">
                    <img class="object-contain h-full" src="{{ product.featured_image }}" />
                  </a>
                {% for sibling in siblings[family] %}
                {% if sibling.handle == product.handle %}{% continue %}{% endif %}
                  <a class="relative h-20 w-20 overflow-hidden p-2 swatch opacity-50 hover:opacity-100 cursor-pointer" neptune-engage="{targets: [
                    { classes:{remove:opacity-50},siblings:{classes:add:opacity-50}}
                  ]}" style="width: 22% ;min-width:22%" onclick="
                  _n.cousin(this,'.product-item','.product-item__color-name').textContent = '{{ sibling.name }}';
                  _n.cousin(this,'.product-item','.product-item__image--main').src = '{{ sibling.image }}';
                  _n.cousin(this,'.product-item','.product-item__image--hover').src = '{{ sibling.image_alt }}';
                  _n.cousin(this,'.product-item','.product-item__link').href = '{% if Collection.handle %}/{{ Collection.handle }}{% endif %}/products/{{ sibling.handle }}';
                  Neptune.periscope.view(_n.qs('.product-item__variants', _n.parents(this,'.product-item')),'/products/{{ sibling.handle }}?view=quick-add');
                  ">
                    <img class="object-contain h-full" src="{{ sibling.image | img_url:'120x120' }}" />
                  </a>
                {% endfor %}
                <!-- <span class="swiper-slide text-xs whitespace-nowrap">+ 4 more Colors</span>  -->
              </div>
              {% endif %}
            </div>


            {% assign strip_from_swatches = {% endraw %}'{{ settings.strip_from_swatches }}'{% raw %} | split:',' %}

            <div id="ProductItemQuickAdd" class="product-item__quickadd hidden active:block" aria-expanded="collapsed">
              <div class="mb-4 children:text-sm children:font-normal">
              {% endraw %}
              {% render 'reviews-summary' %}
              {% raw %}
              </div>
              <p class="mb-2 mt-0 text-xs">Select Size</p>
              <div class="grid grid-cols-5 gap-2 product-item__variants">
                {% for variant in product.variants %}
                  <button 
                    class="swatch product-option__swatch {% unless variant.available %}swatch--disabled cursor-not-allowed {% else %}cursor-pointer {% endunless %}text-center block border border-light rounded text-dark font-subheading hover:border-dark active:bg-dark active:border-dark active:text-light p-2"
                    onclick="{% if variant.available %}Neptune.cart.add({id:{{ variant.id }}}){% endif %}"
                  > 
                    {% assign v = variant.option1 %}
                    {% for strip in strip_from_swatches %}
                      {% assign v = v | remove: strip %}
                    {% endfor %}
                    {{ v }}
                  </button>
                {% endfor %}
              </div>
            </div>

            <button 
            class="button button--primary group flex items-center justify-center mb-10 mt-5 w-full lg:absolute lg:bottom-0 lg:left-0 lg:transform lg:translate-x-off focus:translate-x-0"
              aria-controls="ProductItemQuickAdd"
              aria-expanded="false"
              neptune-engage="{targets: [{selector:_self,classes:toggle:active,attributes:[{att:aria-expanded,toggle:['true','false']}]},{parents:'.product-item__outer',classes:toggle:quick-active},{selector: '_parent .product-item__quickadd', classes:toggle:active,focus: true, attributes:[{att:aria-expanded,toggle:['true','false']}] } ]}"
            >
              <span class="mr-2">Quick Add to Bag</span>
              {% endraw %}
                {% render 'icon' icon: 'plus', width:14 height:14 strokeWidth:2 class: 'group-active:hidden' %}
                {% render 'icon' icon: 'minus', width:14 height:14 strokeWidth:2 class: 'hidden group-active:block' %}
              {% raw %}
            </button>

          </div>
        </div>
      </div>
    </div> 

  </div>
</article>
{% endraw %}
