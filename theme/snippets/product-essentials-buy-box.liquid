
{%- for block in section.blocks -%}
  {%- case block.type -%}

    {%- when '@app' -%}
      {% render block %}

    {%- when 'title' -%}
      <h1 class="product-essentials__{{block.type}} type--product mt-0 mb-3 px-5 lg:px-10" {{ block.shopify_attributes }}>
        {{- product.title | split: " - " | first -}}
      </h1>
    
    {%- when 'promo_messaging' -%}
      <div aria-live="polite"neptune-liquid="{topic:'ProductBadges', source:'eval:_n.parents(_self,`[neptune-product]`)', append:[productBadges,promoMessaging]}" class="flex items-center justify-center px-5 lg:px-10">
        {% raw %}
          {% for promo in promoMessaging %}
            {% if product.tags contains promo.tag %}
              <div 
                tooltip="{% if promo.info_text != blank %}{{ promo.info_text }}{% endif %}"
                tooltip-center
                class="relative flex items-center" 
                style="color:{{ promo.badge_text_color }};"
              >
                <span class="product-item__promo text-left" style="color:{{ promo.badge_text_color }};">{{ promo.collection_badge }}</span>
                {% if promo.info_text != blank %}
                  {% endraw %}
                  {% render 'icon' icon: 'info', strokeWidth: 2, width: 15, height: 15, class: 'ml-1' %}
                  {% raw %}
                {% endif %}
              </div>
            {% endif %}  
          {% endfor %}
        {% endraw %}
      </div>

    {%- when 'liquid_title' -%}
      <h1 class="product-essentials__{{block.type}} type--product mt-0 mb-3 px-5 lg:px-10" {{ block.shopify_attributes }}>
        {{- block.settings.liquid | split: " - " | first -}}
      </h1>

    {%- when 'liquid_title_price' -%}
      <div class="flex align-top justify-between lg:pb-6 pb-4 px-5 lg:px-10">
        <h1 class="product-essentials__{{block.type}} type--product m-0 flex flex-col justify-top" {{ block.shopify_attributes }}>
          {{- block.settings.liquid | split: " - " | first -}}
        </h1>
        <div aria-live="polite"neptune-liquid="{topic:'ProductOptions',source:'eval:_n.parents(_self,`[neptune-product]`)'}" class="lg:pl-4">
          {% render 'product-price' %}                    
        </div>
      </div>

    {%- when 'description' -%}        
      <div class="font-highlight px-5 lg:px-10 mt-5 lg:mt-6">
        {{ product.description }}
      </div>

    {%- when 'product_sku' -%}        
      <div aria-live="polite"neptune-liquid="{topic:'ProductOptions',source:'eval:_n.parents(_self,`[neptune-product]`)'}" class="font-highlight text-xs py-5 px-5 lg:px-10">
        {% assign product_sku = product.variants[0].sku | split: '-' %}
        Style No. {{ product_sku[0] }}-{{ product_sku[1] }}-{{ product_sku[2] }} 
      </div>

    {%- when 'liquid_subtitle' -%}
      <p class="product-essentials__{{block.type}} font-highlight lg:text-base text-xs m-0 px-5 lg:px-10" {{ block.shopify_attributes }}>
        {{- block.settings.liquid -}}
      </p>

    {%- when 'liquid_vendor' -%}
      <p class="product-essentials__{{block.type}} font-highlight lg:text-base text-xs m-0 px-5 lg:px-10" {{ block.shopify_attributes }}>
        {{- block.settings.liquid -}}
      </p>

    {%- when 'liquid_block' -%}
      <div class="product-essentials__{{block.type}} px-5 lg:px-10">
        
        <p class="font-highlight color-primary text-base leading-tight m-0" {{ block.shopify_attributes }}>
          {{- block.settings.title -}}
        </p>
  
        {{- block.settings.liquid -}}
      </div>

    {%- when 'title_icons' -%}
      {% render 'product-title-icons' title:product.title %}

    {%- when 'review_summary' -%}
      {% render 'reviews-summary' product:product %}

    {%- when 'wishlist_toggle' -%}
      {% render 'wishlist-toggle' product:product %}

    {%- when 'compare_trigger' -%}
      {% render 'compare-trigger' product:product %}

    {%- when 'product_form' -%}
      {% render 'product-form' blocksettings:block.settings %}

    {%- when 'shipping_window' -%}
      {% render 'product-shipping-window' tags:product.tags %}

    {%- when 'breadcrumbs' -%}
      <div class="breadcrumbs text-xs font-highlight lg:mb-14 mb-4 px-5 lg:px-10">
        {% render 'breadcrumbs' include_home:false %}
      </div> 

    {%- when 'product_siblings' -%}
      {% include 'product-siblings-selector' section:section %}

    {%- when 'motivator' -%}
      {% render 'product-motivator' tags:product.tags config:block.settings %}
    
    {%- when 'regional_messaging' -%}
      {% render 'product-regional-messaging' tags:product.tags config:block.settings %}

    {%- when 'accordion' -%}
      {% assign previousIndex = forloop.index0 | minus: 1 %}
      {% assign nextIndex = forloop.index0 | plus: 1 %}
      {% unless section.blocks[previousIndex].type == 'accordion' %}
        <div>
      {% endunless %}
        <details type="accordion" group="product-details" class="accordion group">
          <summary class="accordion-title">
            <span>{{ block.settings.title }}</span>
            <span class="flex group-active:hidden accordion-control accordion-control--plus">
              {% render 'icon', icon: 'plus' width:20 height:20 sr:'Open Accordion' %}
            </span>
            <span class="hidden group-active:flex accordion-control accordion-control--minus">
              {% render 'icon', icon: 'minus' width:20 height:20 sr:'Close Accordion' %}
            </span>
          </summary>
          <div class="accordion-panel">
            <div>
              {% if block.settings.panel != blank %}
                {{ block.settings.panel }}
              {% elsif block.settings.panel_liquid != blank %}
                {{ block.settings.panel_liquid }}
              {% endif %}
            </div>
          </div>
        </details>
      {% if section.blocks[nextIndex].type != 'accordion' or section.blocks.size == forloop.index %}
        </div>
      {% endif %}

    {%- when 'payment_widget' -%}
      {% render 'payment-widget' block:block, product.product %}

  {%- endcase -%}
{%- endfor -%}
