{% raw %}

<div class="w-full product-item relative" itemscope="" itemtype="http://schema.org/Product" data-index="0">    
  <a id="product{{product.id}}" class="product-link w-full group" {{'h'}}ref="/products/{{product.handle}}" title="{{product.title}}">

    <div class="pi__img relative">     
      <div class="pi__link ir ir--product block aspect-w-3 aspect-h-4 relative overflow-hidden">      
          
        <img class="absolute top-0 left-0 z-0 block w-full item-fix object-fit" {{'s'}}rc="{{ product.images[0] | img_url: '800x' }}" alt="{{ image.alt | escape }}">
        
        <div class="pi__photo-hover opacity-0 group-hover:opacity-100 absolute left-0 top-0 h-full w-full delay-200 transition-opacity ease-in-out">          
          {%- if product.media[1].alt contains 'https' -%}
            <div class="h-full w-full" onmouseenter="console.log('this:', this);this.innerHTML = this.innerHTML.replace('<!--','').replace('-->',''); this.removeAttribute('onmouseenter');">
              <!--<video class="product-video plyr--setup" preload="true" src="{{product.media[1].alt}}" muted mute autoplay loop="" style="width: calc(100% + 20px);
              margin-left: -10px;
              margin-right: -10px;
              height: 100%;
              object-fit: contain;" poster=""><source src="{{product.images[1].alt}}"></video>-->
            </div>
          {%- else -%}
            <img class="absolute top-0 left-0 z-0 block w-full h-full" {{'s'}}rc="{{ product.images[1] | img_url: '800x' }}" alt="{{ image.alt | escape }}">
          {%- endif -%}
        </div>  
      </div> 

    </div>    
    <div class="product-item-info mx-0 mt-4 leading-tight tracking-wide text-center">      
      <div class="block">        
        <h2 class="MonumentGrotesk-Bold uppercase text-base m-0 tracking-wide leading-tight">{{product.title | split: '-' | first}}</h2>        
        <div class="text-lg leading-tight">{{product.title | split: '-' | last}}</div>        
        <span class="block text-lg leading-tight">
          {% endraw %}
          {% render 'price-removal' %}
          {% raw %}
          <span
          class="global-price"
          global-selector="prices.item.label"
          global-default="{{ product.price | money: 'local' | remove: removal }}"
          global-variant="{{ product.selected_or_first_available_variant.id }}"
          >{{ product.price | money: 'local' | remove: removal }}</span>
        </span> 
      </div>   
    </div>  
  </a>

</div>

{% endraw %}
