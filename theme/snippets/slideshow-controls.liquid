 <div class="pagination slideshow-pagination text-center {% if settings.show_pagination_mobile %}flex {% else %}hidden{% endif %} {% if settings.show_pagination %}lg:flex{% else %} lg:hidden{% endif %}"></div>

  <button class="swiper-prev swiper-button-prev-unique lg:px-5 pt-0 border-0 bg-transparent absolute z-20 left-0 top-0 h-full {% if settings.arrows != blank and settings.arrows_mobile != blank %} flex {% elsif settings.arrows != blank and settings.arrows_mobile == blank %} hidden lg:flex {% elsif settings.arrows == blank and settings.arrows_mobile != blank %} flex lg:hidden {% else %} hidden {% endif %} items-center flex-col justify-center">
    <span class="icon block w-8" style="color: {{ settings.arrow_color }}">
      {% if settings.arrow_type == 'chevron' %}
      {% render 'icon' icon:'chevron-left' width:32 height:32 %}
      {% elsif settings.arrow_type == 'standard' %}
      {% render 'icon' icon:'arrow-wide-left' width:32 height:32 fill:'currentColor' stroke:'none' %}
      {% endif %}
    </span>
  </button>
  <button class="swiper-next swiper-button-next-unique lg:px-5 pt-0 border-0  bg-transparent absolute z-20 right-0 top-0 h-full {% if settings.arrows != blank and settings.arrows_mobile != blank %} flex {% elsif settings.arrows != blank and settings.arrows_mobile == blank %} hidden lg:flex {% elsif settings.arrows == blank and settings.arrows_mobile != blank %} flex lg:hidden {% else %} hidden {% endif %} items-center flex-col justify-center">
    <span class="icon block w-8" style="color: {{ settings.arrow_color }}">
      {% if settings.arrow_type == 'chevron' %}
      {% render 'icon' icon:'chevron-right' width:32 height:32 %}
      {% elsif settings.arrow_type == 'standard' %}
      {% render 'icon' icon:'arrow-wide-right' width:32 height:32 fill:'currentColor' stroke:'none' %}
      {% endif %}
    </span>
  </button>