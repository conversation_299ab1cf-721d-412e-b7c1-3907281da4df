<form id="searchFormMobile" class="relative flex items-center justify-center">

  <input type="search"
    id="Search-Trigger-{{ section.id }}"
    class="h-full m-0 w-full shadow-none lg:pl-8 cursor-pointer bg-transparent placeholder::text-black uppercase absolute top-0 left-0 text-base"
    readonly="readonly"
    right="left"
    autocomplete="off"
    type="button"
    neptune-engage="{
      targets:[
        {
          attributes:[
            {
              att:data-return-focus,
              set:here
            }
          ]
        },
        {% if megaMenu == true %}
          {
            selector:.header__nav,
            classes:toggle:active
          },
          {
            selector:html,
            attributes:[{
              att:data-active-modal-mobile,
              toggle:modal--mobile-menu
            }]
          }
        {% else %}
          {
            selector:html,
            attributes:[
              {
                att:data-active-modal,
                set:panel-nav
              }
            ]
          },
          {
            selector:.panel--search-form,
            methods:reset
          },
          {
            selector:.active-remove,
            classes:{
              remove:active
            }
          },
          {
            selector:'.mobile--nav-item',
            classes:{
              remove:active
            }
          }
        {% endif %}
      ]
    }"
    >

    <span class="flex items-center pointer-events-none block">
      {% render 'icon' icon:'search', height:17, width:17 %}
    </span>

    <span class="sr-only">
      {{ 'general.search.search' | t }}
    </span>

  <input type="submit" value="go" class="sr-only">

</form>