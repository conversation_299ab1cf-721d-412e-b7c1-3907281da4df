{% comment %} 


This is generated by webpack so don't edit this unless you want to be sad when your work gets overwritten in production.
You can edit this file in ../../components/** 


{% endcomment %}






<nav class="{{ class }} overflow-x-scroll story-nav-items-{{title | handle}}">
  <ul class="flex list-none mx-0 lg:mx-12">
    {% for i in (1..10) %}
    {% for link in links %}
    <li class="mx-4">
      <a href="{{ link.url }}" class="block text-center group flex flex-col">
        <span class="border rounded-full rounded-full overflow-hidden w-32 h-32">
          {% render 'lazy-image' with image: link.object.image, image_class: "w-full h-full object-cover border" %}
        </span>
        <h3 class="font-normal clamp-2">{{ link.title }}</h3>
      </a>
    </li>
    {% endfor %}
    {% endfor %}
    <li class="w-20"></li>
  </ul>
</nav>