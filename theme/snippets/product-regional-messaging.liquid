<div neptune-permanent="productMotivator">
  {%- assign domestic_free_shipping = 'products.product.free_shipping' | t -%}
  {%- unless domestic_free_shipping contains 'translation missing' or domestic_free_shipping == '' -%}
  <p class="product__motivator m-0 mt-6 px-5 lg:px-10 leading-normal font-highlight text-xs" data-domestic>
    <span>{{ domestic_free_shipping }}</span>
  </p>
  {%- endunless -%}
  {%- assign international_free_shipping = 'products.product.free_shipping_international' | t -%}
  {%- unless international_free_shipping contains 'translation missing' or international_free_shipping == '' -%}
  <p class="product__motivator m-0 mt-6 px-5 lg:px-10 leading-normal font-highlight text-xs" data-international>
    <span>{{ international_free_shipping }}</span> 
  </p>
  {%- endunless -%}
  <div class="product__motivator m-0 px-5 lg:px-10 leading-normal font-highlight text-xs" data-international>
    {% render 'country-specific-content' messages:config.country class:"mt-1 block"%}
  </div>
</div>