{% if settings.nosto_site_id  != blank %}

  <script type="text/javascript">
    (() => {window.nostojs=window.nostojs||(cb => {(window.nostojs.q=window.nostojs.q||[]).push(cb);});})();
  </script>
  <script type="text/javascript">
    nostojs(api => api.setAutoLoad(false));
  </script>
  <script src="//connect.nosto.com/include/{{ settings.nosto_site_id }}" async></script>

  {% liquid 

    if template.name == 'index' 
      render 'nosto-track' type:'viewFrontPage' customer:customer
    
    elsif template contains 'collection' 
      render 'nosto-track' type:'viewCategory' id:collection.handle customer:customer
    
    elsif template contains 'search'
      render 'nosto-track' type:'viewSearch'  id:search.terms customer:customer
    
    elsif template contains 'product'
      render 'nosto-track' type:'viewProduct' id:product.id customer:customer

    elsif checkout and checkout.order == blank 
      render 'nosto-track' type:'viewCart'

    elsif checkout.order
      render 'nosto-track' checkout:checkout 

    elsif template == '404'
      render 'nosto-track' type:'viewNotFound' customer:customer

    else
      render 'nosto-track' type:'viewOther' customer:customer

    endif

  %}

<script>
  
  document.addEventListener("cart:updated", function (e) {

    nostojs(api => {
      api.defaultSession()
       .setCart({
        items: cart.items.map(item=>{
          return {
            name: item.title,
            price_currency_code: cart.currency,
            product_id: item.product_id.toString(),
            quantity: item.quantity,
            sku_id: item.variant_id.toString(),
            unit_price: item.price
          }
        })
       })
       .viewCart()
       .update().then(data => {
        console.log('nosto', data.recommendations);
      })
    });

  });


  document.addEventListener("product:variantSelected", function (e) {

    console.log('product:variantSelected', e.detail.info)

    nostojs(api => {
      api.defaultSession()
        .viewProductSku(e.detail.info.el.product.id.toString(), e.detail.info.variant.id.toString())
        .load()
        .then(response => {
          console.log('nosto', response);
        })
    });

  });

  window.addEventListener('NeptuneLater:ready', function() {
    ['product_item_click', 'quick_add_click'].forEach(function(type) {
      Neptune.later.register(type, function (event) {
        const addtlData = {}
        var wrapper = event.target.closest('[data-product-id]')
        var section = event.target.closest('[data-section-id]')
        var result = event.target.closest('[data-result-id]')

        if (!!wrapper) addtlData.pid = wrapper.dataset.productId 
        if (!!section && window['NostoRecs-' + section.dataset.sectionId]) addtlData.result_id = window['NostoRecs-' + section.dataset.sectionId].result_id  
        if (!!result) addtlData.result_id = result.dataset.resultId  

        return addtlData
      })
    })

    document.addEventListener("cart:itemAdded", function() {
      Neptune.later.recall()
    })
  })

  window.addEventListener('Neptune:Later', function(event) {
    const eventData = event.detail.info.event

    if (eventData.type != 'product_item_click' && eventData.type != 'quick_add_click') return false
    const arguments = []

    if (eventData.type == 'product_item_click') arguments.push('vp')
    if (eventData.type == 'quick_add_click') arguments.push('cp')

    arguments.push(eventData.pid)

    if (eventData.result_id) arguments.push(eventData.result_id)

    {% comment %} console.log('\n\n\n NeptuneLater: ', eventData.type, arguments, '\n\n\n') {% endcomment %}
    nostojs(api => {
    api
      .defaultSession()
      .recordAttribution(...arguments)
      .done()
    });
  })

</script>

{% endif %}



