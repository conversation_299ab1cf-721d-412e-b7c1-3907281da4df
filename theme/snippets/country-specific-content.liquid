
{%- comment -%}
  You can use the setting below and render this snippet for country specific content.
{%- endcomment -%}
{%- comment -%}
  {
    "type": "textarea",
    "id": "country",
    "label": "Country Specific Messaging",
    "info": "For each country, use the 2 character country code or the currency code. Each country annoucement bar must be separated by a line break and in the follow format: AU | Free Shipping on orders over $50 AUD"
  }
{%- endcomment -%}
{% liquid
 assign countries = messages | newline_to_br | split: '<br />'
 assign bold = false
 assign italic = ""
 if  bold_style
  assign bold = true 
 endif
 if italic_style
  assign italic = "italic"
 endif
%}

<div {% unless fallback %}data-international{% endunless %}>
  <script type="application/json">
    {
      {%- if fallback != blank -%}"fallback": {{ fallback | json }},{%- endif -%}
      "content": {
        {%- for country in countries -%}
          {%- liquid
            assign code = country | split: '_' | first | strip | uppercase
            assign messaging = country | split: '_' | last | strip
          -%}
        "{{ code }}": {{ messaging | json }}{%- unless forloop.last -%},{%- endunless -%}
        {%- endfor -%}
      }
    }  
  </script>
  <div neptune-liquid="{ topic:GlobalE, source:'eval:JSON.parse(_n.qs(`script`, _self.parentElement).innerText)', append:Shopify}">
    <template>
      {% raw %}
        {%- assign messaging = fallback -%}
        {%- if content["ALL"] != blank -%}{%- assign messaging = content["ALL"] -%}{%- endif -%}
        {%- if content[Shopify.country] != blank -%}{%- assign messaging = content[Shopify.country] -%}{%- endif -%}
        {%- if content[Shopify.currency.active] != blank -%}{%- assign messaging = content[Shopify.currency.active] -%}{%- endif -%}
        {%- if content[Shopify.locale] != blank -%}{%- assign messaging = content[Shopify.locale] -%}{%- endif -%}
        <span {% endraw %}class="{{ class }} {{ italic }}">{% if bold %}<strong>{% endif %}{% raw %}{{ messaging }}{% endraw %}{% if bold %}</strong>{% endif %}</span>
    </template>
  </div>

</div>

