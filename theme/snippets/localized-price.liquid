{%- liquid
  assign price = product.price | money_without_trailing_zeros | replace: localization.country.currency.symbol, symbol 
  assign compare_at_price = product.compare_at_price | money_without_trailing_zeros | replace: localization.country.currency.symbol, symbol 

  if localization.country.currency.iso_code == 'ILS'
    assign price = price | remove: 'NIS' | prepend: localization.country.currency.symbol
    assign compare_at_price = compare_at_price | remove: 'NIS' | prepend: localization.country.currency.symbol
  endif

  if localization.country.currency.iso_code == 'INR'
    assign price = price | remove: 'Rs. ' | prepend: localization.country.currency.symbol | remove: ","
    assign compare_at_price = compare_at_price | remove: 'Rs. ' | prepend: localization.country.currency.symbol | remove: ","
  endif

  if localization.country.currency.iso_code == 'KRW'
    assign price = price | remove: ","
    assign compare_at_price = compare_at_price | remove: ","
  endif

  if localization.country.currency.iso_code == 'DKK'
    assign price = price | remove: 'kr' | prepend: localization.country.currency.symbol | remove: "."
    assign compare_at_price = compare_at_price | remove: 'kr' | prepend: localization.country.currency.symbol | remove: "."
  endif

  if localization.country.currency.iso_code == 'NOK'
    assign price = price | remove: 'kr' | prepend: localization.country.currency.symbol | remove: "."
    assign compare_at_price = compare_at_price | remove: 'kr' | prepend: localization.country.currency.symbol | remove: "."
  endif

  if localization.country.currency.iso_code == 'SEK'
    assign price = price | remove: 'kr' | prepend: localization.country.currency.symbol | remove: " "
    assign compare_at_price = compare_at_price | remove: 'kr' | prepend: localization.country.currency.symbol | remove: " "
  endif

  if localization.country.currency.iso_code == 'AED'
    assign price = price | remove: 'Dhs.' | append: localization.country.currency.symbol | remove: ","
    assign compare_at_price = compare_at_price | remove: 'Dhs.' | append: localization.country.currency.symbol | remove: ","
  endif

  assign price = price | json
  assign compare_at_price = compare_at_price | json
-%}
"price":{{ price }},
"compare_at":{{ compare_at_price }}