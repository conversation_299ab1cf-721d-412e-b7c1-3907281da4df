<div class="absolute bottom-5 right-5 flex items-center justify-center" style="z-index:25;">
  <div class="flex items-center justify-center gap-1">
    {% if data.settings.show_audio_controls != blank %}
      <button id="muteUnmuteButton" class="button button--secondary flex items-center justify-center w-10 h-8 px-0 rounded-lg" onclick="muteUnmuteVideo('{{data.id}}');this.classList.toggle('active');event.preventDefault();">
        <span>
          {% render 'icon' icon:'mute' height:20 %}
        </span>
        <span>
          {% render 'icon' icon:'unmute' height:20 %}
        </span>
      </button>
    {% endif %}
    {% if data.settings.show_video_controls != blank %}
      <button id="playPauseButton" class="button button--secondary flex items-center justify-center w-10 h-8 px-0 rounded-lg" onclick="playPauseVideo('{{data.id}}');this.classList.toggle('active');event.preventDefault();">
        <span>
          {% render 'icon' icon:'play' height:18 fill:'#000000' stroke:'none' %}
        </span>
        <span>
          {% render 'icon' icon:'pause' height:18 fill:'#000000' stroke:'none' %}
        </span>
      </button>
    {% endif %}
  </div>
</div>