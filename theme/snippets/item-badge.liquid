{% raw %}
  {% for badge in productBadges | where: 'type', 'text' %}
    {% assign excluded_collections = badge.excluded_collection | replace: ', ', ',' | replace: ' ,', ',' %}
    {% assign collection_to_check = collection.handle %}

    {% if excluded_collections contains ',' %}
      {% assign excluded_collections = excluded_collections | split: ',' %}
    {% endif %}

    {% unless excluded_collections contains collection_to_check %}
      {% if product.tags contains badge.tag  and badge.item != blank %}
        <div id="badge-{{ badge.tag | handle }}" class="product-item__badge button button--secondary" style="color:{{ badge.color }}; border-color:{{ badge.color }}; background-color:{{ badge.bg_color }};">
          <span>{{ badge.item }}</span>
        </div>
      {% endif %}
    {% endunless %}
  {% endfor %}
{% endraw %}