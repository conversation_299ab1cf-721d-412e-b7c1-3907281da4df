{% comment %} 


This is generated by webpack so don't edit this unless you want to be sad when your work gets overwritten in production.
You can edit this file in ../../components/** 


{% endcomment %}






{% if shop.domain contains 'daddies' %}
<svg class="logo-loader w-1/5 block center" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 270 100">
  <g id="Layer_1-2" data-name="Layer 1"><path class="cls-1" d="M76.33,7.71c8.48.1,16.49-.51,24.43.42,7.49.88,14,3.94,18.67,11.32.94-4.24-.06-7.88.94-11.78,8.69.23,17.35-.66,25.92.67,7.69,1.19,14.09,4.8,18.44,12.06V8.22c5.41-1,10.35-.29,15.61-.54V53.79H164.92V40.6C158.51,51,148.81,53.85,137.8,54c-5.79.08-11.59,0-17.85,0V42.42c-4.4,5.17-9.32,8.79-16.14,10.19a80.88,80.88,0,0,1-7.58-12c3.56-1,7-1.71,8.66-5.22a11.6,11.6,0,0,0,.65-7.33c-.91-4.89-5.5-7.14-13.57-6.77-.9,3.51,0,7.18-.53,10.75C88.68,29.24,78.4,12.85,76.33,7.71Zm59.58,13.36v19c4.76.42,9.3.64,12.39-3.5a9.55,9.55,0,0,0,.65-10.4C146.31,21.09,141.24,21.17,135.91,21.07Z"></path><path class="cls-1" d="M34.73,51.11c-3.13-.65-5.59,1.52-8.47,2C17.64,54.75,9,53.6,0,54.09V7.81c9.21.14,18.49-1.09,27.62,1,11.88,2.7,17.46,9.69,17.94,21.82,0,.63.1,1.27.23,2.94L66.83,0c11.34,18.25,22.25,35.79,33.48,53.84H82L66.78,29.62,51.48,53.88H34.05c-.7-1.2.56-1.88.58-2.84l.56-.68C34.64,50.47,34.29,50.56,34.73,51.11Zm-19-29.91V40.14c5,.35,9.67.45,12.59-4.12a9.29,9.29,0,0,0,0-10.36C25.43,21.19,20.75,21,15.69,21.2Z"></path><path class="cls-1" d="M260.1,22.44c-5.91-2.51-11.23-5.89-17.69-3.27.52,3.35,2.93,3.23,4.67,3.67,3.86,1,7.82,1.54,11.56,3,6.74,2.59,9.85,6.38,10,12.23.12,6.36-3.07,11.44-9.45,13.87-10.44,4-20.59,2.33-30.42-2.37-1.18-.57-2.27-1.33-3.94-2.32-.12,2.32-.21,4.32-.32,6.54H184.7V8.12H224c.73,3.82.42,7.71.14,12.1h-23.7c-1.06,1.69-.83,3-.44,4.73h22.36V36H200.73c-1.51,1.62-1,3.08-.77,5h24.22c.3,1.4.51,2.35.79,3.68l7.08-8c2.37,1.15,4.52,2.39,6.82,3.24a37.28,37.28,0,0,0,6.75,1.7c2.42.41,4.93.64,7.06-1.38-.46-2.5-2.46-2.69-4.14-3.08a98.31,98.31,0,0,1-12-3.14c-6.81-2.54-9.89-6.34-10-12.19-.09-6.6,3.24-11.9,9.82-14.09,10.88-3.63,21.19-2.07,31.17,4.42Z"></path></g>
</svg>
  {% else %}
  <svg class="logo-loader w-1/12 block center" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 95.3822">
  <path d="M87.0515,40.41C81.9352,34.0712,75.1,32.3055,67.33,31.9794l-.8214,12.9445c2.6034.974,4.4345,2.4431,4.6277,5.2595l12.4628-1.0125c-.7506,8.3884-1.4763,16.499-2.21,24.6941-4.2572.3847-8.314.751-12.5122,1.13-.5888,3.0241-2.9066,4.6128-5.37,5.95l-.8451,12.8355C70.9689,92.07,77.9048,88.5033,83.39,82.0428a18.9785,18.9785,0,0,0,8.8884,6.4858,28.0608,28.0608,0,0,0,11.1278,1.7661l.9087-12.5994a14.5327,14.5327,0,0,1-3.4259-1.754l-.0292-.0237-.0194-.0158-.0168-.0173c-1.0508-.9033-1.4047-2.0674-1.3085-3.9731.1386-2.7438,1.7434-20.6378,1.7434-20.6378.5965-5.15,1.286-8.0583,5.8423-9.7293l1.0779-13.0855C99.7313,30.2,92.5127,33.947,87.0515,40.41ZM41.3083,41.9341c-3.109,2.9872-5.4167,6.5395-5.9534,10.9336C34.9143,56.4752,34.7,60.11,34.38,63.7324c-.3983,4.51-.745,9.0259-1.22,13.528A12.8325,12.8325,0,0,0,37.0364,88.383a15.6215,15.6215,0,0,0,2.2156,1.8546,26.0846,26.0846,0,0,0,15.9887,4.2208l.7079-12.645c-.4712-.1832-.9288-.3406-1.2595-.4941-3.2256-1.4948-3.65-3.0888-3.4267-5.5338L53.05,54.4823c.0211-.2809.0465-.55.0743-.812.0039-.1377.0109-.2867.0211-.4482a8.6564,8.6564,0,0,1,.7787-3.1189,6.6555,6.6555,0,0,1,2.0145-2.7439A11.2944,11.2944,0,0,1,58.9266,45.4l.8642-12.6452a49.8762,49.8762,0,0,0-6.8274,1.9193A33.764,33.764,0,0,0,41.3083,41.9341Zm-18.05,47.65c.27-3.9238.576-7.845.8913-11.7658C24.6911,71.0829,25.2633,64.35,25.8,57.6142c.4665-5.8483.9418-11.6965,1.3253-17.5506a5.9161,5.9161,0,0,1,1.8523-3.9094c1.4637-1.501,3.3733-2.0725,5.4555-2.64.2294-3.4023.4581-7.12.7025-10.7439a32.904,32.904,0,0,0-5.5626,1.1938,26.3114,26.3114,0,0,0-12.8881,8.3672,16.5264,16.5264,0,0,0-3.8464,9.7553c-.4284,7.0424-1.1126,14.0689-1.6448,21.1054-.5153,6.8108-.962,13.6258-1.4615,20.4373q-.2769,3.7755-.6592,7.5442a11.2423,11.2423,0,0,0,3.6053,9.8758c4.6588,4.2609,10.3219,5.0886,16.536,4.4493.222-3.5854.4352-7.178.6461-10.5829a14.1862,14.1862,0,0,1-3.0556-.4306A4.29,4.29,0,0,1,23.2584,89.5839ZM166.2711,41.9193c5.9084-.5221,12.0636-1.1321,17.9786-1.655a13.3721,13.3721,0,0,0-2.71-8.7376c-2.7181-3.5837-6.5-5.5473-10.7354-6.6748a27.2264,27.2264,0,0,0-7.64-.9731l-.7623,13.1148A5.7385,5.7385,0,0,1,166.2711,41.9193Zm39.1833-26.4307c-4.6865-4.2052-11.2123-5.3751-16.6009-5.1541-.3179,3.4808-.6978,7.3943-1.021,10.9313,1.3052.18,2.2707.23,3.1934.4573a4.1552,4.1552,0,0,1,3.4346,4.5288c-.0925,1.8516-.2569,3.7-.4112,5.548-.3821,4.5843-.7885,9.1666-1.1635,13.7513-.375,4.5913-.7169,9.1853-1.0863,13.7768-.3755,4.6643-.9781,9.322-1.0893,13.9919-.102,4.2718-2.8319,6.3363-5.7383,7.2843-.6891.225-1.3985.3877-2.1726.5992-.2719,3.3941-.534,6.9613-.8045,10.341.3015-.0341.6628-.08,1.0993-.142,7.53-.91,13.972-3.94,18.8155-9.9051a13.33,13.33,0,0,0,3.1057-7.7195c.2381-3.8536.6654-7.695.9942-11.5438q.5777-6.7689,1.1251-13.5411c.413-5.0321.8421-10.0627,1.2189-15.0974q.3825-5.1134.634-10.2356A9.6882,9.6882,0,0,0,205.4544,15.4886Zm-28.29,38.5471a19.0462,19.0462,0,0,0-6.5564-3.6966,30.72,30.72,0,0,0-13.59-1.5768,10.0941,10.0941,0,0,1-6.4509-1.1805,4.2753,4.2753,0,0,1-1.6157-6.2946,9.2228,9.2228,0,0,1,4.165-3.5736c.5861-.2787,1.1926-.509,1.796-.7284l.8375-12.6847c-8.3679,1.4676-15.4528,4.8454-21.25,10.9276-5.1535-5.5008-11.576-7.31-18.9514-7.3555l-.9267,12.8128c2.6858.906,4.5239,2.3607,4.7855,5.2563,3.6-.2647,7.1744-.5179,10.7323-.7795-.0078.59.0284,1.227.0546,1.7326a12.3782,12.3782,0,0,0,3.98,8.4369,20.5658,20.5658,0,0,0,8.2127,4.7349A35.2207,35.2207,0,0,0,154.72,61.4045a22.7652,22.7652,0,0,1,3.5644.0271c2.3958.3026,4.5357,1.2166,5.3455,3.7132.7749,2.39-.6009,4.1288-2.2336,5.6516-.59.55-1.2816.9908-1.968,1.5127l-.9615,13.0624a13.0523,13.0523,0,0,0,1.7154-.3651,37.5763,37.5763,0,0,0,13.8459-6.8983,22.4281,22.4281,0,0,0,6.4911-8.1028C183.1726,64.1188,181.9572,58.2308,177.1643,54.0357ZM145.8178,68.087q-14.4894,1.3776-28.71,2.729c-.784,3.0843-2.9714,4.5962-5.3607,5.9489l-.8194,12.84c7.7544-1.6589,14.3548-4.868,19.749-10.6595a21.447,21.447,0,0,0,9.2641,5.8451,37.6557,37.6557,0,0,0,11.05,1.5442l.9158-12.6685C148.7055,72.9816,146.194,71.7614,145.8178,68.087Z" transform="translate(-9 -10.3089)" fill="currentFill"></path>
  
</svg>
{% endif %}
 <style>
  @keyframes cycle {
    0% {
      stroke-dashoffset: 280%;
    }

    50% {
      stroke-dashoffset: 120%;
      fill: transparent;
    }

    100% {
      stroke-dashoffset: 120%;
      fill: var(--color-brand);
    }
  }
  .loading .logo-loader path {
    stroke: var(--color-brand);
    stroke-width: 1px;
    stroke-dasharray: 280%;
    fill: var(--color-brand);
    stroke-dashoffset: 280%;
    animation: cycle 1.2s infinite;
  }
</style>
