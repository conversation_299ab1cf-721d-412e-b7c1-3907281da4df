<script type="text/javascript" async="" src="https://static.klaviyo.com/onsite/js/klaviyo.js?company_id={{ settings.klaviyo_api_key }}"></script>

<script> 
// load the klaviyo object
!function(){if(!window.klaviyo){window._klOnsite=window._klOnsite||[];try{window.klaviyo=new Proxy({},{get:function(n,i){return"push"===i?function(){var n;(n=window._klOnsite).push.apply(n,arguments)}:function(){for(var n=arguments.length,o=new Array(n),w=0;w<n;w++)o[w]=arguments[w];var t="function"==typeof o[o.length-1]?o.pop():void 0,e=new Promise((function(n){window._klOnsite.push([i].concat(o,[function(i){t&&t(i),n(i)}]))}));return e}}})}catch(n){window.klaviyo=window.klaviyo||[],window.klaviyo.push=function(){var n;(n=window._klOnsite).push.apply(n,arguments)}}}}();
var _learnq = _learnq || [];
// end load the klaviyo object

if ('{{ customer.email }}') { 
  klaviyo.push(['identify', {'$email' : '{{ customer.email }}'}]); 
} 
</script>

<script type="text/javascript">

  var klaviyo_item = {
    Name: {{ product.title|json }},
    ProductID: {{ product.id|json }},
    Categories: [...{{ product.collections|map:'title'|json }},{{ collection.title | json }}],
    ImageURL: "https:{{ product.featured_image.src|img_url:'grande' }}",
    URL: "{{ canonical_url }}",

    Brand: {{ product.vendor|json }},
    Price: {{ product.price|money|json }},
    CompareAtPrice: {{ product.compare_at_price_max|money|json }}
  };

  const LearnQ = {
    track: (event, data) => {
      const  track_data = {...klaviyo_item||{}, ...data||{}}
      klaviyo.push(['track', event, track_data]);    
    }
  }

{% case request.page_type -%}

  {%- when 'product' -%}
    LearnQ.track('Viewed Product', klaviyo_item)

    klaviyo.push(['trackViewedItem', {
      Title: klaviyo_item.Name,
      ItemId: klaviyo_item.ProductID,
      Categories: klaviyo_item.Categories,
      ImageUrl: klaviyo_item.ImageURL,
      Url: klaviyo_item.URL,
      Metadata: {
        Brand: klaviyo_item.Brand,
        Price: klaviyo_item.Price,
        CompareAtPrice: klaviyo_item.CompareAtPrice
      }
    }]);

  {%- when 'collection' -%}
    var klaviyo = klaviyo || [];
    klaviyo.push(['track', 'Viewed Page', {
        PageName: "{{ collection.title }}" 
      }
    ]);

{%- endcase -%}
  
  document.addEventListener('cart:itemAdded',function (e){
    console.log
    
    const item = e.detail.info.items[0]
    
    klaviyo.track('Added to Cart', {
      Name:item.product_title,
      ProductID:item.product_id,
      ImageURL:item.image,
      URL:item.url,
      Brand:item.vendor,
      Price:'$'+(item.price/100),
      CompareAtPrice:'$'+(item.original_price/100),
      VariantID:item.variant_id
    })
  });

</script>
