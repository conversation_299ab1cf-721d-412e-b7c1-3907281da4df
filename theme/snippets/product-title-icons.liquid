{% if product.title contains ' with ' or product.title contains ' w/ ' %}
{% assign features = title | replace: ' w/ ', ' with ' | replace: ' & ', ' and ' | replace: ', ', ' and ' | split: ' with ' | last | split: ' and ' %}

<p class="product-essentials--{{block.type}} font-bold text-xs tracking-wider tracking-widest uppercase">
{% for feature in features %}

{% if forloop.first %} 
With 
{% elsif forloop.last %} 
 and 
{% else %}
, 
{% endif %}
{% render 'icon' icon:feature, class:"bg-black-10 p-1 inline-block -mt-1 rounded-full", width:20 height:20 -%} {{ feature }}
{% endfor %}
</p>

{% endif %}