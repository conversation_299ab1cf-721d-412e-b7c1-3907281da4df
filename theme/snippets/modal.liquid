{% liquid

case position

  when 'left'
    assign classes = classes | append: ' top-0 left-0 lg:w-1/3 h-screen'

  when 'right'
    assign classes = classes | append: ' top-0 right-0 lg:w-1/3 h-screen'

  when 'center'
    assign classes = classes | append: ' left-1/2 top-1/2 max-w-screen-md w-full'

  when 'top'
    assign classes = classes | append: ' top-0 right-0 left-0 w-screen'

  when 'bottom'
    assign classes = classes | append: ' bottom-0 right-0 left-0 w-screen'

  when 'full'
    assign classes = classes | append: ' inset-0 w-screen h-screen overflow-y-scroll'

  when 'border'
    assign classes = classes | append: ' left-1/2 top-1/2 max-w-screen-md w-[95%]'
  
endcase

%}

<aside data-modal="{{ topic }}" class="fixed bg-near-white z-50 modal modal-{{ position }} {{ classes }} animate transition-transform ease-in-out duration-300 focus:outline-none focus:shadow-none" tabindex="0" neptune-brig="{}" neptune-engage="{
  on:keyup,
  which: 27,
  targets:[{
    selector:html,
    attributes:[{
      att:data-active-modal,
      set:_remove
    }]
  },
  { 
    selector:'[data-return-focus]',
    attributes:[{
      att:data-return-focus,
      set:_remove
    }],
    focus:true
  }]
}">

  {{ content }}

</aside>