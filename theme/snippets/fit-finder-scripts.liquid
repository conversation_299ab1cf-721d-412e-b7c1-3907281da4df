{% comment %} 


This is generated by webpack so don't edit this unless you want to be sad when your work gets overwritten in production.
You can edit this file in ../../components/** 


{% endcomment %}






<script type="text/javascript">
	window.addEventListener('DOMContentLoaded', function(){
		_n.qs('#fitanalytics-size-advisor').style.display = 'none'
	  window._fitAnalytics = function() {
	    var widget = new FitAnalyticsWidget({
	      productSerial: "spyder-{{ product.id }}",
	      thumb: "http://fitanalytics-guide.herokuapp.com/img/spyder",
	      manufacturedSizes: {"S": true, "M": false, "L": true},
	      load: function (productSerial) {
	        _n.qs('#fitanalytics-size-advisor').addEventListener('click', function () {
	          widget.open();
	          return false;
	        });
	        _n.qs('#fitanalytics-size-advisor').style.display = 'block'
	      }
	    });
	  };
	})
</script>
<script type="text/javascript" src="//widget.fitanalytics.com/widget.js"></script>
