<form id="searchForm" class="relative flex items-center justify-center lg:p-4 py-4 px-2">

  <input type="search"
    id="Search-Trigger-{{ section.id }}"
    class="h-full m-0 w-full shadow-none lg:pl-8 cursor-pointer bg-transparent placeholder::text-black uppercase absolute top-0 left-0"
    readonly="readonly"
    right="left"
    autocomplete="off"
    aria-expanded="false"
    type="button"
    tabindex="-1"
    neptune-engage="{
      targets:[
        {
          attributes:[
            {
              att: 'aria-expanded',
              set:'true'
            }
          ]
        },
        {
          selector:html,
          attributes:[
            {
              att:data-active-modal,
              set:search
            }
          ]
        },
        {
          selector:'[data-modal=search]',
          attributes:[
            {
              att: 'aria-expanded',
              set:'true'
            }
          ]
        },
        {
          selector:.modal-search-form,
          methods:reset
        },
        {
          selector:'#Search-header',
          focus:true
        },
        {
          selector:.search-section,
          classes:remove:hidden
        },
        {
          selector:'#shopify-section-search',
          classes:toggle:active
        }
      ]
    }"
    >

    <span class="flex search__text items-center pointer-events-none block gap-[7px]">
      {% render 'icon' icon:'search', height:17, width:17 %}
      {% if show_search_text %}
        <span class="leading-[normal]">Search</span>
      {% endif %}
    </span>

    <button 
      type="button" 
      class="search-modal__trigger absolute top-0 left-0 button button--primary px-0 w-full transform translate-x-off focus:translate-x-0"
      aria-expanded="false"
      neptune-brig="{keys:[13]}" 
      onkeydown="if(event.key === 'Enter') { 
        modal.open( 'search', { returnFocus:this, aria:true });
        document.querySelector('#shopify-section-search').classList.toggle('active')
      }"
      x-neptune-engage="{
        preventDefault: true,
        targets:[
          {
            attributes:[
              {
                att: 'aria-expanded',
                set:'true'
              }
            ]
          },
          {
            selector:html,
            attributes:[
              {
                att:data-active-modal,
                set:search
              }
            ]
          },
          {
            selector:'[data-modal=search]',
            attributes:[
              {
                att: 'aria-expanded',
                set:'true'
              }
            ]
          },
          {
            selector:.modal-search-form,
            methods:reset
          },
          {
            selector:#Search-header,
            focus:true
          }
        ]
      }"
    >Open {{ 'general.search.search' | t }}</button>

</form>
