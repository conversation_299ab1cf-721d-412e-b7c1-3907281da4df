{% raw %}

    <article class="absolute min-h-full h-auto w-full top-0 left-0 cart-offers cart-offers__takeover" data-offer-index="{{ offerIndex }}">
      
      {% if offer.prompt != blank %}
        <p class="cart-offers__prompt">{{ offer.prompt | newline_to_br }}</p>
      {% endif %}

      {% for product in offer.products %}

        {% assign productIndex = forloop.index0 %}

        {% if product.in_cart %}{% continue %}{% endif %}

        {% unless offer.product_selection == 'all' %}

          {% if offer.products.size == 1 %}
            <input checked type="{{ offer.product_selection }}" id="{{ offer.title | handle }}-{{ product.handle }}" class="hidden" name="{{ offer.title | handle }}" onchange="
          CartOffers.select({{ offerIndex }}, {{ productIndex }}, this.checked)">
          {% else %}
            <input type="{{ offer.product_selection }}" id="{{ offer.title | handle }}-{{ product.handle }}" class="hidden" name="{{ offer.title | handle }}" onchange="
          CartOffers.select({{ offerIndex }}, {{ productIndex }}, this.checked)">
          {% endif %}

          
        
        {% endunless %}
        
        <label for="{{ offer.title | handle }}-{{ product.handle }}" class="takeover__item offer-item-{{ offer.product_selection }} group flex">
          
          {% endraw %}{% render 'offer-item' %}{% raw %}

        </label>
      
      {% endfor %}

      <div class="cart-offers__button-set w-full">
        <button id="addOffer" class="button button--primary w-full {% unless offer.products.size == 1 %}pointer-events-none opacity-50{% endunless %}" onclick="Neptune.cart.add(_n.qsa('.cart-offers .offer-item-all .variant, .cart-offers input:checked + label .variant').map(el=>{
              return {
                id:el.value,
                quantity:1,
                properties:{_gift:'{{ offer.title }}'}
              }
          })); CartOffers.complete('{{ offer.title }}'); return false;">
          <span>Select</span>
        </button>
        <button class="button button--link w-full" onclick="CartOffers.complete('{{ offer.title }}')">
          No, thanks
        </button>
      </div>

    </article>
{% endraw %}
