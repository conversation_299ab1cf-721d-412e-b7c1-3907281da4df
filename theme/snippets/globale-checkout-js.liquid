
{% comment %} PLEASE do not change this file {% endcomment %}   

{% assign shipping_country_code = localization.country.iso_code %} 
{% assign shipping_state_code = blank %}
{% assign billing_country_code = localization.country.iso_code %}

{% if checkout.shipping_address.country_code != blank %}    
    {% assign shipping_country_code = checkout.shipping_address.country_code %} 
    {% assign shipping_state_code = checkout.shipping_address.province_code %} 
{% else if checkout.billing_address.country_code != blank %}    
    {% assign shipping_country_code = checkout.billing_address.country_code %}  
    {% assign shipping_state_code = checkout.billing_address.province_code %}  
{% endif %}   

{% if checkout.billing_address.country_code != blank %}    
    {% assign billing_country_code = checkout.billing_address.country_code %} 
{% else if checkout.shipping_address.country_code != blank %}    
    {% assign billing_country_code = checkout.shipping_address.country_code %}  
{% endif %} 

<script>   
GLBE_PARAMS = {  
        environment: "www.bglobale.com",   
        siteId: "212b00eaaa7c",   
        isTokenEnabled: "true", 
        billingCountry: "{{ billing_country_code }}",  
        shippingCountry: "{{ shipping_country_code }}",    
        shippingStateCode: "{{ shipping_state_code }}", 
        shop: "{{ shop.permanent_domain}}",        
        culture: "{{ locale }}",   
        merchantId: "1323",   
        operatedCountries: ["AE","AT","AU","BE","BR","CA","CH","CN","DE","DK","ES","FI","FR","GB","GR","HK","IE","IL","IN","IT","JP","KR","LU","MX","NL","NO","NZ","PT","SE","SG","TW"],   
        isOperatedCountry: () => GLBE_PARAMS.operatedCountries.indexOf(GLBE_PARAMS.shippingCountry) > -1,   
        checkoutId: Shopify.Checkout.token,   
        amount: {{ checkout.total_price }},  
        integrationAppUrl: "https://crossborder-integration-qa-int.bglobale.com/",
        suppressNativeAmazonPay: "true"
};     
</script> 

{% if "AE,AT,AU,BE,BR,CA,CH,CN,DE,DK,ES,FI,FR,GB,GR,HK,IE,IL,IN,IT,JP,KR,LU,MX,NL,NO,NZ,PT,SE,SG,TW" contains shipping_country_code %} 
<script src = "https://www.bglobale.com/scripts/platforms/shopify/checkout.js" ></script>  
{% endif %}
