

{% raw %}
  {% assign badges = productBadges | where: 'type', 'image' %}
  {% assign exclusion_badge = {% endraw %}{{ excluded_item_badge }}{% raw %} %}
  {% assign active_collection_exclusion = false %}

  {% if exclusion_badge != false and collection and collection.handle != 'search' %}
    {% assign active_collection_exclusion = true %}
  {% endif %}  

  {% for badge in badges %}
    {% assign excluded_collections = badge.excluded_collection | replace: ', ', ',' | replace: ' ,', ',' %}
    {% assign collection_to_check = collection.handle %}

    {% if excluded_collections contains ',' %}
      {% assign excluded_collections = excluded_collections | split: ',' %}
    {% endif %}


    {% if active_collection_exclusion %}
      {% unless excluded_collections contains collection_to_check %}
        {% if product.tags contains badge.tag %}
          {% assign badge_style = '' %}
          {% assign badge_style_mobile = '' %}
          
          {% if badge.image_width != 0 %}
            {% assign badge_style = badge_style | append: '--badge-image-width: ' | append: badge.image_width | append: 'px;' %}
          {% endif %}
          
          {% if badge.image_width_mobile != 0 %}
            {% assign badge_style_mobile = badge_style_mobile | append: '--badge-image-width-mobile: ' | append: badge.image_width_mobile | append: 'px;' %}
          {% endif %}
  
          {% if badge.image_width != 0 or  badge.image_width_mobile != 0%}
            <img class="product-item-image-badge h-auto absolute {% if badge.image_width != 0 %}custom_width{% endif %} {% if badge.image_width_mobile != 0 %}custom_width_mobile{% endif %}" src="{% endraw %}{{ '' | file_url | split: '?' | first }}{% raw %}{{ badge.image }}" alt="{{ tag }}" data-image-badge-position="{{badge.position}}" style="--badge-image-width: {{ badge.image_width }}px; --badge-image-width-mobile: {{ badge.image_width_mobile }}px;">
          {% else %}
            <img class="product-item-image-badge h-auto absolute" src="{% endraw %}{{ '' | file_url | split: '?' | first }}{% raw %}{{ badge.image }}" alt="{{ tag }}" data-image-badge-position="{{badge.position}}">
          {% endif %}
  
        {% endif %}
      {% endunless %}

    {% else %}
      {% if product.tags contains badge.tag %}
        {% assign badge_style = '' %}
        {% assign badge_style_mobile = '' %}
        
        {% if badge.image_width != 0 %}
          {% assign badge_style = badge_style | append: '--badge-image-width: ' | append: badge.image_width | append: 'px;' %}
        {% endif %}
        
        {% if badge.image_width_mobile != 0 %}
          {% assign badge_style_mobile = badge_style_mobile | append: '--badge-image-width-mobile: ' | append: badge.image_width_mobile | append: 'px;' %}
        {% endif %}

        {% if badge.image_width != 0 or  badge.image_width_mobile != 0%}
          <img class="product-item-image-badge h-auto absolute {% if badge.image_width != 0 %}custom_width{% endif %} {% if badge.image_width_mobile != 0 %}custom_width_mobile{% endif %}" src="{% endraw %}{{ '' | file_url | split: '?' | first }}{% raw %}{{ badge.image }}" alt="{{ tag }}" data-image-badge-position="{{badge.position}}" style="--badge-image-width: {{ badge.image_width }}px; --badge-image-width-mobile: {{ badge.image_width_mobile }}px;">
        {% else %}
          <img class="product-item-image-badge h-auto absolute" src="{% endraw %}{{ '' | file_url | split: '?' | first }}{% raw %}{{ badge.image }}" alt="{{ tag }}" data-image-badge-position="{{badge.position}}">
        {% endif %}
      {% endif %}
    {% endif %}

  {% endfor %}
  
  <style>
    @media (min-width: 1024px) {
      .product-item-image-badge.custom_width{
        width: 100%;
        max-width: var(--badge-image-width);
      }
    }
    @media (max-width: 1023px) {
      .product-item-image-badge.custom_width_mobile{
        width: 100%;
        max-width: var(--badge-image-width-mobile);
      }
    }
  </style>  
{% endraw %}