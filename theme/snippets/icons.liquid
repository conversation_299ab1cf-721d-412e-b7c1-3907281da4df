{% comment %} 



This icons file has custom icons mixed in with the feather icons



{% endcomment %}



<svg class="hidden" xmlns="http://www.w3.org/2000/svg">
	<defs>

		<symbol id="icon-def-account" viewBox="0 0 24 24"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></symbol>

		<symbol id="icon-def-account-alt" viewBox="0 0 18 18"><path d="M9,0a9,9,0,1,0,9,9A9.01,9.01,0,0,0,9,0ZM9,17a8,8,0,1,1,8-8A8.0092,8.0092,0,0,1,9,17ZM6.8351,8.0281c.4441,0,.8041-.7613.8041-1.7s-.36-1.7-.8041-1.7-.8041.7613-.8041,1.7S6.391,8.0281,6.8351,8.0281Zm4.33,0c.4441,0,.8041-.7613.8041-1.7s-.36-1.7-.8041-1.7-.8041.7613-.8041,1.7S10.7212,8.0281,11.1653,8.0281ZM13.5,9v1h.4438A4.8268,4.8268,0,0,1,9,14a4.8268,4.8268,0,0,1-4.9438-4H4.5V9h-2v1h.5276A5.8289,5.8289,0,0,0,9,15a5.8289,5.8289,0,0,0,5.9724-5H15.5V9Z"/></symbol>

		<symbol id="icon-def-accessibility" viewBox="0 0 24 24"><path d="M12,0A12,12,0,1,0,24,12,12,12,0,0,0,12,0Zm0,2.5479A2.2685,2.2685,0,1,1,9.7315,4.8164,2.2686,2.2686,0,0,1,12,2.5479Zm8.3122,6.7829-5.4107,1.3527,1.0639,9.9318a.7562.7562,0,0,1-1.4572.3532L12,14.4761,9.4918,20.9685a.7563.7563,0,0,1-1.4574-.3532l1.0641-9.9318L3.6878,9.3308a.7561.7561,0,0,1,.1834-1.49H20.1288a.7561.7561,0,0,1,.1834,1.49Z"/></symbol>

		<symbol id="icon-def-award" viewBox="0 0 24 24"><circle cx="12" cy="8" r="7"></circle><polyline points="8.21 13.89 7 23 12 20 17 23 15.79 13.88"></polyline></symbol>

		<symbol id="icon-def-alert-circle" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></symbol>

		<symbol id="icon-def-alert-triangle" viewBox="0 0 24 24"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></symbol>
		
		<symbol id="icon-def-arrow-down-circle" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"></circle><polyline points="8 12 12 16 16 12"></polyline><line x1="12" y1="8" x2="12" y2="16"></line></symbol>


		<symbol id="icon-def-bar-chart" viewBox="0 0 24 24"><line x1="12" y1="20" x2="12" y2="10"></line><line x1="18" y1="20" x2="18" y2="4"></line><line x1="6" y1="20" x2="6" y2="16"></line></symbol>


		<symbol id="icon-def-bluetooth" viewBox="0 0 24 24"><polyline points="6.5 6.5 17.5 17.5 12 23 12 1 17.5 6.5 6.5 17.5"></polyline></symbol>

		<symbol id="icon-def-check" viewBox="0 0 24 24"><polyline points="20 6 9 17 4 12"></polyline></symbol>
		<symbol id="icon-def-close" viewBox="0 0 24 24"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></symbol>

		<symbol id="icon-def-arrow-down-left" viewBox="0 0 24 24"><line x1="17" y1="7" x2="7" y2="17"></line><polyline points="17 17 7 17 7 7"></polyline></symbol>
		<symbol id="icon-def-arrow-down-right" viewBox="0 0 24 24"><line x1="7" y1="7" x2="17" y2="17"></line><polyline points="17 7 17 17 7 17"></polyline></symbol>
		<symbol id="icon-def-arrow-down" viewBox="0 0 24 24"><line x1="12" y1="5" x2="12" y2="19"></line><polyline points="19 12 12 19 5 12"></polyline></symbol>
		<symbol id="icon-def-arrow-left-circle" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"></circle><polyline points="12 8 8 12 12 16"></polyline><line x1="16" y1="12" x2="8" y2="12"></line></symbol>
		<symbol id="icon-def-arrow-left" viewBox="0 0 24 24"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></symbol>
		<symbol id="icon-def-arrow-right-circle" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"></circle><polyline points="12 16 16 12 12 8"></polyline><line x1="8" y1="12" x2="16" y2="12"></line></symbol>
		<symbol id="icon-def-arrow-right" viewBox="0 0 24 24"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></symbol>
		<symbol id="icon-def-arrow-up-circle" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"></circle><polyline points="16 12 12 8 8 12"></polyline><line x1="12" y1="16" x2="12" y2="8"></line></symbol>
		<symbol id="icon-def-arrow-up-left" viewBox="0 0 24 24"><line x1="17" y1="17" x2="7" y2="7"></line><polyline points="7 17 7 7 17 7"></polyline></symbol>
		<symbol id="icon-def-arrow-up-right" viewBox="0 0 24 24"><line x1="7" y1="17" x2="17" y2="7"></line><polyline points="7 7 17 7 17 17"></polyline></symbol>
		<symbol id="icon-def-arrow-up" viewBox="0 0 24 24"><line x1="12" y1="19" x2="12" y2="5"></line><polyline points="5 12 12 5 19 12"></polyline></symbol>
		<symbol id="icon-def-arrow-wide-right" viewBox="0 0 32 32"><path d="M22,9a1,1,0,0,0,0,1.42l4.6,4.6H3.06a1,1,0,1,0,0,2H26.58L22,21.59A1,1,0,0,0,22,23a1,1,0,0,0,1.41,0l6.36-6.36a.88.88,0,0,0,0-1.27L23.42,9A1,1,0,0,0,22,9Z"/></symbol>
		<symbol id="icon-def-arrow-wide-left" viewBox="0 0 32 32"><path d="M10.1,23a1,1,0,0,0,0-1.41L5.5,17H29.05a1,1,0,0,0,0-2H5.53l4.57-4.57A1,1,0,0,0,8.68,9L2.32,15.37a.9.9,0,0,0,0,1.27L8.68,23A1,1,0,0,0,10.1,23Z"/></symbol>
   
		<symbol id="icon-def-arrows-width" viewBox="0 0 24 24"><polyline points="15 3 21 3 21 9" style="transform:rotate(45deg);"></polyline><polyline points="9 21 3 21 3 15"></polyline><line x1="21" y1="3" x2="14" y2="10"></line><line x1="3" y1="21" x2="10" y2="14"></line></symbol>

		<symbol id="icon-def-arrow-large-left" fill="none" viewBox="0 0 16 4" id="svg-7902q5e8e" xmlns="http://www.w3.org/2000/svg">
			<path d="M.823 1.823a.25.25 0 0 0 0 .354l1.591 1.59a.25.25 0 0 0 .354-.353L1.354 2 2.768.586a.25.25 0 1 0-.354-.354L.824 1.823ZM1 2.25h15v-.5H1v.5Z" fill="#000"/>
		</symbol>

		<symbol id="icon-def-cart" viewBox="0 0 19 19">
			<path d="M7 0.5H11C12.3807 0.5 13.5 1.61929 13.5 3V4.5H4.5V3C4.5 1.61929 5.61929 0.5 7 0.5Z" stroke="currentColor"/>
			<rect x="0.5" y="4.5" width="18" height="14" stroke="currentColor"/>
		</symbol>

		<symbol id="icon-def-check" viewBox="0 0 24 24"><polyline points="20 6 9 17 4 12"></polyline></symbol>
		
		<symbol id="icon-def-chevron-down" viewBox="0 0 24 24"><polyline points="6 9 12 15 18 9"></polyline></symbol>
		<symbol id="icon-def-chevron-left" viewBox="0 0 24 24"><polyline points="15 18 9 12 15 6"></polyline></symbol>
		<symbol id="icon-def-chevron-right" viewBox="0 0 24 24"><polyline points="9 18 15 12 9 6"></polyline></symbol>
		<symbol id="icon-def-chevron-up" viewBox="0 0 24 24"><polyline points="18 15 12 9 6 15"></polyline></symbol>

		<symbol id="icon-def-dollar-sign" viewBox="0 0 24 24"><line x1="12" y1="1" x2="12" y2="23"></line><path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path></symbol>

		<symbol id="icon-def-email" viewBox="0 0 24 24"><path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path><polyline points="22,6 12,13 2,6"></polyline></symbol>

		<symbol id="icon-def-exit" viewBox="0 0 17 16"><line y1="-0.5" x2="20.8373" y2="-0.5" transform="matrix(0.719862 0.694117 -0.719862 0.694117 0 1)" stroke="currentColor"/><line y1="-0.5" x2="20.8373" y2="-0.5" transform="matrix(0.719862 -0.694117 0.719862 0.694117 1.41422 15.4636)" stroke="currentColor"/></symbol>

		<symbol id="icon-def-exit-sm" viewBox="0 0 12.0107 11.3271"><polygon points="12.011 0.72 11.317 0 6.006 5.121 0.694 0 0 0.72 5.285 5.816 0.317 10.606 1.012 11.327 6.006 6.511 11.001 11.327 11.694 10.606 6.726 5.816 12.011 0.72"/></symbol>

		<symbol id="icon-def-eye" viewBox="0 0 24 24"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle></symbol>

		<symbol id="icon-def-eye-secondary" viewBox="0 0 21 12">
			<path d="M20.606 5.74983C19.5704 4.03119 18.0983 2.58915 16.3483 1.58441C14.5428 0.547119 12.4798 0 10.3819 0C8.28392 0 6.22098 0.547119 4.41539 1.58441C2.66539 2.58915 1.19333 4.03119 0.157742 5.74983C0.0650951 5.90441 0.0650951 6.09763 0.157742 6.25017C1.19333 7.97085 2.66539 9.41085 4.41539 10.4156C6.21892 11.4508 8.28186 12 10.3819 12C12.4819 12 14.5428 11.4529 16.3483 10.4156C18.0983 9.41085 19.5704 7.97085 20.606 6.25017C20.6986 6.09559 20.6986 5.90441 20.606 5.74983ZM10.3819 10.9342C7.62716 10.9342 5.38922 8.72136 5.38922 6.00203C5.38922 3.28271 7.62922 1.0678 10.3819 1.0678C13.1345 1.0678 15.3745 3.28068 15.3745 6C15.3745 8.71932 13.1345 10.9322 10.3819 10.9322V10.9342Z" fill="black"/>
			<path d="M10.3818 3.53296C9.0044 3.53296 7.8844 4.6394 7.8844 6.00008C7.8844 7.36076 9.0044 8.4672 10.3818 8.4672C11.7591 8.4672 12.8791 7.36076 12.8791 6.00008C12.8791 4.6394 11.7591 3.53296 10.3818 3.53296Z" fill="black"/>
		</symbol>

		<symbol id="icon-def-eye-linethrough" viewBox="0 0 20 18">
			<path d="M19.9325 8.88949C18.9264 10.5796 17.4964 11.9976 15.7963 12.9857C15.3123 13.2677 14.8082 13.5137 14.2882 13.7197L13.3922 12.1697C14.2922 11.2736 14.8502 10.0235 14.8502 8.64348C14.8502 5.94934 12.7181 3.75323 10.08 3.70723C10.052 3.70723 10.024 3.70723 9.998 3.70723C9.972 3.70723 9.944 3.70723 9.916 3.70723C9.46797 3.71523 9.03395 3.78524 8.62193 3.90924L8.0439 2.90919C8.66193 2.80319 9.28996 2.74718 9.922 2.74318C9.946 2.74318 9.972 2.74318 9.998 2.74318C10.024 2.74318 10.048 2.74318 10.074 2.74318C12.0881 2.75718 14.0642 3.29321 15.7963 4.30126C17.4964 5.28931 18.9264 6.70538 19.9325 8.39747C20.0225 8.54947 20.0225 8.73748 19.9325 8.88949ZM12.4261 8.64348C12.4261 7.30541 11.3381 6.21736 10 6.21736C9.986 6.21736 9.97 6.21736 9.956 6.21736L12.0781 9.89354C12.2981 9.52752 12.4261 9.1015 12.4261 8.64348ZM15.1303 16.5659C15.2643 16.7959 15.1843 17.0899 14.9542 17.2239C14.8782 17.2679 14.7962 17.2879 14.7142 17.2879C14.5482 17.2879 14.3862 17.2019 14.2962 17.0479L12.6701 14.2338C12.4321 14.2898 12.1941 14.3378 11.9541 14.3798C11.3361 14.4858 10.706 14.5418 10.074 14.5458C10.05 14.5458 10.024 14.5458 9.998 14.5458C9.972 14.5458 9.948 14.5458 9.922 14.5458C7.9079 14.5318 5.9318 13.9957 4.20171 12.9877C2.50363 11.9976 1.07355 10.5816 0.0675034 8.88949C-0.0225011 8.73748 -0.0225011 8.54947 0.0675034 8.39747C1.07355 6.70538 2.50363 5.28931 4.20371 4.30126C4.68773 4.01925 5.19376 3.77323 5.71179 3.56722C5.9298 3.48122 6.14981 3.39922 6.37382 3.32721L4.86974 0.723083C4.73574 0.493071 4.81574 0.199056 5.04575 0.0650497C5.27576 -0.068957 5.56978 0.011047 5.70379 0.241058L7.32987 3.0572L7.9679 4.16325L9.22796 6.34536L11.6061 10.4636L12.8581 12.6337L13.6262 13.9637L15.1303 16.5679V16.5659ZM12.0321 13.1257L10.772 10.9436C10.542 11.0216 10.298 11.0636 10.044 11.0696C10.03 11.0696 10.014 11.0696 10 11.0696C8.66193 11.0696 7.57388 9.98155 7.57388 8.64348C7.57388 8.18746 7.70189 7.75943 7.9219 7.39342C8.0499 7.18141 8.20991 6.9894 8.39592 6.82539L7.14386 4.65528C6.95585 4.79529 6.77584 4.94929 6.60983 5.1173C5.70979 6.01335 5.14976 7.26341 5.14976 8.64348C5.14976 11.3376 7.28186 13.5357 9.92 13.5797C9.948 13.5797 9.974 13.5797 10.002 13.5797C10.03 13.5797 10.058 13.5797 10.084 13.5797C10.532 13.5717 10.966 13.5017 11.3781 13.3777C11.6041 13.3097 11.8241 13.2257 12.0341 13.1257H12.0321Z" fill="black"/>
		  </symbol>

		<symbol id="icon-def-information" viewBox="0 0 18 18">
			<circle cx="9" cy="9" r="8.5" stroke="#E92A14"/>
			<circle cx="9" cy="5" r="1" fill="#E92A14"/>
			<rect x="8" y="7" width="2" height="8" rx="1" fill="#E92A14"/>
		</symbol>
		
		<symbol id="icon-def-facebook" viewBox="0 0 10.0004 20"><path d="M9.391,6.7431H6.8088c0-.6853-.0009-1.3182,0-1.952.0018-.8044.2742-1.088,1.0835-1.104.5431-.0106,1.0879-.0106,1.631,0,.3261.0063.4831-.1049.4769-.4631-.0159-.9182.0035-1.8364-.0141-2.7546A1.5028,1.5028,0,0,0,9.8063,0H6.4553c-.1111.0284-.22.0649-.3333.0845A3.9751,3.9751,0,0,0,2.578,4.3582a19.917,19.917,0,0,1,.0141,2.3156c-.0758.0364-.1014.0595-.1269.0595Q1.5171,6.74.57,6.744c-.5448.0026-.5633.0195-.5633.584C.0047,8.26.0178,9.1946,0,10.1271c-.0071.3822.1269.5369.5113.5253.6427-.0186,1.2853-.0053,1.9738-.0053v.5191c0,2.7405-.0034,5.4809.008,8.2214a2.4151,2.4151,0,0,0,.17.6124H6.6316A2.4059,2.4059,0,0,0,6.8,19.3876c.0115-2.7263.0079-5.4516.0079-8.1769v-.5325c.8349,0,1.5975-.0071,2.36.0036.313.0044.4416-.1245.4743-.4391.097-.9423.22-1.8809.3315-2.82C10.0452,6.8214,9.9782,6.7431,9.391,6.7431Z"/></symbol>

		<symbol id="icon-def-heart" viewBox="0 0 24 24"><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path></symbol>

		<symbol id="icon-def-instagram" viewBox="0 0 20 20"><path d="M19.94,5.877A7.3394,7.3394,0,0,0,19.4754,3.45a4.8953,4.8953,0,0,0-1.1535-1.7711A4.9108,4.9108,0,0,0,16.5507.525,7.3389,7.3389,0,0,0,14.1233.06C13.0568.0116,12.7163,0,10,0S6.9439.0116,5.877.06A7.3366,7.3366,0,0,0,3.45.5247,4.8927,4.8927,0,0,0,1.6785,1.6782,4.9023,4.9023,0,0,0,.525,3.4493,7.3522,7.3522,0,0,0,.06,5.877C.0112,6.9435,0,7.2841,0,10s.0116,3.0563.06,4.1231a7.3378,7.3378,0,0,0,.465,2.4274,4.8949,4.8949,0,0,0,1.1535,1.7712A4.9117,4.9117,0,0,0,3.45,19.4754,7.3394,7.3394,0,0,0,5.877,19.94C6.9435,19.9887,7.2841,20,10,20s3.0563-.0116,4.1231-.06a7.3389,7.3389,0,0,0,2.4274-.4646,5.1144,5.1144,0,0,0,2.9247-2.9247,7.3389,7.3389,0,0,0,.4646-2.4274c.0487-1.0665.06-1.407.06-4.1231S19.9884,6.9439,19.94,5.877Zm-1.8,8.1646a5.5418,5.5418,0,0,1-.3443,1.857,3.3123,3.3123,0,0,1-1.8979,1.8979,5.53,5.53,0,0,1-1.857.3442c-1.0541.048-1.3706.0585-4.041.0585s-2.9865-.01-4.041-.0585a5.5289,5.5289,0,0,1-1.857-.3442,3.0991,3.0991,0,0,1-1.15-.7481,3.0974,3.0974,0,0,1-.7481-1.15,5.5288,5.5288,0,0,1-.3443-1.857c-.048-1.0545-.0585-1.3706-.0585-4.041S1.8116,7.014,1.86,5.96a5.5424,5.5424,0,0,1,.3443-1.857,3.097,3.097,0,0,1,.7481-1.15,3.1,3.1,0,0,1,1.15-.7481A5.5282,5.5282,0,0,1,5.9592,1.86C7.0137,1.8124,7.33,1.8019,10,1.8019s2.9865.01,4.041.0585a5.5366,5.5366,0,0,1,1.857.3443,3.0987,3.0987,0,0,1,1.15.7481,3.1015,3.1015,0,0,1,.7481,1.15A5.532,5.532,0,0,1,18.14,5.96c.048,1.0545.0585,1.3707.0585,4.0411S18.1887,12.9871,18.14,14.0416ZM10,4.8649A5.1353,5.1353,0,1,0,15.1355,10,5.1354,5.1354,0,0,0,10,4.8649Zm0,8.4687A3.3334,3.3334,0,1,1,13.3336,10,3.3335,3.3335,0,0,1,10,13.3336ZM15.3384,3.462a1.2,1.2,0,1,0,1.2,1.2A1.2,1.2,0,0,0,15.3384,3.462Z" /></symbol>

		<symbol id="icon-def-location" viewBox="0 0 18 18"><path d="M18,9A9.0069,9.0069,0,0,0,9.1283.0065C9.1031.0059,9.0789,0,9.0537,0l-.02.0017L9,0A9,9,0,0,0,9,18l.0339-.0017.02.0017c.0252,0,.0494-.0059.0746-.0065A9.0065,9.0065,0,0,0,18,9Zm-1.0253-.5h-2.881a11.7116,11.7116,0,0,0-2.1593-6.9351A8.0054,8.0054,0,0,1,16.9747,8.5ZM8.5,1.0876V8.5H5.0133C5.1376,4.7524,6.6365,1.6169,8.5,1.0876ZM8.5,9.5v7.4124C6.6365,16.3831,5.1376,13.248,5.0133,9.5Zm1,7.4458V9.5h3.5941C12.9674,13.32,11.4132,16.51,9.5,16.9458ZM9.5,8.5V1.0542C11.4132,1.49,12.9674,4.68,13.0941,8.5ZM6.2159,1.51A11.643,11.643,0,0,0,4.0137,8.5H1.0253A8.0038,8.0038,0,0,1,6.2159,1.51ZM1.0253,9.5H4.0137a11.6427,11.6427,0,0,0,2.2022,6.99A8.0039,8.0039,0,0,1,1.0253,9.5Zm10.9091,6.9351A11.7113,11.7113,0,0,0,14.0937,9.5h2.881A8.0054,8.0054,0,0,1,11.9344,16.4351Z"/></symbol>

		<symbol id="icon-def-logout" viewBox="0 0 24 24"><path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path><polyline points="16 17 21 12 16 7"></polyline><line x1="21" y1="12" x2="9" y2="12"></line></symbol>

		<symbol id="icon-def-layout" viewBox="0 0 24 24"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="3" y1="9" x2="21" y2="9"></line><line x1="9" y1="21" x2="9" y2="9"></line></symbol>

		<symbol id="icon-def-map-pin" viewBox="0 0 24 24"><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle></symbol>

		<symbol id="icon-def-menu" style="margin-top:4px;" viewBox="0 0 25 14"><line y1="0.5" x2="25" y2="0.5" stroke="currentColor"/><line y1="7.5" x2="25" y2="7.5" stroke="currentColor"/></symbol>

		<symbol id="icon-def-message-square" viewBox="0 0 24 24"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></symbol>

		<symbol id="icon-def-move" viewBox="0 0 24 24"><polyline points="5 9 2 12 5 15"></polyline><polyline points="9 5 12 2 15 5"></polyline><polyline points="15 19 12 22 9 19"></polyline><polyline points="19 9 22 12 19 15"></polyline><line x1="2" y1="12" x2="22" y2="12"></line><line x1="12" y1="2" x2="12" y2="22"></line></symbol>

		<symbol id="icon-def-minus" viewBox="0 0 24 24"><line x1="5" y1="12" x2="19" y2="12"></line></symbol>

		<symbol id="icon-def-package" viewBox="0 0 24 24"><line x1="16.5" y1="9.4" x2="7.5" y2="4.21"></line><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line></symbol>

		<symbol id="icon-def-phone" viewBox="0 0 24 24"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></symbol>

		<symbol id="icon-def-pinterest" viewBox="0 0 19.86 20.02"><path d="M11.33,1a10,10,0,0,0-3.66,19.4,9.27,9.27,0,0,1,0-2.88l1.18-5A3.46,3.46,0,0,1,8.55,11c0-1.4.81-2.44,1.82-2.44a1.26,1.26,0,0,1,1.26,1.24c0,.06,0,.12,0,.17a20.25,20.25,0,0,1-.83,3.35,1.45,1.45,0,0,0,1.06,1.77,1.24,1.24,0,0,0,.43,0c1.78,0,3.13-1.88,3.13-4.61a4,4,0,0,0-3.84-4.09h-.36a4.38,4.38,0,0,0-4.5,4.22v.16a3.86,3.86,0,0,0,.75,2.3.31.31,0,0,1,.07.29l-.28,1.14c0,.18-.14.23-.34.13-1.25-.58-2-2.42-2-3.89,0-3.17,2.3-6.08,6.64-6.08a5.91,5.91,0,0,1,6.18,5.63c0,.06,0,.12,0,.18,0,3.47-2.18,6.26-5.22,6.26a2.66,2.66,0,0,1-2.3-1.15L9.59,18a11,11,0,0,1-1.25,2.63A10,10,0,1,0,13.52,1.35,10.1,10.1,0,0,0,11.34,1Z" transform="translate(-1.07 -0.99)"/></symbol>

		<symbol id="icon-def-plus" viewBox="0 0 24 24"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></symbol>

		<symbol id="icon-def-search"  viewBox="0 0 17 17"><circle cx="7.5" cy="7.5" r="7" stroke="currentColor"/><line x1="12.3536" y1="12.6464" x2="16.3536" y2="16.6464" stroke="currentColor"/></symbol>

		<symbol id="icon-def-shopping-circle" viewBox="0 0 17 17"><circle cx="8.5" cy="8.5" r="8" stroke="currentColor"/></symbol>

		<symbol id="icon-def-shuffle" viewBox="0 0 24 24"><polyline points="16 3 21 3 21 8"></polyline><line x1="4" y1="20" x2="21" y2="3"></line><polyline points="21 16 21 21 16 21"></polyline><line x1="15" y1="15" x2="21" y2="21"></line><line x1="4" y1="4" x2="9" y2="9"></line></symbol>

		<symbol id="icon-def-smartphone" viewBox="0 0 24 24"><rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect><line x1="12" y1="18" x2="12.01" y2="18"></line></symbol>
		<symbol id="icon-def-sort" viewBox="0 0 24 24"><path d="M7.2073,11.8436a1.0034,1.0034,0,0,0-1.4146,0L1.55,16.0859A1,1,0,0,0,2.9644,17.5L5.5,14.9647V22.45a1,1,0,0,0,2,0V14.9647L10.0356,17.5A1,1,0,0,0,11.45,16.0859ZM22.4492,6.5a1,1,0,0,0-1.414,0L18.5,9.0355V1.55a1,1,0,0,0-2,0V9.0355L13.9648,6.5a1,1,0,1,0-1.414,1.4141l4.2413,4.2418a1.0016,1.0016,0,0,0,1.4158,0l4.2413-4.2418A1,1,0,0,0,22.4492,6.5Z" transform="translate(-1.2573 -0.5503)"/></symbol>
		<symbol id="icon-def-tablet" viewBox="0 0 24 24"><rect x="4" y="2" width="16" height="20" rx="2" ry="2"></rect><line x1="12" y1="18" x2="12.01" y2="18"></line></symbol>

		<symbol id="icon-def-sliders" viewBox="0 0 24 24"><line x1="4" y1="21" x2="4" y2="14"></line><line x1="4" y1="10" x2="4" y2="3"></line><line x1="12" y1="21" x2="12" y2="12"></line><line x1="12" y1="8" x2="12" y2="3"></line><line x1="20" y1="21" x2="20" y2="16"></line><line x1="20" y1="12" x2="20" y2="3"></line><line x1="1" y1="14" x2="7" y2="14"></line><line x1="9" y1="8" x2="15" y2="8"></line><line x1="17" y1="16" x2="23" y2="16"></line></symbol>

		<symbol id="icon-def-tiktok" viewBox="0 0 18.1814 20"><path d="M18.12,5.0856c-.1812-.0086-.3627-.0158-.5436-.0287-.1935-.0137-.3866-.0318-.58-.0511-.0657-.0066-.1305-.0222-.1954-.0347-.0938-.0181-.188-.0349-.281-.0565-.0785-.0183-.1556-.0427-.2333-.0645-.1144-.0322-.2308-.0586-.3424-.0983-.1236-.0438-.2441-.097-.3635-.1513-.1312-.06-.2634-.1187-.3882-.19a3.8958,3.8958,0,0,1-.5979-.4262,3.591,3.591,0,0,1-.5862-.69c-.0667-.0947-.1266-.1943-.1851-.2943q-.1029-.1755-.1971-.3557c-.0386-.0736-.07-.1507-.1041-.2266-.0367-.082-.0764-.163-.1074-.2471-.0367-.0993-.0661-.2011-.0975-.3021-.04-.1272-.0833-.2532-.1155-.382-.0342-.1364-.0626-.2747-.0832-.4137-.0232-.1577-.0408-.3166-.05-.4756C13.06.4458,13.0641.293,13.0645.1405c0-.0476-.0182-.0686-.0687-.07C12.8976.069,12.8.06,12.7015.0579c-.5851-.0146-1.17-.03-1.7555-.0415C10.5534.0085,10.1606.0064,9.7678,0c-.1241-.0018-.1241-.0034-.1241.1165q0,6.5813-.0008,13.1622c0,.2668-.01.5335-.017.8a.5044.5044,0,0,1-.0116.0862c-.0239.1185-.0475.2371-.0739.3551-.0089.04-.0259.0783-.0389.1175-.0271.0816-.05.1644-.0818.2444a2.6239,2.6239,0,0,1-.2108.421,2.7717,2.7717,0,0,1-.3563.46,2.2668,2.2668,0,0,1-.255.2327c-.111.0853-.2293.1622-.3486.2365a2.561,2.561,0,0,1-.2664.1418c-.0792.0373-.1619.0673-.2444.0973-.0752.0272-.1515.0525-.2286.0742-.0738.0209-.1488.0386-.2242.0531a2.8886,2.8886,0,0,1-.3972.0674,5.7944,5.7944,0,0,1-.672,0c-.1775-.0118-.3526-.0551-.5288-.0847a.1861.1861,0,0,1-.0259-.01c-.062-.0215-.1245-.0419-.186-.0644-.127-.0464-.2573-.0864-.3793-.1431-.117-.0545-.226-.1258-.3374-.1917a2.3522,2.3522,0,0,1-.5416-.45,4.4706,4.4706,0,0,1-.2934-.3716,1.7629,1.7629,0,0,1-.1361-.2283,2.37,2.37,0,0,1-.1011-.2262c-.0395-.1006-.0785-.2018-.11-.3049-.0251-.0822-.04-.1677-.0568-.2521a2.4838,2.4838,0,0,1-.0639-.3348,3.9851,3.9851,0,0,1,.0364-1.0255,1.31,1.31,0,0,1,.05-.1993c.0485-.1332.1-.2654.1576-.3953a2.0219,2.0219,0,0,1,.1442-.2785,3.07,3.07,0,0,1,.25-.3425,4.2056,4.2056,0,0,1,.3783-.3943,2.8908,2.8908,0,0,1,.4164-.3024,4.1692,4.1692,0,0,1,.5541-.275,1.0857,1.0857,0,0,1,.1345-.0478c.0706-.02.1425-.0346.2138-.0518a1.9,1.9,0,0,1,.2943-.0686,2.5215,2.5215,0,0,1,.7355-.02c.0429.0092.0888.0045.1327.0106.1094.0152.2185.0327.3276.05a.2263.2263,0,0,1,.0256.01c.0789.0231.1569.05.2372.0675.0745.0161.0805.0071.0805-.0711q0-.3132,0-.6262V7.4461c0-.0745-.0126-.0969-.0741-.1065-.1221-.0191-.2445-.0384-.3674-.0487-.1439-.012-.2885-.0154-.4329-.02-.1164-.0041-.2332-.01-.35-.0066-.17.0045-.34.0158-.5105.0255-.0385.0022-.0771.0085-.1153.0144-.14.0222-.28.0443-.42.0676-.0924.0153-.1848.0308-.2766.05-.0626.013-.1241.0314-.1857.049-.0773.0221-.1539.0464-.231.0688-.0632.0184-.128.032-.19.0539-.0945.0337-.1874.0719-.28.1095-.149.06-.3024.1125-.4453.1845q-.3431.173-.6737.3687c-.1638.0965-.3218.2037-.4753.3154-.1344.0978-.2592.2084-.3875.3141a5.8945,5.8945,0,0,0-.8718.9081c-.1338.1695-.2553.3487-.3759.5278-.0779.1156-.147.2372-.2146.359-.06.1087-.1145.2207-.1692.3323-.0349.0714-.0683.1438-.0982.2174-.0359.0881-.0679.1778-.1006.2672-.0383.1049-.0777.21-.1128.3157-.0292.0886-.054.1787-.0785.2688-.019.0695-.0378.1393-.051.21-.0216.1152-.04.2311-.057.3472-.0254.1774-.0556.3546-.07.533-.0123.1554-.009.3122-.0061.4682.0028.15.0083.3011.0224.4508.0161.1711.0421.3413.0658.5117.0152.109.0323.2179.0521.3263.0122.0672.03.1334.0475.2.0229.0889.0473.1775.0716.2661.0194.0711.038.1425.06.2129.0209.0668.0452.1326.0682.1987.0325.0936.06.1891.0988.28.0627.1475.13.2934.2006.4375.0716.146.1442.2921.2263.4327a4.237,4.237,0,0,0,.2454.3712c.1268.1728.2558.3445.3934.509a5.5229,5.5229,0,0,0,.7821.7524c.1125.0907.228.178.3466.2608.1093.0762.2236.1456.3374.2153.0779.0477.1578.0923.2383.1355.0676.0364.1369.07.2065.1027.1107.052.2217.1036.3337.1528.0753.0331.1523.0622.2289.0921.097.0378.193.0787.2919.1107.1129.0366.2288.0647.3433.0968.0582.0163.116.034.1746.0492.09.0233.1808.0488.2725.0662.114.0216.2291.0368.3439.0533a4.71,4.71,0,0,0,.5259.07c.3757.0109.752.0039,1.128.0029a1.0874,1.0874,0,0,0,.1268-.0139c.0716-.0086.1432-.0166.2144-.0264.1223-.0169.2446-.0338.3664-.0531.0708-.0114.1414-.0255.2112-.0421.0891-.0211.177-.0466.2658-.0688.0765-.0192.1547-.0324.23-.0555.1067-.0328.2118-.07.3164-.1092.1472-.0545.2952-.1075.4391-.1692.1681-.0719.3369-.1448.4973-.2315a6.2114,6.2114,0,0,0,.5642-.3387,7.4125,7.4125,0,0,0,.6071-.4614A5.2322,5.2322,0,0,0,11.65,17.7c.1266-.1593.2437-.326.3586-.4936a3.915,3.915,0,0,0,.2367-.3828c.0971-.1837.1839-.3728.2706-.5616.0564-.1226.1054-.2486.1564-.3735.0177-.0433.0336-.0874.0469-.1322.02-.0673.0367-.1354.0554-.2032q.0406-.1479.0823-.2956c.0039-.0139.01-.0272.0136-.0413.0224-.0909.0477-.1813.0655-.2731.0216-.1118.037-.2246.0545-.337a4.0853,4.0853,0,0,0,.07-.4822c.007-.2109.02-.4213.013-.6337q-.0364-1.1782-.04-2.3574c-.0044-1.2615.0024-2.5231.0047-3.7847,0-.0177.0017-.0355.0037-.0724.0553.029.0984.05.14.0733.0859.0486.17.1.2568.1473.098.0527.1978.1021.2974.1516s.1962.0983.2962.1434c.08.0359.1625.0654.2435.0986.1218.05.2429.1006.3648.15.044.0178.0891.0328.1343.0475.0625.02.1256.039.1883.0586.0748.0234.1493.0478.2243.07.0667.02.1341.0379.2012.0567.0792.0224.1579.0473.2381.0664.0874.0208.1763.0354.2641.0547.0986.0218.1958.0511.2952.0681.1288.022.2593.0348.3891.0519.0165.002.0329.0054.0494.0073.1961.0237.3917.0588.5885.0678.2884.0133.5778.0085.8669.01.1018.0007.1018-.0012.1018-.1V5.1942c0-.0146-.0008-.0291,0-.0436C18.1835,5.1079,18.164,5.0877,18.12,5.0856Z"/></symbol>

		<symbol id="icon-def-twitter" viewBox="0 0 24.2713 19.9999"><path d="M22.9231,2.8633c-.4081.11-.8274.1815-1.2427.2707A5.9891,5.9891,0,0,0,23.6315.4129c-.2317.1118-.3732.1928-.5239.25-.7156.2738-1.4333.5413-2.149.812a.7545.7545,0,0,1-.8571-.203A5.0045,5.0045,0,0,0,11.9393,3.69a15.1063,15.1063,0,0,0-.2553,2.4986A14.4117,14.4117,0,0,1,1.4436.943,4.9868,4.9868,0,0,0,.8889,4.57,5.1478,5.1478,0,0,0,2.8666,7.5786l-.0748.13C2.1182,7.52,1.4446,7.3325.7136,7.1275a5.1592,5.1592,0,0,0,3.814,4.8987l-.0492.1169H2.48a5.2434,5.2434,0,0,0,4.6711,3.5208A10.6569,10.6569,0,0,1,0,17.8251a3.3077,3.3077,0,0,0,.6049.4132,14.3382,14.3382,0,0,0,9.3187,1.5636A14.2682,14.2682,0,0,0,21.825,5.448a.84.84,0,0,1,.365-.77,8.8565,8.8565,0,0,0,2.0813-2.2464C23.7586,2.5978,23.3455,2.75,22.9231,2.8633Z"/></symbol>

		<symbol id="icon-def-truck" viewBox="0 0 24 24"><rect x="1" y="3" width="15" height="13"></rect><polygon points="16 8 20 8 23 11 23 16 16 16 16 8"></polygon><circle cx="5.5" cy="18.5" r="2.5"></circle><circle cx="18.5" cy="18.5" r="2.5"></circle></symbol>

		<symbol id="icon-def-user" viewBox="0 0 20 20"><path d="M10.2252 9.29515C12.5159 9.29515 14.3728 7.43821 14.3728 5.14757C14.3728 2.85693 12.5159 1 10.2252 1C7.93457 1 6.07764 2.85693 6.07764 5.14757C6.07764 7.43821 7.93457 9.29515 10.2252 9.29515Z" stroke="currentColor" /><path d="M10.1786 12.6C3.09515 12.8291 1 15.9107 1 15.9107V19H19.4233V15.9107C19.4233 15.9107 17.3184 12.8272 10.233 12.6H10.1786V12.6Z" stroke="currentColor" /></symbol>

		
		<symbol id="icon-def-x-circle" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></symbol>
		<symbol id="icon-def-x" viewBox="0 0 24 24"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></symbol>
		
		<symbol id="icon-def-zoom-in" viewBox="0 0 24 24"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line><line x1="11" y1="8" x2="11" y2="14"></line><line x1="8" y1="11" x2="14" y2="11"></line></symbol>
		<symbol id="icon-def-zoom-out" viewBox="0 0 24 24"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line><line x1="8" y1="11" x2="14" y2="11"></line></symbol>

		<symbol id="icon-def-play" viewBox="0 0 12 14"><path d="M12 7L0 13.9282L0 0.0717969L12 7Z" fill="currentColor"/></symbol>
		<symbol id="icon-def-pause" viewBox="0 0 10 15"><rect width="2.51488" height="14.251" fill="currentColor"/><rect x="6.70642" width="2.51488" height="14.251" fill="currentColor"/></symbol>
		
    <symbol id="icon-def-mute" viewBox="0 0 32 32"><path fill="currentColor" stroke="none" d="M26.5 2.1c-.3-.2-.7-.2-1 0L13.7 9H6c-.6 0-1 .4-1 1v12c0 .6.4 1 1 1h7.7l11.8 6.9c.2.1.3.1.5.1s.3 0 .5-.1c.3-.2.5-.5.5-.9V3c0-.4-.2-.7-.5-.9z"/></symbol>
		<symbol id="icon-def-unmute" viewBox="0 0 32 32"><path fill="currentColor" stroke="none" d="M29 30c-.3 0-.5-.1-.7-.3l-26-26c-.4-.4-.4-1 0-1.4s1-.4 1.4 0l26 26c.4.4.4 1 0 1.4-.2.2-.4.3-.7.3z"/><path fill="currentColor" stroke="none" d="M27 28.4V3c0-.4-.2-.7-.5-.9-.3-.2-.7-.2-1 0L13.7 9H7.6L27 28.4zM5.2 9.4c-.1.2-.2.4-.2.6v12c0 .6.4 1 1 1h7.7l11.8 6.9c.1 0 .2.1.2.1L5.2 9.4z"/></symbol>

  <symbol id="icon-def-shoppay" viewBox="0 0 424 102" fill="none">
      <path d="M108.665 32.87c-3.402-7.136-9.852-11.746-19.57-11.746a19.48 19.48 0 00-15.303 7.868l-.355.432V1.454a.61.61 0 00-.61-.61h-13.74a.61.61 0 00-.599.61v80.23a.598.598 0 00.598.598h14.717a.61.61 0 00.609-.598V47.475c0-6.648 4.433-11.358 11.525-11.358 7.757 0 9.718 6.383 9.718 12.888v32.679a.598.598 0 00.599.598h14.682a.609.609 0 00.61-.598v-34.63c0-1.185 0-2.349-.155-3.48a30.617 30.617 0 00-2.726-10.704zM35.184 44.76s-7.491-1.76-10.25-2.47c-2.76-.71-7.58-2.217-7.58-5.863 0-3.646 3.89-4.81 7.834-4.81 3.945 0 8.334.954 8.677 5.331a.632.632 0 00.632.576l14.505-.055a.618.618 0 00.587-.414.62.62 0 00.034-.251C48.725 22.797 36.436 17.788 25.1 17.788c-13.442 0-23.271 8.865-23.271 18.64 0 7.136 2.017 13.829 17.874 18.483 2.782.809 6.56 1.862 9.863 2.781 3.967 1.109 6.105 2.782 6.105 5.42 0 3.058-4.432 5.185-8.787 5.185-6.305 0-10.782-2.338-11.148-6.538a.632.632 0 00-.632-.554l-14.472.067a.631.631 0 00-.632.654C.665 75.145 13.431 82.27 25.332 82.27c17.73 0 25.743-9.973 25.743-19.315.022-4.388-.987-14.384-15.891-18.196zm186.611-23.658c-7.369 0-13.542 4.078-17.52 8.998v-8.422a.597.597 0 00-.587-.599h-13.763a.601.601 0 00-.599.599v78.678a.598.598 0 00.599.587h14.727a.587.587 0 00.587-.587V74.492h.222c2.338 3.568 8.732 7.846 17.087 7.846 15.714 0 28.812-13.032 28.812-30.64.011-16.9-13.021-30.596-29.565-30.596zm-1.363 46.242a15.613 15.613 0 1115.226-15.647 15.4 15.4 0 01-4.362 10.987 15.404 15.404 0 01-10.864 4.66zm-74.689-49.7c-13.73 0-20.578 4.666-26.075 8.4l-.166.11a1.364 1.364 0 00-.41 1.807l5.43 9.353a1.373 1.373 0 00.964.665 1.342 1.342 0 001.108-.3l.433-.354c2.825-2.372 7.358-5.54 18.328-6.405 6.106-.488 11.381 1.108 15.27 4.743 4.278 3.945 6.838 10.316 6.838 17.043 0 12.378-7.292 20.157-19.005 20.312-9.652-.055-16.135-5.086-16.135-12.522 0-3.945 1.785-6.516 5.264-9.087a1.349 1.349 0 00.41-1.728l-4.876-9.22a1.42 1.42 0 00-.853-.687 1.371 1.371 0 00-1.108.144c-5.474 3.247-12.19 9.186-11.824 20.6.443 14.528 12.522 25.62 28.224 26.075h1.862c18.661-.61 32.136-14.462 32.136-33.245 0-17.242-12.566-35.704-35.815-35.704z" fill="rgb(90, 49, 244)"></path>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M281.734 1.044h125.652c8.672 0 15.703 7.03 15.703 15.703V85.54c0 8.672-7.031 15.702-15.703 15.702H281.734c-8.672 0-15.702-7.03-15.702-15.702V16.747c0-8.673 7.03-15.703 15.702-15.703zm28.191 54.488c10.483 0 17.985-7.647 17.985-18.362 0-10.66-7.502-18.351-17.985-18.351h-18.506a.829.829 0 00-.831.83v50.787a.84.84 0 00.831.831h6.959a.831.831 0 00.831-.83V56.362a.83.83 0 01.832-.831h9.884zm-.532-29c5.696 0 9.896 4.498 9.896 10.638 0 6.15-4.2 10.638-9.896 10.638h-9.352a.83.83 0 01-.832-.82V27.363a.844.844 0 01.832-.831h9.352zm20.567 34.995a9.875 9.875 0 014.123-8.467c2.704-2.028 6.892-3.08 13.109-3.324l6.593-.222v-1.95c0-3.89-2.615-5.54-6.815-5.54s-6.848 1.484-7.469 3.911a.793.793 0 01-.797.576h-6.505a.816.816 0 01-.807-.588.815.815 0 01-.024-.354c.975-5.762 5.74-10.14 15.902-10.14 10.793 0 14.682 5.02 14.682 14.606v20.368a.823.823 0 01-.239.595.835.835 0 01-.592.247h-6.571a.836.836 0 01-.592-.247.833.833 0 01-.239-.595v-1.518a.619.619 0 00-.783-.65.62.62 0 00-.325.218c-1.962 2.138-5.153 3.69-10.239 3.69-7.458.022-12.412-3.879-12.412-10.616zm23.825-4.433V55.52l-8.532.444c-4.499.232-7.126 2.105-7.126 5.252 0 2.848 2.405 4.433 6.594 4.433 5.696 0 9.064-3.08 9.064-8.544v-.011zm14.772 23.626v5.928a.854.854 0 00.609.864c1.159.316 2.357.462 3.558.433 6.371 0 12.189-2.327 15.514-11.392l14.627-39.018a.847.847 0 00-.112-.753.848.848 0 00-.675-.355h-6.815a.829.829 0 00-.798.576l-8.056 24.712a.854.854 0 01-1.596 0l-9.286-24.778a.855.855 0 00-.787-.543h-6.649a.841.841 0 00-.786 1.108l13.674 35.128a.82.82 0 010 .565l-.432 1.363a7.877 7.877 0 01-7.945 5.618 16.45 16.45 0 01-3.048-.288.839.839 0 00-.918.472.826.826 0 00-.079.36z" fill="rgb(90, 49, 244)"></path>
    </symbol>

    <symbol id="icon-def-shoppay-mono" viewBox="0 0 64 15" stroke="none" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_4329_1022)">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M42.3081 0C40.9983 0 39.9369 1.02561 39.9369 2.29108V12.7098C39.9369 13.9753 40.9983 15.0009 42.3081 15.0009H61.6288C62.9385 15.0009 64 13.9753 64 12.7098V2.29108C64 1.02561 62.9385 0 61.6288 0H42.3081ZM45.6447 10.4675V7.82625H47.3644C48.9357 7.82625 49.7708 6.97525 49.7708 5.68588C49.7708 4.39652 48.9357 3.62456 47.3644 3.62456H44.5376V10.4675H45.6457H45.6447ZM45.6447 4.64189H47.1095C48.1263 4.64189 48.6256 5.04534 48.6256 5.72081C48.6256 6.39627 48.1443 6.79972 47.1542 6.79972H45.6438V4.64097L45.6447 4.64189ZM51.7235 10.6081C52.5681 10.6081 53.1217 10.2487 53.3766 9.63393C53.4489 10.3186 53.876 10.6696 54.8024 10.4325L54.8119 9.70469C54.44 9.73961 54.3668 9.6082 54.3668 9.2314V7.44118C54.3668 6.388 53.6496 5.76584 52.3237 5.76584C50.9978 5.76584 50.2625 6.39719 50.2625 7.46783H51.2793C51.2793 6.95871 51.6512 6.65176 52.3056 6.65176C52.9952 6.65176 53.3138 6.94124 53.3043 7.44118V7.6691L52.1325 7.79224C50.8161 7.93285 50.0894 8.41533 50.0894 9.25714C50.0894 9.95007 50.5983 10.6081 51.7235 10.6081ZM51.9508 9.81865C51.3792 9.81865 51.1519 9.51997 51.1519 9.22221C51.1519 8.81877 51.6236 8.63405 52.5501 8.52929L53.2767 8.45025C53.2311 9.22221 52.6956 9.81865 51.9508 9.81865ZM58.1742 10.8093C57.711 11.8974 56.9662 12.2218 55.8039 12.2218H55.3046V11.3267H55.8401C56.4754 11.3267 56.7846 11.1337 57.1203 10.5805L55.0592 5.90461H56.2034L57.6748 9.31779L58.9827 5.90461H60.0993L58.1742 10.8093Z" fill="#121212"/>
      <path d="M4.49513 6.59292C2.97805 6.27495 2.30273 6.15088 2.30273 5.58569C2.30273 5.05451 2.76024 4.78983 3.67429 4.78983C4.47896 4.78983 5.06677 5.12987 5.49955 5.79523C5.53189 5.84669 5.59942 5.86415 5.65458 5.83658L7.36095 5.00304C7.42182 4.97364 7.44465 4.89828 7.41041 4.8413C6.7018 3.65486 5.39302 3.00513 3.67049 3.00513C1.40675 3.00513 0 4.08312 0 5.79798C0 7.61853 1.71302 8.07895 3.23201 8.39693C4.75099 8.71491 5.42821 8.83897 5.42821 9.40416C5.42821 9.96935 4.93456 10.2359 3.94822 10.2359C3.03797 10.2359 2.3617 9.83333 1.95366 9.05126C1.92322 8.99428 1.85189 8.97039 1.79197 8.9998L0.0894081 9.81403C0.0304368 9.84344 0.0057069 9.91237 0.0361437 9.9721C0.71146 11.2854 2.09824 12.0233 3.95012 12.0233C6.30802 12.0233 7.73285 10.9628 7.73285 9.19555C7.73285 7.4283 6.01222 6.91457 4.49513 6.5966V6.59292Z" fill="#121212"/>
      <path d="M13.6414 3.00515C12.6741 3.00515 11.818 3.33691 11.2036 3.92691C11.1646 3.96275 11.1018 3.93702 11.1018 3.88555V0.116714C11.1018 0.0514643 11.0485 0 10.981 0H8.84568C8.77815 0 8.72488 0.0514643 8.72488 0.116714V11.8166C8.72488 11.8818 8.77815 11.9333 8.84568 11.9333H10.981C11.0485 11.9333 11.1018 11.8818 11.1018 11.8166V6.68484C11.1018 5.69324 11.8875 4.93322 12.947 4.93322C14.0066 4.93322 14.7742 5.67761 14.7742 6.68484V11.8166C14.7742 11.8818 14.8275 11.9333 14.895 11.9333H17.0303C17.0979 11.9333 17.1511 11.8818 17.1511 11.8166V6.68484C17.1511 4.52886 15.6892 3.0079 13.6414 3.0079V3.00607V3.00515Z" fill="#121212"/>
      <path d="M21.4827 2.66968C20.3232 2.66968 19.237 3.01339 18.4571 3.50873C18.4038 3.54273 18.3857 3.61166 18.4181 3.66496L19.3597 5.219C19.394 5.27414 19.4681 5.29436 19.5252 5.26035C20.1178 4.91481 20.795 4.73468 21.4874 4.73928C23.3517 4.73928 24.7213 6.01118 24.7213 7.69112C24.7213 9.12293 23.6247 10.1835 22.235 10.1835C21.1022 10.1835 20.3156 9.54567 20.3156 8.64505C20.3156 8.12949 20.542 7.70674 21.1317 7.40899C21.1926 7.37774 21.2154 7.30422 21.1783 7.24724L20.2899 5.79429C20.2614 5.74651 20.2005 5.72537 20.1454 5.74467C18.9555 6.17109 18.1204 7.19762 18.1204 8.57612C18.1204 10.6613 19.8372 12.2172 22.2322 12.2172C25.0295 12.2172 27.0402 10.3434 27.0402 7.65528C27.0402 4.7742 24.7004 2.66968 21.4836 2.66968H21.4827Z" fill="#121212"/>
      <path d="M33.2845 2.98957C32.204 2.98957 31.2415 3.37463 30.5367 4.05377C30.4977 4.09145 30.4349 4.06388 30.4349 4.01242V3.1945C30.4349 3.12926 30.3816 3.07779 30.3141 3.07779H28.2339C28.1664 3.07779 28.1131 3.12926 28.1131 3.1945V14.8769C28.1131 14.9421 28.1664 14.9936 28.2339 14.9936H30.3693C30.4368 14.9936 30.4901 14.9421 30.4901 14.8769V11.0465C30.4901 10.995 30.5538 10.9693 30.5918 11.0033C31.2938 11.6356 32.223 12.0041 33.2864 12.0041C35.7918 12.0041 37.7454 10.0429 37.7454 7.49637C37.7454 4.9498 35.7899 2.98865 33.2864 2.98865L33.2845 2.99049V2.98957ZM32.8822 10.0255C31.4574 10.0255 30.3778 8.9291 30.3778 7.47983C30.3778 6.03055 31.4555 4.93418 32.8822 4.93418C34.3089 4.93418 35.3847 6.01217 35.3847 7.47983C35.3847 8.94748 34.3232 10.0255 32.8803 10.0255H32.8822Z" fill="#121212"/>
      </g>
      <defs>
      <clipPath id="clip0_4329_1022">
      <rect width="64" height="15" fill="white"/>
      </clipPath>
      </defs>
    </symbol>

    <symbol id="icon-def-afterpay" viewBox="0 0 165.0003 31"><path d="M162.673,6.5013,152.103.6088a4.656,4.656,0,0,0-6.981,3.8936v.6045a1.5186,1.5186,0,0,0,.791,1.3233l1.996,1.1124a.8777.8777,0,0,0,1.317-.734V5.305a1.0105,1.0105,0,0,1,1.515-.8458l9.155,5.1051a.9583.9583,0,0,1,0,1.6891l-9.155,5.1051a1.0105,1.0105,0,0,1-1.515-.8458v-.8a4.6563,4.6563,0,0,0-6.983-3.8936l-10.57,5.8925a4.4207,4.4207,0,0,0,0,7.7872l10.57,5.8925a4.6578,4.6578,0,0,0,6.983-3.8936v-.6045a1.5216,1.5216,0,0,0-.791-1.3233l-1.996-1.115a.8777.8777,0,0,0-1.317.7341v1.5036a1.01,1.01,0,0,1-1.515.8457l-9.155-5.1051a.9609.9609,0,0,1,0-1.6915l9.155-5.1052a1.01,1.01,0,0,1,1.515.8458v.8a4.6552,4.6552,0,0,0,6.981,3.8936l10.57-5.8925A4.4164,4.4164,0,0,0,162.673,6.5013ZM36.5482,18.4136V11.0505h2.7135V7.4338H36.5482V3.3878H32.22v4.046H26.646V6.4255c0-1.3944.55-1.9252,2.0615-1.9252h.9492V1.2823H27.5742c-3.5707,0-5.2535,1.1277-5.2535,4.5768V7.4313H19.9175V11.048h2.4032V24.0165h4.328V11.048h5.5742v8.1275c0,3.3832,1.3384,4.8437,4.8407,4.8437h2.2323V20.3364h-.86C36.9584,20.3364,36.5482,19.8054,36.5482,18.4136ZM13.7016,9.5241A6.9457,6.9457,0,0,0,8.38,7.1367,8.3761,8.3761,0,0,0,0,15.7264a8.2255,8.2255,0,0,0,8.2747,8.5569,6.9716,6.9716,0,0,0,5.4243-2.4206v2.1565h4.2254V7.4338H13.7016ZM8.9924,20.4709a4.648,4.648,0,0,1-4.67-4.7445,4.6278,4.6278,0,0,1,4.67-4.7443,4.6079,4.6079,0,0,1,4.67,4.7443A4.6081,4.6081,0,0,1,8.9924,20.4709ZM49.7713,7.1341c-5.0825,0-8.9293,3.6168-8.9293,8.6254s3.639,8.5238,8.8242,8.5238c4.2911,0,7.5541-2.2555,8.5848-5.7707h-4.43a4.5864,4.5864,0,0,1-4.0518,2.1565,4.2268,4.2268,0,0,1-4.4305-3.8479H58.4929a5.3338,5.3338,0,0,0,.1026-1.0617A8.4509,8.4509,0,0,0,49.7713,7.1341ZM45.375,14.233A4.2107,4.2107,0,0,1,49.7,10.7838a4.0761,4.0761,0,0,1,4.257,3.4492Zm60.318-4.7089a6.9466,6.9466,0,0,0-5.322-2.3874,8.3756,8.3756,0,0,0-8.3772,8.59,8.2254,8.2254,0,0,0,8.2742,8.5569,6.9728,6.9728,0,0,0,5.425-2.4206v2.1565h4.223V7.4338h-4.223Zm-4.707,10.9468a4.648,4.648,0,0,1-4.67-4.7445,4.67,4.67,0,1,1,9.34,0A4.6081,4.6081,0,0,1,100.986,20.4709ZM64.9007,9.0593V7.4363H60.678V24.0217h4.3595V14.2025c-.0053-1.7322.9176-3.0275,2.3769-3.2179a5.5928,5.5928,0,0,1,3.0475.7569V7.51a4.3992,4.3992,0,0,0-1.8538-.3733A4.2715,4.2715,0,0,0,64.9007,9.0593ZM125.074,7.4338l-4.249,9.5727-4.141-9.5727h-5.096l7.078,14.3274-3.8216,8.2431h4.49L129.638,7.4338ZM82.16,7.1367A6.8889,6.8889,0,0,0,76.7355,9.59V7.4338H72.5128V30.0043h4.3595V22.0634a6.7475,6.7475,0,0,0,5.1851,2.2224,8.3779,8.3779,0,0,0,8.3772-8.5924A8.2862,8.2862,0,0,0,82.16,7.1367Zm-.7178,13.3342a4.6481,4.6481,0,0,1-4.67-4.7445,4.67,4.67,0,1,1,9.34,0A4.67,4.67,0,0,1,81.4421,20.4709Z"/></path></symbol>

    <symbol id="icon-def-klarna" viewBox="0 0 452.9 121.1"><path d="M79.7,0H57.4a57.0734,57.0734,0,0,1-23,46l-8.8,6.6L59.8,99.2H87.9L56.4,56.3A78.8433,78.8433,0,0,0,79.7,0ZM0,99.2H22.8V0H0Zm94.5,0H116V0H94.5ZM304.6,28.7c-8.2,0-16,2.5-21.2,9.6V30.6H263V99.2h20.7v-36c0-10.4,7-15.5,15.4-15.5,9,0,14.2,5.4,14.2,15.4V99.3h20.5V55.6C333.8,39.6,321.1,28.7,304.6,28.7ZM181,35a35.7109,35.7109,0,0,0-20.4-6.3,36.2,36.2,0,1,0,0,72.4A35.7109,35.7109,0,0,0,181,94.8v4.4h20.5V30.6H181ZM162.3,82.5c-10.3,0-18.6-7.9-18.6-17.6s8.3-17.6,18.6-17.6,18.6,7.9,18.6,17.6S172.6,82.5,162.3,82.5Zm71-43V30.6h-21V99.2h21.1v-32c0-10.8,11.7-16.6,19.8-16.6h.2v-20C245.1,30.6,237.4,34.2,233.3,39.5ZM397.6,35a35.7109,35.7109,0,0,0-20.4-6.3,36.2,36.2,0,1,0,0,72.4,35.7109,35.7109,0,0,0,20.4-6.3v4.4h20.5V30.6H397.6ZM378.9,82.5c-10.3,0-18.6-7.9-18.6-17.6s8.3-17.6,18.6-17.6,18.6,7.9,18.6,17.6C397.6,74.6,389.2,82.5,378.9,82.5Zm53-43.9a4.9,4.9,0,1,0-4.9-4.9A4.9736,4.9736,0,0,0,431.9,38.6Zm0-8.9a3.9273,3.9273,0,0,1,3.9,4,3.9922,3.9922,0,0,1-3.9,4,3.9273,3.9273,0,0,1-3.9-4A3.9922,3.9922,0,0,1,431.9,29.7Zm-.7,4.6h1l.8,1.9h1l-.9-2.1a1.5509,1.5509,0,0,0,.9-1.5,1.583,1.583,0,0,0-1.8-1.6h-1.9v5.2h.9Zm0-2.5h1c.6,0,.9.3.9.8s-.2.8-.9.8h-1ZM440,74.9a12.9,12.9,0,1,0,12.9,12.9A12.9315,12.9315,0,0,0,440,74.9Z"/></symbol>

	</defs>
</svg>
<script>
	window.addEventListener('DOMContentLoaded', e=>{
		Array.from(document.querySelectorAll('use')).filter(use=>!document.querySelector(use.href.baseVal)).forEach(use=>use.parentNode.remove())
	})
</script>
