<div class="w-full flex flex-column order-0 pb0-l pb-[10px] border-b-[2px] border-white">
  <ul class="account-nav list m-0 p-0 w-full list-none flex w-full lg:justify-center justify-start px-[10px] gap-[10px]">
    <li class="group">
      <a
        class="acc-information-btn button--inverse {% if template.name == 'account' %}active{% endif %}"
        {% if template contains 'account' %}
          neptune-engage='[
            {
              "targets": [
                {
                  "selector": "#accountInformation",
                  "classes": {
                    "add": "block",
                    "remove": "hidden"
                  }
                },
                {
                  "selector": "#orderHistory, #ccsPlus",
                  "classes": {
                    "add": "hidden",
                    "remove": "block"
                  }
                },
                {
                  "selector": ".order-history__title",
                  "classes": {
                    "add": "hidden",
                    "remove": "block"
                  }
                },
                {
                  "selector": ".account__header",
                  "classes": {
                    "add": "block",
                    "remove": "hidden"
                  }
                },
                {
                  "selector": ".active",
                  "classes": {
                    "remove": "active"
                  }
                },
                {
                  "selector": "_self",
                  "classes": {
                    "add": "active"
                  }
                }
              ]
            }
          ]'
        {% else %}
          href="/account/#accountInformation"
        {% endif %}
        >
        <span class="leading-[1.6]">{{ 'customer.account.info' | t }}</span></a> 
    </li>
    <li class="group">
      <a class="order-history-btn block  button--inverse {% if template.name == 'order' %}active{% endif %}"
      {% if template contains 'account' %}
        neptune-engage='[
          {
            "targets": [
              {
                "selector": "#accountInformation",
                "classes": {
                  "add": "hidden",
                  "remove": "block"
                }
              },
              {
                "selector": "#orderHistory",
                "classes": {
                  "add": "block",
                  "remove": "hidden"
                }
              },
              {
                "selector": ".account__header",
                "classes": {
                  "add": "hidden",
                  "remove": "block"
                }
              },
              {
                "selector": ".order-history__title",
                "classes": {
                  "add": "block",
                  "remove": "hidden"
                }
              },
              {
                "selector": ".active",
                "classes": {
                  "remove": "active"
                }
              },
              {
                "selector": "_self",
                "classes": {
                  "add": "active"
                }
              }
            ]
          }
        ]'
      {% else %}
        href="/account/#orderHistory"
      {% endif %}
        >
        <span class="leading-[1.6]">{{ 'customer.account.orders' | t }}</span>
      </a>
    </li>
    <li class="group">
      <a href="/account/addresses" class="logo button--inverse {% if template.name == 'addresses' %}active{% else %}bg-white{% endif %}">
        <span class="leading-[1.6]">{{ 'customer.account.view_addresses' | t }}</span>
      </a>
    </li>
  </ul>
</div>


<script>
  document.addEventListener("DOMContentLoaded", function () {
  const hash = window.location.hash;
  
  setTimeout(() => {
    if (hash == "#orderHistory" || (window.location.search.includes("?page=") && !window.location.pathname.includes("/addresses"))) {
      const button = document.querySelector(".order-history-btn");
      if (button) {
        button.click();
      }
    } else if (hash == "#accountInformation") {
      const button = document.querySelector(".acc-information-btn");
      if (button) {
        button.click();
      }
    }
  }, 100);

});

</script>