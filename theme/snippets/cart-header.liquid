<header class="cart--header">

  <article>

    <div class="flex items-center justify-between p-4 lg:px-8 border-b-2 border-white">
      <h4 class="cart__title font-body font-light text-base m-0">
        {% if settings.free_shipping_enable %}
          {% render 'free-shipping-message' %}
        {% else %}
          {{ 'templates.cart.cart' | t }}
        {% endif %}
      </h4>

      <div class="top-0 left-0 h-full text-left cart__close">
        <button class="flex items-center justify-center bg-transparent border-none cursor-pointer white" neptune-engage="[{
          'targets':[{
            'selector':'html',
            'attributes':[{
              'att':'data-active-modal',
              'set':'_remove'
            }]
          },
          { 
            'selector':'[data-return-focus]',
            'attributes':[{
              'att':'data-return-focus',
              'set':'_remove'
            }],
            'focus':true
          }]
          }]">
          {% render 'icon' icon:'x' width:20 height:20 sr:'Close cart modal' %}
        </button>
      </div>

    </div>

  </article>

</header>