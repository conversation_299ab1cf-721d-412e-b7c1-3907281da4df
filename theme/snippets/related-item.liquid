<div class="w-100 product-item relative" itemscope="" itemtype="http://schema.org/Product" data-index="0">    
  <a id="product{{product.id}}" class="product-link w-100" href="{{product.url}}" title="{{product.title}}">
    <meta itemprop="name" content="{{product.title}}">   
    <meta itemprop="url" content="{{product.url}}">  

    <div itemprop="offers" itemscope="" itemtype="http://schema.org/Offer">     
      <meta itemprop="priceCurrency" content="USD">     
      <meta itemprop="price" content="{{product.price | money}}">     
      <meta itemprop="availability" content="{%- if product.availability -%}http://schema.org/InStock{%- else -%}http://schema.org/OutOfStock{%- endif -%}">     
      <meta itemprop="itemCondition" itemtype="http://schema.org/OfferItemCondition" content="http://schema.org/NewCondition">    
    </div>

    <div class="pi__img aspect-ratio aspect-ratio--3x4">     
      <!-- ngIf: ! isNotProduct() -->
      <div class="pi__link ir ir--product">      
        <!-- ngIf: result.tags.indexOf('flag') > -1 -->       
        <!-- <h1>Heyooooo</h1> -->
        {% include 'responsive-image' with image: product.images[0].src, image_class: "aspect-ratio--object z-0", wrapper_class: "w-100" %}
        
        <!-- ngIf: hasImageHover() -->
        <div class="pi__photo-hover">          
          {%- if product.images[1].alt contains '.mp4' -%}
          <div class="h-100 w-100" onmouseenter="console.log('this:', this);this.innerHTML = this.innerHTML.replace('<!--','').replace('-->',''); this.removeAttribute('onmouseenter');">
          <!--<video class="product-video plyr--setup" preload="true" src="{{product.images[1].alt}}" muted mute autoplay loop="" style="width: calc(100% + 20px);
          margin-left: -10px;
          margin-right: -10px;
          height: 100%;
          object-fit: contain;" poster=""><source src="{{product.images[1].alt}}"></video>-->
          </div>
          {%- else -%}
            {% include 'responsive-image' with image: product.images[1].src, image_class: "aspect-ratio--object z-0", wrapper_class: "w-100" %}
          {%- endif -%}
        </div>  
      </div> 

    </div>    
    <div class="lh-title tc product-item-info mt3 mh0">      
      <div class="db">        
        <h2 class="fw5">{{product.title | split: '-' | first}}</h2>        
        <div class="fw4">{{product.title | split: '-' | last}}</div>        
        <span class="db fw3">
          <span
          class="global-price"
          global-selector="prices.item.label"
          global-default="{{ product.price | money }}"
          global-variant="{{ product.selected_or_first_available_variant.id }}"
          >{{ product.price | money }}</span>
        </span> 
      </div>   
    </div>  

    <div class="quick-shop absolute outline w-100 h-100 top-0">
        
      <form action="/cart/add" method="post" enctype="multipart/form-data" class="db absolute bottom-0 w-100 pointer-all bg-white form-quickshop z-1">
        
        <select name="id" class="dn variant">
          {%- for variant in product.variants -%}
          <option value="{{variant.id}}">{{variant.title}}</option>
          {%- endfor -%}
        </select>
        <noscript><style>select[name=id].dn{display:block;}.option-selection{display:none;}</style></noscript>
        
        {%- unless product.variants.size == 1 -%}
        {%- for option in product.options_with_values -%}
          {%- assign optioni = forloop.index -%}
          
          
          <div class="bg-white {% if option.values.size == 1 %} dn {%else%} flex {% endif %} justify-center option-selection">
            
            {%- for value in option.values -%}
              <label class="pv3 ph2 ma0 lh-1 sans-serif pointer lh-solid" title="{{value}}" for="{{product.handle}}{{option.name|handle}}{{value|handle}}">
                
                {%- assign option_variants = '' -%}
                {%- assign available_variants = '' -%}

                {%- for variant in product.variants -%}
                
                  {%- if variant.option1 == value or variant.option2 == value -%}
                    
                    {%- assign option_variants = option_variants|append:variant.id|append:',' -%}
                    
                    {%- if variant.available%}
                      {%- assign available_variants = available_variants|append:variant.id|append:',' -%}
                    {%- endif -%}

                  {%- endif -%}
                {%- endfor -%}

                <input data-variants="{{option_variants}}" data-size-option data-available="{{available_variants}}" type="radio" id="{{product.handle}}{{option.name|handle}}{{value|handle}}" name="option{{optioni}}" value="{{value}}" class="screenreader" onchange="variantSelection(this)" {%- if product.first_available_variant.option1 == value or product.first_available_variant.option2 == value -%}checked{%- endif -%}>
                {%- if option.name contains 'Color' -%}
                <span class="br-100 dib w1-5 h1-5 ba b--gray bg-near-white lh-solid pointer-none"></span>
                {%- else -%}
                <span class="checkbox-controlled-bold">{{value}}</span>
                {%- endif -%}
              </label>

            {%- endfor -%}
          </div>
          
        {%- endfor -%}
        {%- endunless -%}

        <button type="submit" class="w-100 bn pa3 bg-black white ttc fw6 tracked-slight"><span class="dib mv1">{{'products.product.add_to_cart'|t}}</span></button>


      </form>
    </div>
  </a>
</div>
