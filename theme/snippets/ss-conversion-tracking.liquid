{% comment %}
  event: {
    context: {type: 'product-recommendation', tag: tag, placement: placement},
    profile: {
      placement: placement,
      tag: tag,
      seed: seed // [{sku: '432232334'}, {sku: '234343433'}]
    },
    product: {
      id: productId,
      mappings: mappings, // {core: {uid: '7878'}}
      seed: seed
    }
  }
{% endcomment %}

{% if request.page_type == 'product' %}
  {% assign placement = 'product-page' %}
{% elsif request.page_type == 'collection' and collection.all_products_count == 0 %}
  {% assign placement = 'no-resutlts-page' %}
{% elsif request.page_type == 'index' %}
  {% assign placement = 'home-page' %}
{% elsif request.page_type == 'cart' %}
  {% assign placement = 'basket-page' %}
{% elsif request.page_type == '404' %}
  {% assign placement = '404-page' %}
{% elsif request.page_type == 'customers/account' %}
  {% assign placement = 'user-account-page' %}
{% else %}
  {% assign placement = 'other' %}
{% endif %}

<script>
  // Function to get the visible slides
  const getVisibleSlides = (swiper) => {  
    const activeIndex = swiper.activeIndex; // Get the index of the active slide
    const slides = swiper.slides;

    // Calculate visible slides based on activeIndex and slidesPerView
    const visibleSlides = [];
    const slidesPerView = swiper.params.slidesPerView;

    for (let i = activeIndex; i < activeIndex + slidesPerView; i++) {
      if (slides[i]) {
        visibleSlides.push(slides[i]);
      }
    }

    return visibleSlides;
  }

  const findKey = (obj, keyToFind) => {
    for (const [key, value] of Object.entries(obj)) {
        if (key === keyToFind) return value; // Found
        if (typeof value === 'object' && value !== null) {
            const found = findKey(value, keyToFind);
            if (found) return found; // Found in nested object
        }
    }
    return null; // Not found
  }

  document.addEventListener('template:rendered', e => {
    const topic = Neptune.liquid.topics[e.detail.info.topic]

    const targets = topic.targets.flatMap(target => _n.qsa('.swiper-container', target.el))

    targets.forEach(container => {
      let containerId = topic.topic
      let data = []
      if (containerId == 'cart') {
        const offerIndex = container.dataset.offerIndex
        containerId += `-${offerIndex}`
        data = topic.data.offers.eligible[offerIndex]
      } else if (containerId.includes('ProductRecommendations')) {
        data = topic.data
      }

      const tag = data.searchspring_tags
      if (!tag) return

      const items = _n.qsa('.swiper-slide', container)
      if (items.length == 0) return

      if (SSBeacon.rendered.includes(containerId)) return false

      SSBeacon.register(container, containerId, data, tag)
    })
  })

  // profile.click
  window.addEventListener('NeptuneLater:ready', () => {
    Neptune.later.register('product_item_click', e => {
      const addtlData = {}
      let topic = ''

      const wrapper = e.target.closest('[data-product]')
      if (!!wrapper) {
        // addtlData.pid = wrapper.dataset.productId
        addtlData.product = JSON.parse(wrapper.dataset.product.replace(/`/g, '"'))
      }

      let container = e.target.closest('.product-recommendations[neptune-liquid*=SSRecs]')
      if (!!container) {
        topic = Neptune.liquid.topics[container.id]
        addtlData.tag = topic.data.searchspring_tags

        return addtlData
      }

      if (!container) {
        container = e.target.closest('[data-offer-index]')
      }

      if (!!container) {
        topic = Neptune.liquid.topics['cart']
        addtlData.tag = topic.data.offers.eligible[container.dataset.offerIndex]?.searchspring_tags
      }

      return addtlData
    })
  })

  window.addEventListener('Neptune:Later', e => {
    const eventData = e.detail.info.event
    if (eventData.type != 'product_item_click') return false
    if (!eventData.tag) return false
    const pid = _n.generateUUID()

    const events = [
      {
        id: pid,
        type: 'profile.click',
        event: {
          context: {type: 'product-recommendation', tag:eventData.tag, placement: '{{ placement }}'},
          profile: {tag:eventData.tag, placement: '{{ placement }}', seed: ['{{ product.id }}']}
        }
      },
      {
        id: _n.generateUUID(),
        pid: pid,
        type: 'profile.product.click',
        event: {
          context: {type: 'product-recommendation', tag:eventData.tag, placement: '{{ placement }}'},
          product: {
            id: eventData.product.id, 
            mappings: { 
              core: {
                imageUrl: eventData.product.featured_image,
                msrp: eventData.product.compare_at_price/100,
                name: eventData.product.title,
                price: eventData.product.price/100,
                sku: eventData.product.sku,
                thumbnailImageUrl: eventData.product.thumbnail_image,
                uid: eventData.product.id,
                url: `/products/${eventData.product.handle}`
              }
            }
          }
        }
      }
    ]
    
    SSBeacon.send(events)
  })
  
  // SS Beacon API
  SSBeacon = {
    rendered: [],
    impressions: {},

    register: (container, containerId, data, tag) => {
      SSBeacon.rendered.push(containerId)

      const placement = (containerId.includes('cart')) ?  'basket-page' : '{{ placement }}'

      // profile.render
      let seed = ['']
      if (placement == 'product-page') {
        seed = ['{{ product.id }}']
      } else if (placement == 'basket-page') {
        seed = cart.items.map(item => item.id.toString())
      }

      const pid = _n.generateUUID()
      
      const events = [
        {
          id: pid,
          type: 'profile.render',
          event: {
            context: {type: 'product-recommendation', tag:tag, placement: placement},
            profile: {tag:tag, placement: placement, seed: seed}
          }
        }
      ]

      data.products.forEach(product => {
        events.push({
          id: _n.generateUUID(),
          pid: pid,
          type: 'profile.product.render',
          event: {
            context: {type: 'product-recommendation', tag: tag, placement: placement},
            product: {
              id: product.id, 
              mappings: { 
                core: {
                  imageUrl: product.featured_image,
                  msrp: product.compare_at_price/100,
                  name: product.title,
                  price: product.price/100,
                  sku: product.variants[0].sku,
                  thumbnailImageUrl: product.thumbnail_image,
                  uid: product.id,
                  url: `/products/${product.handle}`
                }
              }
            }
          }
        }) 
      })
      
      SSBeacon.send(events)

      // profile.impression
      SSBeacon.impressions[containerId] = []

      let isInView = false;

      // Intersection Observer
      const observer = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && !isInView) {
          isInView = true;

          // Perform your action here
          if (placement == 'product-page') {
            seed = ['{{ product.id }}']
          } else if (placement == 'basket-page') {
            seed = cart.items.map(item => item.id.toString())
          }

          const pid = _n.generateUUID()

          const events = [
            {
              id: pid,
              type: 'profile.impression',
              event: {
                context: {type: 'product-recommendation', tag:tag, placement: placement},
                profile: {tag:tag, placement: placement, seed: seed}
              }
            }
          ]

          const swiper = container.swiper

          const slides = (swiper) ? getVisibleSlides(swiper) : _n.qsa('.swiper-slide', container)

          slides.forEach(slide => {
            const product = JSON.parse(slide.querySelector('[data-product]').dataset.product.replace(/`/g, '"'))

            SSBeacon.impressions[containerId].push(product.id)

            events.push({
              id: _n.generateUUID(),
              pid: pid,
              type: 'profile.product.impression',
              event: {
                context: {type: 'product-recommendation', tag: tag, placement: placement},
                product: {
                  id: product.id, 
                  mappings: { 
                    core: {
                      imageUrl: product.featured_image,
                      msrp: product.compare_at_price/100,
                      name: product.title,
                      price: product.price/100,
                      sku: product.sku,
                      thumbnailImageUrl: product.thumbnail_image,
                      uid: product.id,
                      url: `/products/${product.handle}`
                    }
                  }
                }
              }
            })
          })

          SSBeacon.send(events)
        }
      });

      observer.observe(container);

      // profile.impression/click

      // If the design is slider
      container.addEventListener('slideChange', e => {
        if (placement == 'product-page') {
          seed = ['{{ product.id }}']
        } else if (placement == 'basket-page') {
          seed = cart.items.map(item => item.id.toString())
        }

        const pid = _n.generateUUID()

        const events = [
          {
            id: pid,
            type: 'profile.click',
            event: {
              context: {type: 'product-recommendation', tag:tag, placement: placement},
              profile: {tag:tag, placement: placement, seed: seed}
            }
          }
        ]

        const swiper = e.detail.info

        const slides = getVisibleSlides(swiper)

        slides.forEach(slide => {
          const product = JSON.parse(slide.querySelector('[data-product]').dataset.product.replace(/`/g, '"'))

          if (SSBeacon.impressions[containerId].includes(product.id)) {
            return false
          }

          SSBeacon.impressions[containerId].push(product.id)

          events.push({
            id: _n.generateUUID(),
            pid: pid,
            type: 'profile.product.impression',
            event: {
              context: {type: 'product-recommendation', tag: tag, placement: placement},
              product: {
                id: product.id, 
                mappings: { 
                  core: {
                    imageUrl: product.featured_image,
                    msrp: product.compare_at_price/100,
                    name: product.title,
                    price: product.price/100,
                    sku: product.sku,
                    thumbnailImageUrl: product.thumbnail_image,
                    uid: product.id,
                    url: `/products/${product.handle}`
                  }
                }
              }
            }
          })
        })

        SSBeacon.send(events)
      })
    },
    
    send: (events) => {
      const extendedEvents = events.map(event => ({
        category: 'searchspring.recommendations.user-interactions',
        context: {
          website: {trackingCode: '{{ settings.ss_site_id }}'},
          userId: _n.getCookie('ssUserId'),
          sessionId: sessionStorage.getItem('ssSessionIdNamespace') || _n.getCookie('ssSessionIdNamespace'),
          pageLoadId: _n.generateUUID()
        },
        ...event
      }))

      const options = {
        method: 'POST',
        mode: 'cors',
        headers: {accept: 'application/json', 'Content-Type': 'application/json'},
        body: JSON.stringify(extendedEvents)
      };

      fetch('https://beacon.searchspring.io/beacon', options)
        .then(response => response.json())
        {% if settings.ss_enable_logs -%}
        .then(response => console.log('SS Beacon ✔︎', extendedEvents, response))
        {%- endif %}
        .catch(err => console.error('SS Beacon', err));
    }
  }
</script>
