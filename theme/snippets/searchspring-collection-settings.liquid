{%- capture remote_url -%}
https://{{ settings.ss_site_id }}.a.searchspring.io/api/search/search.json
?siteId={{ settings.ss_site_id }}&resultsFormat=json
&resultsPerPage={{ section.settings.limit }}
{% if search.performed %}&q={{ search.terms }}{% endif %}
{% if collection %}&bgfilter.collection_name={{ collection.title }}{% endif %}
{% if collection.metafields.custom.hide_products_on_sale %}
&bgfilter.ss_on_sale=0
{% endif %}
${(() => {
  const urlParams = new URLSearchParams(window.location.search);
  const ssSegment = urlParams.get('ss_segment');
  return ssSegment ? `&tag=merch.segment/${ssSegment}` : '';
})()}
&page=${page}
${sort && '&sort.' + sort}
${(() => {
if (!!Object.entries(collection.filters.applied).length) {
 return '&' + Object.entries(collection.filters.applied).map(set=>set[1].map(val=>`filter.${set[0]}=${val}`).join('&')).join('&') 
}
return ''
})() }
{%- endcapture -%}

collection.settings.remote_url = "{{ remote_url | strip_newlines }}";
if (Shopify.country !== 'US') collection.settings.remote_url += '&bgfilter.ss_exclude_flow=0'

collection.settings.remote = {
  method: 'GET', 
  headers: {
    accept: 'application/json'
  }
};

collection.settings.map = {
  products: {
    from: 'results',
    each: {
      id:'uid',
      title:'name',
      handle:'handle',
      price:'price|*100',
      type:'product_type',
      compare_at_price:'msrp|*100',
      price_min:'price_min|*100',
      price_max:'price_max|*100',
      featured_image:'image',
      hover_image: 'ss_image_alt',
      hover_video: 'mfield_product_gallery_video',
      alt_image: 'ss_image_alt',
      alt_images:'gender_alternative',
      images: 'images',
      tags: 'tags|.split(", ")',
      variants:'variants',
      metafields:'metafields',
      siblings:'siblings',
      swatch_image: 'mfield_product_color_swatch'
    }
  },
  filters: {
    from: 'facets',
    each: {
      key: 'field',
      label: 'label',
      options:{
        from:'values',
        each: {
          label:'value',
          value:'value',
          active:'active'
        }
      }
    }
  },
  total_products: 'pagination.totalResults',
  redirect: 'merchandising.redirect'
}

collection.settings.transform = function(data) {
  data.results = _n.array(data.results).map(r => {
    try {
      r.images = r.images.split(`|`)
      r.hover_image = r.images[1]
    } catch (err) {
      console.warn(err);
    }
    try {
      r.metafields = JSON.parse(r.metafields.replace(/&quot;/g, `"`));
    } catch (err) {
      console.warn(err);
    }
    try {
      r.gender_alternative = JSON.parse(r.metafields.find(obj => obj['key'] === 'gender_alternative')?.value.replace(/&quot;/g, `"`))
    } catch (err) {
      console.warn(err);
    }

    try {
      r.siblings = JSON.parse(r.metafields.find(obj => obj['key'] === 'siblings')?.value.replace(/&quot;/g, `"`)).products
      if (r.siblings.length) {
        r.siblings.unshift(_n.map(r,{
          id:'uid',
          title:'name',
          handle:'handle',
          price:'price|*100',
          type:'product_type',
          compare_at_price:'msrp|*100',
          price_min:'price_min|*100',
          price_max:'price_max|*100',
          featured_image:'image',
          hover_image: 'ss_image_alt',
          hover_video: 'mfield_product_gallery_video',
          alt_image: 'ss_image_alt',
          alt_images:'gender_alternative',
          images: 'images',
          swatch_image: 'swatch_image',
          tags: 'tags|.split(", ")',
          variants:'variants',
        }))
        r.siblings.map(s => {
          if (!s.siblings) s.siblings = r.siblings

          if (r.title == s.title) {
            s.variants = JSON.parse(s.variants.replace(/&quot;/g, `"`))
            return s
          }

          s.price = s.price * 100
          if (s.compare_at_price) s.compare_at_price = s.compare_at_price * 100

          return s
        })
      }
    } catch (err) {
      console.warn('Error processing siblings:', err);
    }
    try {
      r.variants = JSON.parse(r.variants.replace(/&quot;/g,`"`));
    } catch (err) {
      console.warn(err);
    }

    if (!!window.localizedPrices && localizedPrices[r.uid]) {
      r.price = localizedPrices[r.uid].price;
      r.compare_at_price = localizedPrices[r.uid].compare_at;
    }

    return r
  })

  return data
}
