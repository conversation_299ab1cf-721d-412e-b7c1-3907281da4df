<script>
  window.addEventListener('DOMContentLoaded', e => {
    
    window.productBadges = JSON.parse(sessionStorage.getItem('productBadges'))
    
    Array.from(document.querySelectorAll('.product__description__property'))
      .filter(el=>el.textContent.includes('badge_checkout'))
      .forEach(el=>{
        const badge = productBadges.find(b=>{
          if(!b.checkout) return false;
          const badgeText =  b.checkout.toLowerCase().replace(/(<([^>]+)>)/gi, "").replace(/\n/g,' ').replace(/ /g,'');
          const elText = el.innerText.toLowerCase().replace(/(<([^>]+)>)/gi, "").replace(/\n/g,' ').replace(/ /g,'').split('badge_checkout:')[1]

          return badgeText == elText
        })
        if(!!badge){
            el.style.color = badge.color
            el.innerHTML = badge.checkout
        }
      })
      
  })
</script>