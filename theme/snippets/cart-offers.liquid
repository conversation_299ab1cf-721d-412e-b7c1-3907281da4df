{% raw %}

{% assign eligible_offers = offers.eligible %}

{% for offer in eligible_offers %}

  {% assign offerIndex = forloop.index0 %}

  {% if offer.products.size > 0 and offer.all_in_cart == false %}    

    {% case offer.display_type %}

      {% when 'carousel' %}
        {% endraw %}{% render 'offer-carousel' %}{% raw %}

      {% when 'takeover' %}
        {% endraw %}{% render 'offer-takeover' %}{% raw %}

      {% endcase %}    

    {% break %}

  {% endif %}

{% endfor %}

{% endraw %}
