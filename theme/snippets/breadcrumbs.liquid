
  {% if include_home %}
    <a href="/" class="breadcrumb__item breadcrumb__item--home">Home</a>
    <span class="breadcrumb__delimiter"> / </span>
  {% endif %}
  {% if search.performed %}
    <a href="/search?q={{ search.terms }}" class="breadcrumb__item breadcrumb__item--home">Search:{{ search.terms }}</a>
    <span class="breadcrumb__delimiter"> / </span>
  {% endif %}
  {% render 'breadcrumb' links:linklists[settings.breadcrumbs].links delimiter:'/' %}

{%- capture breadcrumb_entity_microdata -%}
  "@type": "BreadcrumbList",
  "itemListElement": [{
      "@type": "ListItem",
      "position": 1,
      "name": {{ 'general.breadcrumb.home' | t | json }},
      "item": "{{ shop.url }}"
    }

    {%- if request.page_type == 'product' -%}
      {%- if collection -%}
        ,{
          "@type": "ListItem",
          "position": 2,
          "name": {{ collection.title | json }},
          "item": "{{ shop.url }}{{ collection.url }}"
        }, {
          "@type": "ListItem",
          "position": 3,
          "name": {{ product.title | json }},
          "item": "{{ shop.url }}{{ product.url | within: collection }}"
        }
      {%- else -%}
        ,{
          "@type": "ListItem",
          "position": 2,
          "name": {{ product.title | json }},
          "item": "{{ shop.url }}{{ product.url }}"
        }
      {%- endif -%}
    {%- elsif request.page_type == 'collection' -%}
        ,{
          "@type": "ListItem",
          "position": 2,
          "name": {{ collection.title | json }},
          "item": "{{ shop.url }}{{ collection.url }}"
        }
    {%- elsif request.page_type == 'blog' -%}
        ,{
          "@type": "ListItem",
          "position": 2,
          "name": {{ blog.title | json }},
          "item": "{{ shop.url }}{{ blog.url }}"
        }
    {%- elsif request.page_type == 'article' -%}
        ,{
          "@type": "ListItem",
          "position": 2,
          "name": {{ blog.title | json }},
          "item": "{{ shop.url }}{{ blog.url }}"
        }, {
          "@type": "ListItem",
          "position": 3,
          "name": {{ blog.title | json }},
          "item": "{{ shop.url }}{{ article.url }}"
        }
    {%- elsif request.page_type == 'page' -%}
       ,{
          "@type": "ListItem",
          "position": 2,
          "name": {{ page.title | json }},
          "item": "{{ shop.url }}{{ page.url }}"
        }
    {%- endif -%}
  ]
{%- endcapture -%}
{% if breadcrumb_entity_microdata != blank %}
  <script type="application/ld+json">
  {
    "@context": "http://schema.org",
    {{ breadcrumb_entity_microdata }}
  }
  </script>
{% endif %}