{%- comment -%}
  Usage:
    In your liquid template file, copy the following line
  - {% render 'lazy-image' with image: block.settings.image, image_class: "block absolute top-0 left-0 max-w-full w-full h-full object-cover object-center" %}
{%- endcomment -%}

{%- capture responsive_image_counter -%}{% increment responsive_image_counter %}{%- endcapture -%}

{%- capture image_widths -%}
  {%- if image.width >= 375 -%}{{ image | image_url: width: 375 }} 375w,{%- endif -%}
  {%- if image.width >= 550 -%}{{ image | image_url: width: 550 }} 550w,{%- endif -%}
  {%- if image.width >= 750 -%}{{ image | image_url: width: 750 }} 750w,{%- endif -%}
  {%- if image.width >= 1024 -%}{{ image | image_url: width: 1024 }} 1024w,{%- endif -%}
  {%- if image.width >= 1280 -%}{{ image | image_url: width: 1920 }} 1280w,{%- endif -%}
  {%- if image.width >= 1440 -%}{{ image | image_url: width: 2000 }} 1440w,{%- endif -%}
  {%- if image.width >= 1920 -%}{{ image | image_url: width: 2400 }} 1920w{%- endif -%}
{%- endcapture -%}

<img id="Image-{{ image.id }}-{{ responsive_image_counter }}"
  srcset="{{ image_widths }}"
  sizes="(min-width: 1280px) 1280px, (min-width: 1024px) 1024px, (min-width: 750px) 750px, (min-width: 550px) 550px, 375px"
  src="{{ image | image_url: width: 750 }}"
  {% unless lazy == false %}
  loading="lazy"
  {% endunless %}
  alt="{{ image.alt | escape }}"
  width="{{ image.width }}"
  height="{{ image.width | divided_by: image.aspect_ratio }}"
  class="{{ image_class }}"
  {% if data_unisex != blank %}
  data-unisex="{{ data_unisex }}"
  {% endif %}
>
