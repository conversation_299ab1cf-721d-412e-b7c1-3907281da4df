<button class="w-full flex justify-end lg:px-10 px-5 py-3" neptune-engage="[{
  'targets':[{
    'selector':'html',
    'attributes':[{
      'att':'data-active-modal',
      'set':'_remove'
    }]
  },
  { 
    'selector':'[data-return-focus]',
    'attributes':[{
      'att':'data-return-focus',
      'set':'_remove'
    }],
    'focus':true
  }]
}]">
  {% render 'icon', icon: 'close' width:24 height:24 strokeWidth:1 %}
</button>

<div class="button-container flex gap-1.5 pt-4 pb-5 lg:px-10 px-5">
  <button class="w-full button button--light" neptune-engage="[{
    'targets':[{
      'selector':'html',
      'attributes':[{
        'att':'data-active-modal',
        'set':'_remove'
      }]
    },
    { 
      'selector':'[data-return-focus]',
      'attributes':[{
        'att':'data-return-focus',
        'set':'_remove'
      }],
      'focus':true
    }]
  }]">
    Apply
  </button>

  <button class="w-full button button--light" onclick="Collection.clear()">
    Clear All
  </button>
</div>

{% raw %}

{% for set in collection.filters.all %}

{% endraw %}

  {% if inclusion != blank %}
    {% raw %}
    {% unless collection.settings.filter_exclusions contains set.label %}
      {% continue %}
    {% endunless %}
    {% endraw %}
  {% endif %}

  {% if exclusion != blank %}
    {% raw %}
    {% if collection.settings.filter_exclusions contains set.label %}
      {% continue %}
    {% endif %}
    {% endraw %}
  {% endif %}
  
  {% raw %}
  {% if set.label == blank %}
    {% continue %}
  {% endif %}
  {% endraw %}

  {% if config.filter_options_style_default == 'buttons' %}
    {% assign exposed_accordions = true %} 
  {% endif %}

  {% raw %}
    {% assign label_handle = set.label | handle %}
    {% assign filter_options_style = filterMap[label_handle] %}
    {% if filter_options_style == blank %}
      {% assign filter_options_style = collection.settings.filter_options_style_default %}
    {% endif %}

    {% assign exposed_accordions = false %}
    {% if filter_options_style == 'buttons' or filter_options_style == 'images' %}
      {% assign exposed_accordions = true %}
    {% endif %}
  {% endraw %}

  {% raw %}

    {% if collection.filters.active_set == set.key %}
      <details type="accordion" class="accordion group filter-options--{{ filter_options_style }}" group="collection-filters" open>
    {% elsif exposed_accordions %}
      <details type="accordion" class="accordion group filter-options--{{ filter_options_style }}" open>
    {% else %}
      <details type="accordion" class="accordion group filter-options--{{ filter_options_style }}" group="collection-filters">
    {% endif %}

      <summary class="accordion-title">
        <span>
          {{ set.label | replace: '_', ' ' }}

          {% if collection.filters.applied[set.key].size > 0 %}
            <span class="ml-1">
              ({{ collection.filters.applied[set.key].size }})
            </span>
          {% endif %}
        </span>

        <span class="group-open:hidden group-icons">
          {% endraw %}
          {%- render 'icon', icon: 'plus', width: 16, height: 16, strokeWidth: 2 -%}
          {% raw %}
        </span>
        <span class="hidden group-open:block group-icons">
          {% endraw %}
          {%- render 'icon', icon: 'minus', width: 16, height: 16, strokeWidth: 2 -%}
          {% raw %}
        </span>
      </summary>

      <div class="accordion-panel accordion-panel--{{ set.label | replace: ' ', '-' | downcase }}">
        <div class="field px-0">
          {% for group in set.option_groups %}
            {% if group.options != blank %}
              <div class="group-{{ forloop.index }} mb-3 last:mb-0 {% unless set.label contains 'color' or set.label contains 'Color' or set.label contains 'Size' or set.label contains 'size' %} group-filters {% endunless %}">
                {% if group.name != blank %}
                  <p class="mt-2 mb-0">{{ group.name }}</p>
                {% endif %}
                <div class="accordion-group accordion-group--{{ set.label | replace: ' ', '-' | downcase }} flex flex-wrap gap-x-6 {% if filter_options_style contains 'images' %}dragger-scroll-x cursor-grab active:cursor-grabbing{% endif %}">

                  {% for option in group.options %}
                    {% assign option_handle = option.label | handle %}

                    {% assign label = option.label | trim | replace: '_', ' ' %}
                    {% assign option_label = filterItemNameOverrides[label] %}
                    {% if option_label == blank %}
                      {% assign option_label = label %}
                    {% endif %}

                    <label class="accordion-label">
                      {% if option.active %}
                        <input value="{{ set.key }}" type="checkbox" oninput="Collection.filter('{{ set.key }}', '{{ option.value }}')" checked>
                      {% else %}
                        <input value="{{ set.key }}" type="checkbox" oninput="Collection.filter('{{ set.key }}', '{{ option.value }}')">
                      {% endif %}

                      {%- unless set.label contains 'color' or set.label contains 'Color' -%}
                        {% assign option_image_url = imageMap[option_handle] %}
                        {% if option_image_url != blank %}
                          <img src="{{ option_image_url }}" loading="lazy" class="accordion-image">
                        {% endif %}
                      {%- endunless -%}
                    
                      {% if set.label contains 'color' or set.label contains 'Color' %}
                        <span class="rounded-full inline-block align-middle h-6 w-6 leading-none border border-gray-dark bg-charcoal" style="background-color: {{ colorMap[option_handle] }};"></span>
                      {% endif %}
                      
                      <span class="!hidden"></span>
                      <span>{{ option_label }}</span>
                    </label>
                    
                  {% endfor %}
                </div>
              </div>
            {% endif %} 
          {% endfor %}
        </div>
      </div>
    </details>

{% endfor %}
{% endraw %}
