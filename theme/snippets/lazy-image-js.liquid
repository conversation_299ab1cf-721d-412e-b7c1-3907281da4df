{%- comment -%}
  Usage:
    In your liquid template file, copy the following line
  - {% render 'lazy-image-js' with image: block.settings.image, image_class: "block absolute top-0 left-0 max-w-full w-full h-full object-cover object-center" %}
{%- endcomment -%}

{% raw %}
{%- capture responsive_image_counter -%}{% increment responsive_image_counter %}{%- endcapture -%}
{%- capture image_class -%}{% endraw %}{{ image_class }}{% raw %}{%- endcapture -%}

<img id="Image-{{ image.id }}-{{ responsive_image_counter }}"
  {{'s'}}rc="{{ image.src }}"
  loading="lazy"
  alt="{{ image.alt | escape }}"
  width="{{ image.width }}"
  height="{{ image.width | divided_by: image.aspect_ratio }}"
  class="{{ image_class }}"
>
{% endraw %}