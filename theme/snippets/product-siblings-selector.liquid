{% unless product.tags contains 'hide-swatches' or product.tags contains 'sale' %}
  <div class="product-siblings option-selection flex flex-wrap flex-row gap-2.5 ml-0 pt-2 mb-4 mr-auto px-5 lg:px-10" neptune-permanent="siblings" data-sibling-swatches neptune-sort>

    {% assign color = '' %}
    {% for tag in product.tags %}
      {% assign tag_handle = tag | handleize %}
      {% if tag_handle contains 'color-' %}
        {% assign color = tag_handle | remove: 'color-' | strip %}
        {% break %}
      {% endif %}
    {% endfor %}
  
    <a href="/{% if collection %}collections/{{ collection.handle }}/{% endif %}products/{{ product.handle }}" class="product-siblings__swatch active {{ color }}" data-product-handle="{{ product.handle }}" aria-current="true" aria-selected="true" 
      onclick="Neptune.periscope.load(_n.qs('#shopify-section-{{ section.id}}'), {
        url:'{% unless routes.root_url == '/' %}{{ routes.root_url }}{% endunless %}{% if collection %}/collections/{{collection.handle}}{% endif %}/products/{{ product.handle }}?sections={{ section.id}}',
        path:'{{ section.id}}',
        preload:true,
        skeleton:true
      }).then(()=>{Neptune.product.init()});"
    neptune-engage="{
      preventDefault:1,
      targets:[
        {
          selector: '[data-sibling-swatches] .active',
          classes:remove:active,
          attributes:[{att:'aria-current', set:'false'},{att:'aria-selected', set:'false'}]
        },
        {
          selector: _self,
          classes:add:active,
          attributes:[{att:'aria-current', set:'true'}, {att:'aria-selected', set:'true'}]
        }
      ],
      history:replace:url:{{ product.handle }}
    }">
      {% if product.metafields.product.color_swatch != blank %}
        {% assign swatchImage = product.metafields.product.color_swatch | img_url: '64x' %}
      {% else %}
        {% assign swatchImage = product.images[0].src | image_url: crop: 'region', width: 384, height: 512, crop_left: 831, crop_top: 1187, crop_width: 64, crop_height: 32 %}
      {% endif %}
      <img src="{{ swatchImage }}" alt="{{ product.title }}" loading="lazy" width="400" height="533" class="h-full w-full object-cover">
      <p class="hidden">{{ product.title }}</p>
    </a>

    {% assign siblings = product.metafields.family.siblings.products | default: product.metafields.family.siblings.value.products %}

    {% for sibling in siblings %}

      {% unless sibling.tags contains 'hide-swatches' or sibling.tags contains 'sale' %}

      {% assign color = '' %}
      {% for tag in sibling.tags %}
        {% assign tag_handle = tag | handleize %}
        {% if tag_handle contains 'color-' %}
          {% assign color = tag_handle | remove: 'color-' | strip %}
          {% break %}
        {% endif %}
      {% endfor %}
  
      <a href="/{% if collection %}collections/{{ collection.handle }}/{% endif %}products/{{ sibling.handle }}" class="product-siblings__swatch {{ color }}" aria-current="false" data-product-handle="{{ sibling.handle }}"  aria-selected="false" onclick="Neptune.periscope.load(_n.qs('#shopify-section-{{ section.id}}'), {
        url:'{% unless routes.root_url == '/' %}{{ routes.root_url }}{% endunless %}{% if collection %}/collections/{{collection.handle}}{% endif %}/products/{{ sibling.handle }}?sections={{ section.id}}',
        path:'{{ section.id}}',
        preload:true,
        skeleton:true
      }).then(()=>{Neptune.product.init()});" neptune-engage="{
        preventDefault:1,
        targets:[
          {
            selector: '[data-sibling-swatches] .active',
            classes:remove:active,
            attributes:[{att:'aria-current', set:'false'},{att:'aria-selected', set:'false'}]
          },
          {
            selector: _self,
            classes:add:active,
            attributes:[{att:'aria-current', set:'true'},{att:'aria-selected', set:'true'}]
          }
        ],
        history:replace:url:{{ sibling.handle }}
      }">
        {% if all_products[sibling.handle].metafields.product.color_swatch != blank %}
          {% assign swatchImage = all_products[sibling.handle].metafields.product.color_swatch | img_url: '64x' %}
        {% else %}
          {% assign swatchImage = sibling.featured_image | append: '&crop=region&crop_height=32&crop_left=831&crop_top=1187&crop_width=64' %}
        {% endif %}
        <img src="{{ swatchImage }}" alt="{{ sibling.title }}" loading="lazy" width="400" height="533" class="h-full w-full object-cover">
        <p class="hidden">{{ sibling.title }}</p>
      </a>
      {% endunless %}

    {% endfor %}

  </div>
{% endunless %}

