<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Libre+Caslon+Text&display=swap" rel="stylesheet">

{% style %}

  @font-face {
    font-family: 'MonumentGrotesk-Regular';
    src: url({{'MonumentGrotesk-Regular.woff2' | asset_url }}) format('woff2'),
         url({{'MonumentGrotesk-Regular.woff' | asset_url }}) format('woff');
    font-weight:  normal;
    font-style:   normal;
    font-stretch: normal;
  }

  @font-face {
    font-family: 'MonumentGrotesk-Bold';
    src: url({{'MonumentGrotesk-Bold.woff2' | asset_url }}) format('woff2'),
         url({{'MonumentGrotesk-Bold.woff' | asset_url }}) format('woff');
    font-weight:  normal;
    font-style:   normal;
    font-stretch: normal;
  }

  @font-face {
    font-family: 'ABC Monument Grotesk Black';
    src: url({{'ABCMonumentGrotesk-Black.woff2' | asset_url }}) format('woff2'),
        url({{'ABCMonumentGrotesk-Black.woff' | asset_url }}) format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'ABC Monument Grotesk Light';
    src: url({{'ABCMonumentGrotesk-Light.woff2' | asset_url }}) format('woff2'),
        url({{'ABCMonumentGrotesk-Light.woff' | asset_url }}) format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'ABC Monument Grotesk Medium';
    src: url({{'ABCMonumentGrotesk-Medium.woff2' | asset_url }}) format('woff2'),
        url({{'ABCMonumentGrotesk-Medium.woff' | asset_url }}) format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'ABC Monument Grotesk Medium Italic';
    src: url({{'ABCMonumentGrotesk-MediumItalic.woff2' | asset_url }}) format('woff2'),
        url({{'ABCMonumentGrotesk-MediumItalic.woff' | asset_url }}) format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Futura Condensed BQ';
    src: url({{'FuturaCondensedBQ-ExtraBold.woff2' | asset_url }}) format('woff2'),
        url({{'FuturaCondensedBQ-ExtraBold.woff' | asset_url }}) format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  {{ settings.additonal_font_override_css }}

{% endstyle %}
