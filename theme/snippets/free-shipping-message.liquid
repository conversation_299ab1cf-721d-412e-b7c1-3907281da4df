{% if settings.free_shipping_enable %}
{%- liquid
   assign free_shipping_values = settings.free_shipping_values | newline_to_br | split: '<br />'
   assign free_shipping_message = settings.free_shipping_message | newline_to_br | split: '<br />'
   assign free_shipping_message_success = settings.free_shipping_message_success | newline_to_br | split: '<br />'
-%}
<script>
  window.freeShippingMessages = {
    "values": {
    {%- for value in free_shipping_values -%}
      {%- liquid
        assign code = value | split: '|' | first | strip | uppercase
        assign messaging = value | split: '|' | last | strip
      -%}
      "{{ code }}": {{ messaging | json }}{%- unless forloop.last -%},{%- endunless -%}
    {%- endfor -%}
    },
    "message": {
    {%- for value in free_shipping_message -%}
      {%- liquid
        assign code = value | split: '|' | first | strip | uppercase
        assign messaging = value | split: '|' | last | strip
      -%}
      "{{ code }}": {{ messaging | json }}{%- unless forloop.last -%},{%- endunless -%}
    {%- endfor -%}
    },
    "message_success": {
    {%- for value in free_shipping_message_success -%}
      {%- liquid
        assign code = value | split: '|' | first | strip | uppercase
        assign messaging = value | split: '|' | last | strip
      -%}
    "{{ code }}": {{ messaging | json }}{%- unless forloop.last -%},{%- endunless -%}
    {%- endfor -%}
    }
  }
</script>
{% raw %}

{% capture shipping_threshold %}{% endraw %}{{ settings.free_shipping_values }}{% raw %}{% endcapture %}
{% capture message %}{% endraw %}{{ settings.free_shipping_message }}{% raw %}{% endcapture %}
{% capture message_success %}{% endraw %}{{ settings.free_shipping_message_success }}{% raw %}{% endcapture %}

{% assign shipping_threshold = freeShippingMessages.values[Shopify.country] %}
{% assign message = freeShippingMessages.message[Shopify.country] %}
{% assign message_success = freeShippingMessages.message_success[Shopify.country] %}

{% assign threshold_difference = shipping_threshold | times: 100 | minus: total_price %}
{% assign difference_price = threshold_difference | divided_by: 100 %}

{%- if threshold_difference > 0 -%}
  <span id="freeShippingMsg" class="{% endraw %}{{ class }}{% raw %}">
  	{{- message | replace: shipping_threshold, difference_price -}}
  </span>
{%- else -%}
  <span id="freeShippingMsgSuccess" class="{% endraw %}{{ class }}{% raw %}">
  	{{- message_success -}}
  </span>
{%- endif -%}

{% endraw %}
{% endif %}
