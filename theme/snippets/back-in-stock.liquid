<div class="back-in-stock back-in-stock-container w-full p-0 my-4 hidden order-4">
  <div class="back-in-stock__message back-in-stock__message--success hidden mb-3 font-highlight text-xs">
    {{ 'products.product.back_in_stock.success' | t }}
  </div>
  <div class="back-in-stock__message back-in-stock__message--prompt mb-3">
    {{ 'products.product.back_in_stock.prompt' | t }}
  </div>
  <div class="back-in-stock__field field flex flex-wrap justify-left w-full relative">
    <input type="text"  class="w-full block lg:p-3.5 p-2 border border-black font-bold flex-grow leading-none back-in-stock__input text-[10px]"
    placeholder="{{ 'products.product.back_in_stock.email_placeholder' | t }}" onkeydown="if (event.keyCode == 13) { event.preventDefault(); this.nextElementSibling.click()}">
    <button type="button" 
      class="absolute right-0 top-0 h-full bg-transparent font-light py-0 lg:px-4 px-2 rounded-md text-white tracking-widest ml-auto flex flex-col justify-center items-center"
      data-product-id='{% if product %}{{ product.id }}{% else %}{% raw %}{{ product.id }}{% endraw %}{% endif %}' 
      data-variant-id='{% if variant %}{{ variant.id }}{% else %}{% raw %}{{ product.variants[0].id }}{% endraw %}{% endif %}' 
      onclick="BackInStock.send(this.previousElementSibling.value,this.dataset.productId, this.dataset.variantId); BackInStock.optin(this.closest('.back-in-stock__field').nextElementSibling.querySelector('input')); return false;">
      <span class="sr-only">{{ 'products.product.back_in_stock.submit_label' | t }}</span>
      <span class="text-black text-2xs uppercase bg-white">
        {% render 'icon' icon:'chevron-right', height:16, width:16 %}
      </span>
    </button>
  </div>
  <div class="back-in-stock__opt-in flex items-center gap-2 w-full mt-2">
    <input type="checkbox" id="back-in-stock-optin_{% if product %}{{ product.id }}{% else %}{% raw %}{{ product.id }}{% endraw %}{% endif %}" name="back-in-stock-optin"/>
    <label class="flex-1 mt-0 back-in-stock-optin__label" for="back-in-stock-optin_{% if product %}{{ product.id }}{% else %}{% raw %}{{ product.id }}{% endraw %}{% endif %}">
      {{ 'products.product.back_in_stock.checkbox_label' | t }}
    </label>
  </div>
</div>

<script>
  window.HSTV45 = "{{ settings.klaviyo_api_key | base64_encode }}";
  window.HSTV46 = "{{ settings.klaviyo_private_api_key | base64_encode }}";
  window.listId = "{{ settings.klaviyo_list }}";
</script>