<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Product",
  "@id": "{{ shop.url }}{{ product.url }}",
  {% if product.selected_or_first_available_variant.sku != blank %}
    "gtin13": "{{ product.selected_or_first_available_variant.sku }}",
    "sku": "{{ product.selected_or_first_available_variant.sku }}",
  {% endif %}
  "name": "{{ product.title | escape }}",
  "alternateName": "{{ product.title | escape }} | MOTHER Denim",
  "manufacturer": "{% if product.vendor contains 'Mother' %}MOTHER Denim{% else %}{{ product.vendor | escape }}{% endif %}",
  {% if product.collections %}
    "isSimilarTo": [
      {% for collection in product.collections %}
        "{{ shop.url }}/collections/{{ collection.handle }}"{% unless forloop.last %},{% endunless %}
      {% endfor %}
    ],
  {% endif %}
  {% if product.featured_image %}
    {% assign image_size = product.featured_image.width | append: 'x' %}
    "image": [
      "https:{{ product.featured_image.src | img_url: image_size }}"
    ],
  {% endif %}
  "description": "{{ product.description | strip_html | strip_newlines | escape }}",
  {% if product.variants[0].option1  %}
    "color": "{{ product.variants[0].option1 }}",
  {% endif %}
  "url": "{{ shop.url }}{{ product.url }}",
  "brand": {
    "@type": "Brand",
    "@id": "{{ shop.url }}/#organization",
    "name": "{{ product.vendor | escape }}",
    "logo": "https://cdn.shopify.com/s/files/1/2094/5935/files/MOTHER_Denim_Official_Logo_Lg.jpg?v=1584649242"
  },
  "hasMerchantReturnPolicy" : {
    "@type" : "MerchantReturnPolicy",
    "url": "{{ shop.url }}/pages/customer-care"
  },
  {% if product.variants %}
    "offers": [
      {% for variant in product.variants %}
        {
          "@type": "Offer",
          "availability": "http://schema.org/{% if variant.available %}InStock{% else %}OutOfStock{% endif %}",
          "priceValidUntil": "{% assign seconds = 365 | times: 24 | times: 60 | times: 60 %}{{ 'now' | date: "%s" | plus: seconds | date: "%Y-%m-%d" }}",
          "name": "{{ product.title | escape }} - {{ variant.option1 }} / {{ variant.option2 }}",
          {% if variant.sku != blank %}
            "sku": "{{ variant.sku }}",
            "gtin13": "{{ variant.sku }}",
          {% endif %}
          "price": "{{ variant.price | money | remove: '$' }}",
          "priceCurrency": "{% if localization.country.currency.iso_code %}{{ localization.country.currency.iso_code }}{% else %}{{ shop.currency }}{% endif%}",
          "url": "{{ shop.url }}{{ variant.url }}",
          "itemCondition": "https://schema.org/NewCondition"
        }{% unless forloop.last %},{% endunless %}
      {% endfor %}
    ]
  {% endif %}
}
</script>
