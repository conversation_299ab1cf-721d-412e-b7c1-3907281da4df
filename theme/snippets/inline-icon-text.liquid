{%- if source contains '((' -%}
  {%- liquid
    assign parts = source | split: '(('
  -%}

  {%- for part in parts -%}
    {%- if part contains '))' -%}
      {%- liquid 
        assign icon_ref = part | remove: '))' | split: ' ' | first
        assign dims = part | split: '))' | first | remove: icon_ref | strip | split: 'x' 
      -%}
      {%- capture icon -%}
        <span>{% render 'icon' icon:icon_ref, fill:'currentColor', strokeWidth:0, width:dims[0], height:dims[1] %}</span>
      {%- endcapture -%}
    {% endif %}

    {{ icon }}

    {%- assign bits = part | split: '))' -%}
    {%- if bits.size > 1 -%}
      <span>{{ part | split: '))' | last }}</span>
    {%- endif -%}
  {% endfor %}
{%- else -%}
  {{ source }}
{%- endif -%}