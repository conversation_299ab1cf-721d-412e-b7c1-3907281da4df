{% for i in (1..6) %}
  {% capture image %}image_{{i}}{% endcapture %}
  {% capture url %}url_{{i}}{% endcapture %}
  {% capture position %}image_position_{{i}}{% endcapture %}
  {% capture positionMobile %}image_position_mobile_{{i}}{% endcapture %}
  {% capture title %}title_{{i}}{% endcapture %}

  {% if settings[image] != blank %}
    <li class="{{ settings.mobile_width | default: 'col-span-4'}} {{ settings.desktop_width | default: 'lg:col-span-4' }} panel__item panel__item--{{ type | handle }} group">
      <a href="{{ settings[url] }}" class="panel__item--image-link block">
        <div class="panel__image {{ settings.image_height }} {{ settings.image_height_mobile }} bg-white overflow-hidden {{ settings.corners }} {{ settings.corners_mobile }}">
          {% capture image_classes %}object-cover w-full h-full absolute inset-0 {{ settings[position] }} {{ settings[positionMobile] }}{% endcapture %}
          {% render 'lazy-image' with image: settings[image], image_class: image_classes %}
        </div>
        {% if settings[title] != blank %}<p class="mt-1 mb-2 font-caption-caps text-center">{{ settings[title] }}</p>{% endif %}
      </a>
    </li>
  {% endif %}
{% endfor %}
{% if settings.show_divder != blank %}
  {% render 'panel-item-divider' %}
{% endif %}
{% if settings.subpane_end == true %}{% break %}{% endif %}