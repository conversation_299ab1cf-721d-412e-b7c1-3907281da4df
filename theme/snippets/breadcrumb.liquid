{% for link in links %}
  {% if link.active or link.child_active %}
    <a href="{{ link.url }}" class="breadcrumb__item breadcrumb__item--{{ link.title | handle }}">{{ link.title }}</a>
    <span class="breadcrumb__delimiter">{{ delimiter }}</span>
    {% if link.child_active %} 
      {% render 'breadcrumb' links:link.links delimiter:delimiter %}
    {% endif %}
    {% break %}
  {% endif %}

{% endfor %} 