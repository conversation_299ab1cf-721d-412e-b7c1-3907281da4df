{% comment %} Address book template {% endcomment %}
<script id="address-book-template" type="text/x-template">
	<section id="addressBook" class="ac-section ac-addresses" role="tabpanel">

		<section-header :backClick="onBackClick"
						:options="sectionHeaderOptions"
						:title="sectionHeaderTitle">
		</section-header>

		<address-item v-for="address in paginatedItems"
					class="ac-address"
					:address="address"
					:key="address.id">
		</address-item>

		<div class="ac-addresses__add">
			<button class="btn btn--secondary btn--small" type="button" @click="setView('address-new')">
				{{ 'customer.addresses.add_new' | t }}
			</button>
		</div>

		<pagination :options="paginationOptions"
					:items="items">
		</pagination>

	</section>
</script>

{% comment %} Address item template {% endcomment %}
<script id="address-item-template" type="text/x-template">
	<div class="ac-address">
		<div class="ac-address__view ac-content-box">
			<h2 class="ac-hdg">
				Address ${ address.displayIndex }
				<span v-if="address.default"> (Default)</span>
			</h2>
			<div v-html="addressFormat"></div>
			<div class="ac-content-box__actions">
				<button class="ac-content-box__action btn btn--small" type="button" @click="setViewModelData('currentAddressEdit', address, 'address-edit')">
					{{ 'customer.addresses.edit' | t }}
				</button>
				<button class="ac-content-box__action btn btn--small btn--secondary" type="button" data-confirm-message="{{ 'customer.addresses.delete_confirm' | t }}" @click="destroyAddress($event, address.id)">
					{{ 'customer.addresses.delete' | t }}
				</button>
			</div>
		</div>
	</div>
</script>

{% comment %} Address edit template {% endcomment %}
<script id="address-edit-template" type="text/x-template">
	<section id="addressEdit" class="ac-section ac-address-edit">
		<form method="post" :action="formAction" accept-charset="UTF-8" @submit.prevent="onSubmit($event)">
			<input type="hidden" value="customer_address" name="form_type" />
			<input type="hidden" name="utf8" value="✓" />

			<section-header :backClick="onBackClick"
							:options="sectionHeaderOptions"
							:title="sectionHeaderTitle">
			</section-header>

			<ul class="form-list">

				<li class="form-item form-item--half">
					<div class="input-placeholder">
						<label for="addressEditFirstName">
							{{ 'customer.addresses.first_name' | t }}
						</label>
						<input id="addressEditFirstName" class="input" name="address[first_name]" type="text" :value="address.first_name" required />
					</div>
				</li>

				<li class="form-item form-item--half">
					<div class="input-placeholder">
						<label for="addressEditLastName">
							{{ 'customer.addresses.last_name' | t }}
						</label>
						<input id="addressEditLastName" class="input" name="address[last_name]" type="text" :value="address.last_name" required />
					</div>
				</li>

				<li class="form-item form-item--half">
					<div class="input-placeholder">
						<label for="addressEditCompany">
							{{ 'customer.addresses.company' | t }}
						</label>
						<input id="addressEditCompany" class="input" name="address[company]" type="text" :value="address.company" />
					</div>
				</li>

				<li class="form-item form-item--half">
					<div class="input-placeholder">
						<label for="addressEditPhone">
							{{ 'customer.addresses.phone' | t }}
						</label>
						<input id="addressEditPhone" class="input" name="address[phone]" type="tel" :value="address.phone" />
					</div>
				</li>

				<li class="form-item form-item--half">
					<div class="input-placeholder">
						<label for="addressEditAddress1">
							{{ 'customer.addresses.address1' | t }}
						</label>
						<input id="addressEditAddress1" class="input" name="address[address1]" type="text" :value="address.address1" required />
					</div>
				</li>

				<li class="form-item form-item--half">
					<div class="input-placeholder">
						<label for="addressEditAddress2">
							{{ 'customer.addresses.address2' | t }}
						</label>
						<input id="addressEditAddress2" class="input" name="address[address2]" type="text" :value="address.address2" />
					</div>
				</li>

				<li class="form-item form-item--half">
					<div class="select">
						<select id="addressEditCountry" class="input" name="address[country]" :data-default="address.country_name" required>
							{{ all_country_option_tags }}
						</select>
					</div>
				</li>

				<li class="form-item form-item--half">
					<div id="addressEditProvinceContainer" class="select" style="display: none;">
						<select id="addressEditProvince" class="input" name="address[province]" :data-default="address.province" required>
						</select>
					</div>
				</li>

				<li class="form-item form-item--half">
					<div class="input-placeholder">
						<label for="addressEditCity">
							{{ 'customer.addresses.city' | t }}
						</label>
						<input id="addressEditCity" class="input" name="address[city]" type="text" :value="address.city" required />
					</div>
				</li>

				<li class="form-item form-item--half">
					<div class="input-placeholder">
						<label for="addressEditZip">
							{{ 'customer.addresses.zip' | t }}
						</label>
						<input id="addressEditZip" class="input" name="address[zip]" type="text" :value="address.zip" required />
					</div>
				</li>

				<li v-if="! address.default" class="form-item">
					<div class="checkbox">
						<input id="addressEditDefault" name="address[default]" type="checkbox" value="1" />
						<label for="addressEditDefault">
							{{ 'customer.addresses.set_default' | t }}
						</label>
					</div>
				</li>

				<li class="form-item ac-form-action">
					<button class="btn btn--full" type="submit">
						{{ 'customer.addresses.update' | t }}
					</button>
				</li>

			</ul>

			<input type="hidden" name="_method" value="put">
		</form>
	</section>
</script>

{% comment %} Address new template {% endcomment %}
<script id="address-new-template" type="text/x-template">
	<section id="addressNew" class="ac-section ac-address-new" role="tabpanel" aria-labelledby="addressBookTabTrigger">
		<form method="post" action="/account/addresses" accept-charset="UTF-8" @submit.prevent="onSubmit($event)">
			<input type="hidden" value="customer_address" name="form_type" />
			<input type="hidden" name="utf8" value="✓" />

			<section-header :backClick="onBackClick"
							:options="sectionHeaderOptions"
							:title="sectionHeaderTitle">
			</section-header>

			<ul class="form-list">

				<li class="form-item form-item--half">
					<div class="input-placeholder">
						<label for="addressNewFirstName">
							{{ 'customer.addresses.first_name' | t }}
							<span class="input__asterisk"> *</span>
						</label>
						<input id="addressNewFirstName" class="input" name="address[first_name]" type="text" required />
					</div>
				</li>

				<li class="form-item form-item--half">
					<div class="input-placeholder">
						<label for="addressNewLastName">
							{{ 'customer.addresses.last_name' | t }}
							<span class="input__asterisk"> *</span>
						</label>
						<input id="addressNewLastName" class="input" name="address[last_name]" type="text" required />
					</div>
				</li>

				<li class="form-item form-item--half">
					<div class="input-placeholder">
						<label for="addressNewCompany">
							{{ 'customer.addresses.company' | t }}
						</label>
						<input id="addressNewCompany" class="input" name="address[company]" type="text" />
					</div>
				</li>

				<li class="form-item form-item--half">
					<div class="input-placeholder">
						<label for="addressNewPhone">
							{{ 'customer.addresses.phone' | t }}
							<span class="input__asterisk"> *</span>
						</label>
						<input id="addressNewPhone" class="input" name="address[phone]" type="tel" required />
					</div>
				</li>

				<li class="form-item form-item--half">
					<div class="input-placeholder">
						<label for="addressNewAddress1">
							{{ 'customer.addresses.address1' | t }}
							<span class="input__asterisk"> *</span>
						</label>
						<input id="addressNewAddress1" class="input" name="address[address1]" type="text" required />
					</div>
				</li>

				<li class="form-item form-item--half">
					<div class="input-placeholder">
						<label for="addressNewAddress2">
							{{ 'customer.addresses.address2' | t }}
						</label>
						<input id="addressNewAddress2" class="input" name="address[address2]" type="text" />
					</div>
				</li>
				<li class="form-item form-item--half">
					<div class="select">
						<label for="addressNewCountry">Country</label>
						<select id="addressNewCountry" class="input" name="address[country]" required>
							{{ all_country_option_tags }}
						</select>
					</div>
				</li>

				<li class="form-item form-item--half">
					<label for="addressNewProvince">State/Provice</label>
					<div id="addressNewProvinceContainer" class="select" style="display: none;">
						<select id="addressNewProvince" class="input" name="address[province]" required>
						</select>
					</div>
				</li>

				<li class="form-item form-item--half">
					<div class="input-placeholder">
						<label for="addressNewCity">
							{{ 'customer.addresses.city' | t }}
							<span class="input__asterisk"> *</span>
						</label>
						<input id="addressNewCity" class="input" name="address[city]" type="text" required />
					</div>
				</li>

				<li class="form-item form-item--half">
					<div class="input-placeholder">
						<label for="addressNewZip">
							{{ 'customer.addresses.zip' | t }}
							<span class="input__asterisk"> *</span>
						</label>
						<input id="addressNewZip" class="input" name="address[zip]" type="text" required />
					</div>
				</li>

				<li class="form-item">
					<div class="checkbox">
						<input id="addressNewDefault" name="address[default]" type="checkbox" value="1" />
						<label for="addressNewDefault">
							{{ 'customer.addresses.set_default' | t }}
						</label>
					</div>
				</li>

				<li class="form-item ac-form-action">
					<button class="btn btn--full" type="submit">
						{{ 'customer.addresses.add' | t }}
					</button>
				</li>

			</ul>
		</form>
	</section>
</script>