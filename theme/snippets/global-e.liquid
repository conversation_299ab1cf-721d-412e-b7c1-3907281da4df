{% comment %}
output currency settigns to later injest in Neptune / liquid.js
{% endcomment %}
<script>

  {% case localization.country.currency.iso_code %}
    {% when 'USD' %}
      window.__currencyFormat = "{{ '999999' | money }}";
    {% when 'EUR' %}
      window.__currencyFormat = "{{ localization.country.currency.symbol }}{{ '999999' |  money_without_currency }}";
    {% when 'INR' %}
      window.__currencyFormat = "{{ localization.country.currency.symbol }}{{ '999999' |  money_without_currency }}";
    {% else %}
      window.__currencyFormat = "{{ '999999' | money_with_currency }}";
  {% endcase %}

  Object.defineProperty(window, 'currencies', {
    configurable: true,
    enumerable: true,
    set(value) {
      this._currencies = value;
      _n.trigger(window,'Currencies:assigned')
    },
    get() {
      return this._currencies;
    }
  });

  {%- unless localization.country.iso_code == 'US' -%}

    window.liquidCurrencySchemas = {
      {% for country in localization.available_countries -%}
        {%- liquid 
          assign symbol = country.currency.symbol
          assign number_format = nil
          if country.currency.iso_code == 'EUR'
            assign number_format = 'de-DE'
          endif
        -%}
      "{{ country.currency.iso_code }}":{ 
          "symbol": "{{ symbol }}" ,
          {%- if number_format != nil -%}
          "numberFormat": "{{ number_format }}"
          {%- endif -%}
        }{% unless forloop.last %},{% endunless %}
      {% endfor %}
      }


    // create style tag to hide elements
    var hideStyle = document.createElement('STYLE');
    hideStyle.setAttribute('name','Localized Styles')
    hideStyle.innerHTML = `${`{{ settings.international_hide }}`.split(',').filter(function(c){return !!c}).map(function(c){
      return `html ${c} { display:none!important; } `
    }).join("\n") }`
    console.log('hideStyle.innerHTML',hideStyle.innerHTML)
    document.getElementsByTagName("head")[0].appendChild(hideStyle);

    // handle handle-based redirects

    window.international_redirects = ('{{ settings.international_redirect }}').split(',').map(e=>{return e.trim()}).filter(e=>{return !!e});
    if ( window.international_redirects.length && window.international_redirects.includes(document.location.pathname.split('/').reverse()[0]) ) {
      window.location = '/'
    }

    window.international_tag_redirects = ('{{ settings.international_tag_redirect }}').split(',').map(e=>{
      return e.trim()
    }).filter(e=>{
      return !!e
    })

    // tag-based redirects
    {% if template == "product" %} 
      setTimeout(function(){
        if(window.international_tag_redirects?.filter(value => 
          {{ product.tags | json }}.includes(value)
        ).length) window.location = '/';
      },200)
    {% endif %}

    // tag-based exclusions
    document.addEventListener('template:rendered', function() {
      window.international_tag_redirects.forEach(function(tag){
        document.querySelectorAll(`[data-tags*="${tag}"]`).forEach(function(el){
          el.remove()
        })
      })
    })

    // Wait for currencies object to be defined
    window.addEventListener('Currencies:assigned', () => {
      // Override EUR specifically after the spread
      if (window.currencies && window.currencies.EUR) {
        window.currencies.EUR = {
          ...window.currencies.EUR,
          symbol: '€',
          numberFormat: 'de-DE',
          label: ''  // Remove the label
        };
      }
    });

  {%- endunless -%}

  window.addEventListener('Currencies:assigned', () => {
    if (window.currencies) {
      Object.keys(window.currencies).forEach(key => {
        window.currencies[key].trailingZeros = false;
      });
    }
  });

</script>