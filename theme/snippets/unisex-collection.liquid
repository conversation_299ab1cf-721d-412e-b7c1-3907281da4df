{% liquid
  paginate collection.products by 1000 
  for product in collection.products 
    if product.tags contains 'unisex-images' 
    assign unisex_products = true 
    assign unisex_products_count = forloop.index
    endif 
  endfor 
  endpaginate 
%}
{%- if unisex_products -%}
  <script data-unisex-products type="application/json">
    {
      "products": [
      {% paginate collection.products by 1000 %}
      {%- for product in collection.products -%}
        {%- if product.tags contains 'unisex-images' -%}
          {
            "title": "{{- product.title -}}",
            {%- liquid 
              assign women_count = 0 
              assign women_arr = '' 
              assign men_count = 0 
              assign men_arr = '' 
              for image in product.images 
                assign image_alt = image.alt | downcase 
                assign curr_ind = forloop.index0 | append: '-' 
                if image_alt contains 'women'  and women_count < 2 
                  assign women_count = women_count | plus: 1 
                  assign women_arr = women_arr | append: curr_ind 
                elsif image_alt contains 'men' and men_count < 2 
                  unless image_alt contains 'women' 
                    assign men_count = men_count | plus: 1 
                    assign men_arr = men_arr | append: curr_ind 
                  endunless 
                endif 
              endfor 
              assign women_arr = women_arr | split: '-' 
              assign men_arr = men_arr | split: '-' 
            -%}
            "women": [
              {%- for ind in women_arr -%}
                {% assign curr_ind = ind | plus: 0 %}
                "{{- product.images[curr_ind].src | img_url: '1300x' -}}"{%- if forloop.index0 == 0  and forloop.last == false -%},{%- endif -%}
              {%- endfor -%}
            ], 
            "men": [
              {%- for ind in men_arr -%}
                {% assign curr_ind = ind | plus: 0 %}
                "{{- product.images[curr_ind].src | img_url: '1300x' -}}"{% if forloop.index0 == 0 and forloop.last == false %},{% endif %}
              {%- endfor -%}
            ] 
          }{%- unless unisex_products_count == forloop.index -%},{%- endunless -%}
        {%- endif -%}
      {%- endfor -%}
      {% endpaginate %}
      ]
    }
  </script>
{%- endif -%}

