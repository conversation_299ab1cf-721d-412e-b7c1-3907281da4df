<button 
  class="panel--nav-close pt-5 pr-5 lg:p-4 {% if section.blocks[forloop.index].type == 'nav-item' %}lg:hidden{% endif %}"
  onclick="document.querySelector('[search-suggestions]').innerHTML = '';document.querySelector('[search-results]').innerHTML = '';"
  neptune-engage="{
    on:click,
    targets:[{
      selector:html
      attributes:[{
        att:data-active-modal
        set:_remove
      }],
      classes:{remove:search-in-focus}
    },
    {
      selector:body
      attributes:[{
        att:data-active-modal 
        set:_remove
      }]
    },
    {
      selector:.header__nav,
      classes:remove:active
    },
    {
      selector:.nav__item,
      classes:remove:active
    },
    {
      selector:.mobile--nav-item,
      classes:remove:active
    },
    {
      selector:'[data-return-focus]'
      attributes:[{
        att:data-return-focus 
        set:_remove
      }]
      focus:true
    }]
  }">
  <span class="sr-only">Close</span>
  {%  render 'icon' icon:'exit', height:15, width:15  %}
</button>
