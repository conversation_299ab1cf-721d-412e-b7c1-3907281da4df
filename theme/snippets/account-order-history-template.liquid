<script id="order-history-template" type="text/x-template">
	<section id="orderHistory" class="ac-section ac-order-history" role="tabpanel" tabindex="0" aria-labelledby="orderHistoryTabTrigger">
		<section-header :backClick="onBackClick"
						:options="sectionHeaderOptions"
						:title="sectionHeaderTitle">
		</section-header>
		<template v-if="hasOrders">
			<table class="ac-order-history__table ac-table">
				<thead class="desktop-and-tablet">
					<tr>
						<th>{{ 'customer.orders.date' | t }}</th>
						<th>{{ 'customer.orders.order_number' | t }}</th>
						<th>{{ 'customer.orders.fulfillment_status' | t }}</th>
					</tr>
				</thead>
				<tbody>
					<tr v-for="order in paginatedItems">
						<td data-label="{{ 'customer.orders.date' | t }}">
							${ order.created_at }
						</td>
						<td data-label="{{ 'customer.orders.order_number' | t }}">
							<a class="btn-link" :href="order.order_status_url" target="_blank">
								${ order.name }
							</a>
						</td>
						<td data-label="{{ 'customer.orders.fulfillment_status' | t }}">
							${ order.fulfillment_status_label }
						</td>
					</tr>
				</tbody>
			</table>
			<pagination :options="paginationOptions"
						:items="items">
			</pagination>
		</template>
		<p v-else>
			{{ 'customer.orders.none' | t }}
		</p>
	</section>
</script>