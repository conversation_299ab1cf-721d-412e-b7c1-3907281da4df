{% comment %} 


This is generated by webpack so don't edit this unless you want to be sad when your work gets overwritten in production.
You can edit this file in ../../components/** 


{% endcomment %}






<aside data-modal="inspiration" class="fixed bg-white z-50 modal modal-center left-1/2 top-1/2 max-w-2xl w-full focus:outline-none focus:shadow-none overflow-y-scroll max-h-main" tabindex="0" neptune-engage="{
  on:keyup,
  which: 27,
  targets:[{
    selector:body,
    attributes:[{
      att:data-active-modal,
      set:_remove
    }]
  },
  { 
    selector:'[data-return-focus]',
    attributes:[{
      att:data-return-focus,
      set:_remove
    }],
    focus:true
  }]
}">
	<article inspiration></article>
	<button class="absolute top-3 right-3 bg-gray-800 bg-opacity-50 hover:bg-white hover:text-black p-5 rounded-full text-white transform z-10 transition-all user-select-none" onclick="modal.close()">
    {% render 'icon' icon:'close' %}        
	</button>
</aside>