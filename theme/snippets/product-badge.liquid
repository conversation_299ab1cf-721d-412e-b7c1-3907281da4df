<div class="product-badge__wrapper">
<div 
  class="product-badge__main" 
  neptune-liquid="{topic:'ProductBadges', source:'eval:_n.parents(_self,`[neptune-product]`)', append:productBadges}"
>
{% raw %}
  <template>
  {% assign apply_badge = false %}
  {% for badge in productBadges %}
    {% if product.tags contains badge.tag and badge.product != blank %}
      <div id="badge-{{ badge.tag | handle }}" class="badge product-badge product-badge--product" style="color:{{ badge.color }}; background-color:{{ badge.bg_color }};">
        {{ badge.product }}
      </div>
      {% unless apply_badge %}
        {% assign apply_badge = badge %}
      {% endunless %}
    {% endif %}
  {% endfor %}
  {% if apply_badge %}
    {% assign badge = apply_badge %}
    <script>
      try {
        {% if badge.cart != blank %}
          _n.qs('[neptune-product]').properties._badge_cart = `{{ badge.cart }}`
        {% endif %}
        {% if badge.checkout != blank %}
          _n.qs('[neptune-product]').properties.badge_checkout = `{{ badge.checkout }}`
        {% endif %}
        {% if badge.email != blank %}
          _n.qs('[neptune-product]').properties._badge_email = `{{ badge.email }}`
        {% endif %}
        {% if badge.color != blank %}
          _n.qs('[neptune-product]').properties._badge_color = `{{ badge.color }}`
        {% endif %}
      } catch(err) {}
    </script>
    {% else %}
    <script>
      try {
        delete _n.qs('[neptune-product]').properties._badge_cart
        delete _n.qs('[neptune-product]').properties.badge_checkout
        delete _n.qs('[neptune-product]').properties._badge_email
        delete _n.qs('[neptune-product]').properties._badge_color
      } catch(err) {}
    </script>
  {% endif %}
  </template>
{% endraw %} 
</div>

<ul class="sibling-types">
  {% assign types = product.metafields.family.siblings.products | map: 'product_type' | uniq %}
  {% for type in types %}
  <li class="sibling-type sibling-type--{{ type | split : ' - ' | last  | handle }} {% if template.name == 'product' %}pdp{% endif %} {% if type == product.type %}active{% endif %}">
    <span>{{ type | split : ' - ' | last }}</span>
    <svg><use xlink:href="#icon-def-{{ type | split : ' - ' | last  | handle }}"/></svg>
  </li>
  {% endfor %}
  {% unless types contains product.type %}
  <li class="sibling-type sibling-type--{{ product.type | split : ' - ' | last  | handle }} {% if template.name == 'product' %}pdp{% endif %} active">
    <span>{{ product.type | split : ' - ' | last }}</span>
    <svg><use xlink:href="#icon-def-{{ product.type | split : ' - ' | last  | handle }}"/></svg>
  </li>
  {% endunless %}
</ul> 
</div>