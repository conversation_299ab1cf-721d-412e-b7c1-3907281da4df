<script id="pagination-template" type="text/x-template">
	<ol v-if="show" class="pagination list-reset cf">
		<li class="pagination__item" v-for="n in totalPages" v-if="shouldDisplayLink(n)">
			<button class="pagination__btn btn" :class="setLinkClass(n)" type="button" @click="setPage(n)" >
				<span class="pagination__btn-to-first">...</span>
				${ n }
				<span class="pagination__btn-to-last">...</span>
			</button>
		</li>
	</ol>
</script>