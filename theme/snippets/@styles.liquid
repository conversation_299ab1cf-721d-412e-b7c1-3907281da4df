{% liquid
  assign styles = ''
  for setting in _settings
    if setting contains prefix and _settings[setting] != blank
      assign property = setting | remove: prefix | remove_first: '_' | replace: '_', '-'
      assign styles = styles | append: property | append: ':' | append: _settings[setting] | append: '; '
    endif
  endfor
  if att
    if styles != blank
      echo styles | prepend:'style="' | append: '"'
    endif
  else
    echo styles
  endif
%}
