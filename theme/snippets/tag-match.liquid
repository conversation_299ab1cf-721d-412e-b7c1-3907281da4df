{% liquid
  assign matches = match | split: ','
  assign tagMatch = false
  for matchitem in matches
    assign inclusions = matchitem | split: '+'
    assign passes = true
    for inclusion in inclusions
      assign inc = inclusion | strip
      unless tags contains inc
        assign passes = false
        break
      endunless
    endfor
    if passes
      assign tagMatch = true
      break
    endif
  endfor
%}
