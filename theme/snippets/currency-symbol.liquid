{%- liquid 
  assign symbol = localization.country.currency.symbol

  if localization.country.currency.symbol == '$' and localization.country.currency.iso_code != 'USD'
    assign symbol = symbol | prepend: localization.country.currency.iso_code | remove: 'D'
  endif
  if localization.country.currency.symbol == '¥' and localization.country.currency.iso_code != 'JPY'
    assign symbol = symbol | prepend: localization.country.currency.iso_code | remove: 'Y'
  endif

  echo symbol
-%}
