<script>
    document.addEventListener('DOMContentLoaded', function () {
        window.icons = _n.qsa('svg.icon-library symbol').map(sym => sym.id.replace('icon-def-', ''))
    })
    Neptune.liquid.load('IconLibrary')
</script>
<div neptune-liquid="{topic:IconLibrary}" class="flex items-center justify-center">
    <div class="grid grid-cols-4 gap-10">
        {% raw %}
        {% for icon in icons %}
            <div class="flex flex-col items-center py-4 cursor-pointer hover:bg-gray-100" onclick="navigator.clipboard.writeText('{{icon}}')">
                {% endraw %}
                {% capture icon %}{% raw %}{{ icon }}{% endraw %}{% endcapture %}
                {% render 'icon' icon: icon %}
                {% raw %}
                <span>{{ icon }}</span>
            </div>
        {% endfor %}
        {% endraw %}
    </div>
</div>