{% for tag in product.tags %}
  {% if tag contains 'SizzleFit_' %}
    <!-- fitfinder trigger and label -->
    <a class="_ff_trigger ProductInfo__modal-link ShoppableImage--hide lg:pb-4 pb-2 flex items-center font-link" href="javascript:void(0)" onclick="Sizzle.Fit.launch('{{tag | remove: 'SizzleFit_'}}'); return false;">
      <span>{{ 'products.fit_finder.open_button' | t }}</span> 
      <span class="flex-inline mx-2">
        <svg width="18" height="9" viewBox="0 0 18 9" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_156_684)">
          <path d="M16.8484 9H1.15C0.530463 9 0.144386 8.55807 0.03673 8.11989C-0.0940246 7.58766 0.131187 7.07832 0.610895 6.82282L8.72593 2.82842V2.77973C8.72593 2.317 8.94908 1.88714 9.33804 1.60001C9.50674 1.47767 9.59378 1.27876 9.5715 1.06653C9.54098 0.802708 9.32814 0.587986 9.0654 0.557193C8.90082 0.535138 8.74243 0.585906 8.61662 0.699092C8.49742 0.810614 8.42812 0.967911 8.42812 1.12937C8.42812 1.28167 8.30562 1.40526 8.15465 1.40526C8.00368 1.40526 7.88118 1.28167 7.88118 1.12937C7.88118 0.802708 8.0173 0.49519 8.25488 0.285462C8.49577 0.0724047 8.81544 -0.0287142 9.13098 0.00707275C9.64781 0.0620015 10.0628 0.481042 10.1168 1.00287C10.1646 1.41233 9.99594 1.80225 9.66555 2.04485C9.41971 2.23086 9.27823 2.49926 9.27823 2.78014V2.82883L17.3879 6.81824C17.8693 7.07791 18.0949 7.59058 17.9625 8.12447C17.8305 8.65669 17.3933 9.00041 16.8484 9.00041V9ZM0.862505 7.31468C0.488803 7.51651 0.544074 7.88519 0.569648 7.9938C0.595221 8.10158 0.712777 8.45362 1.14959 8.45362H16.848C17.2848 8.45362 17.4023 8.10158 17.4279 7.99339C17.4535 7.88603 17.5075 7.52067 17.1334 7.31344L8.99899 3.31237L0.862505 7.31468Z" fill="black"/>
          </g>
          <defs>
          <clipPath id="clip0_156_684">
          <rect width="18" height="9" fill="white"/>
          </clipPath>
          </defs>
        </svg>
      </span> 
      {% comment %} <span sizzle-size class="_ff_my_size" style="color:#000;display:inline-block;padding:2px 5px; background-color: #EFEEF0;margin-left:2px;">
        &#63;
      </span> {% endcomment %}
    </a>
    <!-- /fitfinder trigger and label -->
    <link rel="stylesheet" href="https://api.sizzle.fit/css/sizzle.fit.css">
    <script defer>
        window.SizzleConfig = {
          site_id: 'motherdenim',
          api_key: 'M7s9502beff40bf3e4672f703ff4ba',
          api_password: '43bf99502beff40bf3e4672f703ff4ba',
          units: 'imperial'
        };
      </script>
      <script defer src="https://api.sizzle.fit/js/v1.js"></script>
    <script>
      document.addEventListener('Sizzle.Fit:selectSize', function(e){
        _n.qs(`[value="${e.detail.size}"]`).click()
      })
    </script>
  {% endif %}
{% endfor %}