{% assign ab_selected_tab = section.settings.selected_tab | handleize |  strip %}

<header class="p-5 pb-0 flex flex-col items-end lg:p-0 lg:pb-0 lg:absolute lg:right-0 lg:top-0 header__container">
  <nav class="w-full mobile--nav lg:hidden rounded-md border-primary border ">
    <ul class="swiper-container rounded-md list-none m-0 mobile--nav-list p-1 text-2xs font-body uppercase tracking-wider leading-snug bg-transparent text-primary text-center flex flex-row items-center justify-start md:justify-between overflow-x-scroll no-scrollbar"
    neptune-swiper="
    {
      centeredSlides: false,
      slidesPerView: {{ section.settings.slides_pew_view }}, 
      spaceBetween:{{ section.settings.space_tab }},
      navigation: {
        prevEl: '.swiper-mobile-nav.swiper-button-prev-unique',
        nextEl: '.swiper-mobile-nav.swiper-button-next-unique',
        disabledClass: 'swiper-button-disabled'
      }
    }" >
      <div class="swiper-wrapper">
        {% capture mobileNavClasses %}py-2 px-4 whitespace-nowrap flex items-center group-active:bg-gray-200 rounded-md uppercase{% endcapture %}
        {% for link in linklists[block.settings.menu].links %}
           {% assign link_title = link.title | handleize |  strip %}
          <li id="mobileMenu-{{ link_title }}" class="swiper-slide mobile--nav-item group {% if cart.attributes['segment__test__default-menu'] == 'tab' and link_title == ab_selected_tab %} selected {% endif %} {% if cart.attributes['segment__test__default-menu'] != 'tab' and forloop.index == 1 %}selected{% endif %}" 
            neptune-engage="{
              targets:[
                {
                  selector:'_self',
                  classes:{
                    add:active
                  },
                  siblings:{
                    classes:{
                      remove:active
                    }
                  }
                }
                {
                  selector:'.subpanes',
                  classes:{
                    remove:active
                  }
                }
              ]
            }">
            {% if link.url contains 'panel:' %}
              <button class="{{ mobileNavClasses }}" onclick="
                _n.qs('#mainMenu-{{ link.title | handle }}').click();
                document.querySelector('#mobileSearchForm').reset();
                document.querySelector('[search-results]').innerHTML = '';
                document.querySelector('[search-suggestions]').innerHTML = '';
              ">
                <span class="block">{{ link.title }}</span>
              </button>
            {% else %}
              {%- liquid
                assign external_link = false
                if link.type == "http_link" 
                  unless link.url contains '/vendors?' or link.url contains '/types?'
                    assign external_link = true
                  endunless
                endif
              -%}
              <a class="{{ mobileNavClasses }}" href="{{ link.url }}"{% if external_link %} target="_blank"{% endif %}>
                <span class="block">{{ link.title }}</span>
              </a>
            {% endif %}
          </li>
        {% endfor %}
      </div>
    </ul>

    <button class="swiper-mobile-nav swiper-prev swiper-button-prev-unique btn-control btn-control--dark p-2" tabindex="0" aria-label="Prev slide">
    {% render 'icon' icon:'chevron-left' width:30 height:30 %}
    </button>
    <button class="swiper-mobile-nav swiper-next swiper-button-next-unique btn-control btn-control--dark p-2" tabindex="0" aria-label="Next slide">
      {% render 'icon' icon:'chevron-right' width:30 height:30 %}
    </button>
  </nav>
</header>
