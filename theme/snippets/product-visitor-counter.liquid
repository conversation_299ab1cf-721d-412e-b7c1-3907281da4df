{% liquid
    assign includedTags = config.visitor_counter_inclusion_tag | newline_to_br | split: '<br />'
    assign tagCompare = ''
    assign foundMatch = false
    assign showIcon = section.settings.show_icon_vc

    for tag in includedTags
      assign tagCompare = tag
      if product.tags contains tagCompare
        assign foundMatch = true
      endif
    endfor
%}

{% style %}
    
  {% unless foundMatch %}
    .visitor-counter-content-box-carecartbysalespop-2020 {
      display: none !important
    }
  {% endunless %}

  {% if showIcon == false %}
    .visitor-counter-content-box-carecartbysalespop-2020 .light-icon{
      display: none !important;
    }
  {% endif %}

  .visitor-counter-content-box-carecartbysalespop-2020 p, .visitor-counter-content-box-carecartbysalespop-2020 span {
    color: {{ config.visitor_counter_text_color }};
  }
  .content-div-visitor-detail-carecartbysalespop-2020 {
    text-align: {{ config.visitor_counter_text_aligment }};
  }
{% endstyle %}