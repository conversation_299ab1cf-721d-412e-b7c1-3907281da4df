{% comment %} 


This is generated by webpack so don't edit this unless you want to be sad when your work gets overwritten in production.
You can edit this file in ../../components/** 


{% endcomment %}






<span neptune-liquid="{topic:Comparison,source:Comparison.data}">
{% raw %}
{% assign compared = false %}
{% for item in data.items %}
{% if item.handle == '{% endraw %}{{ product.handle }}{% raw %}'%}
  {% assign compared = true %}
{% endif %}
{% endfor %}
  <button class="px-3 py-1 text-xs border border-gray-100 hover:bg-gray-50 hover:border-gray-50 rounded-full flex items-center group" onclick="Comparison.compare({ handle: '{% endraw %}{{ product.handle }}{% raw %}' }); return false;" >
    {% if compared %}
    {% endraw %}{% render 'icon' icon:'check' width:10 height:10 class:'mr-1'%}{% raw %}
    {% endif %}
    {% endraw %}{{'products.compare.toggle' | t}}{% raw %}
  </button>
{% endraw %}
</span>