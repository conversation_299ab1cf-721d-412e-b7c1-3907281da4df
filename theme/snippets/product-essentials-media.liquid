   
{% liquid 

  assign has_mens_images = false
  assign has_non_mens_images = false
  for media in product.media

    assign gender = media.alt | split: ' ' | first | downcase
    
    if gender == 'men'
      assign has_mens_images = true
    else
      assign has_non_mens_images = true
    endif

  endfor
%}

<div class="relative w-full">    

          {%- assign images = product.images -%}

          <div class="product-gallery product-gallery-slider swiper-container" 
            neptune-swiper="
              {% if section.settings.desktop_scroll %}
                {
                  zoom: true,
                  cssMode: true,
                  breakpoints: {
                    1: {
                      slidesPerView: 1
                    },
                    1024: { 
                      enabled: false
                    }
                  }
                }
              {% endif %}"
            {% case section.settings.pagination_type %}
              {% when 'thumbnail' %}
                data-swiper-thumbs='{swiper:_n.qs(".thumbnail-slider").swiper}'
              {% when 'bullets' or 'fraction' %}
                data-swiper-pagination='{"el": ".swiper-pagination","type": "{{ section.settings.pagination_type }}"}'
            {% endcase %}
          >
          
            <div class="product-gallery--wrapper swiper-wrapper {% if section.settings.desktop_scroll %}lg:grid{% endif %}">
         

              {% for media in product.media %}

                {% liquid 
                  if has_mens_images and has_non_mens_images
                    assign gender = media.alt | split: ' ' | first | downcase
                    if collection.handle contains 'mens' 
                      if gender != 'men'
                        continue
                      endif
                    else
                      if gender == 'men'
                        continue
                      endif
                    endif
                  endif
                %}

                {% case media.media_type %}
                  {% when 'image' %}
                    {%- liquid 
                      if section.settings.image_hide_alt != '' and media.alt contains section.settings.image_hide_alt
                        continue
                      endif
                    -%}
                    {% if media.alt contains 'https' %}
                      <div id="productVideo" class="product-gallery--slide swiper-slide product-gallery-{{ forloop.index }} overflow-hidden lg:odd:border-r-2 lg:border-b-2 last:border-b-0 border-white border-inset bg-secondary relative" neptune-uncomment>
                        <!--<video id="productGalleryVideo" class="h-full w-full product-video plyr--setup object-cover" preload="true" src="{{media.alt}}" muted mute autoplay playsinline loop="" poster="">
                          <source src="{{media.alt}}">
                        </video>-->
                        <div>
                          <button id="playPauseButton" class="button button--secondary absolute bottom-5 right-5 flex items-center justify-center w-8 px-0 rounded-lg" neptune-engage="{targets:[{classes:toggle:active}]}">
                            <span>
                              {% render 'icon' icon:'play' height:14 fill:'#000000' %}
                            </span>
                            <span>
                              {% render 'icon' icon:'pause' height:14 fill:'#000000' %}
                            </span>
                          </button>
                        </div>
                      </div>
                    {% else %}
                      <div class="product-gallery--slide swiper-slide product-gallery-{{ forloop.index }} overflow-hidden cursor-pointer lg:odd:border-r-2 lg:border-b-2 lg:last:border-b-0 border-white border-inset bg-secondary" onclick="var viewportWidth = window.innerWidth || document.documentElement.clientWidth;if (viewportWidth > 1024) { const el = _n.qs('[neptune-product]'); const images = el.product.images; const mensImages = images.filter(i=>i.alt && i.alt.match(/\b(MEN)\b/i)); const womensImages = images.filter(i=>i.alt && i.alt.match(/\b(WOMAN|WOMEN)\b/i)); const hasBothTypes = mensImages.length > 0 && womensImages.length > 0; const filteredImages = hasBothTypes ? (window.location.pathname.includes('/mens') ? mensImages : womensImages) : images; Neptune.liquid.load('lightbox',{ images: filteredImages }); modal.open('lightbox')}">
                        <div class="h-full product-gallery--slide-image-wrapper swiper-zoom-container">
                          <div class="absolute w-full h-full left-0 top-0 hidden lg:block"></div>
                          {% assign lazy = true %}
                          {% if forloop.index == 1 %}
                            {% assign lazy = false %}
                          {% endif %}
                          {% render 'lazy-image' image: media, image_class: "w-full h-full object-cover" lazy: lazy  %}
                        </div>
												{% if forloop.first %}
													<div aria-live="polite"neptune-liquid="{topic:'ProductBadges', source:'eval:_n.parents(_self,`[neptune-product]`)', append:[productBadges]}"> 
														{% render 'item-image-badge' %}
													</div>
												{% endif %}
                      </div>
                    {% endif %}
                  {% when 'external_video' %}
                    <div class="product-gallery--slide product--gallery-external_video swiper-slide product-gallery-{{ forloop.index }}">
                      <div class="h-full product-gallery--slide-image-wrapper lg:odd:border-r-2 lg:border-b-2 lg:last:border-b-0 border-white border-inset bg-secondary" data-media-id="{{ media.id }}">
                        {{ media | external_video_tag }}
                      </div>
                    </div>
                  {% when 'video' %}
                    <div class="product-gallery--slide product--gallery-video swiper-slide product-gallery-{{ forloop.index }}">
                      <div class="h-full product-gallery--slide-image-wrapper lg:odd:border-r-2 lg:border-b-2 lg:last:border-b-0 border-white border-inset bg-secondary" data-media-id="{{ media.id }}">
                        <div neptune-uncomment="hover" class="w-full h-full video-wrapper">
                          <video class="w-full h-full product-video plyr--setup object-cover" 
                            preload="true" src="{{ media.sources[1].url }}" muted mute autoplay playsinline loop=""
                            poster="">
                              <source src="{{ media.sources[1].url }}">
                          </video>
                        </div>
                      </div>
                    </div>
                  {% when 'model' %}
                    <div class="product-gallery--slide product--gallery-model swiper-slide product-gallery-{{ forloop.index }}">
                      <div class="h-full product-gallery--slide-image-wrapper">
                        {{ media | model_viewer_tag }}
                      </div>
                    </div>
                {% endcase %}
              {% endfor %}

            </div>

          </div>

          {% if section.settings.pagination_type == 'thumbnail' %}
          <div class="absolute left-0 right-0 z-20 hidden overflow-hidden lg:block max-h-20 bottom-2">
            <div class="relative flex justify-center p-0 m-0" >
              <ul class="flex justify-center p-0 px-5 py-3 m-0 list-none">
                
                {% assign thumb_index = 0 %}
                {%- for image in images -%}

                  {% if image.alt contains '.mp4' %}

                    <li data-variants="{{ image.variants | map:'id' | join:' '}}" class="w-12 mx-2 group border-b-0 border-b-2 cursor-pointer rounded-md overflow-hidden relative {% if forloop.first %}active{% endif %} bg-gray-300 hover:bg-white active:bg-white" onclick="_n.qs('.product-gallery-slider').swiper.slideTo({{thumb_index}})" neptune-engage="{targets:[{
                      selector:_self,
                      classes:add:active,
                      siblings:classes:remove:active
                    }]}">

                      <div class="p-2 overflow-hidden bg-white rounded-md multiply">
                        {% render 'lazy-image' image: image, image_class: "object-contain" %}
                      </div>

                      <span class="absolute top-0 left-0 flex items-center justify-center w-full h-full text-white">
                        {% render 'icon' icon:'play' height:20 width:20 fill:'#ffffff' stroke:'#ffffff' %}
                      </span>

                    </li>
                    {% assign thumb_index = thumb_index | plus: 1 %}
                  {% endif %}
                    <li data-variants="{{ image.variants | map:'id' | join:' '}}" class="w-12 mx-2 group border-b-0 border-b-2 cursor-pointer rounded-md overflow-hidden {% if forloop.first %}active{% endif %} bg-gray-300 hover:bg-white active:bg-white" onclick="_n.qs('.product-gallery-slider').swiper.slideTo({{thumb_index}})" neptune-engage="{targets:[{
                      selector:_self,
                      classes:add:active,
                      siblings:classes:remove:active
                    }]}">

                      <div class="p-2 overflow-hidden bg-white rounded-md multiply">
                        {% render 'lazy-image' image: image, image_class: "object-contain" %}
                      </div>

                    </li>
                  {% assign thumb_index = thumb_index | plus: 1 %}
                {% endfor %}               
              </ul>
            </div>
          </div>
          {% endif %}

          <button class="top-1/2 -translate-y-1/2 absolute hover:bg-dark hover:text-white p-2 right-3 lg:right-6 text-dark transform z-10 transition-all user-select-none{% if images.size == 1 or section.settings.desktop_scroll %} hidden{% endif %}" onclick="_n.qs('.product-gallery-slider').swiper.slideNext()">
            {% render 'icon' icon:'chevron-right' width:40 height:40 strokeWidth:2 %}
          </button>

          <button class="top-1/2 -translate-y-1/2 absolute hover:bg-dark hover:text-white p-2 left-3 lg:left-6 text-dark transform z-10 transition-all user-select-none{% if images.size == 1 or section.settings.desktop_scroll %} hidden{% endif %}" onclick="_n.qs('.product-gallery-slider').swiper.slidePrev()">
            {% render 'icon' icon:'chevron-left' width:40 height:40 strokeWidth:2 %}
          </button>
          
          {% if section.settings.pagination_type == 'bullets' or section.settings.pagination_type == 'fraction' %}
          <div class="product__pagination {% if section.settings.desktop_scroll %}lg:hidden{% endif %}"> 
            <nav class="swiper-pagination product-gallery__pagination"></nav>
          </div>
          {% endif %}

        </div>

<script>
  const productVideo = document.getElementById('productVideo');
  if(!!productVideo){
    const pdpVideo = {
      init: () => {
        pdpVideo.loadVideo();
        pdpVideo.playPause();
      },
      loadVideo: () => {
        if(productVideo.innerHTML.includes('<!--'))
          productVideo.innerHTML = productVideo.innerHTML.replace('<!--','').replace('-->','');
      },
      playPause: () => {
        const v = document.getElementById('productGalleryVideo');
        const b = document.getElementById('playPauseButton');
        if (typeof(v) != 'undefined' && v != null) {
          b.addEventListener('click', () => {
            if (v.paused) {
              v.play();
              b.classList.add('active');
            } else {
              v.pause();
              b.classList.remove('active');
            }
            return false;
          });
        }
      }
    }

  document.addEventListener("DOMContentLoaded", function(){
    pdpVideo.init();
  });
  }
  
</script>
