<div class="search-mobile">
  <form action="{{ routes.search_url }}" method="get" role="search" id="mobileSearchForm" class="flex lg:hidden relative lg:p-10 p-5 pb-0 items-center justify-center w-full group panel--search-form modal-search-form border-t border-white" onclick="searchFocus()">
    <div class="field relative w-full">
      <label for="headerSearchMobile" class="sr-only">
        Search
      </label>
      <input 
      type="search" 
      name="q" 
      id="headerSearchMobile" 
      value="" 
      placeholder="Search..." 
      class="search__input bg-white" 
      autocomplete="off" 
      autocorrect="off" 
      autocapitalize="off" 
      aria-label="Search" 
      aria-autocomplete="list" 
      onfocus="_n.qs('body').classList.add('search-in-focus');
        searchFocus(300);
      "
      >
      <input type="submit" value="go" class="sr-only">
  
      <button type="button" class="btn-none search__mobile_clear absolute activate btn-icon m-0 right-10 top-0 block h-10 w-6 flex flex-col items-center justify-center p-0" onclick="
        let searchForm = document.getElementById('mobileSearchForm');
        let searchInput = document.getElementById('headerSearchMobile');
        searchForm.reset();
        searchInput.focus();
        search.results = false;
        search.suggestions = false;
        search.term = '';
        document.querySelector('[search-suggestions]').style.display = 'none';
        document.querySelector('[search-results]').style.display = 'none';
        "
        neptune-engage="{
          on:click,
          targets:[{
            selector:html
            classes:{remove:search-in-focus}
            attributes:[{
              att:data-active-modal-mobile
              set:_remove
            }]
          },
          {
            selector:.header__nav,
            classes:remove:active
          }
        }"
        >
        <span class="sr-only">Close</span>
        {% render 'icon' icon:'exit' fill="currentColor" height:15 width:15 %}
      </button>
  
      <button class="border-none cursor-pointer text-center absolute animate right-0 top-0 w-10 h-10 flex flex-col items-center justify-center p-0 m-0">
        <span class="text-black flex flex-col justify-center items-center">
          {% render 'icon' icon:'search', height:16, width:16 %}
        </span>
        <span class="icon-fallback-text sr-only">Search</span>
      </button> 
    </div>
  </form>
  <header 
    neptune-liquid="{topic:SearchSuggestions}" 
    class="empty:hidden bg-light" 
    search-suggestions
  >
    <template>
    {% raw %}
      {% if search.results != false and search.results.keywords.size > 0 %}
        <div class="lg:px-5 px-0 pb-[0px] lg:pt-[19px] pt-[10px]">

          <p class="font-body text-body font-normal m-0 pb-2 pt-2 lg:px-0 px-5 border-b-2 border-white text-center capitalize">search suggestions</p>
        
          <div class="mt-[15px] pb-[15px] lg:px-0 px-5">
            {% for item in search.results.keywords %}
              {% if forloop.index <= 5 %}
                {% assign terms = search.term %}
                {% for term in terms %}
                  {% if item.keyword contains term %}
                  {%- capture term_bold -%}
                    <span class="font-normal predictive-word">{{ term }}</span>
                  {%- endcapture -%}
                  <a href="{% if item.redirect %}{{ item.redirect | url }}{% else %}{{Shopify.routes.root}}search?q={{ item.keyword }}{% endif %}" class="mr-8 text-sm hover:underline">{{ item.keyword | replace: term, term_bold }}</a>
                {% else %}
                  <a href="{% if item.redirect %}{{ item.redirect | url }}{% else %}{{Shopify.routes.root}}search?q={{ item.keyword }}{% endif %}" class="mr-8 text-sm hover:underline">{{ item.keyword }}</a>
                {% endif %}             
              {% endfor %}
            {% endif %}
			{% endfor %}
          </div>  
        </div>
      {% endif %}
    {% endraw %}
    </template>
  </header>

  <div
    neptune-liquid="{topic:SearchResults}"
    search-results
    class="search-results empty:hidden bg-light h-full"
  >
    <template>
    {% raw %}
      {% if search.results != false and search.results.products.size > 0  %}
        <div class="lg:px-5 px-0 lg:pb-5 lg:pt-[17px]">

          <p class="font-body text-body capitalize font-body m-0 pb-2 text-center">products</p>
          
          <div class="grid grid-cols-2 lg:grid-cols-4 border-b border-white lg:border-t-0 border-t">
            {% for product in search.results.products limit: 4 %}
              <div>
                {% endraw %}
                {% render 'product-item' %}
                {% raw %}
              </div>
            {% endfor %}
          </div>

          <a  href="/search?q={{ search.term }}" class="block font-body text-[12px] my-4 lg:px-0 px-5">See {{ search.results.total_products }} results for "{{ search.term }}" </a>

        </div>
      {% endif %}
    {% endraw %}
    </template>
  </div> 
</div>

<script>
	function searchFocus(delay) {
		setTimeout(()=>{
			_n.qs('[data-modal="panel-nav"] .panes').scrollTo({ top:_n.qs('.panel--search-form').offsetTop,behavior:'smooth'})
		}, delay || 1)
	}
</script>
