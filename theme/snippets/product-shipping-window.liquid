{% liquid 
  assign shipWindow = false
  for tag in tags
    if tag contains 'Delivery:'
      assign days = tag | split: ':' | last | split: '-'
      assign start = days[0] | times: 60 | times: 60 | times: 24
      assign end = days[1] | times: 60 | times: 60 | times: 24
      assign start = 'now' | plus: start | date: '%a, %b %d'
      assign end = 'now' | plus: end | date: '%a, %b %d'
      assign shipWindow = start | append : ' - ' | append: end
    endif
  endfor
%}
{% if shipWindow %}
<p class="text-sm">
  Estimated Delivery: {{ shipWindow }}
</p>
{% endif %}