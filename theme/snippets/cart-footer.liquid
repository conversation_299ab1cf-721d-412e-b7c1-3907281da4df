<footer class="cart__footer p-4 lg:px-8 border-t-2 border-white">
  {% raw %}

    {% if item_count > 0 %}
      <article tabindex="0" class="flex items-center">
        <div class="flex items-center justify-between w-full">
          <p class="m-0 h5 text-left text-xl lg:text-[1.5rem]">{% endraw %}{{ 'sections.cart.subtotal' | t }}{% raw %}</p>
          {% endraw %}
          {% render 'price-removal' %}
          {% raw %}
          <p class="m-0 h5 text-right text-xl lg:text-[1.5rem]" data-localize="cart-subtotal">{{ items_subtotal_price | money: 'local' | remove: removal }}</p>
        </div>
      </article>

  {% endraw %}

      <div class="mt-3" neptune-permanent="cart-footer">
        <button name="checkout" title="Proceed to Checkout" onclick="document.location='/checkout'" class="button button--primary w-full bag__action bag__checkout decoration-[none_!important]">{{ 'sections.cart.checkout' | t }}</button>
        <a href="/checkout?payment=shop_pay" class="button button--shoppay">
          <span class="block lg:hidden" aria-hidden="true">{% render 'icon' icon:'shoppay-mono' width:'64' height:'15' %}</span>
          <span class="hidden lg:block" aria-hidden="true">{% render 'icon' icon:'shoppay-mono' width:'81' height:'19' %}</span>
          <span class="sr-only">Checkout with ShopPay</span>
        </a>
        <a class="block text-xs underline text-center pt-3 hidden" href="/cart">Go to shopping bag</a>
        {% raw %}
          {% if limit < items_subtotal_price %}
            <p class="m-0 font-light text-center cart-limit">{% endraw %}{{ 'sections.cart.limit' | t }}{% raw %}</p>
            <script>_n.qs('button[name="checkout"]').disabled='disabled'</script>
          {% endif %}
        {% endraw %}
      </div>

  {% raw %}
    {% endif %}
  {% endraw %}
</footer>
