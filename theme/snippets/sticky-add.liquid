<article class="sticky-add sticky bottom-0 z-10 lg:hidden bg-near-white border-t-2 border-white">
  <header class="flex justify-between items-start py-3 px-5">
    <p class="sticky-add__title font-light mb-0 leading-tight">
      {{ product.title | split: ' - ' | first }}
    </p>
    <span class="sticky-add__prices leading-tight pl-2">
      {% if product.compare_at_price != 0 and product.compare_at_price != product.price %}
      <span class="sticky-add__price sticky-add__price--compare">{{ product.compare_at_price | money_without_trailing_zeros}}</span>
      {% endif %}
      <span class="sticky-add__price">{{ product.price | money_without_trailing_zeros }}</span>
    </span>
  </header>
  <main>
    <button class="button rounded-none w-full py-3" onclick="QuickAdd('{{ product.handle }}')">ADD</button>
  </main>
</article>