<div id="acNav" class="ac-nav" aria-label="navigation">
	<div 
		class="ac-nav__list list-reset tablist" 
		role="tablist" 
		aria-label="tabbed navigation" 
		>
		<button 
			id="myInfoTabTrigger" 
			class="tab ac-nav__btn js-nav-btn ac-nav__item js-nav-item" :class="{ 'is-active': isView('info') }" 
			type="button" 
			@click="setMainView('info')" 
			role="tab" 
			aria-selected="true"
			aria-controls="info">
			My Info
		</button>
		<button 
			id="orderHistoryTabTrigger" 
			class="tab ac-nav__btn js-nav-btn ac-nav__item js-nav-item" :class="{ 'is-active': isView('order-history,order') }" 
			type="button" 
			@click="setMainView('order-history')" 
			role="tab" 
			aria-selected="false"
			tabindex="-1"
			aria-controls="orderHistory">
			Order History
		</button>
		<button 
			id="addressBookTabTrigger" 
			class="tab ac-nav__btn js-nav-btn ac-nav__item js-nav-item" :class="{ 'is-active': isView('address-book,address-edit,address-new') }" 
			type="button" 
			@click="setMainView('address-book')" 
			role="tab" 
			aria-selected="false"
			tabindex="-1"
			aria-controls="addressBook">
			Address Book
		</button>
	</div>
{% comment %}	<a class="ac-nav__item" href="/apps/mywishlist">
		My Wishlist
		<span class="ac-nav__icon icon icon--external-link"></span>
	</a>{% endcomment %}
	{{ 'layout.customer.log_out' | t | customer_logout_link }}
</div>