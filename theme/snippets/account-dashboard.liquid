<section id="acDashboard" class="ac-dashboard container container--account is-loading">
	<div class="ac-dashboard__wrap cf tabs">
		<div id="acHeader" class="ac-header">
			<header class="ac-header__section-header section-header">
				<h1 class="section-header__hdg">
					{{ 'customer.account.title' | t }}
				</h1>
			</header>
			{% include 'account-nav' %}
		</div>
		<div id="acContent" class="ac-content">
			<transition name="component-fade" mode="out-in" appear>
				<keep-alive>
					<component :is="currentView"></component>
				</keep-alive>
			</transition>
		</div>
	</div>
</section>

{%- capture accountScripts -%}

	{% comment %} Account data {% endcomment %}
	{% include 'account-data' %}

	{% comment %} Account templates {% endcomment %}
	{% include 'account-info-template' %}
	{% include 'account-order-history-template' %}
	{% include 'account-address-templates' %}
	{% include 'account-section-header-template' %}
	{% include 'account-pagination-template' %}
{%- endcapture -%}

{% include 'minify', variable: accountScripts %}