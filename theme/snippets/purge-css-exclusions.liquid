<div class="container 
w-full 
lg:h-100v 
lg:h-70v 
lg:h-50v 
lg:h-20v 
lg:h-auto 
h-auto-img-l 
h-100v 
h-70v 
h-50v 
h-20v 
h-auto 
h-auto-img 
h-content-below 
 
lg:aspect-w-16 lg:aspect-h-9 
lg:aspect-w-9 lg:aspect-h-16 
lg:aspect-w-4 lg:aspect-h-3 
lg:aspect-w-3 lg:aspect-h-4 
lg:aspect-w-6 lg:aspect-h-4 
lg:aspect-w-4 lg:aspect-h-6 
lg:aspect-w-8 lg:aspect-h-5 
lg:aspect-w-5 lg:aspect-h-8 
lg:aspect-w-7 lg:aspect-h-5 
lg:aspect-w-5 lg:aspect-h-7 
lg:aspect-w-1 lg:aspect-h-1 
 
aspect-w-16 aspect-h-9 
aspect-w-9 aspect-h-16 
aspect-w-4 aspect-h-3 
aspect-w-3 aspect-h-4 
aspect-w-6 aspect-h-4 
aspect-w-4 aspect-h-6 
aspect-w-8 aspect-h-5 
aspect-w-5 aspect-h-8 
aspect-w-7 aspect-h-5 
aspect-w-5 aspect-h-7 
aspect-w-1 aspect-h-1 
lg:px-0 
lg:px-1 
lg:px-2 
lg:px-4 
lg:px-8 
lg:px-16 
lg:px-32 
lg:px-64 
p-0 
p-1 
p-2 
p-4 
p-8 
p-16 
p-32 
p-64 
lg:mt-0 
lg:mt-1 
lg:mt-2 
lg:mt-4 
lg:mt-8 
lg:mt-16 
lg:mt-32 
lg:mt-64 
lg:mb-0 
lg:mb-1 
lg:mb-2 
lg:mb-4 
lg:mb-8 
lg:mb-16 
lg:mb-32 
lg:mb-64 
mt-0 
mt-1 
mt-2 
mt-4 
mt-8 
mt-16 
mt-32 
mt-64 
mb-0 
mb-1 
mb-2 
mb-4 
mb-8 
mb-16 
mb-32 
mb-64 
lg:w-full 
lg:w-1/12 
lg:w-1/5 
lg:w-1/4 
lg:w-1/3 
lg:w-2/5 
lg:w-1/2 
lg:w-3/5 
lg:w-2/3 
lg:w-3/4 
lg:w-4/5 
lg:w-11/12 
w-full 
w-1/4 
w-1/3 
w-1/2 
w-2/3 
w-3/4 
lg:order-none 
lg:order-1 
lg:order-2 
lg:order-3 
lg:order-4 
lg:order-5 
lg:order-6 
order-none 
order-1 
order-2 
order-3 
order-4 
order-5 
order-6 
bg-top-left 
bg-top 
bg-top-right 
bg-left 
bg-center 
bg-right 
bg-bottom-left 
bg-bottom 
bg-bottom-right 
bg-top-left 
bg-top 
bg-top-right 
bg-left 
bg-center 
bg-right 
bg-bottom-left 
bg-bottom 
bg-bottom-right 
lg:text-left 
lg:text-center 
lg:text-right 
text-left 
text-center 
text-right 
lg:text-8xl text-3xl font-bold 
lg:text-7xl text-3xl font-bold 
lg:text-5xl text-2xl font-bold 
lg:text-4xl text-2xl font-bold 
lg:text-3xl text-xl font-bold 
lg:text-2xl text-xl font-bold 
lg:text-xl text-lg font-bold 
lg:text-lg text-base font-bold 
text-base font-bold 
font size, example: 16px
font weight, example: 700
lg:text-8xl text-3xl font-bold 
lg:text-7xl text-3xl font-bold 
lg:text-5xl text-2xl font-bold 
lg:text-4xl text-2xl font-bold 
lg:text-3xl text-xl font-bold 
lg:text-2xl text-xl font-bold 
lg:text-xl text-lg font-bold 
lg:text-lg text-base font-bold 
text-base font-bold 
font size
font weight
lg:text-8xl text-5xl 
lg:text-7xl text-5xl 
lg:text-5xl text-4xl 
lg:text-4xl text-3xl 
lg:text-3xl text-2xl 
lg:text-2xl text-xl 
lg:text-xl text-lg 
lg:text-lg text-base 
text-base 
font size
font weight
font size
font weight

block 
button 
button rounded-lg 
button rounded-full 
button button-large 
button rounded-lg button--large 
button rounded-full button--large 
link underline p-0 bg-transparent border-none 
button 
button rounded-lg 
button rounded-full 
button button-large 
button rounded-lg button--large 
button rounded-full button--large 
link underline p-0 bg-transparent border-none 
button 
button rounded-lg 
button rounded-full 
button button-large 
button rounded-lg button--large 
button rounded-full button--large 
link underline p-0 bg-transparent border-none 
lg:w-full 
lg:w-3/4 
lg:w-1/2 
lg:w-1/4 
lg:items-start lg:justify-start 
lg:items-center lg:justify-start 
lg:items-end lg:justify-start 
lg:items-start lg:justify-center 
lg:items-center lg:justify-center 
lg:items-end lg:justify-center 
lg:items-start lg:justify-end 
lg:items-center lg:justify-end 
lg:items-end lg:justify-end 
lg:py-0 
lg:py-1 
lg:py-2 
lg:py-4 
lg:py-8 
lg:py-16 
lg:py-32 
lg:py-64 
lg:px-0 
lg:px-1 
lg:px-2 
lg:px-4 
lg:px-8 
lg:px-16 
lg:px-32 
lg:px-64 
items-start justify-start 
items-center justify-start 
items-end justify-start 
items-start justify-center 
items-center justify-center 
items-end justify-center 
items-start justify-end 
items-center justify-end 
items-end justify-end 
py-0 
py-1 
py-2 
py-4 
py-8 
py-16 
py-32 
py-64 
px-0 
px-1 
px-2 
px-4 
px-8 
px-16 
px-32 
px-64 
h1 
h2 
h3 
h4
h5
border-r-0
lg:border-b-0
"></div>


.quick-shop [data-available=""] ~ *, .quick-shop [data-available=""] ~ * *, .quick-shop [data-available].unavailable, .quick-shop [data-available].unavailable ~ *, .quick-shop button[disabled] {}
[data-available=""] ~ *, [data-available=""] ~ * *, [data-available].unavailable, [data-available].unavailable ~ *, button[disabled] {}

.m-0	{ margin: 0;}	
.m-0.5	{ margin: 0.125rem;}
.m-1	{ margin: 0.25rem;}
.m-1.5	{ margin: 0.375rem;}
.m-2	{ margin: 0.5rem;}
.m-2.5	{ margin: 0.625rem;}	
.m-3	{ margin: 0.75rem;}	
.m-3.5	{ margin: 0.875rem;}
.m-4	{ margin: 1rem;}
.m-5	{ margin: 1.25rem;}	
.m-6	{ margin: 1.5rem;}
.m-8	{ margin: 2rem;}
.m-10	{ margin: 2.5rem;}
.m-11	{ margin: 2.75rem;}	
.m-12	{ margin: 3rem;}	
.m-14	{ margin: 3.5rem;}	
.m-16	{ margin: 4rem;}	
.m-20	{ margin: 5rem;}	
.m-24	{ margin: 6rem;}	
.m-28	{ margin: 7rem;}	
.m-32	{ margin: 8rem;}	
.m-36	{ margin: 9rem;}
.m-40	{ margin: 10rem;}	

.bag__flow_info {}

html.flow-localized {


body.proxy #shopify-section-header, #shopify-section-footer {}
body.proxy .application__header, .application__footer{}
body.proxy .application__header {}
body.proxy main.flow-checkout {}
body.proxy .checkout-layout__breadcrumb-container {}
body.proxy .application__content-area {}
body.proxy .checkout-layout, .checkout-layout__checkout-content {}
body.proxy.checkout-layout__checkout-content-container {}
.button.button--default {}
.button.button--default.button--secondary {}
.button.button--default.button--positive {}
.text-field__input {}
.select-field__input {}
.checkbox__input~label:before,.checkbox__input:checked~label:before, .checkbox__input:indeterminate~label:before {}
.checkbox__input:checked~label:after {}
.proxy-logo.dn { }
.flow-consent-container {}

.flow-consent-container .flow-consent-message-container-notice {}
.flow-consent-container .flow-consent-message { }
.flow-consent-container .flow-consent-dismiss-btn {}
.page-wrap {}
  .hdr--promo-on  .page-wrap {}
  .bag__total {
    &.mb2 {
      margin-bottom: 0.5rem;
    }
  }
  .bag__flow_info {
    display: block;
  }
  .bag--mini {
    top: 62px;
  }
  .pv-help {
    display: none;
  }
  shopify-payment-terms {
    display: none !important;
  }
}

[flow-selector="prices.compare_at.label"] {
    text-decoration: line-through;
    color: #999999;
    margin-right: 3px;
}

/* Country Picker dropdown container (Name depends on element being used as a container) */
.dropdown-container {
  display: inline-block;
  color: white;
  cursor: pointer;
  position: relative;
}

/* Dropdown Trigger */
.flow-country-picker-modal-trigger,
.flow-country-picker-dropdown-trigger,
.flow-currency-picker-dropdown-trigger {
  border: none;
  border-radius: 0;
  cursor: pointer;
  display: inline-block;
  font-size: 1rem;
  margin-bottom: 0;
  padding: 1rem;
  #country-picker-mobile & {
    padding: 1rem 2rem;
    font-size: .875rem;
    display: block;
    &:after {
      content: "+";
      position: absolute;
      right: 4rem;
      top: 12px;
      color: #999;
      font-size: 18px;
      line-height: 10px;
    }
    &.flow-country-picker-open {
      &:after {
        content: "-";
      }
    }
  }
}

.flow-country-picker-dropdown-trigger::after,
.flow-currency-picker-dropdown-trigger::after {}

.flow-country-picker-modal-trigger > img,
.flow-country-picker-dropdown-trigger > img {}

.flow-country-picker-modal-trigger-text,
.flow-country-picker-dropdown-trigger-text,
.flow-currency-picker-dropdown-trigger-text {}


/* Backdrop that takes over the screen */
.flow-country-picker-dropdown-backdrop {
  background-color: rgba(0, 0, 0, 0);
  height: 100%;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 10;
}

.flow-country-picker-instruction-txt {
  color: black;
  display: block;
  padding: 12px 10px;
  line-height: 14px;
  border-bottom: 1px solid #dddddd;
  margin-bottom: 3px;
  #country-picker-mobile & {
    padding: 1rem 4rem;
    font-size: .875rem;
  }
}

/* Dropdown button on hover & focus */
.flow-country-picker-modal-trigger:hover,
.flow-country-picker-modal-trigger:focus,
.flow-country-picker-modal-trigger.flow-country-picker-open,
.flow-country-picker-dropdown-trigger:hover,
.flow-country-picker-dropdown-trigger:focus,
.flow-country-picker-dropdown-trigger.flow-country-picker-open,
.flow-currency-picker-dropdown-trigger:hover,
.flow-currency-picker-dropdown-trigger:focus,
.flow-currency-picker-dropdown-trigger.flow-country-picker-open {
  background-color: #f7f7f7;
}

.flow-country-picker-country-logo {
  display: none;
}

.flow-country-picker-country-logo > img {
  display: none;
}

/* Dropdown Content (Hidden by Default) */
.flow-country-picker-dropdown-menu {
  visibility: hidden;
  opacity: 0;
  border-radius: 0;
  -webkit-transition: visiblity 0.10s, opacity 0.10s linear;
  transition: visiblity 0.10s, opacity 0.10s linear;
  position: absolute;
  background-color: #f7f7f7;
  min-width: 180px;
  max-height: 400px;
  overflow-y: scroll;
  z-index: 100;
  font-size: 1rem;
  border: 1px solid #ddd;
  margin-top: 0px;
  left: 50%;
  transform: translateX(-50%);
  top: calc(100% + 9px);
  .svg-check {
    display: none;
  }
  #country-picker-mobile & {
    position: relative;
    border-left: none;
    border-right: none;
    max-height: 0;
    overflow: hidden;
    &.flow-country-picker-show {
      max-height: 2000px;
    }
  }
}
.flow-country-picker-dropdown-menu a:last-child {
  margin-bottom: 4px;
}

/* Links inside the dropdown */
.flow-country-picker-dropdown-menu a {
  text-decoration: none;
  padding: 6px 10px;
  line-height: 14px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-line-pack: center;
      align-content: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  #country-picker-mobile & {
    padding: 1rem 4rem;
    font-size: .875rem;
  }
}

.flow-country-picker-dropdown-menu a:hover {
  opacity: 1;
  background-color: #f7f7f7;
}

/* The actual text inside the dropdown option */
.flow-country-picker-dropdown-option-text {
  -ms-flex-item-align: center;
  align-self: center;
  color: #000000;
  text-transform: uppercase;
  position: relative;
  display: inline-block;
  width: calc(100% - 33px);
  top: -1px;
  cursor: pointer;
  &:hover {
    font-family: 'MonumentGrotesk-Bold';
  }
}

.flow-country-picker-show {
  visibility: visible;
  opacity: 1;
}

.flow-country-picker-open {
  color: #000000;
}

/* Modal Country Picker */
.flow-country-picker-modal {
  background-color: white;
  border-radius: 4px;
  -webkit-box-shadow: 0 2px 24px 0 rgba(0, 0, 0, .15), 0 2px 4px 0 rgba(0, 0, 0, .15);
    box-shadow: 0 2px 24px 0 rgba(0, 0, 0, .15), 0 2px 4px 0 rgba(0, 0, 0, .15);
  max-height: 700px;
  left: calc(50% - 375px);
  padding: 20px;
  position: fixed;
  top: 25vh;
  width: 700px;
  z-index: 9;
}

/* An item inside the modal */
.flow-country-picker-modal-item-container {
  cursor: pointer;
  display: inline-block;
  font-size: 12px;
  padding: 7px 5px 7px 0;
  width: 100%;
}
.flow-country-picker-modal-item-container:hover {
    background-color: #dddddd;
  }

.flow-country-picker-modal-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-line-pack: center;
      align-content: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}

/* The selected item in the modal */
.flow-country-picker-selected-modal-item {
  background-color: #C0DFFF;
  cursor: auto;
  cursor: initial;
}

/* Backdrop that takes over the screen */
.flow-country-picker-modal-backdrop {
  background-color: rgba(0, 0, 0, .30);
  height: 100%;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 8;
}

/* The text within the modal item */
.flow-country-picker-modal-text {
  -ms-flex-item-align: center;
      align-self: center;
  color: #000000;
  line-height: 15px;
}

/* Modal item logo */
.flow-country-picker-modal-logo {
  -ms-flex-item-align: center;
      align-self: center;
  border-radius: 2px;
  -webkit-box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .10);
          box-shadow: 0 2px 8px 0 rgba(0, 0, 0, .10);
  flex-shrink: 0;
  height: 15px;
  margin-left: 10px;
  margin-right: 8px;
  width: 21px;
}

/* Modal Header */
.flow-country-picker-modal-header {
  padding-left: 5px;
  padding-bottom: 16px;
  border-bottom: 1px #dddddd solid;
  margin-bottom: 16px;
}

.flow-country-picker-modal-title {
  color: #000000;
  display: inline-block;
}

/* Modal close icon */
.flow-country-picker-modal-close {
  float: right;
  cursor: pointer;
  width: 10px;
  height: 10px;
  margin-top: 7px;
  stroke: #bfbfbf;
}
.flow-country-picker-modal-close:hover {
    stroke: #808080;
  }

/* Modal Body */
.flow-country-picker-modal-body {
  max-height: 650px;
  overflow-y: scroll;
}

/* Modal Body Content */
.flow-country-picker-modal-body-content {
  -webkit-column-count: 4;
          column-count: 4;
}

/* Currently selected experience text */
.flow-country-picker-current-experience-txt {
  display: inline-block;
  padding-left: 7px;
  bottom: 6px;
  position: relative;
}

.flow-country-picker-selected-logo {
  display: inline-block;
}

.flow-country-picker-button-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}

.flow-country-picker-button {
  font-size: 1rem;
  display: inline-block;
  min-width: 80px;
  color: #ffffff;
  border: solid 1px #191919;
  border-radius: 4px;
  background-color: #000000;
  cursor: pointer;
  flex-basis: 30%;
  margin-left: 20px;
  padding: 8px;
}

.flow-country-picker-button.flow-country-picker-button-secondary {
  border: solid 1px #191919;
  color: #000000;
  background-color: #ffffff;
}

.flow-country-picker-advanced .flow-country-picker-modal-body-content {
  display: flex;
  justify-content: center;
  padding: 30px 0;
}

.flow-country-picker-advanced .flow-country-picker-wrapper,
.flow-country-picker-advanced .flow-currency-picker-wrapper {
  padding: 20px;
}

/* Some example responsive rules */
@media (max-width: 768px) {
  .flow-country-picker-modal {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100%;
    width: 100%;
    max-height: unset;
  }
  .flow-country-picker-modal-body-content {
    column-count: unset;
  }
}

.flow-information-tooltip {
  max-width: 100%; 
  width: 220px; 
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  padding: 20px;
}

.flow-information-close {
  svg {
    width: 100%;
    height: auto;
  }
}

.global-information {
  display: none;
  .global-localized & {
    display: flex;
  }
}

#SizzleFitModal {
  bottom: auto !important;
  height: 100vh;
}
#SizzleFitModal .SizzleFit_box {
  max-width: 90%;
  border-radius: 0;
  top: 50%;
  bottom: inherit;
  transform: translate(-50%, -50%);
  @media(min-width:1040px){
    max-width: 460px;
  }
}
#SizzleFitModal .SizzleFit_guts {
  padding: 0 20px 20px;
  text-align: left;
  text-transform: uppercase;
  font-size: 11px;
  @media(min-width:1040px){
    padding: 0 40px 30px;
  }
}
#SizzleFitModal .SizzleFit_question {
  border: none;
  padding-top: 0;
  padding-bottom: 0;
  margin-top: 0;
  margin-bottom: 0;
}
#SizzleFitModal .SizzleFit_question_prompt {
  font-size: 11px;
  font-family: 'MonumentGrotesk-Bold';
}
#SizzleFitModal .SizzleFit_question_fieldset {
  position: relative;
  width: 100%;
}
#SizzleFitModal .SizzleFit_question_fieldset label {
  position: absolute;
  right: 0;
  top: 12px;
  font-size: 8px;
  font-style: italic;
}
#SizzleFitModal .SizzleFit_question_fieldset label .SizzleFit_question_text {
  display: none;
}
#SizzleFitModal .SizzleFit_question_fieldset label .SizzleFit_requirement_text--required {
  color: #0000FF;
}
#SizzleFitModal .SizzleFit_question_fieldset label .SizzleFit_requirement_text--optional {
  color: #666;
}

#SizzleFitModal .SizzleFit_cta button {
  width: 100%;
  border: none;
  padding: .5rem 1rem;
  background-color: black;
  text-color: white;
  text-transform: uppercase;
  border-radius: 0;
  margin-bottom: 1rem;
  line-height: 1.5;
}
#SizzleFitModal .SizzleFit_header h3 {
  font-family: 'MonumentGrotesk-Bold';
  text-transform: uppercase;
  font-size: 11px;
}
#SizzleFitModal .SizzleFit_question select {
  padding: .5rem 1rem;
}
#SizzleFitModal .SizzleFit_header {
  padding: 1rem 20px;
  position: relative;
  border: none;
  @media(min-width:1040px){
    padding: 1rem 40px;
  }
}
#SizzleFitModal .SizzleFit_close {
  @apply absolute;
  top: 3px;
  right: 6px;
  width: 40px;
  height: 40px;
  padding: .5rem;
}
#SizzleFitModal .SizzleFit_header .SizzleFit_units {
  @media(min-width:1040px){
    margin-right: 0;
  }
}
#SizzleFitModal #SizzleFit_recommendation_statement .SizzleFit_size_badge {
  font-family: 'MonumentGrotesk-Bold';
  text-transform: uppercase;
  font-size: 11px;
}
#SizzleFitModal #SizzleFit_demographic_percent {
  display: inline-block;
  margin-bottom: .5rem;
}
#SizzleFitModal .SizzleFit_demographics_bar {
  background: #0000FF;
}

#SizzleFitModal .SizzleFit_size_scale_indicator {
  width: 20px;
  height: 20px;
  border-radius: 100%;
  top: -10px;
  bottom: inherit;
  background: #0000FF;
  transform: none;
}

#SizzleFitModal .SizzleFit_size_scale_bar {
  background: #000;
  height: 1px;
  margin-bottom: 2rem;
}

#SizzleFitModal .SizzleFit_size_scale_legend {
  color: #000;
  font-size: 11px;
  margin-bottom: 1.5rem;
}

#SizzleFitModal .SizzleFit_header .SizzleFit_units_option {
  text-transform: uppercase;
  color: #000;
  font-size: 11px;
  padding: 10px 20px;
  line-height: 1;
}

#SizzleFitModal .SizzleFit_header .SizzleFit_units_option.active {
  background-color: #EFEEF0;
}

#SizzleFitModal .SizzleFit_quote {
  border: none;
  background: #EFEEF0;
  font-style: normal;
  display: none;
}
#SizzleFitModal #SizzleFit_demographic_statement {
  display: none;
}
#SizzleFitModal #SizzleFit_recommendation_statement {
  margin-bottom: 3rem;
}
#SizzleFit_demographics {
  margin-bottom: 3rem;
}
@media (max-width:1039px){
  #SizzleFitModal .SizzleFit_question select {
    font-size: 16px;
  }
}
#SizzleFitModal #SizzleFit_results {
  display: none;
}

#onetrust-consent-sdk #onetrust-banner-sdk {
  bottom: 30px !important;
  max-width: 400px !important;
  left: inherit !important;
  right: 30px !important;
  padding: 0 !important;
}

@media(max-width:1039px){
  #onetrust-consent-sdk #onetrust-banner-sdk {
    left: 30px !important;
  }
}

#onetrust-banner-sdk:not(.ot-iab-2) #onetrust-group-container {
  width: 100% !important;
}

#onetrust-banner-sdk #onetrust-policy {
  margin: 0 !important;
  font-size: 10px !important;
}

#onetrust-banner-sdk .onetrust-close-btn-ui.onetrust-lg {
  display: none !important;
}

#onetrust-consent-sdk #onetrust-accept-btn-handler, #onetrust-banner-sdk #onetrust-reject-all-handler {
  background-color: transparent !important;
  border-radius: 0 !important;
  background-color: #f7f7f7 !important;
  margin-right: 0 !important;
  color: #000 !important;
  text-transform: uppercase !important;
  font-size: 10px !important;
}

#onetrust-consent-sdk #onetrust-pc-btn-handler.cookie-setting-link {
  margin: 0 !important;
  padding: 0 !important;
}

#onetrust-banner-sdk:not(.ot-iab-2) #onetrust-button-group-parent {
    margin: auto !important;
    width: 100% !important;
    position: static !important;
    transform: translateY(0) !important;
    padding-bottom: 15px;
}

#onetrust-banner-sdk #onetrust-button-group {
    display: flex !important;
    flex-direction: column !important;
}

#onetrust-banner-sdk #onetrust-close-btn-container {
  display: none !important;
}

#onetrust-consent-sdk #onetrust-pc-btn-handler.cookie-setting-link {
  order: 2;
  font-size: 10px !important;
}

button#onetrust-accept-btn-handler {
  order: 1;
}

#onetrust-banner-sdk .ot-sdk-container, #onetrust-pc-sdk .ot-sdk-container, #ot-sdk-cookie-policy .ot-sdk-container {
  padding: 0 !important;
}

#onetrust-banner-sdk #onetrust-close-btn-container-mobile, #onetrust-banner-sdk #onetrust-policy-title {
  display: none !important;
}

.isp_polite_powered_by_id {
  display:none !important;
}

.shopify-section div#isp_search_result_page_container {

  display:flex;


  #isp_left_container {
    width: 25%;
    margin:0;
  }

  #isp_center_container {
    width:75%;
    margin-left:3rem;

    ul#isp_search_results_container {
      float:none;
      display:flex;
      flex-wrap:wrap;

      li.isp_grid_product {
        width:50%;
        margin:0;
        height:448px;

        div.isp_product_image_wrapper {
          height:350px;
        }

        .isp_product_color_swatch,
        a.isp_product_quick_view_button {
          display:none !important;
        }
        &:hover {
          a.isp_product_quick_view_button {
            display:none !important;
          }
        }
      }
    }
  }

}

@media (min-width: 1024px) {
  .template-collection #MainContent #ss-collection-filters, .template-search #MainContent #ss-collection-filters, .template-collection #MainContent #ss-sort, .template-search #MainContent #ss-sort {
    top: 1px;
  }
}

#ss-collection-filters, #ss-sort {
  @media only screen and (max-width: 1040px) {
    max-height: 0;
    overflow: hidden;
    visibility: hidden;
    display: block !important;
  }
  &.ss-open {
    z-index: 30 !important;
    @media only screen and (max-width: 1040px) {
      max-height: 100vh;
      visibility: visible;
      padding-bottom: 180px;
      overflow-y: scroll;
      z-index: 40 !important;
    }
  }
}

[data-collection-view="2"] .product-grid-item.pure-u-lg-1-4, [data-collection-view="2"] .collection-injection-banner {
  width: 50%!important;
}

.parent-group.active .group-active:underline {
  text-decoration: underline;
}

.parent-group.active .group-active:font-semibold {
  font-weight: 600;
}

.ss-auto__border-top {
  left: 0;
  margin: -0.875em 0.3125em 0 -0.375em;
  position: absolute;
  right: 0;
  top: 0; }

.ss-auto__results {
  margin: 0;
  padding: 1.25em 1.25em 0.625em;
  position: absolute;
  right: 0;
  top: 100%;
  text-align: left;
  -webkit-transition: opacity 100ms ease-in-out;
  transition: opacity 100ms ease-in-out;
  width: 100%;
  background-color: #f7f7f7;
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  height: calc(100vh - 133px);
  overflow-y: scroll; }

.ss-auto__title {
  font-size: 0.875em;
  margin: 0 0 0.57143em;
  text-transform: uppercase;
  font-family: "MonumentGrotesk-Bold"; }

.ss-auto__hr {
  border: 0;
  height: 0.0625rem;
  margin: 0.625em 0;
  background-color: #ddd; }

.ss-auto__suggestions {
  margin: 0 0 1.5625em;
  padding: 0.25em 0;
  display: flex; 
  list-style: none;
}

.ss-auto__suggestion {
  cursor: pointer;
  font-size: 0.875em;
  padding: 0.42857em 0 !important;
  font-weight: 300;
  margin-right: 2rem; }

.ss-auto__suggestion em {
  font-style: normal;
  font-weight: 700; }

.ss-auto__suggest span {
  display: inline-block; }

.ss-auto__suggest em {
  font-style: normal; }

.ss-auto__products {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  flex-direction: row;
  flex-wrap: wrap;
  padding: 0.3125em 0; }

.ss-auto__product {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-direction: column;
  flex-direction: column;
  font-size: 0.875em;
  margin: 0.42857em 0;
  width: 25%; }
  @media (max-width: 60em) {
    .ss-auto__product {
      width: 50%; } }

.ss-auto-p em {
  font-style: normal;
  font-weight: 700; }

.ss-auto-p__name {
  font-weight: 300;
  line-height: 1.1; }

.ss-auto-p__price {
  display: block;
  font-size: 11px;
  padding-top: 0.71429em;
  font-weight: 300; }

.ss-auto-p__image {
  min-width: 4.6875rem; }

.h-0 {}

.ss-auto-p__figure {
  margin: 0;
  padding-bottom: 133.33333%; }

.ss-auto-p__figure img {
  display: block; }

.ss-auto-p__info {
  padding: 0;
  text-align: center;
  margin-top: 1rem;
  font-size: 11px; }

.btn__ss-auto-more {
  border: 0;
  display: block;
  font-size: 0.875em;
  padding: 0.42857em 0 0.57143em;
  position: relative;
  text-align: left;
  text-transform: lowercase;
  width: 100%;
  z-index: 2;
  font-family: "MonumentGrotesk-Bold"; }

.btn__ss-auto-more span {
  position: relative; }

.btn__ss-auto-more span:before {
  bottom: -0.35714em;
  content: "";
  display: inline-block;
  display: block;
  height: 0.71429em;
  left: 0;
  margin: 0 -0.35714em;
  position: absolute;
  right: 0;
  z-index: -1; 
}

.new-hidden {
  display: none !important;
}

.quick-shop {
  form {
    -webkit-transition: all 350ms cubic-bezier(0.455, 0.03, 0.515, 0.955);
    transition: all 350ms cubic-bezier(0.455, 0.03, 0.515, 0.955);
    visibility: hidden;
    transform: translateY(40px);
    overflow-x: scroll;   
  }
}

.product-grid-item {
  a {
    &:hover, &:focus {
      .quick-shop form {
        transform: translateY(3px);
        visibility: visible;
      }
    }
  }
}

.ss-pagination,
.mother-pagination {
  text-align:center;
  display:block;
  width:100%;
  & > * {
    display:inline-block;
  }
  ol {
    list-style:none;
    padding:0;
    
    li {
      display:inline-block;
      text-align:center;
      position:relative;
      width:2rem;
      
      a {
        padding:1rem 0;
        display:block;
      }
      &.current {
        span {
          position:relative;
          display:block;
          color:#fff; 
        }
        &:before {
          display:block;
          content: '';
          position: absolute;
          width: 1.5rem;
          height: 1.5rem;
          background: #000;
          border-radius: 100%;
          padding: 0;
          display: block;
          transform: translate(-50%, -50%);
          top: 50%;
          left: 50%;
        }
      }
            
    }
  }
  .pages-prev,.pages-next {
    display:inline-block;
    a {
      display:block;
      padding:1rem 0;
      width:2rem;
      text-align:center;
      &:before {
        content:'&larr;';
        display:block;
        position:relative;
      }
    }
  }
  .pages-prev a:before {
    content:'\2190';
  }
  .pages-next a:before {
    content:'\2192';
  }
}

.active-show {
  display:none;
}
.active span.active-show {
  display:inline-block;
}
.active div.active-show,
.active ul.active-show {
  display:block;
}
.active .flex.active-show {
  display:flex;
}
.active .active-hide {
  display:none;
}
.active.active-fw6,
.active .active-fw6 {
  font-weight:600
}
.active.active-black,
.active .active-black {
  color:#000000
}
.active.active-underline,
.active .active-underline {
  text-decoration:underline;
}

.active.active-bb,
.active .active-bb {
  border-bottom:1px solid #000
}

a.skip-to-main-content {
    background: white;
    left:-999px;
    height:1px;
    overflow:hidden;
    position:absolute;
    top:auto;
    width:1px;
    z-index:-999;
}

[data-whatinput="keyboard"] a.skip-to-main-content:focus, [data-whatinput="keyboard"] a.sskip-to-main-content:active {
    left: auto;
    top: auto;
    width: auto;
    height: auto;
    overflow:auto;
    padding:5px;
    text-align:center;
    font-size:1;
    z-index:999;
}

body .acsb-skip-link {
    display: none !important;
}
[data-whatinput="keyboard"] {
    .acsb-skip-link {
        display: inline-flex !important;
    }
}

.ada-outline-fix:focus {
    outline: 0;
    border: none;
    [data-whatinput="keyboard"] & {
        border: 2px solid rgb(59, 153, 252);
    }
}

button:focus {
    outline: -webkit-focus-ring-color auto 5px;
}

@media (min-width: 1024px) {
  .show-on-focus  {
    display: block;
    height: 0;
    overflow: hidden;
    position: absolute;
    width: 0;
    top: 18px;
  }

  button.show-on-focus {
    background: transparent;
    border: 0;
    line-height: 0;
    padding: 0;
  }

  .show-on-focus.active,
  .show-on-focus:focus {
    overflow: visible;
    position: relative;
  }
}


.announcement-bar .swiper-slide:not(.swiper-slide-active):not(:only-child) {
  visibility: hidden;
}

.video-stop-play {
  bottom: 15px;
  left: 8px;
  height: 20px;
  z-index: 30;
}

.video-stop-play button {
  background-color: transparent;
  border: 0;
  left: 0;
  position: absolute;
  top: 0;
  z-index:40;
}

.video-stop-play button {
  color: white;
  font-size: 24px;
  -webkit-text-stroke: 1px black;
}

*:focus{outline:none!important;}

[data-whatinput="keyboard"] *:focus{outline:2px solid #3b99fc !important; border: none!important;}

.dn {display: none;}
.db {display: block;}

@media (min-width: 1024px) {
  .dn-l {display: none;}
  .db-l {display: block;}
}

/* PLP Filters & Sort
========================================================================== */

.filter-set {
  position: relative;

  li.active {
    &:after {
      content: '';
      height: 4px;
      width: 4px;
      border-radius: 100%;
      background: black;
      display: block;
      position: absolute;
      top: 24px;
      right: 18px;

      @media (max-width: 1024px) {
        top: 36px;
        right: 44px;
      }
    }
  }
  
  &.active {
    .active-underline {
      text-decoration: none;
    }

    li.active {
      .active-underline {
        text-decoration: underline;
      }
    }

    .filter-set-label {
      text-decoration: none;
    }
  }
  .filter-set-label {
    .plus {
      display: block;
    }
    .minus {
      display: none;
    }
  }
  &.active {
    .filter-set-label {
      text-decoration: underline;
      font-family: 'MonumentGrotesk-Bold';
      .plus {
        display: none;
      }
      .minus {
        display: block;
      }
    }
  }
}

.collection-tools {
  @media(max-width: 1039px){
    border-top: 1px solid #ddd;
  }
  @media(min-width: 1040px){
    margin-left: auto;
  }
  > ul {
    @media(min-width: 1040px){
      margin-left: auto;
    }
  }
  .checkbox-controlled-height {
    li {
      .active-underline {
        font-family: 'MonumentGrotesk-Regular';
        text-decoration: none;
      }
      &.active {
        .active-underline {
          text-decoration: underline;
          font-family: 'MonumentGrotesk-Bold';
        }
      }
    }
  }
  .active-black {
    label {
      font-family: 'MonumentGrotesk-Regular';
      text-decoration: none;
    }
    &.active {
      label {
        text-decoration: underline;
        font-family: 'MonumentGrotesk-Bold';
      }
    }
  }
}


.quick-shop [data-available=""] ~ *,
.quick-shop [data-available=""] ~ * *,
.quick-shop [data-available].unavailable, 
.quick-shop [data-available].unavailable ~ *,
.quick-shop button[disabled] {
  opacity:.5;
  text-decoration: line-through;
  .quick-shop & {
    pointer-events: none;
  }
}

.quick-shop [data-available=""] ~ *, .quick-shop [data-available=""] ~ * *, .quick-shop [data-available].unavailable, .quick-shop [data-available].unavailable ~ *, .quick-shop button[disabled]

[data-available=""] ~ *, [data-available=""] ~ * *, [data-available].unavailable, [data-available].unavailable ~ *, button[disabled]

input[type=checkbox] ~ .checkbox-controlled-bold, input[type=radio] ~ .checkbox-controlled-bold

collection-filters h-100-header bt bl-l b--border-gray checkbox-controlled-display mt0-l tl w-100 z-3 ng-scope tailwinds absolute h-main overflow-y-scroll bg-near-white border-t lg:border-l border-gray-dark right-0 lg:mt-0 text-left w-full z-30 top-0 lg:max-w-md ss-open

float-right

#onetrust-consent-sdk #onetrust-banner-sdk {
  bottom: 30px !important;
  max-width: 400px !important;
  left: inherit !important;
  right: 30px !important;
  padding: 0 !important;
}

@media(max-width:1039px){
  #onetrust-consent-sdk #onetrust-banner-sdk {
    left: 30px !important;
  }
}

#onetrust-banner-sdk:not(.ot-iab-2) #onetrust-group-container {
  width: 100% !important;
}

#onetrust-banner-sdk #onetrust-policy {
  margin: 0 !important;
  font-size: 10px !important;
}

#onetrust-banner-sdk .onetrust-close-btn-ui.onetrust-lg {
  display: none !important;
}

#onetrust-consent-sdk #onetrust-accept-btn-handler, #onetrust-banner-sdk #onetrust-reject-all-handler {
  background-color: transparent !important;
  border-radius: 0 !important;
  background-color: #f7f7f7 !important;
  margin-right: 0 !important;
  color: #000 !important;
  text-transform: uppercase !important;
  font-size: 10px !important;
}

#onetrust-consent-sdk #onetrust-pc-btn-handler.cookie-setting-link {
  margin: 0 !important;
  padding: 0 !important;
}

#onetrust-banner-sdk:not(.ot-iab-2) #onetrust-button-group-parent {
    margin: auto !important;
    width: 100% !important;
    position: static !important;
    transform: translateY(0) !important;
    padding-bottom: 15px;
}

#onetrust-banner-sdk #onetrust-button-group {
    display: flex !important;
    flex-direction: column !important;
}

#onetrust-banner-sdk #onetrust-close-btn-container {
  display: none !important;
}

#onetrust-consent-sdk #onetrust-pc-btn-handler.cookie-setting-link {
  order: 2;
  font-size: 10px !important;
}

button#onetrust-accept-btn-handler {
  order: 1;
}

#onetrust-banner-sdk .ot-sdk-container, #onetrust-pc-sdk .ot-sdk-container, #ot-sdk-cookie-policy .ot-sdk-container {
  padding: 0 !important;
}

#onetrust-banner-sdk #onetrust-close-btn-container-mobile, #onetrust-banner-sdk #onetrust-policy-title {
  display: none !important;
}

.flow-consent-message-container-notice {
  border: 1px solid #ddd;
  padding: 1rem;
  align-items: center;
  flex-direction: row;
  display: flex;
  .flow-consent-message > p {
    margin: 0;
  }
  .flow-consent-dismiss-btn {
    margin-left: auto;
  }
}

.section-title, h2{
    font-size: 32px;
}

.section-description{
    font-size: 18px;
}

.page-width{
    max-width: 1200px;
    margin: 0 auto;
}

@media screen and (min-width: 990px) {
  .page-width--narrow {
    max-width: 72.6rem;
  }
}

input[type=text],input[type=email],input[type=number], textarea, input[type=date], select{
    outline: none; 
}

.hero-banner{
    background-repeat: no-repeat;
    background-size: cover;
}

.account-menu-wrapper{
    border: 1px solid #A0A0A0;
    display: none;
}

.account-dropdown-wrapper:hover .account-menu-wrapper{
    display: block;
}

.icon svg.hidden {
    display: block;
}

@media screen and (min-width: 1024px){
    .group:hover .lg\:group-hover\:visible{
        visibility: visible;
    }    
    .group:hover .lg\:group-hover\:pointer-events-auto, .gropu:hover .lg\:group-focus\:pointer-events-auto{
        pointer-events: auto;
    }
}

/* Background Colors
--------------------------------------------- */

.bg-black-10 {
    background-color: rgba(0,0,0,.1);
}

.bg-black-20 {
    background-color: rgba(0,0,0,.2);
}

.bg-black-30 {
    background-color: rgba(0,0,0,.3);
}

.bg-black-40 {
    background-color: rgba(0,0,0,.4);
}

.bg-black-50 {
    background-color: rgba(0,0,0,.5);
}

.bg-black-60 {
    background-color: rgba(0,0,0,.6);
}

.bg-black-70 {
    background-color: rgba(0,0,0,.7);
}

.bg-black-80 {
    background-color: rgba(0,0,0,.8);
}

.bg-black-90 {
    background-color: rgba(0,0,0,.9);
}

.bg-black-100 {
    background-color: rgba(0,0,0,1);
}


#MainContent {
  z-index:0;
  background:#f7f7f7;
}

#shopify-section-footer {
  z-index:-1;
  // position:sticky;
  // bottom:0;
}
.user-select-none {
  user-select:none;
}
.has-angle-shade:after {
    position: absolute;
    display: block;
    background: #374151;
    opacity: .05;
    pointer-events: none;
    content: '';
    height: 200px;
    width: 200%;
    z-index: 100;
    bottom: 0px;
    transform: rotate(-5deg);
}

/* Challenge
--------------------------------------------- */

.shopify-challenge__container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.shopify-challenge__button {
  background: var(--os-primary);
}


/* Helpers
--------------------------------------------- */

.circle-divider::after {
  content: '\2022';
  margin: 0 1.3rem 0 1.5rem;
}

.circle-divider:last-of-type::after {
  display: none;
}

@media (min-width: 1024px) {
  .dropdown--wrap {
    @apply lg:absolute lg:transform lg:-translate-x-1/2 lg:left-1/2 lg:top-full lg:w-52 w-full lg:pt-2.5 box-border;
  }
  .dropdown--content {
    @apply bg-white lg:rounded lg:shadow-2xl py-6 px-8 w-full;
  }
  .dropdown--nav-item:before {
    content: "";
    display: block;
    position: absolute;
    width: 0px;
    height: 0px;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid rgb(255, 255, 255);
    bottom: -18px;
    left: 50%;
    transform: rotate(90deg) translateX(-50%);
    opacity: 0;
  }
  details[open] .dropdown--nav-item:before { 
    opacity: 1; 
  }
}


.multiply {
    mix-blend-mode:multiply;
}

@media screen and (min-width: 768px){
    .md\:grid-cols-5 {
        grid-template-columns: repeat(5,minmax(0,1fr));
    }
    .md\:grid-cols-6{
        grid-template-columns: repeat(6,minmax(0,1fr));
    }
}

.fade-in {
  opacity: 0;
}

.fade-in-rise {
  opacity: 0;
  transform: translateY(40px);
  &.active {
    opacity: 1;
    transform: translateY(0);
  }
}

.in-view {
  
  &.in-view\:fade-in{
    opacity:1;
  }

  &.in-view\:fade-in-rise {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate {
    
    transition: all 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);

  &.animate-slow {
    transition-duration: 1.2s
  }
  &.animate-fast {
    transition-duration: 0.3s
  }

    &.push-hover {
    transform: scale(1.05);
    :hover > & {
      transform: scale(1.0);
    }
  }
  &.pull-hover {
    transform: scale(1.0);
    :hover > & {
      transform: scale(1.05);
    }
  }
}

.swiper-button-disabled {
    opacity:0;
    pointer-events:none;
}

.accordion--section .accordion:after {
  display: none;
}

#collectionFilters {
    @media screen and (min-width: 768px){
        max-height: calc(100vh - 82px);
    }
}

.pagination {
  .swiper-pagination-bullet {
    width: 84px;
    margin: 16px 4px;
    height: 2px;
    border-radius: 0;
    box-shadow: rgb(0 0 0 / 20%) 0px 0px 20px 5px;
  }
  .swiper-pagination-bullet-active {
    background-color: #ffffff;
  }
}

shopify-payment-terms {
  margin: 0;
  padding: 0;
  line-height: 1.5;
  font-size: 11px;
}

.br5 {
  border-radius: 42px;
}

.br6 {
  border-radius: 248px;
}

@media screen and (min-width: 1024px){
  .br5-l {
    border-radius: 42px;
  }

  .br6-l {
    border-radius: 248px;
  }
}

.br--top-right {
  border-top-right-radius: 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.br--top-left {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.br--bottom-right {
  border-bottom-right-radius: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.br--bottom-left {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

@media screen and (min-width: 1024px){
  .br--top-right-l {
    border-top-right-radius: 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }

  .br--top-left-l {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }

  .br--bottom-right-l {
    border-bottom-right-radius: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }

  .br--bottom-left-l {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
}

.top-nav-l {
  @media screen and (min-width: 1024px){
    top: 100px;
  }
}

.product--img-xl {
  width: 130%;
  transform: translateX(-12%);
  max-width: 130%;
  height: auto;
}

.active-show {
  display:none;
}
.active span.active-show {
  display:inline-block;
}
.active div.active-show,
.active ul.active-show {
  display:block;
}
.active .flex.active-show {
  display:flex;
}
.active .active-hide {
  display:none;
}

.checkbox-controlled-display {
  display:none;
  letter-spacing: 0;
}
input[type=checkbox]:checked ~ .checkbox-controlled-display,
input[type=radio]:checked ~ .checkbox-controlled-display {
  display:block;
}
input[type=checkbox]:checked ~ .checkbox-controlled-display.flex,
input[type=radio]:checked ~ .checkbox-controlled-display.flex {
  display:flex;
}
input[type=checkbox]:checked ~ .checkbox-controlled-display.dib,
input[type=radio]:checked ~ .checkbox-controlled-display.dib {
  display:inline-block;
}

input[type=checkbox] ~ .checkbox-controlled-bold,
input[type=radio] ~ .checkbox-controlled-bold {
  min-width: 20px;
  height: 20px;
  @media only screen and (max-width: 1039px) {
    min-width: 25px;
    height: 25px;
    line-height: 25px;
  }
}

input[type=checkbox]:checked ~ .checkbox-controlled-bold,
input[type=radio]:checked ~ .checkbox-controlled-bold {
  font-weight: normal;
  background-color: #000;
  color: #fff;
  position: relative;
  z-index: 2;
}

.checkbox-controlled-height {
  max-height:0;
  overflow:hidden;
  transition:max-height 0.2s cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

input[type=checkbox]:checked ~ .checkbox-controlled-height,
input[type=radio]:checked ~ .checkbox-controlled-height {
  max-height: 150vh;
}

input[type=checkbox]:checked + h5,
input[type=radio]:checked + h5 {
  background: red;
}


.checkbox-controlled .active-show {
  display: none;
}


input[type=checkbox]:checked ~ .checkbox-controlled .active-show,
input[type=radio]:checked ~ .checkbox-controlled .active-show,
input[type=radio]:checked ~ .active-show,
input[type=radio]:checked ~ * .active-show{
  display:block;
}
input[type=checkbox]:checked ~ .checkbox-controlled .active-show.flex,
input[type=radio]:checked ~ .checkbox-controlled .active-show.flex,
input[type=radio]:checked ~ .active-show.flex,
input[type=radio]:checked ~ * .active-show.flex {
  display:flex;
}
input[type=checkbox]:checked ~ .checkbox-controlled .active-show.dib,
input[type=radio]:checked ~ .checkbox-controlled .active-show.dib,
input[type=radio]:checked ~ .active-show.dib,
input[type=radio]:checked ~ * .active-show.dib {
  display:inline-block;
}

input[type=checkbox]:checked ~ .checkbox-controlled .active-hide,
input[type=radio]:checked ~ .checkbox-controlled .active-hide,
input[type=radio]:checked ~ .active-hide,
input[type=radio]:checked ~ * .active-hide {
  display:none;
}

input[type=radio]:checked ~ .nav__sub1-btn {
  .plus {
    display: none;
  }
}

input[type=radio]:checked ~ .nav__sub1-btn {
  .minus {
    display: inline-block;
  }
}

.checkbox-controlled-display-s {
  display:none;
}
input[type=checkbox]:checked ~ .checkbox-controlled-display-s,
input[type=radio]:checked ~ .checkbox-controlled-display-s {
  display:block;
}
input[type=checkbox]:checked ~ .checkbox-controlled-display-s.flex,
input[type=radio]:checked ~ .checkbox-controlled-display-s.flex {
  display:flex;
}
input[type=checkbox]:checked ~ .checkbox-controlled-display-s.dib,
input[type=radio]:checked ~ .checkbox-controlled-display-s.dib {
  display:inline-block;
}
.checkbox-controlled-height-s {
  max-height:0;
  overflow:hidden;
  transition:
  max-height 0.2s ease;
}
input[type=checkbox]:checked ~ .checkbox-controlled-height-s,
input[type=radio]:checked ~ .checkbox-controlled-height-s {
  max-height: 150vh;
}

.checkbox-controlled-display-l {
  display:none;
}
input[type=checkbox]:checked ~ .checkbox-controlled-display-l,
input[type=radio]:checked ~ .checkbox-controlled-display-l {
  display:block;
}
input[type=checkbox]:checked ~ .checkbox-controlled-display-l.flex,
input[type=radio]:checked ~ .checkbox-controlled-display-l.flex {
  display:flex;
}
input[type=checkbox]:checked ~ .checkbox-controlled-display-l.dib,
input[type=radio]:checked ~ .checkbox-controlled-display-l.dib {
  display:inline-block;
}

.blog-articles {
  @media (min-width: 1024px) {
    padding-top: 100px;
    padding-bottom: 100px;
  }
  .blog-articles__article {
    width: 100%;
    text-align: center;
    @media (min-width: 1024px) {
      width: 50%;
    }
    &:nth-child(1n), &:nth-child(6n) {
      @media (min-width: 1024px) {
        padding-left: 60px;
        padding-right: 50px;
      }
    }
    &:nth-child(2n){
      @media (min-width: 1024px) {
        padding-right: 60px;
        padding-left: 160px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    &:nth-child(4n){
      @media (min-width: 1024px) {
        padding-left: 60px;
        padding-right: 100px;
        padding-top: 100px;
      }
    }
    &:nth-child(5n){
      @media (min-width: 1024px) {
        padding-right: 60px;
      }
    }
    &:nth-child(3n) {
      @media (min-width: 1024px) {
        width: 100%;
        padding-left: 0px;
        padding-right: 0px;
        padding-bottom: 100px;
        padding-top: 100px;
      }
      .article-card__image {
        height: 0;
        position: relative;
        padding-bottom: 56.25%;
        img {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100% !important;
          object-fit: cover;
        }
      }
    }
  }
  .article-card {
    margin-bottom: 40px;
    width: 100%;
  }
}

.article-card__info {
  width: 460px;
  margin: 0 auto;
  max-width: 100%;
}

.article-card__title {
  font-size: 20px;
}

.blog__breadcrumbs {
  position: absolute;
  top: 102px;
  width: 100%;
  left: 0;
  z-index: 5;
  .breadcrumbs {
    margin: 0 0.5rem;
  }
}

.multipleSlidesWrapper .swiper-slide .block__text_content {
  display:none;
}

.multipleSlidesWrapper .slideshow-controls--content {
  opacity:1;
  transform: translateY(0px);
  min-height: 150px;
  @media (min-width: 1024px) {
    width: 33.3333333%;
  }
}
.multipleSlidesWrapper .slideshow-controls--content.loading {
  opacity:0;
  transform: translateY(20px);
}

.multipleSlidesWrapper .slideshow-controls .slideshow-controls--arrows {
  @media (min-width: 1024px) {
    width: 624px;
    transform: translateX(-50%);
    left: 50%;
  }
}

.multipleSlidesWrapper .block__text_content a:hover, .multipleSlidesWrapper .block__text_content a:focus {
  text-decoration: none;
}

[neptune-liquid]:not([neptune-templated]){
  opacity: 0;
  height: 0;
  pointer-events: none;
  display: none;
}

[type="search"] {
  -webkit-appearance: none; 
}

#shopify-section-mega-menu {
  display: none;
}

#shopify-section-collection [data-badge="new"] {
    display: none !important;
}

.shop-pay-logo {
  fill: #5a31f4;
  color: #5a31f4;
}

.shopify-payment-terms svg, shopify-payment-terms svg path  {
  fill: #5a31f4;
  color: #5a31f4;
}

.panel__item--image-text a {
    height: calc(100vh - 190px);
}
