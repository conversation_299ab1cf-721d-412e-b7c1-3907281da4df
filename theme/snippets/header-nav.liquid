<nav class="header--nav hidden lg:flex lg:flex-grow-0 lg:flex-shrink-0 lg:items-start lg:top-main top-main-sm left-0 z-40 flex-col justify-center lg:w-auto w-full h-screen max-w-full px-0 overflow-x-hidden transition-transform duration-300 ease-in-out lg:static header__nav lg:h-auto  lg:px-4 lg:py-0 lg:overflow-visible lg:max-h-auto max-h-main">

  {% if megaMenu == true %}{% render 'panel-item-search-bar' %}{% endif %}

  {% capture navLinkClasses %}nav--item-link block overflow-hidden lg:overflow-visible brand-semibold relative lg:leading-none leading-loose uppercase cursor-pointer{% endcapture %}
  
  <ul class="flex flex-col items-center px-4 pt-4 pb-32 lg:p-0 m-0 mt-0 tracking-wide list-none nav_level_1 lg:flex-row lg:flex-1 lg:w-full lg:mx-0-75 lg:border-none">
    {% for link in linklists[section.settings.menu].links %}
      <li id="mainMenu-{{ link.title | handleize }}" class="nav--item w-full px-3 py-5 nav__item lg:w-auto lg:h-full active-remove cursor-pointer whitespace-nowrap group" onclick="
        document.querySelectorAll('[search-results]').forEach((sr)=>{sr.innerHTML = ''});
        document.querySelectorAll('[search-suggestions]').forEach((ss)=>{ss.innerHTML = ''});
        document.querySelector('body').classList.remove('search-in-focus');
        document.querySelector('#shopify-section-search').classList.remove('active');
      ">
        <div class="relative flex p-3 nav_item__wrap">
          {%- assign main_nav_handle = link.title | handle -%}
          {% if link.url contains 'panel:' %}
            <button class="{{ navLinkClasses }}" {% if main_nav_handle == section.settings.exclusive_link_handle -%}style="color: {{ section.settings.nav_exclusive_color }};"{% endif %}>
              <span class="block">{{ link.title }}</span>
            </button>
          {% else %}
            {%- liquid
              assign external_link = false
              if link.type == "http_link" 
                unless link.url contains '/vendors?' or link.url contains '/types?'
                  assign external_link = true
                endunless
              endif
            -%}
            <a 
              class="{{ navLinkClasses }}" {% if main_nav_handle == section.settings.exclusive_link_handle -%}style="color: {{ section.settings.nav_exclusive_color }};"{% endif %}
              href="{{ link.url }}"{% if external_link %} target="_blank"{% endif %}
            >
              <span class="block">{{ link.title }}</span>
            </a>
          {% endif %}
          
        </div>

      </li>
    {% endfor %}
  </ul>

  {% if megaMenu == true %}
    <div class="lg:hidden mt-auto flex flex-row items-center lg:p-10 p-5 text-xs uppercase">
      <span>
        {% render 'icon' icon:'user', height:18, width:18 %}
      </span>
      {% unless customer %}
        <a href="/account/login" class="px-3 block leading-none">{{ 'customer.log_in' | t }}</a>
      {% else %}
        <a href="/account/logout" class="px-3 block leading-none">{{ 'customer.log_out' | t }}</a>
      {% endunless %}
    </div>
  {% endif %}
</nav>
