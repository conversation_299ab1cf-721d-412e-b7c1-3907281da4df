{% if title != blank and content != blank %}
<li class="accordion border-0 border-b border-primary {% if open == true %}active{% endif %}">
  <button 
    id="AccordionTitle-{{key}}" 
    class="accordion-title flex flex-row items-center w-full px-2 py-1 border-0 bg-transparent outline-none text-left {{ fontSize }} {{ fontSizeMobile }}" 
    aria-expanded="false" 
    aria-disabled="false" 
    aria-controls="AccordionSection-{{key}}" 
    neptune-engage='targets:[
      { 
        selector:_parent,
        classes:toggle:active,
        siblings:classes:remove:active
      },
      {
        selector:"_grandparent button",
        attributes:[{att:"aria-disabled" set:"false"} {att:"aria-expanded" set:"false"}]
      },
      { 
        selector:"_grandparent .active button",
        attributes:[{att:"aria-disabled" set:"true"} {att:"aria-expanded" set:"true"}]
      },
      {
        selector:"_grandparent .accordion-panel",
        attributes:{att:"aria-hidden" set:"true"}
      },
      { 
        selector:"_grandparent .active .accordion-panel",
        attributes:{att:"aria-hidden" set:"false"}
      }
    ]'
    data-id="{{ title | handleize }}"
  >
    <span>{{ title }}</span>

    <span class="active-hide ml-auto">
      {% render 'icon', icon: 'plus', class:'p-1'%}
    </span>
    <span class="hide active-show ml-auto">
      {% render 'icon', icon: 'minus', class:'p-1' %}
    </span>

  </button>
  <div 
    id="AccordionSection-{{ forloop.index }}" 
    aria-hidden="true" 
    aria-labelledby="AccordionTitle-{{ forloop.index }}" 
    role="region" 
    class="accordion-panel active-show">
    <div class="py-4 px-6 {{ fontSize }} {{ fontSizeMobile }} border-t">
      {{ content }}
      <input class="sr-only" type="text">
    </div>
  </div>
</li>
{% endif %}
