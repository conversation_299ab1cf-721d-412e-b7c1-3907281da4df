{% raw %}

  {% for offer in offers.all %}
    {% if offer.settings.success != blank and offer.settings.eligible %}
      <span class="cart__offer-message cart__offer-message--success">
        <span class="message">
          {{ offer.settings.success }}
        </span>
      </span>
      {% break %}
    {% endif %}

    {% if offer.settings.eligible == false and offer.settings.approach != blank %}
      <span class="cart__offer-message cart__offer-message--approach">
        {% assign progress = total_price | divided_by: offer.settings.threshold_cents %}
        {% assign balance = offer.settings.threshold_cents | minus: total_price | money %}
        {% unless progress >= 1 %}
          <span class="message">
            {{ offer.settings.approach | replace: "{{ balance }}", balance }}
          </span>
          {% comment %}
          <div class="approach__bar relative w-full h-3 mb-0 progress-bar">
            <span class="approach__bar-fill absolute inset-0 origin-left bg-primary progress" style="transform: scaleX({{ progress }})"></span>
          </div>
          {% endcomment %}
        {% endunless %}
      </span>
      {% break %}
    {% endif %}
  {% endfor %}

  {% endraw %}
