{% liquid 
	if config.tags != blank
		assign matches = config.tags | split: ','
		assign tagMatch = false
		for matchitem in matches
		  assign inclusions = matchitem | split: '+'
		  assign passes = true
		  for inclusion in inclusions
		    assign inc = inclusion | strip
		    unless tags contains inc
		      assign passes = false
		      break
		    endunless
		  endfor
		  if passes
		  	assign tagMatch = true
		  	break
			endif
		endfor
	else
		assign tagMatch = true
	endif

  if config.link_product
    assign product_url = all_products[config.link_product].url
  endif
%}
{% if tagMatch %}
  <div class="product-motivator flex">
    <div class="product-motivator__text">
      <h5 class="product-motivator__title m-0" style="font-size:{{config.title_size}}px;">{{config.title}}</h5>
			{% if config.link != blank or product_url != blank %}
				<span class="product-motivator__link-prefix" style="font-size:{{config.link_text_size}}px;">{{config.link_prefix}}</span>
				<a href="{% if product_url != blank %}{{product_url}}{% else %}{{config.link}}{% endif %}" class="product-motivator__link" style="font-size:{{config.link_text_size}}px;">{{config.link_text}}</a>
			{% endif %}
    </div>
  </div>
{% endif %}
