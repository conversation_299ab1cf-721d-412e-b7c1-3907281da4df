

<section class="lg:w-1/4">
  
  {% for heading in headings %}

    {% assign heading = heading | strip_html %}
    {% assign body_stripped = bodies[forloop.index0] | strip_html | strip%}
    {% if body_stripped != blank %}

      <article class="relative px-5 lg:flex lg:items-start lg:my-20 lg:px-10 product-description">


        {% if heading and bodies[forloop.index0] %}
        
          <h2 class="flex-grow-0 flex-shrink-0 font-mono pt-4 my-3 lg:my-0 lg:pt-2 lg:w-1/5 tracked uppercase">{{heading | strip_html | strip}}</h2>

        {% endif %}

        <div class="flex-grow-0 flex-shrink-0 lg:w-4/5 lg:text-2xl leading-relaxed">
          {{ 
            bodies[forloop.index0] 
            | replace: '<ul', '<ul class="flex flex-wrap lg:text-base"' 
            | replace: '<h3', '<h3 class="font-bold"'
            | replace: '<blockquote>', '<blockquote class="bg-white border-l-0 m-7 max-w-3xl ml-0 not-italic px-8 py-4 py-5 text-base">'
            | replace: '<li', '<li class="mb-4 lg:mb-8 lg:pr-6 lg:w-1/2"'
            | replace: '<p>', '<p class="mb-4 lg:mb-8">'
          }}
        </div>
        
      </article>

    {% endif %}
  {% endfor %}

</section>
