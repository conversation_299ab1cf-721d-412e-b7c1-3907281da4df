<style>
  .duty-terms-error .content-box,
  .duty-terms-error .input-checkbox {
    border: 2px solid red;
  }

  .duty-terms-error .field__message--error {
    display: block
  }

  .duty-terms-error .checkbox__label {
    color: red;
  }

  #continue_button:disabled ~ .button-blocker {
    height: 100%;
    width: 100%;
    top: 0;
    position: absolute;
  }
</style>
<template data-international-checkbox>
  <div class="section__header">
    <h2 class="section__title">Duties and Taxes</h2>
  </div>

  <div class="section__content">
    <div class="content-box">
      <div class="content-box__row">
        <div class="checkbox-wrapper">
          <div class="checkbox__input">
            <input class="input-checkbox" data-backup="duty_terms" aria-controls="section--duty-terms" type="checkbox" value="1" name="checkout[duty_terms]" id="checkout_duty_terms" onchange="window.dutyTermsAccepted = this.checked; document.querySelector('#continue_button').disabled = !this.checked; document.querySelector('[data-duty-terms]').classList.remove('duty-terms-error');">
          </div>

          <label class="checkbox__label content-box__emphasis" for="checkout_duty_terms">
            {{ 'checkout.international.duty_checkbox' | t }}
          </label>          
        </div>
      </div>
    </div>
    <p class="field__message field__message--error" id="error-for-expiry">{{ 'checkout.international.duty_error' | t }}</p>
  </div>
</template>
<script>
  window.dutyTermsContent = {
    {%- liquid
     assign terms_content = settings.duty_terms_messages | newline_to_br | split: '<br />'
    -%}
    {%- for country in terms_content -%}
      {%- liquid
        assign code = country | split: '|' | first | strip | uppercase
        assign messaging = country | split: '|' | last | strip
      -%}
    "{{ code }}": {{ messaging | json }}{%- unless forloop.last -%},{%- endunless -%}
    {%- endfor -%}
  }
  document.addEventListener('DOMContentLoaded', function() {

    handleLocalizedContent();

    if (Shopify.Checkout.step != 'payment_method') return false
    if (Shopify.Checkout.step == 'payment_method' && Shopify.Checkout.shippingAddress.countryCode == 'US') return false

    // If there isn't messaging in the theme settings don't show the checkbox
    if (Shopify.Checkout.step == 'payment_method' && !dutyTermsContent[Shopify.Checkout.shippingAddress.countryCode]) return false

    var formCheck = setInterval(function() {
      // Native Shopify
      // if (!document.querySelector('[data-payment-method]')) return false
      // Global E content
      if (!document.querySelector('#glbe-secureContainer')) return false

      var wrapper = document.createElement('div')
      var buttonBlock = document.createElement('div')

      wrapper.classList.add('section')
      wrapper.classList.add('section--duty-terms')
      wrapper.setAttribute('data-duty-terms', '')
      wrapper.innerHTML = document.querySelector('template[data-international-checkbox]').innerHTML
      if (!!dutyTermsContent[Shopify.Checkout.shippingAddress.countryCode]) wrapper.querySelector('label').innerHTML = dutyTermsContent[Shopify.Checkout.shippingAddress.countryCode]
      document.querySelector('[data-payment-method]').after(wrapper)

      document.querySelector('#continue_button').disabled = true
      buttonBlock.classList.add('button-blocker')
      buttonBlock.addEventListener('click', function(e) {
        document.querySelector('#continue_button').classList.add('btn--loading')
        document.querySelector('[data-duty-terms]').classList.add('duty-terms-error')
        document.querySelector('#continue_button').classList.remove('btn--loading')
      })

      document.querySelector('#continue_button').parentNode.style.position = 'relative'
      document.querySelector('#continue_button').after(buttonBlock)

      clearInterval(formCheck)
    }, 200)
  })

  function handleLocalizedContent() {

    if (Shopify.Checkout.shippingAddress.countryCode != 'US' && Shopify.Checkout.shippingAddress.countryCode != 'CA') {
      document.querySelectorAll('[data-international]').forEach(function(element) { element.classList.add('hidden'); });
      document.querySelectorAll('[data-domestic]').forEach(function(element) { element.classList.remove('hidden'); });
      document.querySelectorAll('[data-buyer-accepts-sms]').forEach(function(element) { element.classList.add('hidden'); });
      // console.log('top')
    }
     else {
      document.querySelectorAll('[data-domestic]').forEach(function(element) { element.classList.add('hidden'); });
      // console.log('bottom')
    }
  }

  
</script>
