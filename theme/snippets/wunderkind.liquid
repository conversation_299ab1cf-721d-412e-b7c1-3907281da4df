<script>

  (function(d) {
    var e = d.createElement('script');
    e.src = d.location.protocol + '//tag.wknd.ai/4937/i.js';
    e.async = true;
    d.getElementsByTagName("head")[0].appendChild(e);
  }(document));

  window.wunderkind = {

    {% if request.page_type == 'product' %}
    product: {
      exclusion:false,
      id:'{{ product.handle }}',
      sku:'{{ product.selected_or_first_available_variant.sku }}',
      name:'{{ product.title | escape }}',
      url:'https://{{ shop.domain }}{{ product.url }}',
      imgUrl:'{{ product.images[0].src | img_url:'grande' }}',
      price:{{ product.price | divided_by: 100 }},
      inStock:{{product.avaiable | json }},
      itemCategory:'{% for tag in product.tags %}{% if tag contains 'Sub Class:'%}{{ tag | remove:'Sub Class: ' | strip }}{% break %}{% endif %}{% endfor %}'
    },
    {% endif %}

    {% if request.page_type == 'collection' %}
    category: {
      categoryIds:{{ collection.products | map: 'handle' | json }},
      title:{{ collection.title | escape | json }},
    },
    {% endif %}

    {% if customer %}
    user: {
      email:{{ customer.email | json }}
    },
    {% endif %}

    {% liquid
      assign quantity = 0
      for item in cart.items
        assign quantity = quantity | plus: item.quantity 
      endfor
    %}
    cart: {
      value: {{ cart.total_price | divided_by: 100 }},
      quantity: {{ quantity }}
    }
  }

  document.addEventListener('product:variantSelected', e => {
    wunderkind.product.sku = e.detail.info.variant.sku
  })


</script>
