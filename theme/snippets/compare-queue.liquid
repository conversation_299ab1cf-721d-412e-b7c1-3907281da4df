{% comment %} 


This is generated by webpack so don't edit this unless you want to be sad when your work gets overwritten in production.
You can edit this file in ../../components/** 


{% endcomment %}






{% unless template == 'collection.compare' %}
<div class="fixed z-40 bottom-0 right-0 p-4 flex justify-end pointer-events-none" neptune-liquid="{topic:Comparison,source:Comparison.data}">
  {% raw %}
  {% if data.items.size > 0 %}
  <button class="bg-yellow-600 flex p-2 rounded-md text-white w-full pointer-events-auto cursor-pointer" onclick="Comparison.view()">
    <span class="bg-white bold flex h-6 items-center justify-center mr-2 rounded-full text-sm text-yellow-700 w-6">
      {{ data.items.size }}
    </span>
    {% endraw %}{{'products.compare.view' | t}}{% raw %}
    {% endraw %}{% render 'icon' icon:'chevron-right' class:'ml-auto' %}{% raw %}
  </button>
  {% endif %}

  {% endraw %}
</div>
{% endunless %}
