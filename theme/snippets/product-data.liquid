{
  "id": {{ product.id}},
  "handle": {{ product.handle | json }},
  "available": {{ product.available | json }},
  "title": {{ product.title | json }},
  {% if options %}
  "options_with_values": [
    {%- for option in product.options_with_values -%}
    {
      "name": {{ option.name | escape | json }},
      "position": {{forloop.index | json }},
      "values": [{%- for value in option.values -%}{{ value | escape | json }}{%- unless forloop.last -%},{%-endunless-%}{%- endfor -%}
      ]
    }{%- unless forloop.last -%},{%-endunless-%}
    {%-endfor-%}
  ],
  "image_constraint_options": ["Color"],
  {% endif %}
  {% if variants %}
  "variants":[
    {%-for variant in product.variants -%}
    {
      "id":{{ variant.id | json}},
      "available":{{ variant.available | json}},
      "option1":{{ variant.option1 | escape | json}},
      "option2":{{ variant.option2 | escape | json}},
      "option3":{{ variant.option3 | escape | json}},
      "options":[{% for option in variant.options %}{{option | escape | json}}{%- unless forloop.last -%},{%-endunless-%}{% endfor %}],
      "price":{{ variant.price | json}},
      "compare_at_price":{{ variant.compare_at_price | json}},
      "inventory_quantity":{{ variant.inventory_quantity | json}},
      "title":{{ variant.title | escape | json}},
      "sku": {{variant.sku | json }}
      {%- if variant_images %},
      {%- capture variantImages -%}
      {%- assign images_start = false -%}
      {%- for image in product.images -%}
        {%- if image.variants.size > 0-%}
          {%- assign has_variant = false %}
          {%- for imgvariant in image.variants-%}
            {%- if imgvariant.id == variant.id -%}
              {%- assign images_start = true -%}
              {%- assign has_variant = true %}
            {%-endif-%}
          {%- endfor -%}
          {%- if images_start == true and has_variant == false -%}{%- break -%}{%- endif -%}
        {%- endif -%}
        {%- if images_start -%}
        { 
          "src": {{image.src | json}},
          "alt": {{image.alt | escape | json}},
          "aspect_ratio": {{image.aspect_ratio | json}},
          "width": {{image.width | json}},
          "height": {{image.height | json}}
        },
        {%- endif -%}
      {%-endfor-%}
      {%-endcapture-%}
      {%- assign vis = variantImages | strip | size | minus: 1 -%}
      "images": [{{ variantImages | strip | slice: 0, vis}}]
      {%- endif -%}
    }{%- unless forloop.last -%},{%-endunless-%}
    {%-endfor-%}
  ],
  {% endif %}
  {% if product.media %}
    "images":[{%- for image in product.images -%}
      { 
        "src": {{image.src | json}},
        "alt": {{image.alt | escape | json}},
        "aspect_ratio": {{image.aspect_ratio | json}},
        "width": {{image.width | json}},
        "height": {{image.height | json}},
        "variants":[
          {%-for variant in image.variants -%}
          {
            "id":{{ variant.id | json}},
            "option1":{{ variant.option1 | json}}
          }{%- unless forloop.last -%},{%-endunless-%}
        {%-endfor-%}]
      }{%- unless forloop.last -%},{%-endunless-%}
    {%-endfor-%}],
    "videos": {{ product.media | where: 'media_type', 'video' | json }},
    {% assign image_size = image_size | default: '600x' %}
    "featured_image": {{ product.images[0] | product_image_url: image_size | json }},
    "hover_image": {{ product.images[1] | product_image_url: image_size | json }},
    "hover_video": {{ product.images[1].alt | json }},
  {% endif %}
  {% if siblings %}
  "siblings":{{ siblings | json }},
  {% endif %}
  "price": {{ product.price }},
  "compare_at_price": {{ product.compare_at_price | default: product.price}},
  "tags": {{ product.tags | json }},
  "current_size": ""
}
