<ul class="list-none m-0 grid w-full col-span-4 grid-cols-4 gap-x-4 lg:p-10 p-5">
  
  {%- liquid 

    assign skip = false 

    for block in blocks offset: offset

      if breakon == 'parent' and block.settings.subpane_start
        assign skip = true
      endif

      if skip
        if block.settings.subpane_end
          assign skip = false
        endif
        continue
      endif

      case block.type

        when 'nav-item'
          render 'panel-item-nav-item' settings:block.settings index:forloop.index blocks:blocks type:block.type id:block.id offset:offset

        when 'image-link'
          render 'panel-item-image-link' settings:block.settings type:block.type id:block.id

        when 'link-list'
          render 'panel-item-link-list' settings:block.settings type:block.type id:block.id

        when 'divider'
          render 'panel-item-divider' settings:block.settings type:block.type id:block.id

        when 'image-text'
          render 'panel-item-image-text' settings:block.settings type:block.type id:block.id

        when 'parent'
          if breakon == 'parent' 
             break
          endif

      endcase

      unless breakon == 'parent'
        if block.settings.subpane_end
          break
        endif
      endunless 

    endfor

  -%}
  
</ul>