{%- liquid
   assign stretch = '' 
   for tag in product.tags
    if tag contains 'stretch_'
      assign stretch = tag | split: '_' | last
    endif
  endfor
-%}

<div class="w-full pt-2 mb-4 stretch-meter__container">
  {%- if stretch != blank -%}
    {%- assign stretch_handle = stretch | handle -%}
    {%- assign level = stretch_handle | plus: 0 -%}

    {%- if level -%}
      {%- assign total_levels = levels | default: 5  -%}
      {%- assign total_adjusted = total_levels | minus: 1 -%}
      
      {%- assign percentage_unit = 100 | divided_by: total_adjusted -%}
      {%- assign level_offset = level | minus: 1 -%}
      
      {%- if level == 1 -%}
        {%- assign percentage = 0 -%}
      {%- elsif level == total_levels -%}
        {%- assign percentage = 100 -%}
      {%- else -%}
        {%- assign percentage = percentage_unit | times: level_offset -%}
      {%- endif -%}
      
      <div class="stretch-meter relative">
        <div class="stretch-unit absolute percent-{{ percentage }}" style="left: {{ percentage }}%;"></div>
      </div>
      
      <div class="flex flex-row">
        <div class="w-1/2 text-left font-caption-caps mt-3.5">Stretch</div>
        <div class="w-1/2 text-right font-caption-caps mt-3.5">Rigid</div>
      </div>
    {% endif %}

    {%- style -%}
      .stretch-meter {
        width: 100%;
        height: 1px;
        background-color: #000;
        position: relative;
      }
      .stretch-unit {
        width: 10px;
        height: 10px;
        background-color: #000;
        border-radius: 50%;
        transform: translateX(-50%);
        top: -4px;
      }

      .stretch-unit.percent-0 {
        transform: translateX(1px);
      }

      .stretch-unit.percent-100{
        transform: translateX(-11px);
      }

      .stretch-meter::after {
        content: "";
        display: block;
        position: absolute;
        height: 15px;
        width: 1px;
        background: #000;
        top: 50%;
        right:0;
        transform: translateY(-50%);
      }

      .stretch-meter::before {
        content: "";
        display: block;
        position: absolute;
        height: 15px;
        width: 1px;
        background: #000;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
      }

    {%- endstyle -%}
  {%- endif -%}
</div>

{%- if stretch == blank -%}
  {%- style -%}
  .product-essentials__liquid_block:has(.stretch-meter__container:empty) {
    display: none;
  }
  {%- endstyle -%}
{%- endif -%}