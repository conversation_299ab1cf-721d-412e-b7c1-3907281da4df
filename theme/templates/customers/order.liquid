{% comment %}
  The data-label attributes on <td> elements are mobile-friendly
  helpers used for responsive-table labels
{% endcomment %}

<section>
  <div class="border-b-[2px] border-white py-[13px]">
    <h1 class="text-xl text-center font-normal leading-[27px]">{{ 'customer.orders.title' | t }}</h1>
  </div>
  <div class="mt-[10px] flex flex-wrap flex-row">
    {% include 'account-menu' %}

    <div class="template-order max-w-[500px] w-full mx-auto mb-[21px] px-[10px]">
      <a href="/account/#orderHistory"> 
        <span class="flex align-start justify-start items-center gap-[5px] my-[15px] leading-none">
          <span>{% render 'icon' icon:'arrow-large-left' strokeWidth:0 width:15 height:14%}</span> 
          <span class="underline text-[12px]">{{ 'customer.login_page.cancel' | t }}</span>
        </span>
      </a>

      <div class="px-[15px] py-[6px] border-b-2 border-[#F7F8FC] bg-white rounded-tl-[10px] rounded-tr-[10px]">
        <p class="text-base m-0 leading-[1.4]">Order: {{ order.name }}</p>
      </div>
      <div class="p-[15px] border-b-2 border-[#F7F8FC] bg-white">
        <p class="mb-0 leading-[1.4]">{{ order.fulfillment_status_label }}</p>
        <p class="mb-0 leading-[1.4]"><span class="text-gray">{{ 'customer.orders.fulfillment_status' | t }}: {{ order.created_at | date: "%B %d, %Y" }}</span></p>
        <p class="mb-0 leading-[1.4]"><span class="text-gray">{{ 'customer.orders.payment_status' | t }}: {{ order.financial_status_label }}</span></p>
        {% if order.total_price > 0 %}
        <p class="mb-0 leading-[1.4]"><span class="text-gray">{{ 'customer.orders.total' | t }}: {{ order.total_price | money }}</span></p>
        {% endif %}
        {% if order.cancelled %}
          {%- assign cancelled_at = order.cancelled_at | date: "%B %d, %Y" -%}
          <p class="text-[#E92A14]">{{ 'customer.order.cancelled_html' | t: date: cancelled_at }}</p>
          <p>{{ 'customer.order.cancelled_reason' | t: reason: order.cancel_reason }}</p>
        {% endif %}
      </div>
      <div class="p-[15px] border-b-2 border-[#F7F8FC] bg-white">

          <div class="flex flex-col gap-[15px]">
            {% for line_item in order.line_items %}

              {% unless line_item.properties._bundle == 'component' %}

                <div class="flex flex-row gap-[5px]">
                  <img 
                    src="{{ line_item.image | img_url: '112x' }}" 
                    alt="{{ line_item.product.title | escape }}" 
                    class="w-[112px] h-auto object-cover mr-[14px]"
                    loading="lazy">

                  <div class="flex flex-col">
                    <p class="leading-normal text-[16px] mb-0 max-w-[80%]">{{line_item.title}}</p>
                    {% for option in line_item.options_with_values %}
                      {% unless option.name == "Title" %}
                        <p class="leading-normal text-[12px] mb-0">{{ option.name }}: {{ option.value }}</p>
                      {% endunless %}
                    {% endfor %}
                    {% assign price = item.final_price %}

                    {% if line_item.properties._bundle == 'primary' %}
                      {% assign reverse_items = order.line_items | reverse %}
                      {% assign showIncludes = false %} 
                      {% for subitem in reverse_items %}{% if subitem.properties._bundle == 'component' and subitem.properties._instance == line_item.properties._instance %} {% assign showIncludes = true %} {% endif %} {% endfor %} 
                      <h4 class="font-normal mt-2 {% if showIncludes == true %}db{% else %}dn{% endif %}">Includes</h4>
                      {% for subitem in reverse_items %}
                        {% if subitem.properties._bundle == 'component' and subitem.properties._instance == line_item.properties._instance %}
                          
                          <p class="mb-0"><span class="text-black">{{subitem.title}} - {{subitem.final_price | money }}</span></p>
                          {% assign price = price | plus: subitem.final_price %}
  
                        {% endif %}
                      {% endfor %}
  
                    {% endif %}
                    <p class="mb-0 text-[12px] mb-0"><span class="text-black">{{ 'customer.order.sku' | t }}: {{ line_item.sku }}</span></p>
                    <p class="mb-0 text-[12px] mb-0"><span class="text-black">{{ 'customer.order.quantity' | t }}: {{ line_item.quantity }}</span></p>
                  </div>
                   <p class="mb-0 flex flex-col justify-end pb-[10px]">{{ line_item.quantity | times: line_item.price | money }}</p>
                </div>

              {% endunless %}

            {% endfor %}
          </div>
          
      </div>
      <div class="p-[15px] border-b-2 border-[#F7F8FC] bg-white">
        <div class="text-black">
          <p class="text-xl leading-[1.4] mb-0">Order Summary</p>
        </div>

        <div class="text-black mt-5">
          <p class="uppercase text-[11px] mb-0">{{ 'customer.order.billing_address' | t }}</p>
          {{ order.billing_address | format_address }}
        </div>

        <div class="text-black leading-relaxed mt-5">
          <div class="flex justify-between">
            <div>{{ 'customer.order.subtotal' | t }} : </div>
            <div data-label="{{ 'customer.order.subtotal' | t }}">{{ order.line_items_subtotal_price | money }}</div>
          </div>

          {% for discount in order.discounts %}
            <div class="flex justify-between">
              <div>{{ discount.code }} {{ 'customer.order.discount' | t }}</div>
              <div data-label="{{ 'customer.order.discount' | t }}">{{ discount.savings | money }}</div>
            </div>
          {% endfor %}

          {% for shipping_method in order.shipping_methods %}
            <div class="flex justify-between">
              <div>{{ 'customer.order.shipping' | t }} ({{ shipping_method.title }}) :</div>
              <div data-label="{{ 'customer.order.shipping' | t }} ({{ shipping_method.title }})">{{ shipping_method.price | money }}</div>
            </div>
          {% endfor %}

          {% for tax_line in order.tax_lines %}
            <div class="flex justify-between">
              <div>{{ 'customer.order.tax' | t }} ({{ tax_line.title }} {{ tax_line.rate | times: 100 }}%) : </div>
              <div data-label="{{ 'customer.order.tax' | t }} ({{ tax_line.title }} {{ tax_line.rate | times: 100 }}%)">{{ tax_line.price | money }}</div>
            </div>
          {% endfor %}
        </div>

        <div class="text-black flex justify-between mt-5 text-xl leading-[1.4]">
          <div>{{ 'customer.order.total' | t }} :</div>
          <div data-label="{{ 'customer.order.total' | t }}">{{ order.total_price | money }} {{ order.currency }}</div>
        </div>
      </div>
      <div class="p-[15px] border-b-2 border-[#F7F8FC] bg-white rounded-bl-[10px] rounded-br-[10px]">
        <div class="text-black">
          <p class="text-xl leading-[1.4] mb-0">Shipping</p>
        </div>

        <div class="text-black mt-5">
          <p class="uppercase text-[11px] mb-0">{{ 'customer.order.shipping_address' | t }}</p>
          {{ order.shipping_address | format_address }}
        </div>
      </div>
    </div>

  </div>
</section>

<script src="{{ 'customer-forms.js' | asset_url }}" defer="defer"></script>