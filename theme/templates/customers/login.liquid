<div class="customer login container lg:px-20 px-0 lg:mb-60 max-w-[1000px] lg:mt-[50px] lg:mb-[260px] mb-[35px] mt-[11px]">
  <div class="account__recover max-w-[350px] hidden mx-auto text-center mt-[27px] mb-0 mb-[190px]">
  
    <h1 id="recover" class="lg:mb-1  mt-4 mb-0 text-center font-normal text-[20px] capitalize lg:px-0 px-[20px] leading-[28px]" tabindex="-1">
      {{ 'customer.recover_password.title' | t }}
    </h1>
    <div>
      <p class="mb-[30px]">
        {{ 'customer.recover_password.subtext' | t }}
      </p>

      {%- form 'recover_customer_password' -%}
        {% assign recover_success = form.posted_successfully? %}
        <div class="field">
          <label class="input--label" for="RecoverEmail">
            {{ 'customer.login_page.email' | t }} <span class="required">*</span>
          </label>
          <input type="email"
            class="input"
            value=""
            name="email"
            id="RecoverEmail"
            autocorrect="off"
            autocapitalize="off"
            autocomplete="email"
            {% if form.errors %}
              aria-invalid="true"
              aria-describedby="RecoverEmail-email-error"
              autofocus
            {% endif %}
            placeholder="{{ 'customer.login_page.email' | t }}"
          >
        </div>
        {%- if form.errors -%}
          <small id="RecoverEmail-email-error" class="form__message mb-[20px] text-[12px]">
            {{ form.errors.messages['form'] }}
          </small>
        {%- endif -%}

        <button class="form-btn-submit btn w-full mb-[20px] py-[6.5px] px-[32px] rounded-[5px]" type="submit">
          {{ 'customer.login_page.submit' | t }}
        </button>

        <a class="leading-normal p-0 flex mt-[14px]" href="#login"
          neptune-engage='{
            preventDefault: true,
            on:click,
            targets: [
              {
                selector:.account__recover,
                classes:add:hidden
              },
              {
                selector:.account__login,
                classes:remove:hidden
              }
            ]
          }'
         onclick="history.replaceState(null, null, window.location.pathname + window.location.search);">
          <span class="flex align-start justify-center items-center gap-[5px]"><span>{% render 'icon' icon:'arrow-large-left' strokeWidth:0 width:15 height:14%}</span> <span class="underline text-[12px]">{{ 'customer.login_page.cancel' | t }}</span></span>
        </a>
      {%- endform -%}
    </div>
  </div>

  <div class="account__login flex lg:flex-row flex-col">
    <div class="account__login_form lg:w-1/2 w-full lg:pr-[67px] relative lg:border-0 border-b-[2px] border-white pb-8 lg:pb-0 lg:mb-0 mb-[4px]"> 
      <h1 id="login" class="lg:mb-4  my-4 text-center font-normal text-[20px] capitalize lg:px-0 px-[20px] leading-[28px]" tabindex="-1">
        {{ 'customer.login_page.sign_in' | t }}
      </h1>   

      {%- if recover_success == true -%}
        <h3 class="form__message mt-0 mb-[32px] leading-[1.5] text-center" tabindex="-1" autofocus>
          {{ 'customer.recover_password.success' | t }}
        </h3>
      {%- endif -%}
      <div class="lg:px-0 px-[20px]">

        {%- form 'customer_login', novalidate: 'novalidate' -%}
          {%- if form.errors -%}
            <p class="customer_error_message text-[#E92A14] text-[12px] flex justify-start gap-[5px] mb-2"> {% render 'icon' icon: 'information' height:18 width:18 %}{{ 'customer.login_page.error_message' | t }}</p>
          {%- endif -%}

          <div class="field">
            <label class="input--label" for="CustomerEmail">
              {{ 'customer.login_page.email' | t }} <span class="required"> *</span>
            </label>        
            <input
                type="email"
                class="input"
                name="customer[email]"
                id="CustomerEmail"
                autocomplete="email"
                autocorrect="off"
                autocapitalize="off"
                required
                {% if form.errors contains 'form' %}
                  aria-invalid="true"
                {% endif %}
                placeholder="{{ 'customer.login_page.email' | t }}"
              >
            <p id="emailError" class="text-red-500 text-sm hidden">
              {{ 'customer.login_page.invalid_email' | t }} 
            </p>
          </div>

          {%- if form.password_needed -%}

            <div class="field relative"> 
              <label class="input--label" for="CustomerPassword">
                {{ 'customer.login_page.password' | t }}  <span class="required">*</span>
              </label>         
              <input
                type="password"
                class="input pr-10"
                name="customer[password]"
                autocomplete="current-password"
                {% if form.errors contains 'form' %}
                  aria-invalid="true"
                {% endif %}
                placeholder="{{ 'customer.login_page.password' | t }}"
              >

              <button type="button" class="toggle-password absolute right-3 top-10 text-xl cursor-pointer"
                onclick="
                  var passwordInput = this.closest('.field').querySelector('input[type=\'password\'], input[type=\'text\']');
                  if (passwordInput) {
                    passwordInput.type = passwordInput.type === 'password' ? 'text' : 'password';
                    passwordInput.focus();
                    this.querySelector('.icon-show').classList.toggle('hidden');
                    this.querySelector('.icon-hide').classList.toggle('hidden');
                  }
                ">
                <span class="icon-show">{% render 'icon' icon: 'eye-secondary' height:20 width:20 %}</span>
                <span class="icon-hide hidden">{% render 'icon' icon: 'eye-linethrough' height:20 width:20 %}</span>
              </button>
            </div>
          {%- endif -%}

          <button class="form-btn-submit btn w-full mb-5 py-[6.5px] px-[32px] rounded-[5px]" type="submit">
            {{ 'customer.login_page.sign_in' | t }}
          </button>

          <a 
            class="border-b border-black p-0 inline-block text-xs leading-none " href="#recover"
            neptune-engage='{
              preventDefault: true,
              on:click,
              targets: [
                {
                  selector:.account__recover,
                  classes:remove:hidden
                },
                {
                  selector:.account__login,
                  classes:add:hidden
                }
              ]
            }'
          >
            {{ 'customer.login_page.forgot_password' | t }}
          </a>
          
        {%- endform -%}
      </div>
      {%- if shop.checkout.guest_login -%}
        <div>
          <hr>
          <h2>{{ 'customer.login_page.guest_title' | t }}</h2>

          {%- form 'guest_login' -%}
            <button class="btn w-full mb-5 py-[6.5px] px-[32px] rounded-[5px]">
              {{ 'customer.login_page.guest_continue' | t }}
            </button>
          {%- endform -%}
        </div>
      {%- endif -%}
    </div> 
    <div class="lg:w-1/2 w-full lg:pl-[67px] account__register-button">
      <div class="section-header">
        <h2 class="lg:mb-4  my-4 text-center font-normal text-[20px] capitalize lg:px-0 px-[20px] leading-[28px]">
          {{ 'customer.account.new_account' | t }}
        </h2>
      </div>
      <div class="pb-[27px] lg:px-0 px-[20px]">
        <p class="ac-login__register-desc text-[16px] leading-[1.4]">
          {{ 'customer.account.create_account_description' | t }}
        </p>
      </div>
      <div class="ac-login__col-ft lg:px-0 px-[20px]">
          {{ 'customer.account.create_account' | t | customer_register_link }}
      </div>
    </div>
  </div>
</div>

{% style %}
  @media only screen and (min-width: 1025px) {
    .account__login_form:after {
      background-color: #FFFFFF;
      bottom: 0;
      content: "";
      display: block;
      position: absolute;
      right: 0;
      top: 55%;
      width: 2px;
      height: calc(100% + 60px);
      transform: translateY(-50%);
    } 
  }
  .btn a {
    color: #ffffff;
  }
{% endstyle %}

<script src="{{ 'customer-forms.js' | asset_url }}" defer="defer"></script>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    if (window.location.hash === '#recover') {
      const loginElement = document.querySelector('.account__login');
      const recoverElement = document.querySelector('.account__recover');
      
      if (loginElement) {
        loginElement.classList.add('hidden');
      }
      if (recoverElement) {
        recoverElement.classList.remove('hidden');
      }
    }
  });
</script>





