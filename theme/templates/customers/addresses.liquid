<section class="customer lg:pb-8 lg:pt-[21px] lg:pt-[21px] lg:pb-4 pt-4 pb-3 min-vh-75">

  <div class="border-b-[2px] border-white pb-[21px] mb-[10px]">
    <h1 class="my-0 text-[20px] text-center font-normal leading-[28px] block">{{ 'customer.account.view_addresses' | t }}</h1>
  </div>

  <div class="flex flex-col">
    {% include 'account-menu' %}
    <div class="lg:w-4/5 w-full  max-w-[750px] mx-auto mt-[15px] lg:px-0 px-[10px]">

      <div class="inline-block mb-[15px]" data-address>
        <button class="btn bg-primary btn--rounded" type="button" neptune-engage='[
          {
            "targets": [
              {
                selector:html
                attributes:[{
                  att:data-active-modal 
                  set:newAddress
                }]
              },
              {
                "selector":"[data-add-address-form]",
                "classes":{
                  "toggle":"active"
                }
              }
            ]
          }
        ]'>
          {{ 'customer.addresses.add_new' | t }}
        </button>
          <div data-add-address-form class="modal-center modal fixed left-1/2 top-1/2 z-50 bg-white" style="width: 500px; max-width: 90%; max-height: 90%; overflow-y: auto;">
            <button 
              class="modal-close p-2 icon bg-transparent text-black border-none absolute top-[0.3rem] right-[0.3rem]"
              neptune-engage='[
                {
                  "targets": [
                    {
                      "selector": "[data-add-address-form]",
                      "classes": {
                        "toggle": "active"
                      }
                    },
                    {
                      selector:html
                      attributes:[{
                        att:data-active-modal 
                        set:_remove
                      }]
                    }
                  ]
                }
              ]'>
              {% render 'icon' icon:'close' %}
            </button>
            <div class="flex flex-col">
              <div class="py-[12px] bg-white">
              {% form 'customer_address', customer.new_address %}
                <h2 class="modal-title text-xl font-normal">{{ 'customer.addresses.add_new' | t }}</h2>

                <div data-address-fields class="address-form px-[14px]">
                  <div class="field field-group mt-4 relative">
                    <label class="form--label input--label" for="AddressFirstNameNew">
                      {{ 'customer.addresses.first_name' | t }} <span class="required">*</span>
                    </label>
                    <input type="text"
                      name="address[first_name]"
                      id="AddressFirstNameNew"
                      class="w-full form--input input"
                      value="{{ form.first_name }}"
                      placeholder="{{ 'customer.addresses.first_name' | t }}"
                      autocapitalize="words">
                  </div>
                  
                  <div class="field field-group mt-4 relative">
                    <label class="form--label input--label" for="AddressLastNameNew">
                      {{ 'customer.addresses.last_name' | t }} <span class="required">*</span>
                    </label>
                    <input type="text"
                      name="address[last_name]"
                      id="AddressLastNameNew"
                      class="w-full form--input input"
                      value="{{ form.last_name }}"
                      placeholder="{{ 'customer.addresses.last_name' | t }}"
                      autocapitalize="words">
                  </div>
                  
                  <div class="field field-group mt-4 relative">
                    <label class="form--label input--label" for="AddressCompanyNew">
                      {{ 'customer.addresses.company' | t }}
                    </label>
                    <input type="text"
                      name="address[company]"
                      id="AddressCompanyNew"
                      class="w-full form--input input"
                      value="{{ form.company }}"
                      placeholder="{{ 'customer.addresses.company' | t }}"
                      autocapitalize="words">
                  </div>
                  
                  <div class="field field-group mt-4 relative">
                    <label class="form--label input--label" for="AddressAddress1New">
                      {{ 'customer.addresses.address1' | t }} <span class="required">*</span>
                    </label>
                    <input type="text"
                      name="address[address1]"
                      id="AddressAddress1New"
                      class="w-full form--input input"
                      value="{{ form.address1 }}"
                      placeholder="{{ 'customer.addresses.address1' | t }}"
                      autocapitalize="words">
                  </div>
                  
                  <div class="field field-group mt-4 relative">
                    <label class="form--label input--label" for="AddressAddress2New">
                      {{ 'customer.addresses.address2' | t }}
                    </label>
                    <input type="text"
                      name="address[address2]"
                      id="AddressAddress2New"
                      class="w-full form--input input"
                      value="{{ form.address2 }}"
                      placeholder="{{ 'customer.addresses.address2' | t }}"
                      autocapitalize="words">
                  </div>
                  
                  <div class="field field-group mt-4 relative">
                    <label class="form--label input--label" for="AddressCityNew">
                      {{ 'customer.addresses.city' | t }} <span class="required">*</span>
                    </label>
                    <input type="text"
                      name="address[city]"
                      id="AddressCityNew"
                      class="w-full form--input input"
                      value="{{ form.city }}"
                      placeholder="{{ 'customer.addresses.city' | t }}"
                      autocapitalize="words">
                  </div>
                  
                  <div class="field field-group mt-4 relative">
                    <label class="form--label input--label" for="AddressCountryNew">
                      {{ 'customer.addresses.country' | t }} <span class="required">*</span>
                    </label>
                    <select
                      id="AddressCountryNew"
                      name="address[country]"
                      class="address-country w-full input"
                      data-default="{{ form.country }}"
                      value="{{ form.country }}"
                    >
                      {{ country_option_tags }}
                    </select>
                  </div>

                  <div id="AddressProvinceContainerNew" class="address-province-container field field-group mt-4 relative" style="display: none">
                    <label class="form--label input--label" for="AddressProvinceNew">
                      {{ 'customer.addresses.province' | t }}
                    </label>
                    <div class="select">
                      <select
                        name="address[province]"
                        id="AddressProvinceNew"
                        class="address-province w-full input"
                        data-default="{{ form.province }}"
                        autocomplete="address-level1"
                        data-provinces="{{ form.province }}"
                      >
                      </select>
                      <span class="svg-wrapper">{{- 'icon-caret.svg' | inline_asset_content -}}</span>
                    </div>
                  </div>

                  <div id="AddressProvinceInputContainerNew" class="address-province-input-container field field-group mt-4 relative" style="display: none">
                    <label class="form--label input--label" for="AddressProvinceInputNew">
                      {{ 'customer.addresses.province' | t }}
                    </label>
                    <input type="text"
                      name="address[province]"
                      id="AddressProvinceInputNew"
                      class="address-province-input w-full input"
                      data-default="{{ form.province }}"
                      data-provinces="{{ form.province }}"
                    />
                  </div>

                  <div class="field field-group mt-4 relative">
                    <label class="form--label input--label" for="AddressZipNew">
                      {{ 'customer.addresses.zip' | t }} <span class="required">*</span>
                    </label>
                    <input type="text"
                      name="address[zip]"
                      id="AddressZipNew"
                      class="w-full form--input input"
                      value="{{ form.zip }}"
                      placeholder="{{ 'customer.addresses.zip' | t }}"
                      autocapitalize="characters">
                  </div>
                  
                  <div class="field field-group mt-4 relative">
                    <label class="form--label input--label" for="AddressPhoneNew">
                      {{ 'customer.addresses.phone' | t }} <span class="required">*</span>
                    </label>
                    <input type="tel"
                      name="address[phone]"
                      id="AddressPhoneNew"
                      class="w-full form--input input"
                      value="{{ form.phone }}"
                      placeholder="{{ 'customer.addresses.phone' | t }}"
                      pattern="[0-9\-]*">
                  </div>
                </div>

                <div class="field-checkbox field mt-4 px-[14px]">
                  
                  <label class="inline-block font-normal leading-normal text-sm ml8px flex items-center space-x-2" for="address_default_address_new">

                    {{ form.set_as_default_checkbox | replace: 'type', 'class="hidden" type' }}
                    <span class="checkbox relative">
                      {% render 'icon' icon: 'check' width:16 height:16 strokeWidth:2 %}
                    </span>
                    <span class="cursor-pointer">
                      {{ 'customer.addresses.set_default' | t }}
                    </span>
                  </label>
                </div>
                <div class="field field-group mt-4 relative flex justify-center">
                  <button class="btn bg-primary btn--rounded form-btn-submit" type="submit">{{ 'customer.addresses.add' | t }}</button>
                  <button class="btn bg-secondary text-black ml-2" type="button" neptune-engage='[
                    {
                      "targets": [
                        {
                          "selector": "[data-add-address-form]",
                          "classes": {
                            "toggle": "active"
                          }
                        },
                        {
                          selector:html
                          attributes:[{
                            att:data-active-modal 
                            set:_remove
                          }]
                        }
                      ]
                    }
                  ]'>
                    {{ 'customer.addresses.cancel' | t }}
                  </button>
                </div>
              {% endform %}
              </div>
            </div>
          </div>
      </div>  
      <div id="addressInformation">
        {% paginate customer.addresses by 6 %}
            
            {% unless customer.addresses.size == 0 %}

            <div class="grid lg:grid-cols-2 grid-cols-1 gap-[10px] mb-[30px]">

              {% assign count = 0 %}

              {% for address in customer.addresses %}

                {% assign count = count | plus: 1 %}

                <div data-address class="w-full rounded-[10px] bg-white">

                  <div class="flex flex-row"> 

                    <div class="flex-grow text-gray">

                      <div class="flex justify-between items-center pl-[14px] pr-[9px] pt-[10px] pb-[8px] border-b-[2px] border-[#F7F8FC]">
                        {% if address == customer.default_address %}
                          <p class="text-[16px] m-0 leading-[1.4]">Address {{ count }} - {{ 'customer.addresses.default' | t }}</p>
                        {% else %}
                          <p class="text-[16px] m-0 leading-[1.4]">Address {{ count }}</p>
                        {% endif %}
                        <div class="">
                          <button class="underline text-[12px]" type="button" 
                            data-address-toggle 
                            neptune-engage='[
                              {
                                "targets": [
                                  {
                                    selector:html
                                    attributes:[{
                                      att:data-active-modal 
                                      set:editAddress
                                    }]
                                  },
                                  {
                                    "selector":"[data-address-form-{{count}}]",
                                    "classes":{
                                      "toggle":"active"
                                    }
                                  }
                                ]
                              }
                            ]'>
                              {{ 'customer.addresses.edit' | t }}
                          </button>
                          <form class="inline-block ml-4" data-address-delete-form method="post" action="/account/addresses/{{ address.id }}" data-confirm-message="{{ 'customer.addresses.delete_confirm' | t }}">
                            <input type="invisible" class="hidden" name="_method" value="delete"/>
                            <button class="underline text-[12px]" type="submit">
                              {{ 'customer.addresses.delete' | t }}
                            </button>
                          </form>
                        </div>

                      </div>

                      <div class="px-[14px] pb-[18px] pt-[14px]">
                        {{ address | format_address }}
                      </div>
                    </div>

                    <div data-address-form-{{count}} class="modal-center modal fixed left-1/2 top-1/2 z-50 bg-white" style="width: 500px; max-width: 90%; max-height: 90%; overflow-y: auto;">
                      <button 
                        class="modal-close p-2 icon bg-transparent text-black border-none absolute top-[0.3rem] right-[0.3rem]"
                        neptune-engage='[
                          {
                            "targets": [
                              {
                                selector:html
                                attributes:[{
                                  att:data-active-modal 
                                  set:_remove
                                }]
                              },
                              {
                                "selector": "[data-address-form-{{count}}]",
                                "classes": {
                                  "toggle": "active"
                                }
                              }
                            ]
                          }
                        ]'>
                        {% render 'icon' icon:'close' %}
                      </button>
                      <div class="flex flex-col">
                        <div class="py-[12px] bg-white">
                          {% form 'customer_address', address %}
                            <h4 class="modal-title text-xl font-normal">{{ 'customer.addresses.edit_address' | t }}</h4>
                            <div data-address-fields class="address-form px-[14px]">
                              <div class="field field-group mt-4 relative">
                                <label class="form--label input--label" for="AddressFirstName_{{ form.id }}">
                                  {{ 'customer.addresses.first_name' | t }} <span class="required">*</span>
                                </label>
                                <input type="text"
                                  name="address[first_name]"
                                  id="AddressFirstName_{{ form.id }}"
                                  class="w-full form--input input"
                                  value="{{ form.first_name }}"
                                  placeholder="{{ 'customer.addresses.first_name' | t }}"
                                  autocapitalize="words">
                              </div>
                              <div class="field field-group mt-4 relative">
                                <label class="form--label input--label" for="AddressLastName_{{ form.id }}">
                                  {{ 'customer.addresses.last_name' | t }} <span class="required">*</span>
                                </label>
                                <input type="text"
                                  name="address[last_name]"
                                  id="AddressLastName_{{ form.id }}"
                                  class="w-full form--input input"
                                  value="{{ form.last_name }}"
                                  placeholder="{{ 'customer.addresses.last_name' | t }}"
                                  autocapitalize="words">
                              </div>
                              <div class="field field-group mt-4 relative">
                                <label class="form--label input--label" for="AddressCompany_{{ form.id }}">
                                  {{ 'customer.addresses.company' | t }}
                                </label>
                                <input type="text"
                                  name="address[company]"
                                  id="AddressCompany_{{ form.id }}"
                                  class="w-full form--input input"
                                  value="{{ form.company }}"
                                  placeholder="{{ 'customer.addresses.company' | t }}"
                                  autocapitalize="words">
                              </div>
                              <div class="field field-group mt-4 relative">
                                <label class="form--label input--label" for="AddressAddress1_{{ form.id }}">
                                  {{ 'customer.addresses.address1' | t }} <span class="required">*</span>
                                </label>
                                <input type="text"
                                  name="address[address1]"
                                  id="AddressAddress1_{{ form.id }}"
                                  class="w-full form--input input"
                                  value="{{ form.address1 }}"
                                  placeholder="{{ 'customer.addresses.address1' | t }}"
                                  autocapitalize="words">
                              </div>
                              <div class="field field-group mt-4 relative">
                                <label class="form--label input--label" for="AddressAddress2_{{ form.id }}">
                                  {{ 'customer.addresses.address2' | t }}
                                </label>
                                <input type="text"
                                  name="address[address2]"
                                  id="AddressAddress2_{{ form.id }}"
                                  class="w-full form--input input"
                                  value="{{ form.address2 }}"
                                  placeholder="{{ 'customer.addresses.address2' | t }}"
                                  autocapitalize="words">
                              </div>
                              <div class="field field-group mt-4 relative">
                                <label class="form--label input--label" for="AddressCity_{{ form.id }}">
                                  {{ 'customer.addresses.city' | t }} <span class="required">*</span>
                                </label>
                                <input type="text"
                                  name="address[city]"
                                  id="AddressCity_{{ form.id }}"
                                  class="w-full form--input input"
                                  value="{{ form.city }}"
                                  placeholder="{{ 'customer.addresses.city' | t }}"
                                  autocapitalize="words">
                              </div>

                              <div class="field field-group mt-4 relative">
                                <label class="form--label input--label" for="AddressCountryNew">
                                  {{ 'customer.addresses.country' | t }} <span class="required">*</span>
                                </label>
                                <select
                                  id="AddressCountry_{{ form.id }}"
                                  name="address[country]"
                                  class="address-country w-full input"
                                  data-default="{{ form.country }}"
                                >
                                  {{ country_option_tags }}
                                </select>
                              </div>
            
                              <div id="AddressProvinceContainerNew" class="address-province-container field field-group mt-4 relative" style="display: none">
                                <label class="form--label input--label" for="AddressProvinceNew">
                                  {{ 'customer.addresses.province' | t }}
                                </label>
                                <div class="select">
                                  <select id="AddressProvince_{{ form.id }}" name="address[province]" class="address-province w-full input" autocomplete="address-level1" data-default="{{ form.province }}" data-provinces="{{ form.province }}">
                                  </select>
                                  <span class="svg-wrapper">{{- 'icon-caret.svg' | inline_asset_content -}}</span>
                                </div>
                              </div>
            
                              <div id="AddressProvinceInputContainerNew" class="address-province-input-container field field-group mt-4 relative" style="display: none">
                                <label class="form--label input--label" for="AddressProvinceInputNew">
                                  {{ 'customer.addresses.province' | t }}
                                </label>
                                <input type="text" id="AddressProvince_Optional_{{ form.id }}" data-default="{{ form.province }}"  name="address[province]" data-provinces="{{ form.province }}" class="address-province-input w-full input" />
                              </div>
                              
                              <div class="field field-group mt-4 relative">
                                <label class="form--label input--label" for="AddressZip_{{ form.id }}">
                                  {{ 'customer.addresses.zip' | t }} <span class="required">*</span>
                                </label>
                                <input type="text"
                                  name="address[zip]"
                                  id="AddressZip_{{ form.id }}"
                                  class="w-full form--input input"
                                  value="{{ form.zip }}"
                                  placeholder="{{ 'customer.addresses.zip' | t }}"
                                  autocapitalize="characters">
                              </div>
                              <div class="field field-group mt-4 relative">
                                <label class="form--label input--label" for="AddressPhone_{{ form.id }}">
                                  {{ 'customer.addresses.phone' | t }} <span class="required">*</span>
                                </label>
                                <input type="tel"
                                  name="address[phone]"
                                  id="AddressPhone_{{ form.id }}"
                                  class="w-full form--input input"
                                  value="{{ form.phone }}"
                                  placeholder="{{ 'customer.addresses.phone' | t }}"
                                  pattern="[0-9\-]*">
                              </div>
                            </div>
                            
                            <div class="field-checkbox field mt-4 px-[14px]">
                              <label class="inline-block font-normal leading-normal text-sm ml8px flex items-center space-x-2" for="address_default_address_{{ form.id }}">
                                {{ form.set_as_default_checkbox | replace: 'type', 'class="hidden" type' }}
                                <span class="checkbox relative">
                                  {% render 'icon' icon: 'check' width:16 height:16 strokeWidth:2 %}
                                </span>
                                <span class="cursor-pointer">
                                  {{ 'customer.addresses.set_default' | t }}
                                </span>
                              </label>
                            </div>
                            <div class="field field-group mt-4 relative flex justify-center">
                              <button class="btn bg-primary btn--rounded form-btn-submit" type="submit">{{ 'customer.addresses.update' | t }}</button>
                              <button class="btn bg-secondary text-black ml-2" type="button" data-address-toggle data-form-id="{{ form.id }}" neptune-engage='[
                                {
                                  "targets": [
                                    {
                                      selector:html
                                      attributes:[{
                                        att:data-active-modal 
                                        set:_remove
                                      }]
                                    },
                                    {
                                      "selector": "[data-address-form-{{count}}]",
                                      "classes": {
                                        "toggle": "active"
                                      }
                                    }
                                  ]
                                }
                              ]'>
                                {{ 'customer.addresses.cancel' | t }}
                              </button>
                            </div>
                          {% endform %}
                        </div>
                      </div>
                    </div>

                  </div>
                </div>
              {% endfor %}
            </div>

            {% endunless %}



            {% if paginate.pages > 1 %}
              {% include 'pagination' %}
            {% endif %}

          {% endpaginate %}
        </div>
      </div>
  </div>
</section>

<script src="{{ 'customer-forms.js' | asset_url }}" defer="defer"></script>