{{ 'customer.css' | asset_url | stylesheet_tag }}

<div class="customer reset-password text-center lg:mt-[65px] mt-[27px] mb-[250px]">
  <svg style="display: none">
    <symbol id="icon-error" viewBox="0 0 13 13">
      <circle cx="6.5" cy="6.50049" r="5.5" stroke="white" stroke-width="2"/>
      <circle cx="6.5" cy="6.5" r="5.5" fill="#EB001B" stroke="#EB001B" stroke-width="0.7"/>
      <path d="M5.87413 3.52832L5.97439 7.57216H7.02713L7.12739 3.52832H5.87413ZM6.50076 9.66091C6.88091 9.66091 7.18169 9.37267 7.18169 9.00504C7.18169 8.63742 6.88091 8.34917 6.50076 8.34917C6.12061 8.34917 5.81982 8.63742 5.81982 9.00504C5.81982 9.37267 6.12061 9.66091 6.50076 9.66091Z" fill="white"/>
      <path d="M5.87413 3.17832H5.51535L5.52424 3.537L5.6245 7.58083L5.63296 7.92216H5.97439H7.02713H7.36856L7.37702 7.58083L7.47728 3.537L7.48617 3.17832H7.12739H5.87413ZM6.50076 10.0109C7.06121 10.0109 7.5317 9.57872 7.5317 9.00504C7.5317 8.43137 7.06121 7.99918 6.50076 7.99918C5.94031 7.99918 5.46982 8.43137 5.46982 9.00504C5.46982 9.57872 5.94031 10.0109 6.50076 10.0109Z" fill="white" stroke="#EB001B" stroke-width="0.7">
    </symbol>
  </svg>
  <h1 class="lg:mb-1  my-4 text-center font-normal text-[20px] capitalize lg:px-0 px-[20px]">
    {{ 'customer.reset_password.title' | t }}
  </h1>
  <p class="mb-[30px]">
    {{ 'customer.reset_password.subtext' | t: email: email | replace: email, "<strong>" | append: email | append: "</strong>" }}
  </p>

  {%- form 'reset_customer_password' -%}

    <div class="field">    
      <label for="password" class="input--label">
        {{ 'customer.reset_password.password' | t }} <span class="required">*</span>
      </label>
      {%- if form.errors contains 'password' -%}
        <small id="password-error" class="form__message error-message left-0">
          {{ form.errors.translated_fields['password'] | capitalize }} {{ form.errors.messages['password'] }}
        </small>
      {%- endif -%}
      <input
        type="password"
        name="customer[password]"
        id="password"
        autocomplete="new-password"
        {% if form.errors contains 'password' %}
          aria-invalid="true"
          aria-describedby="password-error"
        {% endif %}
        placeholder="{{ 'customer.reset_password.password' | t }}"
      >
      <button type="button" class="toggle-password absolute right-3 top-10 text-xl cursor-pointer"
        onclick="
          var passwordInput = this.closest('.field').querySelector('input[type=\'password\'], input[type=\'text\']');
          if (passwordInput) {
            passwordInput.type = passwordInput.type === 'password' ? 'text' : 'password';
            passwordInput.focus();
            this.querySelector('.icon-show').classList.toggle('hidden');
            this.querySelector('.icon-hide').classList.toggle('hidden');
          }
        ">
        <span class="icon-show">{% render 'icon' icon: 'eye-secondary' height:20 width:20 %}</span>
        <span class="icon-hide hidden">{% render 'icon' icon: 'eye-linethrough' height:20 width:20 %}</span>
      </button>
    </div>

    <div class="field">     
      <label for="password_confirmation" class="input--label">
        {{ 'customer.reset_password.password_confirm' | t }} <span class="required">*</span>
      </label> 
      {%- if form.errors contains 'password_confirmation' -%}
        <small id="password_confirmation-error" class="form__message error-message left-0">
          {{ form.errors.translated_fields['password_confirmation'] | capitalize }} {{ form.errors.messages['password_confirmation'] }}
        </small>
      {%- endif -%}
      <input
        type="password"
        name="customer[password_confirmation]"
        id="password_confirmation"
        autocomplete="new-password"
        {% if form.errors contains 'password_confirmation' %}
          aria-invalid="true"
          aria-describedby="password_confirmation-error"
        {% endif %}
        placeholder="{{ 'customer.reset_password.password_confirm' | t }}"
      >
      <button type="button" class="toggle-password absolute right-3 top-10 text-xl cursor-pointer"
      onclick="
        var passwordInput = this.closest('.field').querySelector('input[type=\'password\'], input[type=\'text\']');
        if (passwordInput) {
          passwordInput.type = passwordInput.type === 'password' ? 'text' : 'password';
          passwordInput.focus();
          this.querySelector('.icon-show').classList.toggle('hidden');
          this.querySelector('.icon-hide').classList.toggle('hidden');
        }
      ">
        <span class="icon-show">{% render 'icon' icon: 'eye-secondary' height:20 width:20 %}</span>
        <span class="icon-hide hidden">{% render 'icon' icon: 'eye-linethrough' height:20 width:20 %}</span>
      </button>
    </div>

    <button class="form-btn-submit btn btn--primary w-full my-2 register-submit" type="submit">
      {{ 'customer.reset_password.submit' | t }}
    </button>
  {%- endform -%}
</div>

<script src="{{ 'customer-forms.js' | asset_url }}" defer="defer"></script>
