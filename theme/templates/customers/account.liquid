<div class="border-b-[2px] border-white lg:pb-[21px] lg:pt-[21px] pt-[27px] pb-[26px]  mb-[10px]">
  <div class="account__header flex flex-col items-center">
    <h1 class="text-[20px] my-0 text-center font-normal mb-4 leading-[28px]">Welcome back, {{ customer.first_name }}</h1>
    <a href="/account/logout" class="text-black text-center text-[12px] block underline lg:mb-[8px]">
      <span>{{ 'customer.account.log_out' | t }}</span>
    </a>
  </div>

  <h2 class="order-history__title my-0 text-[20px] text-center font-normal hidden leading-[28px]">{{ 'customer.orders.title' | t }}</h2>
</div>
<div class="account-container flex flex-wrap flex-row justify-center">
  {% include 'account-menu' %}
  <div id="accountInformation" class="account-information flex flex-col gap-[10px] customer lg:w-4/5 w-full  max-w-[500px] mx-auto lg:mt-[31px] lg:mb-[38px] mt-[19px] lg:mb-0 mb-[29px]  lg:px-0 px-[10px] ">
    {% for address in customer.addresses %}

      {% if address == customer.default_address %}
        {% assign count = count | plus: 1 %}

        <div data-address class="w-full rounded-[10px] bg-white mb-[10px]">
    
          <div class="flex flex-row"> 
    
            <div class="flex-grow text-gray">
    
              <div class="flex justify-between items-center pl-[14px] pr-[9px] pt-[10px] pb-[8px] border-b-[2px] border-[#F7F8FC]">
                <p class="text-[16px] m-0 leading-[1.4]">{{ 'customer.account.address' | t }}</p>
                <div class="">
                  <button class="underline text-[12px]" type="button" 
                    data-address-toggle 
                    neptune-engage='[
                      {
                        "targets": [
                          {
                            selector:html
                            attributes:[{
                              att:data-active-modal 
                              set:editAddress
                            }]
                          },
                          {
                            "selector":"[data-address-form-{{count}}]",
                            "classes":{
                              "toggle":"active"
                            }
                          }
                        ]
                      }
                    ]'>
                      {{ 'customer.addresses.edit' | t }}
                  </button>
                </div>
    
              </div>
    
              <div class="px-[14px] pb-[18px] pt-[14px]">
                {{ address | format_address }}
              </div>
            </div>
    
            <div data-address-form-{{count}} class="modal-center modal fixed left-1/2 top-1/2 z-50 bg-white" style="width: 500px; max-width: 90%; max-height: 90%; overflow-y: auto;">
              <button 
                class="modal-close p-2 icon bg-transparent text-black border-none absolute top-[0.3rem] right-[0.3rem]"
                neptune-engage='[
                  {
                    "targets": [
                      {
                        selector:html
                        attributes:[{
                          att:data-active-modal 
                          set:_remove
                        }]
                      },
                      {
                        "selector": "[data-address-form-{{count}}]",
                        "classes": {
                          "toggle": "active"
                        }
                      }
                    ]
                  }
                ]'>
                {% render 'icon' icon:'close' %}
              </button>
              <div class="flex flex-col">
                <div class="py-[12px] bg-white">
                  {% form 'customer_address', address %}
                    <h4 class="modal-title text-xl font-normal">{{ 'customer.addresses.edit_address' | t }}</h4>
                    <div data-address-fields class="address-form px-[14px]">
                      <div class="field field-group mt-4 relative">
                        <label class="form--label input--label" for="AddressFirstName_{{ form.id }}">
                          {{ 'customer.addresses.first_name' | t }} <span class="required">*</span>
                        </label>
                        <input type="text"
                          name="address[first_name]"
                          id="AddressFirstName_{{ form.id }}"
                          class="w-full form--input input"
                          value="{{ form.first_name }}"
                          placeholder="{{ 'customer.addresses.first_name' | t }}"
                          autocapitalize="words">
                      </div>
                      <div class="field field-group mt-4 relative">
                        <label class="form--label input--label" for="AddressLastName_{{ form.id }}">
                          {{ 'customer.addresses.last_name' | t }} <span class="required">*</span>
                        </label>
                        <input type="text"
                          name="address[last_name]"
                          id="AddressLastName_{{ form.id }}"
                          class="w-full form--input input"
                          value="{{ form.last_name }}"
                          placeholder="{{ 'customer.addresses.last_name' | t }}"
                          autocapitalize="words">
                      </div>
                      <div class="field field-group mt-4 relative">
                        <label class="form--label input--label" for="AddressCompany_{{ form.id }}">
                          {{ 'customer.addresses.company' | t }}
                        </label>
                        <input type="text"
                          name="address[company]"
                          id="AddressCompany_{{ form.id }}"
                          class="w-full form--input input"
                          value="{{ form.company }}"
                          placeholder="{{ 'customer.addresses.company' | t }}"
                          autocapitalize="words">
                      </div>
                      <div class="field field-group mt-4 relative">
                        <label class="form--label input--label" for="AddressAddress1_{{ form.id }}">
                          {{ 'customer.addresses.address1' | t }} <span class="required">*</span>
                        </label>
                        <input type="text"
                          name="address[address1]"
                          id="AddressAddress1_{{ form.id }}"
                          class="w-full form--input input"
                          value="{{ form.address1 }}"
                          placeholder="{{ 'customer.addresses.address1' | t }}"
                          autocapitalize="words">
                      </div>
                      <div class="field field-group mt-4 relative">
                        <label class="form--label input--label" for="AddressAddress2_{{ form.id }}">
                          {{ 'customer.addresses.address2' | t }}
                        </label>
                        <input type="text"
                          name="address[address2]"
                          id="AddressAddress2_{{ form.id }}"
                          class="w-full form--input input"
                          value="{{ form.address2 }}"
                          placeholder="{{ 'customer.addresses.address2' | t }}"
                          autocapitalize="words">
                      </div>
                      <div class="field field-group mt-4 relative">
                        <label class="form--label input--label" for="AddressCity_{{ form.id }}">
                          {{ 'customer.addresses.city' | t }} <span class="required">*</span>
                        </label>
                        <input type="text"
                          name="address[city]"
                          id="AddressCity_{{ form.id }}"
                          class="w-full form--input input"
                          value="{{ form.city }}"
                          placeholder="{{ 'customer.addresses.city' | t }}"
                          autocapitalize="words">
                      </div>
                      <div class="field field-group mt-4 relative">
                        <label class="form--label input--label" for="AddressCountry_{{ form.id }}">
                          {{ 'customer.addresses.country' | t }} <span class="required">*</span>
                        </label>
                        <select
                          name="address[country]"
                          id="AddressCountry_{{ form.id }}"
                          class="address-country w-full input"
                          data-default="{{ form.country }}">
                          {{ country_option_tags }}
                        </select>
                      </div>
                      <div id="AddressProvinceContainerNew" class="address-province-container field field-group mt-4 relative" style="display: none">
                        <label class="form--label input--label" for="AddressProvince_{{ form.id }}">
                          {{ 'customer.addresses.province' | t }} <span class="required">*</span>
                        </label>
                        <div class="select">
                          <select
                            name="address[province]"
                            id="AddressProvince_{{ form.id }}"
                            class="address-province w-full input"
                            data-default="{{ form.province }}"
                            autocomplete="address-level1"
                            data-provinces="{{ form.province }}"
                          >
                          </select>
                          <span class="svg-wrapper">{{- 'icon-caret.svg' | inline_asset_content -}}</span>
                        </div>
                      </div>
                      <div id="AddressProvinceInputContainerNew" class="address-province-input-container field field-group mt-4 relative" style="display: none">
                        <label class="form--label input--label" for="AddressProvinceInput_{{ form.id }}">
                          {{ 'customer.addresses.province' | t }}
                        </label>
                        <input type="text"
                          name="address[province]"
                          id="AddressProvinceInput_{{ form.id }}"
                          class="address-province-input w-full input"
                          data-default="{{ form.province }}"
                          data-provinces="{{ form.province }}"
                        />
                      </div>
                      <div class="field field-group mt-4 relative">
                        <label class="form--label input--label" for="AddressZip_{{ form.id }}">
                          {{ 'customer.addresses.zip' | t }} <span class="required">*</span>
                        </label>
                        <input type="text"
                          name="address[zip]"
                          id="AddressZip_{{ form.id }}"
                          class="w-full form--input input"
                          value="{{ form.zip }}"
                          placeholder="{{ 'customer.addresses.zip' | t }}"
                          autocapitalize="characters">
                      </div>
                      <div class="field field-group mt-4 relative">
                        <label class="form--label input--label" for="AddressPhone_{{ form.id }}">
                          {{ 'customer.addresses.phone' | t }} <span class="required">*</span>
                        </label>
                        <input type="tel"
                          name="address[phone]"
                          id="AddressPhone_{{ form.id }}"
                          class="w-full form--input input"
                          value="{{ form.phone }}"
                          placeholder="{{ 'customer.addresses.phone' | t }}"
                          pattern="[0-9\-]*">
                      </div>
                    </div>
                    <div class="field-checkbox field mt-4 px-[14px]">
                      <label class="inline-block font-normal leading-normal text-sm ml8px flex items-center space-x-2" for="address_default_address_{{ form.id }}">
                        {{ form.set_as_default_checkbox | replace: 'type', 'class="hidden" type' }}
                        <span class="checkbox relative">
                          {% render 'icon' icon: 'check' width:16 height:16 strokeWidth:2 %}
                        </span>
                        <span class="cursor-pointer">
                          {{ 'customer.addresses.set_default' | t }}
                        </span>
                      </label>
                    </div>
                    <div class="field field-group mt-4 relative flex justify-center">
                      <button class="btn bg-primary btn--rounded form-btn-submit" type="submit">{{ 'customer.addresses.update' | t }}</button>
                      <button class="btn bg-secondary text-black ml-2" type="button" data-address-toggle data-form-id="{{ form.id }}" neptune-engage='[
                        {
                          "targets": [
                            {
                              selector:html
                              attributes:[{
                                att:data-active-modal 
                                set:_remove
                              }]
                            },
                            {
                              "selector": "[data-address-form-{{count}}]",
                              "classes": {
                                "toggle": "active"
                              }
                            }
                          ]
                        }
                      ]'>
                        {{ 'customer.addresses.cancel' | t }}
                      </button>
                    </div>
                  {% endform %}
                </div>
              </div>
            </div>
    
          </div>
        </div>
      {% endif %}

    {% endfor %}
    <div class="w-full rounded-[10px] bg-white mb-[10px]">
        <div class="flex justify-between items-center pl-[14px] pr-[9px] pt-[10px] pb-[8px] border-b-[2px] border-[#F7F8FC]">
          <p class="text-[16px] m-0 leading-[1.4]">
            {{ 'customer.account.email' | t }}
          </p>
        </div>
        <p class="px-[14px] pb-[18px] pt-[14px] leading-[1.2]">
        {{ customer.email }}<br />
        </p>
    </div>
    
    
    <div class="w-full rounded-[10px] bg-white">
      <div class="flex justify-between items-center pl-[14px] pr-[9px] pt-[10px] pb-[8px] border-b-[2px] border-[#F7F8FC]">
        <p class="text-[16px] m-0 leading-[1.4]">
          {{ 'customer.account.password' | t }}
        </p>
        <a href="/account/login#recover" class="underline text-[12px]">{{ 'customer.account.reset_password' | t }}</a>
      </div>
      <p class="text-[20px] px-[14px] pb-[18px] pt-[14px] leading-[0.5]">••••••••••</p>
    </div>
  </div>

  <div id="orderHistory" class="order-history hidden w-[750px] lg:order-1 order-2 py-4 lg:py-0 mt-[20px]">
    <div class="">
      {% paginate customer.orders by 6 %}
        {% if customer.orders.size != 0 %}
          <div class="order-history__content grid lg:grid-cols-2 grid-cols-1 gap-[10px]">
            {% for order in customer.orders %}
              <div class="w-full rounded-[10px] bg-white">
                <div class="flex flex-row">
                  <div class="flex-grow text-gray">
                    <div class="flex justify-between items-center pl-[14px] pr-[9px] pt-[10px] pb-[8px] border-b-[2px] border-[#F7F8FC]">
                      <p class="text-[16px] m-0 leading-[1.4]">Order: {{ order.name }}</p>
                      <a class="underline text-[12px]" href="{{ order.customer_url }}">View Order</a>
                    </div>
                    <div class="px-[14px] pb-[18px] pt-[14px]">
                      <p class="mb-0 leading-[1.4]">{{ order.fulfillment_status_label }}</p>
                      <p class="mb-0 leading-[1.4]"><span class="text-gray">{{ 'customer.orders.fulfillment_status' | t }}: {{ order.created_at | date: "%B %d, %Y" }}</span></p>
                      <p class="mb-0 leading-[1.4]"><span class="text-gray">{{ 'customer.orders.payment_status' | t }}: {{ order.financial_status_label }}</span></p>
                      {% if order.total_price > 0 %}
                      <p class="mb-0 leading-[1.4]"><span class="text-gray">{{ 'customer.orders.total' | t }}: {{ order.total_price | money }}</span></p>
                      {% endif %}
                    </div> 
                 
                  </div>
                </div>
              </div>
            {% endfor %}
          </div>
        {% else %}
          <p class="my-4">{{ 'customer.orders.none' | t }}</p>
        {% endif %}
        {% if paginate.pages > 1 %}
          {% include 'pagination' %}
        {% endif %}
      {% endpaginate %}
    </div>
  </div>
  
</div>

<script src="{{ 'customer-forms.js' | asset_url }}" defer="defer"></script>

<script>
  setTimeout(function(){
    window.scroll({
      top: 0, 
      left: 0, 
      behavior: 'smooth'
    });
  }, 800);
</script>
