<section class="customer register container lg:px-20 px-5 mb-[64px]">
  <div class="w-full {% if settings.create_an_account_image != blank %}lg:w-1/2{% endif %} lg:px-16 lg:pb-[67px] lg:pt-[51px] flex flex-col justify-center">
    <div class="w-full flex flex-column items-center justify-center">
      <div class="max-w-[350px] w-full px-4 pt-[11px] pb-[17px] lg:p-0">
        <h1 class="lg:mb-4  my-4 text-center font-normal text-[20px] capitalize lg:px-0 px-[20px] leading-[28px]">{{ 'customer.register.title' | t }}</h1>
        <div class="type--article mt-0 mb-5">{{ settings.create_an_account_paragraph_1 }}</div>
        {% form 'create_customer', class:'mt-3' %}
          {{ form.errors | default_errors }}
          <input class="hidden" type="invisible" name="return_to" value="/account"/>
          <div class="field mt-1 relative">
            <label for="FirstName" class="input--label">
              {{ 'customer.register.first_name' | t }} <span class="required">*</span>
            </label>
            <input type="text"
              name="customer[first_name]"
              class="w-full form--input"
              id="FirstName"
              placeholder="{{ 'customer.register.first_name' | t }}"
              autofocus
              {% if form.first_name %}value="{{ form.first_name }}"{% endif %}>
          </div>

          <div class="field mt-1 relative">
            <label for="LastName" class="input--label">
              {{ 'customer.register.last_name' | t }} <span class="required">*</span>
            </label>
            <input type="text"
              name="customer[last_name]"
              class="w-full form--input"
              id="LastName"
              placeholder="{{ 'customer.register.last_name' | t }}"
              {% if form.last_name %}value="{{ form.last_name }}"{% endif %}>
          </div>
          
          <div class="field mt-1 relative">
            <label for="Email" class="input--label">
              {{ 'customer.register.email' | t }} <span class="required">*</span>
            </label>
            <input type="email"
              name="customer[email]"
              id="Email"
              class="w-full form--input {% if form.errors contains 'email' %}input-error{% endif %}"
              placeholder="{{ 'customer.register.email' | t }}"
              value="{% if form.email %}{{ form.email }}{% endif %}"
              spellcheck="false"
              autocomplete="off"
              autocapitalize="off">
          </div>

          <div class="field mt-1 relative">
            <label for="CreatePassword" class="input--label">
              {{ 'customer.register.password' | t }} <span class="required">*</span>
            </label>
            <input type="password"
              name="customer[password]"
              id="CreatePassword"
              class="w-full form--input {% if form.errors contains 'password' %}input-error{% endif %}"
              placeholder="{{ 'customer.register.password' | t }}">
              <button type="button" class="toggle-password absolute right-3 top-10 text-xl cursor-pointer"
                onclick="
                  var passwordInput = this.closest('.field').querySelector('input[type=\'password\'], input[type=\'text\']');
                  if (passwordInput) {
                    passwordInput.type = passwordInput.type === 'password' ? 'text' : 'password';
                    passwordInput.focus();
                    this.querySelector('.icon-show').classList.toggle('hidden');
                    this.querySelector('.icon-hide').classList.toggle('hidden');
                  }
                ">
                <span class="icon-show">{% render 'icon' icon: 'eye-secondary' height:20 width:20 %}</span>
                <span class="icon-hide hidden">{% render 'icon' icon: 'eye-linethrough' height:20 width:20 %}</span>
              </button>
          </div>
          <div class="field mt-3 relative">
            <label for="create_an_account_sign_up_agreement">
              <input type="checkbox" id="create_an_account_sign_up_agreement" value="create_an_account_sign_up_agreement">
              <span class="relative">
                {% render 'icon' icon: 'plus' width:16 height:16 strokeWidth:1 %}
              </span> 
              <span class="capitalize text-[12px] cursor-pointer">
                {{ 'customer.register.checkbox' | t }}
              </span>
            </label>
          </div>
          <button type="submit" class="form-btn-submit btn btn--primary w-full my-2 register-submit" type="submit">
            {{ 'customer.register.submit' | t }}
          </button>

        {% endform %}
      </div>
    </div>
  </div>
  {% if settings.create_an_account_image != blank %}
    <div class="relative mt-3 w-full lg:mt-0 lg:w-1/2 lg:h-full h-50v flex justify-center items-center">
      {% render 'lazy-image' with image: settings.create_an_account_image, image_class: "block absolute top-0 left-0 max-w-full w-full h-full object-cover object-center" %}
      {% if settings.login_title != blank or settings.login_title != blank or settings.login_link_title != blank %}
        <div class="absolute w-full h-full z-10 top-0 left-0 bg-black bg-opacity-50"></div>
      {% endif %}
      <div class="relative z-20 p-8 lg:max-w-2xl w-full">
        {% if settings.login_title != blank %}
          <p class="type--secondary mb-2" {% if settings.login_text_color != blank %}style="color: {{ settings.login_text_color }}"{% endif %}>{{ settings.login_title }}</p>
        {% endif %}
        {% if settings.login_subtitle != blank %}
          <p class="type--article mt-0 mb-5" {% if settings.login_text_color != blank %}style="color: {{ settings.login_text_color }}"{% endif %}>{{ settings.login_subtitle }}</p>
        {% endif %}
        {% if settings.login_link_title != blank %}
          <a class="btn bg-primary inline-block" href="{{ settings.login_url }}">{{ settings.login_link_title }}</a>
        {% endif %}
      </div>
    </div>
  {% endif %}
</section> 

<script src="{{ 'customer-forms.js' | asset_url }}" defer="defer"></script>
