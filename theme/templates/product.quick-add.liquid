{% layout none %}

<div class="product-item__variants">
  <div class="option-selection quick-add__form--wrapper">
    {% for variant in product.variants %}
    <label class="quick-add__form--option cursor-pointer swatch--nested" title="{{ variant.option2 }}" onclick="if(this.dataset.available>0){_n.cousin(this,'.product-item','.product-item__add-to-cart').disabled=false}else{_n.cousin(this,'.product-item','.product-item__add-to-cart').disabled=true}" data-available="{{ variant.inventory_quantity }}">   
      <input data-variants="" data-available="{{ variant.inventory_quantity }}" type="radio" name="option2" value="{{ variant.id }}" class="dn hidden">    
      <span class="checkbox-controlled-bold">{{ variant.option2 }}</span> 
    </label>
    {% endfor %}
  </div>
</div>
