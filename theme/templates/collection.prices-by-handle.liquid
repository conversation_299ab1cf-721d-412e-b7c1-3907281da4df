{% layout none %}
{
  "products": {
    {% liquid 
    assign params = content_for_header | split: '?view=prices-by-handle\u0026' | last | split: '"' | first | split: '\u0026'
    assign symbol = localization.country.currency.symbol
    if localization.country.currency.symbol == '$' and localization.country.currency.iso_code != 'USD'
      assign symbol = symbol | prepend: localization.country.currency.iso_code | remove: 'D'
    endif
    if localization.country.currency.symbol == '¥' and localization.country.currency.iso_code != 'JPY'
      assign symbol = symbol | prepend: localization.country.currency.iso_code | remove: 'Y'
    endif
    for segment in params
      if segment contains 'handle'
        assign params = segment | split: '=' | last
        assign handles = params | split: ','
        assign handles_end_index = handles.size  | plus: 1
        break
      endif
    endfor
    for handle in handles 
      assign product = all_products[handle]
      assign price = product.price | money_without_trailing_zeros
      assign compare_at_price = product.compare_at_price | money_without_trailing_zeros
      echo '"' | append: product.handle | append: '": { '
      echo '"price": "' | append: price | replace: localization.country.currency.symbol, symbol | append: '",'
      echo '"compare_at": "' | append: compare_at_price | replace: localization.country.currency.symbol, symbol | append: '"'
      echo '}'
      unless forloop.last 
        echo ',' 
      endunless
    endfor 
    %}
  }
}
