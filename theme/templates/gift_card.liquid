{% layout none %}

<!doctype html>
<html lang="{{ request.locale.iso_code }}">
  <head>
    <script src="{{ 'vendor/qrcode.js' | shopify_asset_url }}" defer></script>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="{{ settings.color_background }}">
    <link rel="canonical" href="{{ canonical_url }}">
    <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>

    {%- if settings.favicon != blank -%}
      <link rel="icon" type="image/png" href="{{ settings.favicon | img_url: '32x32' }}">
    {%- endif -%}

    {%- unless settings.type_header_font.system? -%}
      <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
    {%- endunless -%}

    {%- assign formatted_initial_value = gift_card.initial_value | money_without_trailing_zeros | strip_html -%}

    <title>{{ 'gift_cards.issued.title' | t: value: formatted_initial_value, shop: shop.name }}</title>

    <meta name="description" content="{{ 'gift_cards.issued.subtext' | t }}">

    {{ content_for_header }}

    {%- liquid
      assign body_font_bold = settings.type_body_font | font_modify: 'weight', 'bold'
      assign body_font_italic = settings.type_body_font | font_modify: 'style', 'italic'
      assign body_font_bold_italic = body_font_bold | font_modify: 'style', 'italic'
    %}

    {% style %}
      {{ settings.type_body_font | font_face: font_display: 'swap' }}
      {{ body_font_bold | font_face: font_display: 'swap' }}
      {{ body_font_italic | font_face: font_display: 'swap' }}
      {{ body_font_bold_italic | font_face: font_display: 'swap' }}
      {{ settings.type_header_font | font_face: font_display: 'swap' }}

      :root {
        --font-body-family: {{ settings.type_body_font.family }}, {{ settings.type_body_font.fallback_families }};
        --font-body-style: {{ settings.type_body_font.style }};
        --font-body-weight: {{ settings.type_body_font.weight }};

        --font-heading-family: {{ settings.type_header_font.family }}, {{ settings.type_header_font.fallback_families }};
        --font-heading-style: {{ settings.type_header_font.style }};
        --font-heading-weight: {{ settings.type_header_font.weight }};

        --color-base-text: {{ settings.colors_text.red }}, {{ settings.colors_text.green }}, {{ settings.colors_text.blue }};
        --color-base-background-1: {{ settings.colors_background_1.red }}, {{ settings.colors_background_1.green }}, {{ settings.colors_background_1.blue }};
        --color-base-background-2: {{ settings.colors_background_2.red }}, {{ settings.colors_background_2.green }}, {{ settings.colors_background_2.blue }};
        --color-base-solid-button-labels: {{ settings.colors_solid_button_labels.red }}, {{ settings.colors_solid_button_labels.green }}, {{ settings.colors_solid_button_labels.blue }};
        --color-base-outline-button-labels: {{ settings.colors_outline_button_labels.red }}, {{ settings.colors_outline_button_labels.green }}, {{ settings.colors_outline_button_labels.blue }};
        --color-base-accent-1: {{ settings.colors_accent_1.red }}, {{ settings.colors_accent_1.green }}, {{ settings.colors_accent_1.blue }};
        --color-base-accent-2: {{ settings.colors_accent_2.red }}, {{ settings.colors_accent_2.green }}, {{ settings.colors_accent_2.blue }};

        --page-width: {{ settings.page_width | divided_by: 10 }}rem;
      }
    {% endstyle %}

    {%- unless settings.type_body_font.system? -%}
      <link rel="preload" as="font" href="{{ settings.type_body_font | font_url }}" type="font/woff2" crossorigin>
    {%- endunless -%}
    {%- unless settings.type_header_font.system? -%}
      <link rel="preload" as="font" href="{{ settings.type_header_font | font_url }}" type="font/woff2" crossorigin>
    {%- endunless -%}

    {{ 'template-giftcard.css' | asset_url | stylesheet_tag }}
  </head>

  <body>
    <header class="gift-card__title">
      <span class="h2">{{ shop.name }}</span>
      <h1 class="gift-card__heading">{{ 'gift_cards.issued.subtext' | t }}</h1>
      <div class="gift-card__price">
        <p>
          {% if settings.currency_code_enabled %}
            {{ gift_card.initial_value | money_with_currency }}
          {% else %}
            {{ gift_card.initial_value | money }}
          {% endif %}
        </p>
        {%- if gift_card.enabled == false or gift_card.expired -%}
          <p class="gift-card__label badge badge--{{ settings.sold_out_badge_color_scheme }}">{{ 'gift_cards.issued.expired' | t }}</p>
        {%- endif -%}
      </div>

      {% if settings.currency_code_enabled %}
        {%- assign gift_card_balance = gift_card.balance | money_with_currency -%}
      {% else %}
        {%- assign gift_card_balance = gift_card.balance | money -%}
      {% endif %}
      {%- if gift_card.balance != gift_card.initial_value -%}
        <p class="gift-card__label caption-large">{{ 'gift_cards.issued.remaining_html' | t: balance: gift_card_balance }}</p>
      {%- endif -%}
    </header>

    <main class="gift-card">
      <div class="gift-card__image-wrapper">
        <img src="{{ 'gift-card/card.svg' | shopify_asset_url }}" alt="" class="gift-card__image" height="{{ 570 | divided_by: 1.5 }}" width="570" loading="lazy">
      </div>
      <div class="gift-card__qr-code" data-identifier="{{ gift_card.qr_identifier }}"></div>
      <div class="gift-card__information">
        <input
          type="text"
          class="gift-card__number"
          value="{{ gift_card.code | format_code }}"
          aria-label="{{ 'gift_cards.issued.gift_card_code' | t }}"
          readonly
        >
        <div class="gift-card__copy-code">
          <button class="link gift-card__copy-link">{{ 'gift_cards.issued.copy_code' | t }}</button>
          <span class="gift-card__copy-success form__message" role="status"></span>
          <template>
            {{ 'gift_cards.issued.copy_code_success' | t }}
          </template>
        </div>
        {%- if gift_card.pass_url -%}
          <a href="{{ gift_card.pass_url }}" class="gift_card__apple-wallet">
            <img src="{{ 'gift-card/add-to-apple-wallet.svg' | shopify_asset_url }}" width="120" height="40" alt="{{ 'gift_cards.issued.add_to_apple_wallet' | t }}" loading="lazy">
          </a>
        {%- endif -%}
        <div class="gift-card__buttons no-print">
          <a
            href="{{ shop.url }}"
            class="button"
            target="_blank"
            rel="noopener"
            aria-describedby="a11y-new-window-message"
          >
            {{ 'gift_cards.issued.shop_link' | t }}
          </a>
          <button
            class="button button--secondary"
            onclick="window.print();"
          >
            {{ 'gift_cards.issued.print_gift_card' | t }}
          </button>
        </div>
      </div>
    </main>

    <div hidden>
      <span id="a11y-new-window-message">{{ 'accessibility.link_messages.new_window' | t }}</span>
    </div>
  </body>
</html>

<script>
  var string = { qrImageAlt: {{ 'gift_cards.issued.qr_image_alt' | t | json }} };
  document.addEventListener('DOMContentLoaded', function() {
   new QRCode( document.querySelector('.gift-card__qr-code'), {
    text: document.querySelector('.gift-card__qr-code').dataset.identifier,
    width: 120,
    height: 120,
    imageAltText: string.qrImageAlt
    });
  });

  var template = document.getElementsByTagName("template")[0];
  var clonedTemplate = template.content.cloneNode(true);

  var isMessageDisplayed = false
  document
  .querySelector('.gift-card__copy-link')
  .addEventListener('click', () => {
    navigator.clipboard.writeText(document.querySelector('.gift-card__number').value).then(function () {
      if (!isMessageDisplayed) {
        document.querySelector('.gift-card__copy-success').appendChild(clonedTemplate);
        isMessageDisplayed = true
      }
    });
  });
</script>
