<div class="mb-8 w-full bg-near-white"  neptune-liquid="{
      topic: recently-viewed,
      source:'recentlyViewed'
    }">
  <template>
    <h1 class="h5 text-center border-b-2 border-white m-0 p-1 ">Recently Viewed</h1>
    <div class="collection__tools tooling">
      <div class="collection__tools--count">
        {% raw %}<span>{{products.size}} Products</span>{% endraw %}
      </div>
    </div> 
  {% raw %}
  {% unless products.size == 0 %}
    <div
      id="RecentlyViewed" 
      role="region"
      aria-label="recently-viewed"
      class="recently-viewed-collection-products"
    >
      <div class="collection-grid flex content-start flex-row flex-wrap p-0 m-0">
        {% for product in products %}
        {% endraw %}
        {%- render 'product-item' action:'quick-add-toggle' class:'text-left'-%}
        {% raw %}
        {% endfor %}
      </div>          
    </div>
    {% endunless %}
  {% endraw %}
  </template>
</div> 

<style>
  #shopify-section-recently-viewed {
    display: none !important;
  }
</style>

<script>
  (function(){
    
    let rv = {products:[]}
    try{
      rv = JSON.parse(localStorage.getItem('recentlyViewed'));
    } catch(err) {}
    
    if(!rv)
      rv = {products:[]}

    window.recentlyViewed = rv;

    {% if template contains 'product' %}

      var handle = {{product.handle|json}};
      rv.products = JSON.parse(JSON.stringify(
          rv.products.filter(function(p, i) {return p.handle != handle})
        )
      ).slice(0,10);
      window.recentlyViewed = JSON.parse(JSON.stringify(rv));

      rv.products.unshift({{product|json}})
      localStorage.setItem('recentlyViewed', JSON.stringify(rv))

    {% endif %}

  })()
</script>