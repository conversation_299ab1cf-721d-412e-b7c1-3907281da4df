{"settings_schema": {"colors": {"name": "Colors", "settings": {"colors_solid_button_labels": {"label": "Solid button label", "info": "Used as foreground color on accent colors."}, "colors_accent_1": {"label": "Accent 1", "info": "Used for solid button background."}, "colors_accent_2": {"label": "Accent 2"}, "header__1": {"content": "Primary colors"}, "header__2": {"content": "Secondary colors"}, "colors_text": {"label": "Text", "info": "Used as foreground color on background colors."}, "colors_outline_button_labels": {"label": "Outline button", "info": "Also used for text links."}, "colors_background_1": {"label": "Background 1"}, "colors_background_2": {"label": "Background 2"}}}, "typography": {"name": "Typography", "settings": {"type_header_font": {"label": "Font", "info": "Selecting a different font can affect the speed of your store. [Learn more about system fonts.](https://help.shopify.com/en/manual/online-store/os/store-speed/improving-speed#fonts)"}, "header__1": {"content": "Headings"}, "header__2": {"content": "Body"}, "type_body_font": {"label": "Font", "info": "Selecting a different font can affect the speed of your store. [Learn more about system fonts.](https://help.shopify.com/en/manual/online-store/os/store-speed/improving-speed#fonts)"}}}, "styles": {"name": "Styles", "settings": {"header__1": {"content": "Badges"}, "header__2": {"content": "Decorative elements"}, "sale_badge_color_scheme": {"options__1": {"label": "Background 2"}, "options__2": {"label": "Accent 1"}, "options__3": {"label": "Accent 2"}, "label": "Sale badge color scheme"}, "sold_out_badge_color_scheme": {"options__1": {"label": "Background 1"}, "options__2": {"label": "Inverse"}, "label": "Sold out badge color scheme"}, "accent_icons": {"options__1": {"label": "Accent 1"}, "options__2": {"label": "Accent 2"}, "options__3": {"label": "Outline button"}, "options__4": {"label": "Text"}, "label": "Accent icons"}}}, "social-media": {"name": "Social media", "settings": {"header": {"content": "Social accounts"}, "social_twitter_link": {"label": "Twitter", "info": "https://twitter.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://tiktok.com/@shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "https://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}}}, "search_input": {"name": "Search input", "settings": {"header": {"content": "Product suggestions"}, "predictive_search_enabled": {"label": "Enable product suggestions"}, "predictive_search_show_vendor": {"label": "Show vendor", "info": "Visible when product suggestions enabled."}, "predictive_search_show_price": {"label": "Show price", "info": "Visible when product suggestions enabled."}}}, "favicon": {"name": "Favicon", "settings": {"favicon": {"label": "Favicon image", "info": "Will be scaled down to 32 x 32px"}}}, "currency_format": {"name": "Currency format", "settings": {"content": "Currency codes", "paragraph": "Cart and checkout prices always show currency codes. Example: $1.00 USD.", "currency_code_enabled": {"label": "Show currency codes"}}}, "layout": {"name": "Layout", "settings": {"page_width": {"label": "Maximum width", "options__1": {"label": "1200px"}, "options__2": {"label": "1600px"}}}}}, "sections": {"announcement-bar": {"name": "Announcement bar", "blocks": {"announcement": {"name": "Announcement", "settings": {"text": {"label": "Text"}, "color_scheme": {"label": "Color scheme", "options__1": {"label": "Background 1"}, "options__2": {"label": "Background 2"}, "options__3": {"label": "Inverse"}, "options__4": {"label": "Accent 1"}, "options__5": {"label": "Accent 2"}}, "link": {"label": "Link"}}}}}, "apps": {"name": "Apps", "settings": {"include_margins": {"label": "Make section margins the same as theme"}}, "presets": {"name": "Apps"}}, "collage": {"name": "Collage", "settings": {"heading": {"label": "Heading"}, "desktop_layout": {"label": "Desktop layout", "options__1": {"label": "Left large block"}, "options__2": {"label": "Right large block"}}, "mobile_layout": {"label": "Mobile layout", "options__1": {"label": "Collage"}, "options__2": {"label": "Column"}}}, "blocks": {"image": {"name": "Image", "settings": {"image": {"label": "Image"}, "image_padding": {"label": "Add image padding", "info": "Select image padding if you don't want your image to be cropped."}, "color_scheme": {"options__1": {"label": "Accent 1"}, "options__2": {"label": "Accent 2"}, "options__3": {"label": "Background 1"}, "options__4": {"label": "Background 2"}, "options__5": {"label": "Inverse"}, "label": "Color scheme", "info": "Select image padding to make color visible."}}}, "product": {"name": "Product", "settings": {"product": {"label": "Product"}, "secondary_background": {"label": "Show secondary background"}, "second_image": {"label": "Show second image on hover"}, "image_padding": {"label": "Add image padding", "info": "Select image padding if you don't want your images to be cropped."}}}, "collection": {"name": "Collection", "settings": {"collection": {"label": "Collection"}, "image_padding": {"label": "Add image padding", "info": "Select image padding if you don't want your image to be cropped."}, "color_scheme": {"options__1": {"label": "Accent 1"}, "options__2": {"label": "Accent 2"}, "options__3": {"label": "Background 1"}, "options__4": {"label": "Background 2"}, "options__5": {"label": "Inverse"}, "label": "Color scheme"}}}, "video": {"name": "Video", "settings": {"cover_image": {"label": "Cover image"}, "video_url": {"label": "URL", "info": "Video plays in a pop-up if the section contains other blocks.", "placeholder": "Use a YouTube or Vimeo URL"}, "image_padding": {"label": "Add image padding", "info": "Select image padding if you don't want your cover image to be cropped."}, "description": {"label": "Video alt text", "info": "Describe the video to make it accessible for customers using screen readers."}}}}, "presets": {"name": "Collage"}}, "collection-list": {"name": "Collection list", "settings": {"title": {"label": "Heading"}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "info": "Add images by editing your collections. [Learn more](https://help.shopify.com/en/manual/products/collections)"}, "color_scheme": {"options__1": {"label": "Accent 1"}, "options__2": {"label": "Accent 2"}, "options__3": {"label": "Background 1"}, "options__4": {"label": "Background 2"}, "options__5": {"label": "Inverse"}, "label": "Color scheme"}, "swipe_on_mobile": {"label": "Enable swipe on mobile"}, "image_padding": {"label": "Add image padding"}, "show_view_all": {"label": "Enable \"View all\" button if list includes more collections than shown"}}, "blocks": {"featured_collection": {"name": "Collection", "settings": {"collection": {"label": "Collection"}}}}, "presets": {"name": "Collection list"}}, "contact-form": {"name": "Contact Form", "presets": {"name": "Contact form"}}, "custom-liquid": {"name": "Custom Liquid", "settings": {"custom_liquid": {"label": "Custom Liquid", "info": "Add app snippets or other Liquid code to create advanced customizations."}}, "presets": {"name": "Custom Liquid"}}, "featured-blog": {"name": "Blog posts", "settings": {"heading": {"label": "Heading"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Blog posts"}, "show_view_all": {"label": "Enable \"View all\" button if blog includes more blog posts than shown"}, "show_image": {"label": "Show featured image", "info": "For best results, use an image with a 2:3 aspect ratio. [Learn more](https://help.shopify.com/en/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "soft_background": {"label": "Show secondary background"}, "show_date": {"label": "Show date"}, "show_author": {"label": "Show author"}}, "presets": {"name": "Blog posts"}}, "featured-collection": {"name": "Featured collection", "settings": {"title": {"label": "Heading"}, "collection": {"label": "Collection"}, "products_to_show": {"label": "Maximum products to show"}, "show_view_all": {"label": "Enable \"View all\" button if collection has more products than shown"}, "swipe_on_mobile": {"label": "Enable swipe on mobile"}, "header": {"content": "Product card"}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Show second image on hover"}, "add_image_padding": {"label": "Add image padding"}, "show_image_outline": {"label": "Show image border"}, "show_vendor": {"label": "Show vendor"}, "show_rating": {"label": "Show product rating", "info": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/manual/online-store/themes/os20/themes-by-shopify/dawn/sections#featured-collection-show-product-rating)"}}, "presets": {"name": "Featured collection"}}, "featured-product": {"name": "Featured product", "blocks": {"text": {"name": "Text", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Body"}, "options__2": {"label": "Subtitle"}, "options__3": {"label": "Uppercase"}}}}, "title": {"name": "Title"}, "price": {"name": "Price"}, "quantity_selector": {"name": "Quantity selector"}, "variant_picker": {"name": "Variant picker", "settings": {"picker_type": {"label": "Type", "options__1": {"label": "Dropdown"}, "options__2": {"label": "<PERSON><PERSON>"}}}}, "buy_buttons": {"name": "Buy buttons", "settings": {"show_dynamic_checkout": {"label": "Show dynamic checkout buttons", "info": "Using the payment methods available on your store, customers see their preferred option, like PayPal or Apple Pay. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "description": {"name": "Description"}, "share": {"name": "Share", "settings": {"text": {"label": "Text"}, "featured_image_info": {"content": "If you include a link in social media posts, the page’s featured image will be shown as the preview image. [Learn more](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "A store title and description are included with the preview image. [Learn more](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}}}, "custom_liquid": {"name": "Custom liquid", "settings": {"custom_liquid": {"label": "Custom liquid"}}}, "rating": {"name": "Product rating", "settings": {"paragraph": {"content": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/manual/online-store/themes/os20/themes-by-shopify/dawn/sections#featured-product-rating)"}}}}, "settings": {"product": {"label": "Product"}, "secondary_background": {"label": "Show secondary background"}, "header": {"content": "Media", "info": "Learn more about [media types](https://help.shopify.com/manual/products/product-media)"}, "hide_variants": {"label": "Hide unselected variants’ media on desktop"}, "enable_video_looping": {"label": "Enable video looping"}}, "presets": {"name": "Featured product"}}, "footer": {"name": "Footer", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "Heading", "info": "Heading required to display the menu."}, "menu": {"label": "<PERSON><PERSON>", "info": "Displays only top-level menu items."}}}, "text": {"name": "Text", "settings": {"heading": {"label": "Heading"}, "subtext": {"label": "Subtext"}}}}, "settings": {"color_scheme": {"options__1": {"label": "Accent 1"}, "options__2": {"label": "Accent 2"}, "options__3": {"label": "Background 1"}, "options__4": {"label": "Background 2"}, "options__5": {"label": "Inverse"}, "label": "Color scheme"}, "newsletter_enable": {"label": "Show email signup"}, "newsletter_heading": {"label": "Heading"}, "header__1": {"content": "<PERSON>ail Signup", "info": "Subscribers added automatically to your “accepted marketing” customer list. [Learn more](https://help.shopify.com/en/manual/customers/manage-customers)"}, "header__2": {"content": "Social media icons", "info": "To display your social media accounts, link them in your theme settings."}, "show_social": {"label": "Show social media icons"}, "header__3": {"content": "Country/region selector"}, "header__4": {"info": "To add a country/region, go to your [payment settings.](/admin/settings/payments)"}, "enable_country_selector": {"label": "Enable country/region selector"}, "header__5": {"content": "Language selector"}, "header__6": {"info": "To add a language, go to your [language settings.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Enable language selector"}, "header__7": {"content": "Payment methods"}, "payment_enable": {"label": "Show payment icons"}}}, "header": {"name": "Header", "settings": {"logo": {"label": "Logo image"}, "logo_width": {"unit": "px", "label": "Custom logo width"}, "logo_position": {"label": "Logo position on large screens", "options__1": {"label": "Middle left"}, "options__2": {"label": "Top left"}, "options__3": {"label": "Top center"}}, "menu": {"label": "<PERSON><PERSON>"}, "show_line_separator": {"label": "Show separator line"}, "enable_sticky_header": {"label": "Enable sticky header", "info": "Header shows on the screen as customers scroll up."}}}, "image-banner": {"name": "Image banner", "settings": {"image": {"label": "First image"}, "image_2": {"label": "Second image"}, "desktop_text_box_position": {"options__1": {"label": "Top"}, "options__2": {"label": "Center"}, "options__3": {"label": "Bottom"}, "label": "Desktop text position"}, "show_text_box": {"label": "Show text box on desktop"}, "image_overlay_opacity": {"label": "Image overlay opacity"}, "color_scheme": {"options__1": {"label": "Accent 1"}, "options__2": {"label": "Accent 2"}, "options__3": {"label": "Background 1"}, "options__4": {"label": "Background 2"}, "options__5": {"label": "Inverse"}, "label": "Color scheme", "info": "Visible when text box displayed"}, "header": {"content": "Mobile Layout"}, "stack_images_on_mobile": {"label": "Stack images on mobile"}, "show_text_below": {"label": "Show text below images"}, "adapt_height_first_image": {"label": "Adapt section height to first image size"}}, "blocks": {"heading": {"name": "Heading", "settings": {"heading": {"label": "Heading"}, "heading_size": {"options__1": {"label": "Medium"}, "options__2": {"label": "Large"}, "label": "Heading font size"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Description"}}}, "buttons": {"name": "Buttons", "settings": {"button_label_1": {"label": "First button label", "info": "Leave the label blank to hide the button."}, "button_link_1": {"label": "First button link"}, "button_style_secondary_1": {"label": "Use outline button style"}, "button_label_2": {"label": "Second button label", "info": "Leave the label blank to hide the button."}, "button_link_2": {"label": "Second button link"}, "button_style_secondary_2": {"label": "Use outline button style"}}}}, "presets": {"name": "Image banner"}}, "image-with-text": {"name": "Image with text", "settings": {"image": {"label": "Image"}, "height": {"options__1": {"label": "Adapt to image"}, "options__2": {"label": "Small"}, "options__3": {"label": "Large"}, "label": "Image ratio"}, "color_scheme": {"options__1": {"label": "Background 1"}, "options__2": {"label": "Background 2"}, "options__3": {"label": "Inverse"}, "options__4": {"label": "Accent 1"}, "options__5": {"label": "Accent 2"}, "label": "Color scheme"}, "layout": {"options__1": {"label": "Image first"}, "options__2": {"label": "Text first"}, "label": "Desktop layout", "info": "Image first is the default mobile layout."}}, "blocks": {"heading": {"name": "Heading", "settings": {"heading": {"label": "Heading"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Description"}}}, "button": {"name": "<PERSON><PERSON>", "settings": {"button_label": {"label": "Button label", "info": "Leave the label blank to hide the button."}, "button_link": {"label": "Button link"}}}}, "presets": {"name": "Image with text"}}, "main-article": {"name": "Blog post", "blocks": {"featured_image": {"name": "Featured image", "settings": {"image_height": {"label": "Featured image height", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}, "info": "For best results, use an image with a 16:9 aspect ratio. [Learn more](https://help.shopify.com/en/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "title": {"name": "Title", "settings": {"blog_show_date": {"label": "Show date"}, "blog_show_author": {"label": "Show author"}}}, "content": {"name": "Content"}, "share": {"name": "Share", "settings": {"text": {"label": "Text"}, "featured_image_info": {"content": "If you include a link in social media posts, the page’s featured image will be shown as the preview image. [Learn more](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "A store title and description are included with the preview image. [Learn more](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}}}}}, "main-blog": {"name": "Blog posts", "settings": {"header": {"content": "Blog post card"}, "show_image": {"label": "Show featured image", "info": "For best results, use an image with a 2:3 aspect ratio. [Learn more](https://help.shopify.com/en/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "show_date": {"label": "Show date"}, "show_author": {"label": "Show author"}, "paragraph": {"content": "Change excerpts by editing your blog posts. [Learn more](https://help.shopify.com/en/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"}}}, "main-cart-footer": {"name": "Subtotal", "settings": {"show_cart_note": {"label": "Enable cart note"}}, "blocks": {"subtotal": {"name": "Subtotal price"}, "buttons": {"name": "Checkout button"}}}, "main-cart-items": {"name": "Items", "settings": {"show_vendor": {"label": "Show vendor"}}}, "main-collection-banner": {"name": "Collection banner", "settings": {"paragraph": {"content": "Add a description or image by editing your collection. [Learn more](https://help.shopify.com/en/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Show collection description"}, "show_collection_image": {"label": "Show collection image", "info": "For best results, use an image with a 16:9 aspect ratio. [Learn more](https://help.shopify.com/en/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-collection-product-grid": {"name": "Product grid", "settings": {"products_per_page": {"label": "Products per page"}, "enable_filtering": {"label": "Enable filtering", "info": "Customize [filters](/admin/menus)"}, "enable_sorting": {"label": "Enable sorting"}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Show second image on hover"}, "add_image_padding": {"label": "Add image padding"}, "show_image_outline": {"label": "Show image border"}, "show_vendor": {"label": "Show vendor"}, "show_rating": {"label": "Show product rating", "info": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/manual/online-store/themes/os20/themes-by-shopify/dawn/page-types#product-grid-show-product-rating)"}, "header__1": {"content": "Filtering and sorting"}, "header__3": {"content": "Product card"}, "enable_tags": {"label": "Enable filtering", "info": "[Customize filters](/admin/menus)"}, "collapse_on_larger_devices": {"label": "Collapse on larger screens"}}}, "main-list-collections": {"name": "Collections list page", "settings": {"title": {"label": "Heading"}, "sort": {"label": "Sort collections by:", "options__1": {"label": "Alphabetically, A-Z"}, "options__2": {"label": "Alphabetically, Z-A"}, "options__3": {"label": "Date, new to old"}, "options__4": {"label": "Date, old to new"}, "options__5": {"label": "Product count, high to low"}, "options__6": {"label": "Product count, low to high"}}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "info": "Add images by editing your collections. [Learn more](https://help.shopify.com/en/manual/products/collections)"}, "color_scheme": {"options__1": {"label": "Accent 1"}, "options__2": {"label": "Accent 2"}, "options__3": {"label": "Background 1"}, "options__4": {"label": "Background 2"}, "options__5": {"label": "Inverse"}, "label": "Color scheme"}, "image_padding": {"label": "Add image padding"}}}, "main-page": {"name": "Page"}, "main-password-footer": {"name": "Password footer", "settings": {"color_scheme": {"options__1": {"label": "Accent 1"}, "options__2": {"label": "Accent 2"}, "options__3": {"label": "Background 1"}, "options__4": {"label": "Background 2"}, "options__5": {"label": "Inverse"}, "label": "Color scheme"}}}, "main-password-header": {"name": "Password header", "settings": {"logo": {"label": "Logo image"}, "logo_max_width": {"label": "Custom logo width", "unit": "px"}, "color_scheme": {"options__1": {"label": "Accent 1"}, "options__2": {"label": "Accent 2"}, "options__3": {"label": "Background 1"}, "options__4": {"label": "Background 2"}, "options__5": {"label": "Inverse"}, "label": "Color scheme"}}}, "main-product": {"name": "Product information", "blocks": {"text": {"name": "Text", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Body"}, "options__2": {"label": "Subtitle"}, "options__3": {"label": "Uppercase"}}}}, "title": {"name": "Title"}, "price": {"name": "Price"}, "quantity_selector": {"name": "Quantity selector"}, "variant_picker": {"name": "Variant picker", "settings": {"picker_type": {"label": "Type", "options__1": {"label": "Dropdown"}, "options__2": {"label": "<PERSON><PERSON>"}}}}, "buy_buttons": {"name": "Buy buttons", "settings": {"show_dynamic_checkout": {"label": "Show dynamic checkout buttons", "info": "Using the payment methods available on your store, customers see their preferred option, like PayPal or Apple Pay. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "pickup_availability": {"name": "Pickup availability"}, "description": {"name": "Description"}, "share": {"name": "Share", "settings": {"text": {"label": "Text"}, "featured_image_info": {"content": "If you include a link in social media posts, the page’s featured image will be shown as the preview image. [Learn more](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "A store title and description are included with the preview image. [Learn more](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}}}, "custom_liquid": {"name": "Custom liquid", "settings": {"custom_liquid": {"label": "Custom liquid", "info": "Add app snippets or other Liquid code to create advanced customizations."}}}, "collapsible_tab": {"name": "Collapsible tab", "settings": {"heading": {"info": "Include a heading that explains the content.", "label": "Heading"}, "content": {"label": "Tab content"}, "page": {"label": "Tab content from page"}, "icon": {"label": "Icon", "options__1": {"label": "None"}, "options__2": {"label": "Box"}, "options__3": {"label": "Chat bubble"}, "options__4": {"label": "Check mark"}, "options__5": {"label": "Dryer"}, "options__6": {"label": "Eye"}, "options__7": {"label": "Heart"}, "options__8": {"label": "Iron"}, "options__9": {"label": "Leaf"}, "options__10": {"label": "Leather"}, "options__11": {"label": "Lock"}, "options__12": {"label": "Map pin"}, "options__13": {"label": "<PERSON>ts"}, "options__14": {"label": "Plane"}, "options__15": {"label": "Price tag"}, "options__16": {"label": "Question mark"}, "options__17": {"label": "Return"}, "options__18": {"label": "Ruler"}, "options__19": {"label": "Shirt"}, "options__20": {"label": "Shoe"}, "options__21": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__22": {"label": "Star"}, "options__23": {"label": "Truck"}, "options__24": {"label": "Washing"}}}}, "popup": {"name": "Pop-up", "settings": {"link_label": {"label": "Link label"}, "page": {"label": "Page"}}}, "rating": {"name": "Product rating", "settings": {"paragraph": {"content": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/manual/online-store/themes/os20/themes-by-shopify/dawn/page-types#product-rating-block)"}}}}, "settings": {"header": {"content": "Media", "info": "Learn more about [media types.](https://help.shopify.com/manual/products/product-media)"}, "enable_sticky_info": {"label": "Enable sticky product information on large screens"}, "hide_variants": {"label": "Hide other variants’ media after selecting a variant"}, "enable_video_looping": {"label": "Enable video looping"}}}, "main-search": {"name": "Search results", "settings": {"image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Show second image on hover"}, "add_image_padding": {"label": "Add image padding"}, "show_image_outline": {"label": "Show image border"}, "show_vendor": {"label": "Show vendor"}, "show_rating": {"label": "Show product rating", "info": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/manual/online-store/themes/os20/themes-by-shopify/dawn/page-types#search-results-show-product-rating)"}, "header__1": {"content": "Product card"}, "header__2": {"content": "Blog card"}, "article_show_date": {"label": "Show date"}, "article_show_author": {"label": "Show author"}}}, "multicolumn": {"name": "Multicolumn", "settings": {"title": {"label": "Heading"}, "image_width": {"label": "Image width", "options__1": {"label": "One-third width of column"}, "options__2": {"label": "Half width of column"}, "options__3": {"label": "Full width of column"}}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "options__4": {"label": "Circle"}}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "background_style": {"label": "Secondary background", "options__1": {"label": "None"}, "options__2": {"label": "Show as column background"}, "options__3": {"label": "Show as section background"}}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "swipe_on_mobile": {"label": "Enable swipe on mobile"}}, "blocks": {"column": {"name": "Column", "settings": {"image": {"label": "Image"}, "title": {"label": "Heading"}, "text": {"label": "Description"}}}}, "presets": {"name": "Multicolumn"}}, "newsletter": {"name": "Email signup", "settings": {"color_scheme": {"label": "Color scheme", "options__1": {"label": "Accent 1"}, "options__2": {"label": "Accent 2"}, "options__3": {"label": "Background 1"}, "options__4": {"label": "Background 2"}, "options__5": {"label": "Inverse"}}, "full_width": {"label": "Make section full width"}, "paragraph": {"content": "Each email subscription creates a customer account. [Learn more](https://help.shopify.com/en/manual/customers)"}}, "blocks": {"heading": {"name": "Heading", "settings": {"heading": {"label": "Heading"}}}, "paragraph": {"name": "Subheading", "settings": {"paragraph": {"label": "Description"}}}, "email_form": {"name": "Email form"}}, "presets": {"name": "Email signup"}}, "page": {"name": "Page", "settings": {"page": {"label": "Page"}}, "presets": {"name": "Page"}}, "product-recommendations": {"name": "Product recommendations", "settings": {"heading": {"label": "Heading"}, "paragraph__1": {"content": "Dynamic recommendations use order and product information to change and improve over time. [Learn more](https://help.shopify.com/en/themes/development/recommended-products)"}, "header__2": {"content": "Product card"}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Show second image on hover"}, "add_image_padding": {"label": "Add image padding"}, "show_image_outline": {"label": "Show image border"}, "show_vendor": {"label": "Show vendor"}, "show_rating": {"label": "Show product rating", "info": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/manual/online-store/themes/os20/themes-by-shopify/dawn/page-types#product-recommendations-show-product-rating)"}}}, "rich-text": {"name": "Rich text", "settings": {"color_scheme": {"options__1": {"label": "Accent 1"}, "options__2": {"label": "Accent 2"}, "options__3": {"label": "Background 1"}, "options__4": {"label": "Background 2"}, "options__5": {"label": "Inverse"}, "label": "Color scheme"}, "full_width": {"label": "Make section full width"}}, "blocks": {"heading": {"name": "Heading", "settings": {"heading": {"label": "Heading"}, "heading_size": {"options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}, "label": "Heading font size"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Description"}}}, "button": {"name": "<PERSON><PERSON>", "settings": {"button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "button_style_secondary": {"label": "Use outline button style"}}}}, "presets": {"name": "Rich text"}}, "video": {"name": "Video", "settings": {"heading": {"label": "Heading"}, "cover_image": {"label": "Cover image"}, "video_url": {"label": "URL", "placeholder": "Use a YouTube or Vimeo URL", "info": "Video plays in the page."}, "description": {"label": "Video alt text", "info": "Describe the video to make it accessible for customers using screen readers."}, "image_padding": {"label": "Add image padding", "info": "Select image padding if you don't want your cover image to be cropped."}, "full_width": {"label": "Make section full width"}}, "presets": {"name": "Video"}}, "tabbed-section": {"name": "Tabbed Content", "presets": {"name": "Tab Content"}, "blocks": {"text-content": {"name": "Text Content", "settings": {"button-label": {"label": "Button Label"}, "pane-content": {"label": "Pane Content"}}}, "liquid-content": {"name": "Liquid Content", "settings": {"button-label": {"label": "Button Label"}, "pane-content": {"label": "Pane Content"}}}}}}}