{"settings_schema": {"colors": {"name": "Ren<PERSON>r", "settings": {"colors_solid_button_labels": {"label": "<PERSON><PERSON> dü<PERSON> et<PERSON>", "info": "Vurgu renklerinde ön plan rengi olarak kullanılır."}, "colors_accent_1": {"label": "1. <PERSON><PERSON><PERSON>", "info": "Sabit düğme arka planı için kullanılır."}, "colors_accent_2": {"label": "2. <PERSON><PERSON><PERSON>"}, "header__1": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "header__2": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "colors_text": {"label": "<PERSON><PERSON>", "info": "Arka plan renklerinde ön plan rengi olarak kullanılır."}, "colors_outline_button_labels": {"label": "<PERSON><PERSON>ş çizgi düğmes<PERSON>", "info": "<PERSON><PERSON> b<PERSON>ğlantıları için de kullanılır."}, "colors_background_1": {"label": "1. Arka plan"}, "colors_background_2": {"label": "2. Arka plan"}}}, "typography": {"name": "Tipografi", "settings": {"type_header_font": {"label": "Yazı tipi", "info": "Farklı bir yazı tipi seçmeniz mağazanızın hızını etkileyebilir. [Sistem yazı tipleri hakkında daha fazla bilgi edinin.](https://help.shopify.com/en/manual/online-store/os/store-speed/improving-speed#fonts)"}, "header__1": {"content": "Başlıklar"}, "header__2": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "type_body_font": {"label": "Yazı tipi", "info": "Farklı bir yazı tipi seçmeniz mağazanızın hızını etkileyebilir. [Sistem yazı tipleri hakkında daha fazla bilgi edinin.](https://help.shopify.com/en/manual/online-store/os/store-speed/improving-speed#fonts)"}}}, "styles": {"name": "Stiller", "settings": {"sold_out_badge_color_scheme": {"options__1": {"label": "1. Arka plan"}, "options__2": {"label": "<PERSON><PERSON>"}, "label": "Tükendi rozeti renk şeması"}, "header__1": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "header__2": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "sale_badge_color_scheme": {"options__1": {"label": "2. Arka plan"}, "options__2": {"label": "1. <PERSON><PERSON><PERSON>"}, "options__3": {"label": "2. <PERSON><PERSON><PERSON>"}, "label": "İndirim rozeti renk şeması"}, "accent_icons": {"options__1": {"label": "1. <PERSON><PERSON><PERSON>"}, "options__2": {"label": "2. <PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON>ş çizgi düğmes<PERSON>"}, "options__4": {"label": "<PERSON><PERSON>"}, "label": "<PERSON><PERSON><PERSON> sim<PERSON>"}}}, "social-media": {"name": "So<PERSON>al medya", "settings": {"share_facebook": {"label": "Facebook'ta paylaş"}, "share_twitter": {"label": "Twitter'da tweet'le"}, "share_pinterest": {"label": "Pinterest'te pin ekle"}, "header__1": {"content": "Sosyal medya <PERSON><PERSON>"}, "header__2": {"content": "So<PERSON>al medya he<PERSON>ı"}, "social_twitter_link": {"label": "Twitter", "info": "https://twitter.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://tiktok.com/@shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "https://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}, "header": {"content": "So<PERSON>al medya he<PERSON>ı"}}}, "currency_format": {"name": "Para birimi biçimi", "settings": {"content": "Para birimi kodları", "currency_code_enabled": {"label": "Para birimi kodlarını göster"}, "paragraph": "Sepet ve ödeme ücretleri her zaman para birimi kodlarını gösterir. Örnek: 1,00 USD."}}, "favicon": {"name": "Favicon", "settings": {"favicon": {"label": "<PERSON><PERSON><PERSON>", "info": "Ölçeği 32 x 32 piksele düşürülür"}}}, "layout": {"name": "D<PERSON>zen", "settings": {"page_width": {"label": "<PERSON><PERSON><PERSON><PERSON> genişlik", "options__1": {"label": "1200 piksel"}, "options__2": {"label": "1600 piksel"}}}}, "search_input": {"name": "<PERSON><PERSON><PERSON> ara", "settings": {"header": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "predictive_search_enabled": {"label": "<PERSON><PERSON><PERSON><PERSON>kinleştir"}, "predictive_search_show_vendor": {"label": "Satıcıyı göster", "info": "Ürün önerileri etkinleştirildiğinde görünür."}, "predictive_search_show_price": {"label": "Fiyatı göster", "info": "Ürün önerileri etkinleştirildiğinde görünür."}}}}, "sections": {"announcement-bar": {"name": "<PERSON><PERSON><PERSON>", "blocks": {"announcement": {"name": "<PERSON><PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>"}, "color_scheme": {"label": "Renk şeması", "options__1": {"label": "1. Arka plan"}, "options__2": {"label": "2. Arka plan"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "1. <PERSON><PERSON><PERSON>"}, "options__5": {"label": "2. <PERSON><PERSON><PERSON>"}}, "link": {"label": "Bağlantı"}}}}}, "collage": {"name": "<PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "Başlık"}, "desktop_layout": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options__1": {"label": "Sol geniş blok"}, "options__2": {"label": "Sağ geniş blok"}}, "mobile_layout": {"label": "<PERSON><PERSON>", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}}, "blocks": {"image": {"name": "Image", "settings": {"image": {"label": "G<PERSON><PERSON><PERSON>"}, "image_padding": {"label": "<PERSON><PERSON><PERSON><PERSON> do<PERSON><PERSON> e<PERSON>", "info": "Görselinizin kırpılmasını istemiyorsanız görsel dolgusunu seçin."}, "color_scheme": {"options__1": {"label": "1. <PERSON><PERSON><PERSON>"}, "options__2": {"label": "2. <PERSON><PERSON><PERSON>"}, "options__3": {"label": "1. Arka plan"}, "options__4": {"label": "2. Arka plan"}, "options__5": {"label": "<PERSON><PERSON>"}, "label": "Renk şeması", "info": "<PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON> ya<PERSON> i<PERSON>in görsel dol<PERSON><PERSON>u seçin."}}}, "product": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"product": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "secondary_background": {"label": "İkincil arka planı göster"}, "second_image": {"label": "Üstüne gelindiğinde ikinci görseli göster"}, "image_padding": {"label": "<PERSON><PERSON><PERSON><PERSON> do<PERSON><PERSON> e<PERSON>", "info": "Görsellerinizin kırpılmasını istemiyorsanız görsel dolgusunu seçin."}}}, "collection": {"name": "Koleksiyon", "settings": {"collection": {"label": "Koleksiyon"}, "image_padding": {"label": "<PERSON><PERSON><PERSON><PERSON> do<PERSON><PERSON> e<PERSON>", "info": "Görselinizin kırpılmasını istemiyorsanız görsel dolgusunu seçin."}, "color_scheme": {"options__1": {"label": "1. <PERSON><PERSON><PERSON>"}, "options__2": {"label": "2. <PERSON><PERSON><PERSON>"}, "options__3": {"label": "1. Arka plan"}, "options__4": {"label": "2. Arka plan"}, "options__5": {"label": "<PERSON><PERSON>"}, "label": "Renk şeması"}}}, "video": {"name": "Video", "settings": {"cover_image": {"label": "Kapak gö<PERSON>"}, "video_url": {"label": "URL", "info": "Bölümde başka bloklar varsa video açılır pencerede oynatılır.", "placeholder": "YouTube veya Vimeo URL'si kullanın"}, "image_padding": {"label": "<PERSON><PERSON><PERSON><PERSON> do<PERSON><PERSON> e<PERSON>", "info": "Kapak görselinizin kırpılmasını istemiyorsanız görsel dolgusunu seçin."}, "description": {"label": "Video alternatif metni", "info": "Ekran okuyucu kullanan müşterilerin daha kolay erişebilmesini sağlamak için videoyu açıklayın."}}}}, "presets": {"name": "<PERSON><PERSON><PERSON>"}}, "collection-list": {"name": "Koleks<PERSON><PERSON>esi", "settings": {"title": {"label": "Başlık"}, "image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Portre"}, "options__3": {"label": "<PERSON><PERSON>"}, "info": "Koleksiyonlarınızı düzenleyerek görsel ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/en/manual/products/collections)"}, "color_scheme": {"options__1": {"label": "1. <PERSON><PERSON><PERSON>"}, "options__2": {"label": "2. <PERSON><PERSON><PERSON>"}, "options__3": {"label": "1. Arka plan"}, "options__4": {"label": "2. Arka plan"}, "options__5": {"label": "<PERSON><PERSON>"}, "label": "Renk şeması"}, "swipe_on_mobile": {"label": "Mobil cihazda kaydırmayı etkinleştir"}, "image_padding": {"label": "<PERSON><PERSON><PERSON><PERSON> do<PERSON><PERSON> e<PERSON>"}, "show_view_all": {"label": "Liste gösterilenden daha fazla koleksiyon içeriyorsa \"Tümünü gö<PERSON><PERSON><PERSON><PERSON><PERSON>\" <PERSON><PERSON><PERSON><PERSON><PERSON> etkinleştir"}}, "blocks": {"featured_collection": {"name": "Koleksiyon", "settings": {"collection": {"label": "Koleksiyon"}}}}, "presets": {"name": "Koleks<PERSON><PERSON>esi"}}, "contact-form": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "presets": {"name": "İletişim formu"}}, "custom-liquid": {"name": "Özel Liquid", "settings": {"custom_liquid": {"label": "Özel Liquid", "info": "Gelişmiş özelleştirmeler oluşturmak için uygulama parçacıkları veya başka Liquid kodu ekleyin."}}, "presets": {"name": "Özel Liquid"}}, "featured-blog": {"name": "Blog gönderileri", "settings": {"heading": {"label": "Başlık"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Blog gönderileri"}, "show_view_all": {"label": "Blog gösterilenden daha fazla blog gönderisi içeriyorsa \"Tümünü görüntüle\" düğ<PERSON>ini etkinleştir"}, "show_image": {"label": "<PERSON>ne çıkan görseli göster", "info": "En iyi sonuçlar için 2:3 en-boy oran<PERSON>na sahip bir gö<PERSON>l kullanın. [<PERSON>ha fazla bilgi edinin](https://help.shopify.com/en/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "soft_background": {"label": "İkincil arka planı göster"}, "show_date": {"label": "<PERSON><PERSON><PERSON>"}, "show_author": {"label": "Yazarı göster"}}, "blocks": {"title": {"name": "Başlık", "settings": {"show_date": {"label": "<PERSON><PERSON><PERSON>"}, "show_author": {"label": "Yazarı göster"}}}, "summary": {"name": "Alıntı"}, "link": {"name": "Bağlantı"}}, "presets": {"name": "Blog gönderileri"}}, "featured-collection": {"name": "Öne çıkan koleksiyon", "settings": {"title": {"label": "Başlık"}, "collection": {"label": "Koleksiyon"}, "products_to_show": {"label": "Gösterilecek maksimum ürün sayısı"}, "show_view_all": {"label": "Koleksiyon gösterilenden daha fazla ürün içeriyorsa \"Tümünü gör<PERSON><PERSON><PERSON>le\" <PERSON><PERSON><PERSON><PERSON><PERSON> etkinleştir"}, "swipe_on_mobile": {"label": "Mobil cihazda kaydırmayı etkinleştir"}, "header": {"content": "Ürün kartı"}, "image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Portre"}, "options__3": {"label": "<PERSON><PERSON>"}}, "show_secondary_image": {"label": "Üstüne gelindiğinde ikinci görseli göster"}, "add_image_padding": {"label": "<PERSON><PERSON><PERSON><PERSON> do<PERSON><PERSON> e<PERSON>"}, "show_vendor": {"label": "Satıcıyı göster"}, "show_image_outline": {"label": "Görsel kenarlığını göster"}}, "presets": {"name": "Öne çıkan koleksiyon"}}, "footer": {"name": "Altbilgi", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "Başlık", "info": "Menünün gösterilmesi için başlık gereklidir."}, "menu": {"label": "<PERSON><PERSON>", "info": "Yalnızca üst taraftaki menü öğeleri gösterilir."}}}, "text": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "Başlık"}, "subtext": {"label": "Alt metin"}}}}, "settings": {"color_scheme": {"options__1": {"label": "1. <PERSON><PERSON><PERSON>"}, "options__2": {"label": "2. <PERSON><PERSON><PERSON>"}, "options__3": {"label": "1. Arka plan"}, "options__4": {"label": "2. Arka plan"}, "options__5": {"label": "<PERSON><PERSON>"}, "label": "Renk şeması"}, "newsletter_enable": {"label": "E-posta kaydını göster"}, "newsletter_heading": {"label": "Başlık"}, "header__1": {"content": "E-posta Kaydı", "info": "\"Kabul edilen pazar<PERSON>a\" mü<PERSON><PERSON><PERSON> listenize otomatik olarak eklenen aboneler. [Daha fazla bilgi edinin](https://help.shopify.com/en/manual/customers/manage-customers)"}, "header__2": {"content": "So<PERSON><PERSON> medya sim<PERSON>eri", "info": "Sosyal medya hesaplarınızı göstermek için tema ayarlarınızda bu hesapları bağlayın."}, "show_social": {"label": "Sosyal medya simgeleri<PERSON>"}, "header__3": {"content": "<PERSON><PERSON><PERSON>/bö<PERSON> se<PERSON>"}, "header__4": {"info": "<PERSON><PERSON><PERSON>/bölge e<PERSON> [ödeme a<PERSON>larınıza ](/admin/settings/payments) gidin."}, "enable_country_selector": {"label": "<PERSON><PERSON><PERSON>/bö<PERSON>kinleştir"}, "header__5": {"content": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "header__6": {"info": "Dil eklemek için [dil ayarların<PERSON>za](/admin/settings/languages) gidin."}, "enable_language_selector": {"label": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> etkin<PERSON>ştir"}, "header__7": {"content": "<PERSON><PERSON><PERSON>"}, "payment_enable": {"label": "<PERSON>deme simgeleri<PERSON>"}}}, "header": {"name": "Üstbilgi", "settings": {"logo": {"label": "Lo<PERSON>"}, "logo_width": {"unit": "piksel", "label": "Özel logo genişliği"}, "logo_position": {"label": "Geniş ekranlardaki logo konumu", "options__1": {"label": "Orta sol"}, "options__2": {"label": "Üst sol"}, "options__3": {"label": "Üst orta"}}, "menu": {"label": "<PERSON><PERSON>"}, "show_line_separator": {"label": "Ayırıcı satırı göster"}, "enable_sticky_header": {"label": "Yapışkanlı üstbilgiyi etkinleştir", "info": "Müşteriler yukarı doğru kaydırırken üstbilgi ekranda görünür."}}}, "image-banner": {"name": "Görsel banner'ı", "settings": {"image": {"label": "İlk görsel"}, "image_2": {"label": "<PERSON><PERSON><PERSON>"}, "desktop_text_box_position": {"options__1": {"label": "Üst"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Alt"}, "label": "<PERSON><PERSON><PERSON><PERSON><PERSON> metin konumu"}, "color_scheme": {"options__1": {"label": "1. <PERSON><PERSON><PERSON>"}, "options__2": {"label": "2. <PERSON><PERSON><PERSON>"}, "options__3": {"label": "1. Arka plan"}, "options__4": {"label": "2. Arka plan"}, "options__5": {"label": "<PERSON><PERSON>"}, "label": "Renk şeması", "info": "<PERSON><PERSON> kutusu gösterildiğinde görünür"}, "stack_images_on_mobile": {"label": "Mobilde görselleri üst üste ekle"}, "adapt_height_first_image": {"label": "Bölüm yüksekliğini ilk görselin boyutuna uyarla"}, "show_text_box": {"label": "Masaüstünde metin kutusunu g<PERSON>"}, "image_overlay_opacity": {"label": "Görsel yer paylaşımı opaklığı"}, "header": {"content": "<PERSON><PERSON>"}, "show_text_below": {"label": "Metni görsellerin altında göster"}}, "blocks": {"heading": {"name": "Başlık", "settings": {"heading": {"label": "Başlık"}, "heading_size": {"options__1": {"label": "Orta"}, "options__2": {"label": "Büyük"}, "label": "Başlık yazı tipi boyutu"}}}, "text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>ı<PERSON><PERSON>"}}}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"button_label_1": {"label": "<PERSON><PERSON> düğme et<PERSON>", "info": "Düğmeyi gizlemek için etiketi boş bırakın."}, "button_link_1": {"label": "İlk düğme bağlantısı"}, "button_style_secondary_1": {"label": "<PERSON><PERSON><PERSON> ç<PERSON> düğ<PERSON> stil<PERSON> kullan"}, "button_label_2": {"label": "<PERSON><PERSON><PERSON> düğ<PERSON>", "info": "Düğmeyi gizlemek için etiketi boş bırakın."}, "button_link_2": {"label": "İkinci düğme bağlantısı"}, "button_style_secondary_2": {"label": "<PERSON><PERSON><PERSON> ç<PERSON> düğ<PERSON> stil<PERSON> kullan"}}}}, "presets": {"name": "Görsel banner'ı"}}, "image-with-text": {"name": "<PERSON><PERSON>", "settings": {"image": {"label": "G<PERSON><PERSON><PERSON>"}, "height": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Küçük"}, "options__3": {"label": "Büyük"}, "label": "Görsel oranı"}, "color_scheme": {"options__1": {"label": "1. Arka plan"}, "options__2": {"label": "2. Arka plan"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "1. <PERSON><PERSON><PERSON>"}, "options__5": {"label": "2. <PERSON><PERSON><PERSON>"}, "label": "Renk şeması"}, "layout": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON> metin"}, "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON>, varsayılan mobil düzendir."}}, "blocks": {"heading": {"name": "Başlık", "settings": {"heading": {"label": "Başlık"}}}, "text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>ı<PERSON><PERSON>"}}}, "button": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"button_label": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Düğmeyi gizlemek için etiketi boş bırakın."}, "button_link": {"label": "<PERSON><PERSON><PERSON><PERSON> bağlantısı"}}}}, "presets": {"name": "<PERSON><PERSON>"}}, "main-article": {"name": "Blog gönderisi", "blocks": {"featured_image": {"name": "<PERSON>ne <PERSON>", "settings": {"image_height": {"label": "Öne çıkan görsel yüksekliği", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Büyük"}, "info": "En iyi sonuçlar için 16:9 en-boy oran<PERSON>na sahip bir gö<PERSON>l kullanın. [<PERSON>ha fazla bilgi edinin](https://help.shopify.com/en/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "title": {"name": "Başlık", "settings": {"blog_show_date": {"label": "<PERSON><PERSON><PERSON>"}, "blog_show_author": {"label": "Yazarı göster"}}}, "content": {"name": "İçerik"}, "social_sharing": {"name": "Sosyal medya <PERSON>"}, "share": {"name": "Paylaş", "settings": {"featured_image_info": {"content": "Sosyal medya gönderilerine bağlantı eklerseniz sayfanın öne çıkan görseli, ö<PERSON>zleme görseli olarak gösterilir. [Daha fazla bilgi edinin](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON><PERSON> ve açıklaması, önizleme görseline dahildir. [Daha fazla bilgi edinin](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "text": {"label": "<PERSON><PERSON>"}}}}}, "main-blog": {"name": "Blog gönderileri", "settings": {"header": {"content": "Blog gönderisi kartı"}, "show_image": {"label": "<PERSON>ne çıkan görseli göster", "info": "En iyi sonuçlar için 2:3 en-boy oran<PERSON>na sahip bir gö<PERSON>l kullanın. [<PERSON>ha fazla bilgi edinin](https://help.shopify.com/en/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "paragraph": {"content": "Blog gönderilerinizi düzenleyerek alıntılarınızı değiştirin. [Daha fazla bilgi edinin](https://help.shopify.com/en/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"}, "show_date": {"label": "<PERSON><PERSON><PERSON>"}, "show_author": {"label": "Yazarı göster"}}, "blocks": {"title": {"name": "Başlık", "settings": {"show_date": {"label": "<PERSON><PERSON><PERSON>"}, "show_author": {"label": "Yazarı göster"}}}, "summary": {"name": "Alıntı"}, "link": {"name": "Bağlantı"}}}, "main-cart-footer": {"name": "Alt toplam", "settings": {"show_cart_note": {"label": "<PERSON><PERSON> notunu <PERSON>"}}, "blocks": {"subtotal": {"name": "Alt toplam fiyatı"}, "buttons": {"name": "<PERSON><PERSON><PERSON>"}}}, "main-cart-items": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"show_vendor": {"label": "Satıcıyı göster"}}}, "main-collection-banner": {"name": "Koleksiyon banner'ı", "settings": {"paragraph": {"content": "Koleksiyonunuzu düzenleyerek açıklama veya görsel ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/en/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Koleksiyon açıklamasını görüntüle"}, "show_collection_image": {"label": "Koleksiyon görselini görüntüle", "info": "En iyi sonuçlar için 16:9 en-boy oran<PERSON>na sahip bir gö<PERSON>l kullanın. [<PERSON>ha fazla bilgi edinin](https://help.shopify.com/en/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-collection-product-grid": {"name": "Ürün ızgarası", "settings": {"products_per_page": {"label": "<PERSON><PERSON> b<PERSON><PERSON><PERSON>"}, "enable_filtering": {"label": "<PERSON>lt<PERSON><PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [filtreler](/admin/menus)"}, "enable_sorting": {"label": "Sıralamayı etkinleştir"}, "image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Portre"}, "options__3": {"label": "<PERSON><PERSON>"}}, "show_secondary_image": {"label": "Üstüne gelindiğinde ikinci görseli göster"}, "add_image_padding": {"label": "<PERSON><PERSON><PERSON><PERSON> do<PERSON><PERSON> e<PERSON>"}, "show_vendor": {"label": "Satıcıyı göster"}, "header__1": {"content": "Filtreleme ve sıralama"}, "header__3": {"content": "Ürün kartı"}, "enable_tags": {"label": "<PERSON>lt<PERSON><PERSON><PERSON><PERSON>", "info": "[Filt<PERSON>eri özelleştirin](/admin/menus)"}, "show_image_outline": {"label": "Görsel kenarlığını göster"}, "enable_sort": {"label": "Sıralamayı etkinleştir"}, "collapse_on_larger_devices": {"label": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> ekranlard<PERSON> da<PERSON>"}}}, "main-list-collections": {"name": "Koleksiyonlar listesi sayfası", "settings": {"title": {"label": "Başlık"}, "sort": {"label": "Koleksiyonları sıralama ölçütü:", "options__1": {"label": "Alfabetik olarak, A-Z"}, "options__2": {"label": "Alfabetik olarak, Z-A"}, "options__3": {"label": "<PERSON><PERSON><PERSON>, yeniden eskiye"}, "options__4": {"label": "<PERSON><PERSON><PERSON>, eskiden yeniye"}, "options__5": {"label": "<PERSON><PERSON><PERSON><PERSON>, yüksekten düşüğe"}, "options__6": {"label": "<PERSON><PERSON><PERSON><PERSON>, dü<PERSON><PERSON>kten yükseğe"}}, "image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Portre"}, "options__3": {"label": "<PERSON><PERSON>"}, "info": "Koleksiyonlarınızı düzenleyerek görsel ekleyin. [Daha fazla bilgi edinin](https://help.shopify.com/en/manual/products/collections)"}, "color_scheme": {"options__1": {"label": "1. <PERSON><PERSON><PERSON>"}, "options__2": {"label": "2. <PERSON><PERSON><PERSON>"}, "options__3": {"label": "1. Arka plan"}, "options__4": {"label": "2. Arka plan"}, "options__5": {"label": "<PERSON><PERSON>"}, "label": "Renk şeması"}, "image_padding": {"label": "<PERSON><PERSON><PERSON><PERSON> do<PERSON><PERSON> e<PERSON>"}}}, "main-page": {"name": "Say<PERSON>"}, "main-password-footer": {"name": "<PERSON><PERSON><PERSON>", "settings": {"color_scheme": {"options__1": {"label": "1. <PERSON><PERSON><PERSON>"}, "options__2": {"label": "2. <PERSON><PERSON><PERSON>"}, "options__3": {"label": "1. Arka plan"}, "options__4": {"label": "2. Arka plan"}, "options__5": {"label": "<PERSON><PERSON>"}, "label": "Renk şeması"}}}, "main-password-header": {"name": "<PERSON><PERSON><PERSON> ü<PERSON>bilgisi", "settings": {"logo": {"label": "Lo<PERSON>"}, "logo_max_width": {"label": "Özel logo genişliği", "unit": "piksel"}, "color_scheme": {"options__1": {"label": "1. <PERSON><PERSON><PERSON>"}, "options__2": {"label": "2. <PERSON><PERSON><PERSON>"}, "options__3": {"label": "1. Arka plan"}, "options__4": {"label": "2. Arka plan"}, "options__5": {"label": "<PERSON><PERSON>"}, "label": "Renk şeması"}}}, "main-product": {"blocks": {"text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>"}, "text_style": {"label": "Text style", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}, "options__3": {"label": "Büyük harf"}}}}, "title": {"name": "Başlık"}, "price": {"name": "<PERSON><PERSON><PERSON>"}, "quantity_selector": {"name": "<PERSON><PERSON>"}, "variant_picker": {"name": "Varyasyon <PERSON>ç<PERSON>", "settings": {"picker_type": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Açılır liste"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "buy_buttons": {"name": "Satın al düğmeleri", "settings": {"show_dynamic_checkout": {"label": "Dinamik ödeme düğmelerini göster", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON>, mağazanızda bulunan ödeme yöntemlerini kullanarak PayPal veya Apple Pay gibi tercih ettikleri seçeneği görür. [Daha fazla bilgi edinin](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "pickup_availability": {"name": "<PERSON><PERSON><PERSON> alım stok durumu"}, "description": {"name": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "share": {"name": "Paylaş", "settings": {"featured_image_info": {"content": "Sosyal medya gönderilerine bağlantı eklerseniz sayfanın öne çıkan görseli, ö<PERSON>zleme görseli olarak gösterilir. [Daha fazla bilgi edinin](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON><PERSON> ve açıklaması, önizleme görseline dahildir. [Daha fazla bilgi edinin](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "text": {"label": "<PERSON><PERSON>"}}}, "collapsible_tab": {"name": "Daraltılabilen sekme", "settings": {"heading": {"info": "İçeriği açıklayan bir başlık ekleyin.", "label": "Başlık"}, "content": {"label": "<PERSON><PERSON><PERSON>"}, "page": {"label": "<PERSON><PERSON><PERSON>ı<PERSON> sekme içeriği"}, "icon": {"options__1": {"label": "Hiç<PERSON>i"}, "options__2": {"label": "kutusu eklenemez"}, "options__3": {"label": "<PERSON><PERSON><PERSON> balonu"}, "options__4": {"label": "<PERSON><PERSON>"}, "options__5": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__6": {"label": "Göz"}, "options__7": {"label": "<PERSON><PERSON><PERSON>"}, "options__8": {"label": "Ütü"}, "options__9": {"label": "<PERSON><PERSON><PERSON>"}, "options__10": {"label": "<PERSON><PERSON>"}, "options__11": {"label": "<PERSON><PERSON>"}, "options__12": {"label": "<PERSON><PERSON> pini"}, "options__13": {"label": "Pantolonlar"}, "options__14": {"label": "Uçak"}, "options__15": {"label": "<PERSON><PERSON><PERSON>"}, "options__16": {"label": "<PERSON><PERSON>"}, "options__17": {"label": "İade"}, "options__18": {"label": "Cetvel"}, "options__19": {"label": "Gömlek"}, "options__20": {"label": "Ayakkabı"}, "options__21": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__22": {"label": "Yıld<PERSON>z"}, "options__23": {"label": "<PERSON><PERSON><PERSON>"}, "options__24": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "label": "<PERSON>m<PERSON>"}}}, "popup": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pencere", "settings": {"link_label": {"label": "Bağlantı etiketi"}, "page": {"label": "Say<PERSON>"}}}, "custom_liquid": {"name": "Özel liquid", "settings": {"custom_liquid": {"label": "Özel liquid", "info": "Gelişmiş özelleştirmeler oluşturmak için uygulama parçacıkları veya başka Liquid kodu ekleyin."}}}}, "settings": {"header": {"content": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON> hakkında daha fazla bilgi edinin: [medya tü<PERSON>.](https://help.shopify.com/manual/products/product-media)"}, "enable_video_looping": {"label": "Video döngüs<PERSON><PERSON><PERSON>ş<PERSON>r"}, "enable_sticky_info": {"label": "Geniş ekranlarda sabit ürün bilgisini etkinleştir"}, "hide_variants": {"label": "Bir varyasyon seçtikten sonra diğer varyasyonların medyasını gizleyin"}}, "name": "<PERSON><PERSON><PERSON>n bilgi<PERSON>i"}, "main-search": {"name": "<PERSON><PERSON>", "settings": {"image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Portre"}, "options__3": {"label": "<PERSON><PERSON>"}}, "show_secondary_image": {"label": "Üstüne gelindiğinde ikinci görseli göster"}, "add_image_padding": {"label": "<PERSON><PERSON><PERSON><PERSON> do<PERSON><PERSON> e<PERSON>"}, "show_vendor": {"label": "Satıcıyı göster"}, "header__1": {"content": "Ürün kartı"}, "header__2": {"content": "Blog kartı"}, "article_show_date": {"label": "<PERSON><PERSON><PERSON>"}, "article_show_author": {"label": "Yazarı göster"}, "show_image_outline": {"label": "Görsel kenarlığını göster"}}}, "multicolumn": {"name": "Çoklu sütun", "settings": {"title": {"label": "Başlık"}, "image_width": {"label": "G<PERSON><PERSON><PERSON> genişliği", "options__1": {"label": "Sütun genişliğinin üçte biri"}, "options__2": {"label": "Sütun genişliğinin yarısı"}, "options__3": {"label": "Sütun genişliğinin tama<PERSON>ı"}}, "image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Portre"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}}, "column_alignment": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}}, "background_style": {"label": "İkincil arka plan", "options__1": {"label": "Hiç<PERSON>i"}, "options__2": {"label": "Sütun arka planı olarak göster"}, "options__3": {"label": "Bölüm arka planı olarak göster"}}, "button_label": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "button_link": {"label": "<PERSON><PERSON><PERSON><PERSON> bağlantısı"}, "swipe_on_mobile": {"label": "Mobil cihazda kaydırmayı etkinleştir"}}, "blocks": {"column": {"name": "<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "G<PERSON><PERSON><PERSON>"}, "title": {"label": "Başlık"}, "text": {"label": "<PERSON><PERSON>ı<PERSON><PERSON>"}}}}, "presets": {"name": "Çoklu sütun"}}, "newsletter": {"name": "E-posta kaydı", "settings": {"color_scheme": {"label": "Renk şeması", "options__1": {"label": "1. <PERSON><PERSON><PERSON>"}, "options__2": {"label": "2. <PERSON><PERSON><PERSON>"}, "options__3": {"label": "1. Arka plan"}, "options__4": {"label": "2. Arka plan"}, "options__5": {"label": "<PERSON><PERSON>"}}, "full_width": {"label": "Bölümü tam genişlikli yap"}, "paragraph": {"content": "Her e-posta aboneliği bir müşteri hesabı oluşturur. [Daha fazla bilgi edinin](https://help.shopify.com/en/manual/customers)"}}, "blocks": {"heading": {"name": "Başlık", "settings": {"heading": {"label": "Başlık"}}}, "paragraph": {"name": "Alt başlık", "settings": {"paragraph": {"label": "<PERSON><PERSON>ı<PERSON><PERSON>"}}}, "email_form": {"name": "E-posta formu"}}, "presets": {"name": "E-posta kaydı"}}, "page": {"name": "Say<PERSON>", "settings": {"page": {"label": "Say<PERSON>"}}, "presets": {"name": "Say<PERSON>"}}, "product-recommendations": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "Başlık"}, "header__1": {"content": "<PERSON><PERSON><PERSON><PERSON>", "info": "Dinamik önerilerinin zamanla değişmesi ve gelişmesi için sipariş ve ürün bilgileri kullanılır. [Daha fazla bilgi edinin](https://help.shopify.com/en/themes/development/recommended-products)"}, "header__2": {"content": "Ürün kartı"}, "image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Portre"}, "options__3": {"label": "<PERSON><PERSON>"}}, "show_secondary_image": {"label": "Üstüne gelindiğinde ikinci görseli göster"}, "add_image_padding": {"label": "<PERSON><PERSON><PERSON><PERSON> do<PERSON><PERSON> e<PERSON>"}, "show_vendor": {"label": "Satıcıyı göster"}, "paragraph__1": {"content": "Dinamik önerilerinin zamanla değişmesi ve gelişmesi için sipariş ve ürün bilgileri kullanılır. [Daha fazla bilgi edinin](https://help.shopify.com/en/themes/development/recommended-products)"}, "show_image_outline": {"label": "Görsel kenarlığını göster"}}}, "rich-text": {"name": "<PERSON><PERSON> metin", "settings": {"color_scheme": {"options__1": {"label": "1. <PERSON><PERSON><PERSON>"}, "options__2": {"label": "2. <PERSON><PERSON><PERSON>"}, "options__3": {"label": "1. Arka plan"}, "options__4": {"label": "2. Arka plan"}, "options__5": {"label": "<PERSON><PERSON>"}, "label": "Renk şeması"}, "full_width": {"label": "Bölümü tam genişlikli yap"}}, "blocks": {"heading": {"name": "Başlık", "settings": {"heading": {"label": "Başlık"}, "heading_size": {"options__1": {"label": "Küçük"}, "options__2": {"label": "Orta"}, "label": "Başlık yazı tipi boyutu", "options__3": {"label": "Büyük"}}}}, "text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>ı<PERSON><PERSON>"}}}, "button": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"button_label": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "button_link": {"label": "<PERSON><PERSON><PERSON><PERSON> bağlantısı"}, "button_style_secondary": {"label": "<PERSON><PERSON><PERSON> ç<PERSON> düğ<PERSON> stil<PERSON> kullan"}}}}, "presets": {"name": "<PERSON><PERSON> metin"}}, "apps": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"include_margins": {"label": "B<PERSON>lüm kenar boşluklarını temayla aynı yap"}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "video": {"name": "Video", "settings": {"heading": {"label": "Başlık"}, "cover_image": {"label": "Kapak gö<PERSON>"}, "video_url": {"label": "URL", "placeholder": "YouTube veya Vimeo URL'si kullanın", "info": "Video sayfada oynatılır."}, "description": {"label": "Video alternatif metni", "info": "Ekran okuyucu kullanan müşterilerin daha kolay erişebilmesini sağlamak için videoyu açıklayın."}, "image_padding": {"label": "<PERSON><PERSON><PERSON><PERSON> do<PERSON><PERSON> e<PERSON>", "info": "Kapak görselinizin kırpılmasını istemiyorsanız görsel dolgusunu seçin."}, "full_width": {"label": "Bölümü tam genişlikli yap"}}, "presets": {"name": "Video"}}, "featured-product": {"name": "<PERSON><PERSON>ü<PERSON>", "blocks": {"text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>"}, "text_style": {"label": "<PERSON><PERSON> stili", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}, "options__3": {"label": "Büyük harf"}}}}, "title": {"name": "Başlık"}, "price": {"name": "<PERSON><PERSON><PERSON>"}, "quantity_selector": {"name": "<PERSON><PERSON>"}, "variant_picker": {"name": "Varyasyon <PERSON>ç<PERSON>", "settings": {"picker_type": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Açılır <PERSON>ü"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "buy_buttons": {"name": "Satın al düğmeleri", "settings": {"show_dynamic_checkout": {"label": "Dinamik ödeme düğmelerini göster", "info": "<PERSON><PERSON><PERSON><PERSON><PERSON>, mağazanızda bulunan ödeme yöntemlerini kullanarak PayPal veya Apple Pay gibi tercih ettikleri seçeneği görür. [Daha fazla bilgi edinin](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "description": {"name": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "share": {"name": "Paylaş", "settings": {"featured_image_info": {"content": "Sosyal medya gönderilerine bağlantı eklerseniz sayfanın öne çıkan görseli, önizleme görseli olarak gösterilir. [Daha fazla bilgi edinin](https://help.shopify.com/en/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON><PERSON> ve açıklaması, önizleme görseline dahildir. [Daha fazla bilgi edinin](https://help.shopify.com/en/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "<PERSON><PERSON>"}}}, "custom_liquid": {"name": "Özel liquid", "settings": {"custom_liquid": {"label": "Özel liquid"}}}}, "settings": {"product": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "secondary_background": {"label": "İkincil arka planı göster"}, "header": {"content": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON> hakkında daha fazla bilgi edinin: [medya tü<PERSON>](https://help.shopify.com/manual/products/product-media)"}, "enable_video_looping": {"label": "Video döngüs<PERSON><PERSON><PERSON>ş<PERSON>r"}}, "presets": {"name": "<PERSON><PERSON>ü<PERSON>"}}}}