{"general": {"password_page": {"login_form_heading": "Shop mit Passwort betreten:", "login_password_button": "Mit Passwort betreten", "login_form_password_label": "Passwort", "login_form_password_placeholder": "<PERSON><PERSON>rt", "login_form_error": "Falsches Passwort!", "login_form_submit": "Betreten", "modal": "Passwort-Modal", "admin_link_html": "Bist du der Shop-Inhaber? <a href=\"/admin\" class=\"link underlined-link\"><PERSON>er einloggen</a>", "powered_by_shopify_html": "Dieser Shop wird unterstützt von {{ shopify }}"}, "social": {"alt_text": {"share_on_facebook": "Auf Facebook teilen", "share_on_twitter": "Auf Twitter twittern", "share_on_pinterest": "<PERSON><PERSON>nen"}, "links": {"twitter": "Twitter", "facebook": "Facebook", "pinterest": "Pinterest", "instagram": "Instagram", "tumblr": "Tumblr", "snapchat": "Snapchat", "youtube": "YouTube", "vimeo": "Vimeo", "tiktok": "TikTok"}}, "continue_shopping": "Weiter shoppen", "pagination": {"label": "Seitennummerierung", "page": "Seite {{ number }}", "next": "Nächste Seite", "previous": "Vorherige Seite"}, "search": {"search": "<PERSON><PERSON>"}, "cart": {"view": "<PERSON><PERSON> ({{ count }}) anzeigen", "item_added": "Art<PERSON><PERSON> wurde in den Warenkorb gelegt"}, "share": {"copy_to_clipboard": "<PERSON>", "share": "Teilen", "share_url": "Link", "success_message": "Link in die Zwischenablage kopiert", "close": "Teilen schließen"}}, "date_formats": {"month_year": "%B %Y"}, "newsletter": {"label": "E-Mail", "success": "Danke für deine Anmeldung", "button_label": "Abonnieren"}, "accessibility": {"skip_to_text": "Direkt zum Inhalt", "close": "Schließen", "unit_price_separator": "pro", "vendor": "Anbieter:", "error": "<PERSON><PERSON>", "refresh_page": "<PERSON>n du dich für eine Auswahl entscheidest, wird die Seite komplett aktualisiert.", "link_messages": {"new_window": "Wird in einem neuen Fenster geöffnet.", "external": "Öffnet externe Webseite."}, "next_slide": "Nach rechts schieben", "previous_slide": "Nach links schieben", "loading": "Wird geladen ...", "of": "von", "skip_to_product_info": "Zu Produktinformationen springen", "total_reviews": "Bewertungen insgesamt", "star_reviews_info": "{{ rating_value }} von {{ rating_max }} <PERSON><PERSON>"}, "blogs": {"article": {"blog": "Blog", "read_more_title": "Weiterlesen: {{ title }}", "read_more": "Wei<PERSON>lesen", "comments": {"one": "{{ count }} Kommentar", "other": "{{ count }} <PERSON><PERSON><PERSON><PERSON>"}, "moderated": "<PERSON><PERSON> beach<PERSON>, dass Kommentare vor der Veröffentlichung freigegeben werden müssen.", "comment_form_title": "Hinterlasse einen Kommentar", "name": "Name", "email": "E-Mail", "message": "Kommentar", "post": "Kommentar posten", "back_to_blog": "Zurück zum Blog", "share": "<PERSON><PERSON> Artikel teilen", "success": "Dein Kommentar wurde erfolgreich gepostet! Vielen Dank!", "success_moderated": "Dein Kommentar wurde erfolgreich gepostet. Da unser Blog moderiert wird, werden wir ihn erst kurze Zeit später veröffentlichen."}}, "onboarding": {"product_title": "Beispiel für Produkttitel", "collection_title": "Name deiner Kategorie"}, "products": {"product": {"add_to_cart": "In den Warenkorb legen", "description": "Beschreibung", "on_sale": "Sale", "product_variants": "Produktvarianten", "quantity": {"label": "<PERSON><PERSON><PERSON>", "input_label": "<PERSON><PERSON><PERSON> {{ product }}", "increase": "Erhöhe die Menge für {{ product }}", "decrease": "Verringere die Menge für {{ product }}"}, "price": {"from_price_html": "Von {{ price }}", "regular_price": "<PERSON><PERSON>", "sale_price": "Verkaufspreis", "unit_price": "Grundpreis"}, "share": "Dieses Produkt teilen", "sold_out": "Ausverkauft", "unavailable": "Nicht verfügbar", "vendor": "<PERSON><PERSON><PERSON>", "video_exit_message": "{{ title }} <PERSON><PERSON><PERSON> das Video auf derselben Seite im Vollbildmodus.", "xr_button": "In deinem Bereich an<PERSON>hen", "xr_button_label": "\"An<PERSON><PERSON> in deinem Raum\" lä<PERSON> den Artikel in ein Augmented-Reality-Fenster", "pickup_availability": {"view_store_info": "Shop-Informationen anzeigen", "check_other_stores": "Verfügbarkeit in anderen Shops überprüfen", "pick_up_available": "Abholung verfügbar", "pick_up_available_at_html": "Abholung bei <span class=\"color-foreground\">{{ location_name }}</span> verfügbar", "pick_up_unavailable_at_html": "Abholung bei <span class=\"color-foreground\">{{ location_name }}</span> derzeit nicht verfügbar", "unavailable": "Verfügbarkeit für Abholungen konnte nicht geladen werden", "refresh": "Aktualisieren"}, "media": {"open_featured_media": "Dargestellte Medien in Galerieansicht öffnen", "open_media": "Medien {{ index }} in Galerieansicht öffnen", "play_model": "3D-Viewer abspielen", "play_video": "Video abspielen"}, "view_full_details": "Vollständige Details anzeigen"}, "modal": {"label": "Medien-Galerie"}}, "templates": {"search": {"no_results": "<PERSON>ine Ergebnisse gefunden für \"{{ terms }}\". Überprüfe die Schreibweise oder versuche es mit einer anderen Suchanfrage.", "results_with_count": {"one": "{{ count }} <PERSON><PERSON><PERSON><PERSON> für “{{ terms }}” gefunden", "other": "{{ count }} Ergebnisse für “{{ terms }}” gefunden"}, "title": "Suchergebnisse", "page": "Seite", "products": "Produkte", "search_for": "Nach \"{{ terms }}\" suchen"}, "cart": {"cart": "<PERSON><PERSON><PERSON>"}, "contact": {"form": {"name": "Name", "email": "E-Mail", "phone": "Telefonnummer", "comment": "Kommentar", "send": "Senden", "post_success": "<PERSON><PERSON>, dass du uns kontaktiert hast. Wir werden uns so schnell wie möglich bei dir melden.", "error_heading": "Bitte passe Folgendes an:"}}, "404": {"title": "Seite nicht gefunden", "subtext": "404"}}, "sections": {"header": {"announcement": "Ankündigung", "menu": "<PERSON><PERSON>", "cart_count": {"one": "{{ count }} <PERSON><PERSON><PERSON>", "other": "{{ count }} <PERSON><PERSON><PERSON>"}}, "cart": {"title": "<PERSON><PERSON>", "caption": "Artikel im Warenkorb", "remove_title": "{{ title }} ent<PERSON>nen", "subtotal": "Zwischensumme", "new_subtotal": "Neue Zwischensumme", "note": "Spezielle Bestellanweisungen", "checkout": "<PERSON><PERSON><PERSON><PERSON>", "empty": "<PERSON><PERSON> ist leer", "cart_error": "Beim Aktualisieren deines Warenkorbs ist ein Fehler aufgetreten. Bitte versuche es erneut.", "cart_quantity_error_html": "Du kannst deinem Warenkorb nur [<PERSON><PERSON><PERSON>] Stück dieses Artikels hinzufügen.", "taxes_and_shipping_policy_at_checkout_html": "St<PERSON>ern und <a href=\"{{ link }}\">Versand</a> werden beim Checkout berechnet", "taxes_included_but_shipping_at_checkout": "Inklusive Steuern, Versand wird beim Checkout berechnet", "taxes_included_and_shipping_policy_html": "Inklusive Steuern. <a href=\"{{ link }}\">Versand </a> wird beim Checkout berechnet", "taxes_and_shipping_at_checkout": "Steuern und Versand werden beim Checkout berechnet", "headings": {"product": "Produkt", "price": "Pre<PERSON>", "total": "Gesamtsumme", "quantity": "<PERSON><PERSON><PERSON>"}, "update": "Aktualisieren", "login": {"title": "Hast du ein Konto?", "paragraph_html": "<a href=\"{{ link }}\" class=\"link underlined-link\"><PERSON>gge dich ein</a>, damit es beim <PERSON>out schneller geht."}}, "footer": {"payment": "Zahlungsmethoden", "social_placeholder": "Folge uns auf Social Media!"}, "featured_blog": {"view_all": "Alle anzeigen", "onboarding_title": "Blog-Beitrag", "onboarding_content": "Verschaffe deinen Kunden eine Übersicht über deinen Blog-Beitrag"}, "featured_collection": {"view_all": "Alle anzeigen", "view_all_label": "Alle Produkte in der Kategorie {{ collection_name }} anzeigen"}, "collection_list": {"view_all": "Alle anzeigen"}, "collection_template": {"title": "<PERSON><PERSON><PERSON>", "sort_by_label": "Sortieren nach:", "sort_button": "<PERSON><PERSON><PERSON><PERSON>", "product_count": {"one": "{{ product_count }} von {{ count }} Produkt", "other": "{{ product_count }} von {{ count }} Produkten"}, "empty": "<PERSON>ine Produkte gefunden", "apply": "<PERSON><PERSON><PERSON>", "clear": "Löschen", "clear_all": "Alles löschen", "from": "<PERSON>", "filter_and_sort": "Filtern und sortieren", "filter_by_label": "Filter:", "filter_button": "Filter", "max_price": "Der höchste Preis ist {{ price }}", "reset": "Z<PERSON>ücksetzen", "to": "Nach", "use_fewer_filters_html": "Verwende weniger Filter oder <a class=\"{{ class }}\" href=\"{{ link }}\">lö<PERSON> alle</a>", "filters_selected": {"one": "{{ count }} ausgewählt", "other": "{{ count }} ausgewählt"}, "product_count_simple": {"one": "{{ count }} Produkt", "other": "{{ count }} Produkte"}}, "video": {"load_video": "Video laden: {{ description }}"}}, "localization": {"country_label": "Land/Region", "language_label": "<PERSON><PERSON><PERSON>", "update_language": "Sprache aktualisieren", "update_country": "Land/Region aktualisieren"}, "customer": {"account": {"title": "Ko<PERSON>", "details": "Kontodetails", "view_addresses": "<PERSON><PERSON><PERSON> anzeigen", "return": "Zur<PERSON> zu Kontodetails"}, "account_fallback": "Ko<PERSON>", "activate_account": {"title": "Konto aktivieren", "subtext": "<PERSON><PERSON><PERSON> ein Passwort, um dein Konto zu aktiveren.", "password": "Passwort", "password_confirm": "Passwort bestätigen", "submit": "Konto aktivieren", "cancel": "<PERSON><PERSON><PERSON>"}, "addresses": {"title": "<PERSON><PERSON><PERSON>", "default": "Standard", "add_new": "Neue Adresse hinzufügen", "edit_address": "<PERSON><PERSON><PERSON> bear<PERSON>", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Nachname", "company": "Unternehmen", "address1": "Adresse 1", "address2": "Adresse 2", "city": "Ort", "country": "Land/Region", "province": "Bundesland/Provinz", "zip": "PLZ", "phone": "Telefonnummer", "set_default": "Als Standard-Adresse festlegen", "add": "<PERSON><PERSON><PERSON>", "update": "Adresse aktualisieren", "cancel": "Abbrechen", "edit": "<PERSON><PERSON><PERSON>", "delete": "Löschen", "delete_confirm": "B<PERSON> du sicher, dass du diese Adresse löschen möchtest?"}, "log_in": "Einloggen", "log_out": "Abmelden", "login_page": {"cancel": "Abbrechen", "create_account": "<PERSON><PERSON> er<PERSON>", "email": "E-Mail", "forgot_password": "Hast du dein Passwort vergessen?", "guest_continue": "Fortfahren", "guest_title": "Als Gast fortsetzen", "password": "Passwort", "title": "<PERSON><PERSON>", "sign_in": "Anmelden", "submit": "Senden"}, "orders": {"title": "Bestellhistorie", "order_number": "Bestellung", "order_number_link": "Bestellnummer {{ number }}", "date": "Datum", "payment_status": "Zahlungsstatus", "fulfillment_status": "Fulfillmentstatus", "total": "Gesamtsumme", "none": "Du hast noch keine Bestellungen aufgegeben."}, "recover_password": {"title": "Setze dein Passwort zurück", "subtext": "Wir werden dir eine E-Mail zum Zurücksetzen deines Passworts schicken", "success": "Wir haben dir eine E-Mail mit einem Link zum Aktualisieren deines Passworts geschickt."}, "register": {"title": "<PERSON><PERSON> er<PERSON>", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Nachname", "email": "E-Mail", "password": "Passwort", "submit": "<PERSON><PERSON><PERSON><PERSON>"}, "reset_password": {"title": "Passwort für Konto zurücksetzen", "subtext": "Gib ein neues Passwort für {{ email }} ein", "password": "Passwort", "password_confirm": "Passwort bestätigen", "submit": "Passwort zurücksetzen"}, "order": {"title": "Bestellung {{ name }}", "date_html": "Aufgegeben am {{ date }}", "cancelled_html": "Bestellung storniert am {{ date }}", "cancelled_reason": "Grund: {{ reason }}", "billing_address": "Re<PERSON>nungsadress<PERSON>", "payment_status": "Zahlungsstatus", "shipping_address": "Lieferadresse", "fulfillment_status": "Fulfillmentstatus", "discount": "<PERSON><PERSON><PERSON>", "shipping": "<PERSON>ers<PERSON>", "tax": "<PERSON><PERSON><PERSON>", "product": "Produkt", "sku": "SKU", "price": "Pre<PERSON>", "quantity": "<PERSON><PERSON>", "total": "Gesamtsumme", "fulfilled_at_html": "Ausgeführt am {{ date }}", "track_shipment": "Sendung nachverfolgen", "tracking_url": "Tracking-Link", "tracking_company": "Versanddienstleister", "tracking_number": "Trackingnummer", "subtotal": "Zwischensumme"}}, "gift_cards": {"issued": {"title": "Hier ist dein Geschenkgutschein im Wert von {{ value }} für {{ shop }}!", "subtext": "<PERSON><PERSON>", "gift_card_code": "Gutscheincode", "shop_link": "Weiter shoppen", "remaining_html": "{{ balance }} verbleibend", "add_to_apple_wallet": "Zu Apple Wallet hinzufügen", "qr_image_alt": "QR-Code – <PERSON><PERSON><PERSON>, um Geschenkgutschein einzul<PERSON>sen", "copy_code": "Code kopieren", "expired": "Abgelaufen", "copy_code_success": "Code erfolgreich kopiert", "print_gift_card": "<PERSON><PERSON><PERSON>"}}}