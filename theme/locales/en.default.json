{"general": {"password_page": {"login_form_heading": "Enter store using password:", "login_password_button": "Enter using password", "login_form_password_label": "Password", "login_form_password_placeholder": "Your password", "login_form_error": "Wrong password!", "login_form_submit": "Enter", "modal": "Password modal", "admin_link_html": "Are you the store owner? <a href=\"/admin\" class=\"link underlined-link\">Log in here</a>", "powered_by_shopify_html": "This shop will be powered by {{ shopify }}"}, "social": {"alt_text": {"share_on_facebook": "Share on Facebook", "share_on_twitter": "Tweet on Twitter", "share_on_pinterest": "Pin on Pinterest"}, "links": {"twitter": "Twitter", "facebook": "Facebook", "pinterest": "Pinterest", "instagram": "Instagram", "tumblr": "Tumblr", "snapchat": "Snapchat", "youtube": "YouTube", "vimeo": "Vimeo", "tiktok": "TikTok"}}, "continue_shopping": "Continue shopping", "pagination": {"label": "Pagination", "page": "Page {{ number }}", "next": "Next page", "previous": "Previous page", "current_page": "Page {{ current }} of {{ total }}"}, "search": {"search": "Search", "title": "Search for products on our site", "placeholder": "Search", "submit": "Search", "empty": "Please check the spelling, try a new search, or explore our list of brands.", "heading": {"one": "Search result for", "other": "Search results for"}, "results_with_count": {"one": "{{ count }} result for \"{{ terms }}\"", "other": "{{ count }} results for \"{{ terms }}\""}, "no_results_html": "Please try a different search term or go back to the <a href=\"/\">homepage</a>."}, "cart": {"view": "View my cart", "item_added": "Item added to your cart"}, "share": {"close": "Close share", "copy_to_clipboard": "Copy link", "share_url": "Link", "success_message": "Link copied to clipboard"}, "persistent_messaging": {"persistent_message": ""}}, "date_formats": {"month_year": "%B %Y"}, "newsletter": {"label": "Email", "success": "Thanks for subscribing", "button_label": "Subscribe"}, "accessibility": {"skip_to_text": "Skip to content", "skip_to_product_info": "Skip to product information", "close": "Close", "unit_price_separator": "per", "vendor": "Vendor:", "error": "Error", "refresh_page": "Choosing a selection results in a full page refresh.", "accessibility_footer_link": "Accessibility", "link_messages": {"new_window": "Opens in a new window.", "external": "Opens external website."}, "of": "of", "next_slide": "Slide right", "previous_slide": "Slide left", "loading": "Loading...", "total_reviews": "total reviews", "star_reviews_info": "{{ rating_value }} out of {{ rating_max }} stars"}, "blogs": {"article": {"blog": "Blog", "read_more_title": "Read more: {{ title }}", "comments": {"one": "{{ count }} comment", "other": "{{ count }} comments"}, "moderated": "Please note, comments need to be approved before they are published.", "comment_form_title": "Leave a comment", "name": "Name", "email": "Email", "message": "Comment", "post": "Post comment", "back_to_blog": "Back to blog", "share": "Share this article", "success": "Your comment was posted successfully! Thank you!", "success_moderated": "Your comment was posted successfully. We will publish it in a little while, as our blog is moderated."}}, "onboarding": {"product_title": "Example product title", "collection_title": "Your collection's name"}, "products": {"product": {"add_to_cart": "Add to Bag", "quick_add_cart": "Add", "view_details": "View full product details", "description": "Description", "on_sale": "Sale", "select_size": "Select size", "product_variants": "Product variants", "media": {"open_featured_media": "Open featured media in gallery view", "open_media": "Open media {{ index }} in gallery view", "play_model": "Play 3D Viewer", "play_video": "Play video"}, "quantity": {"label": "Quantity", "input_label": "Quantity for {{ product }}", "increase": "Increase quantity for {{ product }}", "decrease": "Decrease quantity for {{ product }}"}, "pickup_availability": {"view_store_info": "View store information", "check_other_stores": "Check availability at other stores", "pick_up_available": "Pickup available", "pick_up_available_at_html": "Pickup available at <span class=\"color-foreground\">{{ location_name }}</span>", "pick_up_unavailable_at_html": "Pickup currently unavailable at <span class=\"color-foreground\">{{ location_name }}</span>", "unavailable": "Couldn't load pickup availability", "refresh": "Refresh"}, "price": {"from_price_html": "From {{ price }}", "regular_price": "Regular price", "sale_price": "Sale price", "unit_price": "Unit price"}, "quick_add": {"select_size": "Select size", "add_to_cart": "Add", "optional_add_to_cart": "Add to Bag", "add_to_cart_variants_exposed": "Add to Bag"}, "share": "Share this product", "sold_out": "Worth the wait...", "unavailable": "Unavailable", "vendor": "<PERSON><PERSON><PERSON>", "video_exit_message": "{{ title }} opens full screen video in same window.", "view_full_details": "View full details", "xr_button": "View in your space", "xr_button_label": "View in your space, loads item in augmented reality window", "accordion_description": "Description", "accordion_size": "Size & Fit", "accordion_details": "Details", "free_shipping": "United States: Free Shipping On Orders Over $50", "free_shipping_international": "International: Duties and Taxes are Included", "back_in_stock": {"success": "Your notification has been registered.", "prompt": "Get Notified When Item Restocks.", "submit_label": "Submit", "email_placeholder": "Email address *", "checkbox_label": "Sign up for early access marketing"}}, "fit_finder": {"open_button": "Find your size"}, "size_chart": {"open_button": "Size Guide"}, "modal": {"label": "Media gallery"}}, "collections": {"general": {"view_all": "View all", "no_matches": "Sorry, there are no products in this collection", "items_with_count": {"one": "{{ count }} product", "other": "{{ count }} products"}}, "sorting": {"title": "Sort", "featured": "Featured", "best_selling": "Best Selling", "az": "Alphabetically, A-Z", "za": "Alphabetically, Z-A", "price_ascending": "Price, low to high", "price_descending": "Price, high to low", "date_descending": "Date, new to old", "date_ascending": "Date, old to new"}, "filters": {"title": "Filter", "title_tags": "Filter", "all_tags": "All products"}}, "templates": {"search": {"no_results": "No results found for “{{ terms }}”. Check the spelling or use a different word or phrase.", "page": "Page", "products": "Products", "results_with_count": {"one": "{{ count }} result found for “{{ terms }}”", "other": "{{ count }} results found for “{{ terms }}”"}, "title": "Search results", "search_for": "Search for “{{ terms }}”"}, "cart": {"cart": "Added to shopping bag", "cart_empty": "You have no items in your shopping cart."}, "contact": {"form": {"name": "Name", "email": "Email", "phone": "Phone number", "comment": "Comment", "send": "Send", "post_success": "Thanks for contacting us. We'll get back to you as soon as possible.", "error_heading": "Please adjust the following:"}}, "404": {"title": "Page not found", "subtext": "404"}}, "sections": {"header": {"announcement": "Announcement", "menu": "<PERSON><PERSON>", "cart_count": {"one": "{{ count }} item", "other": "{{ count }} items"}}, "cart": {"title": "Your cart", "caption": "Cart items", "remove_title": "Remove {{ title }}", "subtotal": "Subtotal", "new_subtotal": "New subtotal", "note": "Order special instructions", "checkout": "Proceed to Checkout", "empty": "Your cart is empty", "cart_error": "There was an error while updating your cart. Please try again.", "cart_quantity_error_html": "You can only add [quantity] of this item to your cart.", "taxes_and_shipping_policy_at_checkout_html": "Taxes and <a href=\"{{ link }}\">shipping</a> calculated at checkout", "taxes_included_but_shipping_at_checkout": "Tax included and shipping calculated at checkout", "taxes_included_and_shipping_policy_html": "Tax included. <a href=\"{{ link }}\">Shipping</a> calculated at checkout.", "taxes_and_shipping_at_checkout": "Taxes and shipping calculated at checkout", "headings": {"product": "Product", "price": "Price", "total": "Total", "quantity": "Quantity"}, "update": "Update", "login": {"title": "Have an account?", "paragraph_html": "<a href=\"{{ link }}\" class=\"link underlined-link\">Log in</a> to check out faster."}, "limit": "Your cart exceeds the {{ limit | money }} limit. Please remove some items."}, "footer": {"payment": "Payment methods"}, "featured_blog": {"view_all": "View all", "onboarding_title": "Blog post", "onboarding_content": "Give your customers a summary of your blog post"}, "featured_collection": {"view_all": "View all", "view_all_label": "View all products in the {{ collection_name }} collection"}, "collection_list": {"view_all": "View all"}, "collection_template": {"apply": "Apply", "clear": "Clear", "clear_all": "Clear all", "empty": "No products found", "from": "From", "filter_and_sort": "Filter and sort", "filter_by_label": "Filter:", "filter_button": "Filter", "filters_selected": {"one": "{{ count }} selected", "other": "{{ count }} selected"}, "max_price": "The highest price is {{ price }}", "product_count": {"one": "{{ product_count }} of {{ count }} product", "other": "{{ product_count }} of {{ count }} products"}, "product_count_simple": {"one": "{{ count }} product", "other": "{{ count }} products"}, "reset": "Reset", "sort_button": "Sort", "sort_by_label": "Sort by:", "title": "Collection", "to": "To", "use_fewer_filters_html": "Use fewer filters or <a class=\"{{ class }}\" href=\"{{ link }}\">clear all</a>"}, "video": {"load_video": "Load video: {{ description }}"}}, "localization": {"country_label": "Country/region", "language_label": "Language", "update_language": "Update language", "update_country": "Update country/region"}, "customer": {"account": {"title": "My Account", "info": "My Info", "email": "My Email", "address": "My Address", "password": "My Password", "reset_password": "Reset Password", "orders": "Order History", "details": "Account details", "view_addresses": "Address Book", "return": "Return to Account details", "log_out": "Log Out", "create_account": "Register", "create_account_description": "Members are provided a faster checkout experience, accomodations for multiple shipping addresses, order tracking and more.", "new_account": "New Account"}, "account_fallback": "Account", "activate_account": {"title": "Activate account", "subtext": "Create your password to activate your account.", "password": "Password", "password_confirm": "Confirm password", "submit": "Activate account", "cancel": "Decline invitation"}, "addresses": {"title": "Addresses", "default": "<PERSON><PERSON><PERSON>", "add_new": "Add address", "edit_address": "Edit address", "first_name": "First name", "last_name": "Last name", "company": "Company", "address1": "Address 1", "address2": "Address 2", "city": "City", "country": "Country/region", "province": "Province", "zip": "Postal/ZIP code", "phone": "Phone", "set_default": "Set as default address", "add": "Add address", "update": "Update address", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "delete_confirm": "Are you sure you wish to delete this address?"}, "log_in": "Log in", "log_out": "Log out", "login_page": {"cancel": "Go Back", "create_account": "Create account", "email": "Email", "forgot_password": "Forgot your password?", "guest_continue": "Continue", "guest_title": "Continue as a guest", "password": "Password", "title": "<PERSON><PERSON>", "sign_in": "Sign in", "submit": "Submit", "error_message": "Your email address or password is incorrect, please try again"}, "order": {"title": "Order {{ name }}", "date_html": "Placed on {{ date }}", "cancelled_html": "Order Cancelled on {{ date }}", "cancelled_reason": "Reason: {{ reason }}", "billing_address": "Billing Address", "payment_status": "Payment Status", "shipping_address": "Shipping Address", "fulfillment_status": "Fulfillment Status", "discount": "Discount", "shipping": "Shipping", "tax": "Tax", "product": "Product", "sku": "SKU", "price": "Price", "quantity": "Quantity", "total": "Total", "fulfilled_at_html": "Fulfilled {{ date }}", "track_shipment": "Track shipment", "tracking_url": "Tracking link", "tracking_company": "Carrier", "tracking_number": "Tracking number", "subtotal": "Subtotal"}, "orders": {"title": "Order history", "order_number": "Order", "order_number_link": "Order number {{ number }}", "date": "Date", "payment_status": "Payment status", "fulfillment_status": "Fulfillment status", "total": "Total", "none": "You haven't placed any orders yet."}, "recover_password": {"title": "Reset your password", "subtext": "We will send you an email to reset your password", "success": "We've sent you an email with a link to update your password."}, "register": {"title": "Create Account", "first_name": "First Name", "last_name": "Last Name", "email": "Email", "password": "Password", "submit": "Create", "cancel": "Return to Store", "checkbox": "Sign up for our newsletter"}, "reset_password": {"title": "Reset account password", "subtext": "Enter a new password for {{ email }}", "password": "Password", "password_confirm": "Confirm password", "submit": "Reset password"}}, "gift_cards": {"issued": {"title": "Here's your {{ value }} gift card for {{ shop }}!", "subtext": "Your gift card", "gift_card_code": "Gift card code", "shop_link": "Continue shopping", "remaining_html": "Remaining {{ balance }}", "add_to_apple_wallet": "Add to Apple Wallet", "qr_image_alt": "QR code — scan to redeem gift card", "copy_code": "Copy code", "expired": "Expired", "copy_code_success": "Code copied successfully", "print_gift_card": "Print"}}, "checkout": {"international": {"duty_checkbox": "Please confirm you accept our Terms: Duties and Taxes of approximately 32% are non-refundable.", "duty_error": "Please accept the terms to continue with payment."}, "domestic": {"shipping_message": "Note: Signature required on US shipments over $800"}}}